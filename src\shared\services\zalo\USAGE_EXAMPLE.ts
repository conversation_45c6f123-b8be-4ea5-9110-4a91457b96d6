/**
 * <PERSON><PERSON> dụ sử dụng ZaloTokenUtilsService trong các processor và service
 */

import { Injectable, Logger } from '@nestjs/common';
import { ZaloTokenUtilsService } from './zalo-token-utils.service';
import { ZaloConsultationService } from './zalo-consultation.service';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class ZaloProcessorExample {
  private readonly logger = new Logger(ZaloProcessorExample.name);

  constructor(
    private readonly zaloTokenUtils: ZaloTokenUtilsService,
    private readonly zaloConsultationService: ZaloConsultationService,
    private readonly httpService: HttpService,
  ) {}

  /**
   * Ví dụ 1: Gửi tin nhắn với retry mechanism
   */
  async sendMessageWithRetry(oaId: string, userId: string, message: string): Promise<any> {
    try {
      // Sử dụng executeWithTokenRetry để tự động xử lý token và retry
      const result = await this.zaloTokenUtils.executeWithTokenRetry(
        async (accessToken) => {
          return await this.zaloConsultationService.sendConsultationTextMessage(
            accessToken,
            userId,
            message,
          );
        },
        oaId,
        3 // maxRetries
      );

      this.logger.log(`✅ Message sent successfully: ${result.message_id}`);
      return result;
    } catch (error) {
      this.logger.error(`❌ Failed to send message: ${error.message}`);
      throw error;
    }
  }

  /**
   * Ví dụ 2: Gửi tin nhắn với userId và zaloOfficialAccountId
   */
  async sendMessageByUserAndOaId(
    userId: string, 
    zaloOfficialAccountId: string, 
    zaloUserId: string, 
    message: string
  ): Promise<any> {
    try {
      const result = await this.zaloTokenUtils.executeWithTokenRetryByUserAndId(
        async (accessToken) => {
          return await this.zaloConsultationService.sendConsultationTextMessage(
            accessToken,
            zaloUserId,
            message,
          );
        },
        userId,
        zaloOfficialAccountId,
        3 // maxRetries
      );

      this.logger.log(`✅ Message sent successfully: ${result.message_id}`);
      return result;
    } catch (error) {
      this.logger.error(`❌ Failed to send message: ${error.message}`);
      throw error;
    }
  }

  /**
   * Ví dụ 3: Gọi Zalo API trực tiếp với retry
   */
  async callZaloApiWithRetry(oaId: string, apiPath: string, data: any): Promise<any> {
    try {
      const result = await this.zaloTokenUtils.executeWithTokenRetry(
        async (accessToken) => {
          const response = await firstValueFrom(
            this.httpService.post(`https://openapi.zalo.me/v2.0/oa${apiPath}`, data, {
              headers: {
                'access_token': accessToken,
                'Content-Type': 'application/json',
              },
            })
          );
          return response.data;
        },
        oaId,
        3
      );

      this.logger.log(`✅ API call successful`);
      return result;
    } catch (error) {
      this.logger.error(`❌ API call failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Ví dụ 4: Lấy token trực tiếp (không khuyến khích, nên dùng executeWithTokenRetry)
   */
  async getTokenDirectly(oaId: string): Promise<string> {
    try {
      // Lấy token với retry
      const accessToken = await this.zaloTokenUtils.getValidAccessTokenWithRetry(oaId, 3);
      
      this.logger.log(`✅ Got access token for OA ${oaId}`);
      return accessToken;
    } catch (error) {
      this.logger.error(`❌ Failed to get token: ${error.message}`);
      throw error;
    }
  }

  /**
   * Ví dụ 5: Kiểm tra trạng thái OA trước khi xử lý
   */
  async processMessageIfOaActive(oaId: string, userId: string, message: string): Promise<any> {
    try {
      // Kiểm tra OA có active không
      const isActive = await this.zaloTokenUtils.isOfficialAccountActive(oaId);
      
      if (!isActive) {
        this.logger.warn(`OA ${oaId} is not active, skipping message processing`);
        return { success: false, reason: 'OA not active' };
      }

      // Lấy thông tin OA
      const oaInfo = await this.zaloTokenUtils.getOfficialAccount(oaId);
      this.logger.log(`Processing message for OA: ${oaInfo?.name}`);

      // Gửi tin nhắn
      return await this.sendMessageWithRetry(oaId, userId, message);
    } catch (error) {
      this.logger.error(`❌ Failed to process message: ${error.message}`);
      throw error;
    }
  }

  /**
   * Ví dụ 6: Xử lý batch messages với error handling
   */
  async processBatchMessages(oaId: string, messages: Array<{userId: string, content: string}>): Promise<Array<{success: boolean, messageId?: string, error?: string, userId: string}>> {
    const results: Array<{success: boolean, messageId?: string, error?: string, userId: string}> = [];

    for (const msg of messages) {
      try {
        const result = await this.sendMessageWithRetry(oaId, msg.userId, msg.content);
        results.push({ success: true, messageId: result.message_id, userId: msg.userId });
      } catch (error: any) {
        this.logger.error(`Failed to send message to user ${msg.userId}: ${error.message}`);
        results.push({ success: false, error: error.message, userId: msg.userId });

        // Nếu là lỗi token không thể khôi phục, dừng batch
        if (error.message.includes('RESOURCE_NOT_FOUND') || error.message.includes('CONFIGURATION_ERROR')) {
          this.logger.error(`Critical error detected, stopping batch processing`);
          break;
        }
      }
    }

    return results;
  }

  /**
   * Ví dụ 7: Cleanup expired tokens (chạy định kỳ)
   */
  async performTokenCleanup(): Promise<void> {
    try {
      this.logger.log('🧹 Starting token cleanup...');
      await this.zaloTokenUtils.cleanupExpiredTokens();
      this.logger.log('✅ Token cleanup completed');
    } catch (error) {
      this.logger.error(`❌ Token cleanup failed: ${error.message}`);
    }
  }
}

/**
 * Ví dụ sử dụng trong Processor
 */
export class ZaloAiResponseProcessorWithTokenUtils {
  private readonly logger = new Logger(ZaloAiResponseProcessorWithTokenUtils.name);

  constructor(
    private readonly zaloTokenUtils: ZaloTokenUtilsService,
    private readonly zaloConsultationService: ZaloConsultationService,
  ) {}

  async processZaloMessage(oaId: string, zaloUserId: string, messageContent: string): Promise<any> {
    try {
      // Thay vì kiểm tra token thủ công, sử dụng executeWithTokenRetry
      const result = await this.zaloTokenUtils.executeWithTokenRetry(
        async (accessToken) => {
          // Gửi tin nhắn với access token đã được validate và refresh nếu cần
          return await this.zaloConsultationService.sendConsultationTextMessage(
            accessToken,
            zaloUserId,
            messageContent,
          );
        },
        oaId,
        3 // Retry tối đa 3 lần
      );

      this.logger.log(`✅ Message sent to Zalo user ${zaloUserId}, message ID: ${result.message_id}`);
      return result;
    } catch (error) {
      this.logger.error(`❌ Failed to send message to user ${zaloUserId}: ${error.message}`);
      throw error;
    }
  }
}

/**
 * Migration guide từ code cũ sang ZaloTokenUtilsService
 * 
 * TRƯỚC ĐÂY:
 * ```typescript
 * const oa = await this.zaloOaService.getOaByOaId(oaId);
 * if (!oa || !this.zaloOaService.isTokenValid(oa)) {
 *   // Handle error
 * }
 * const result = await this.zaloConsultationService.sendMessage(oa.accessToken, userId, message);
 * ```
 * 
 * BÂY GIỜ:
 * ```typescript
 * const result = await this.zaloTokenUtils.executeWithTokenRetry(
 *   async (accessToken) => {
 *     return await this.zaloConsultationService.sendMessage(accessToken, userId, message);
 *   },
 *   oaId,
 *   3
 * );
 * ```
 */
