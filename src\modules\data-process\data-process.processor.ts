import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName } from '../../queue/queue-name.enum';
import { DataProcessService } from './data-process.service';

// Temporary enum for compilation
export enum DataProcessJobName {
  BULK_CREATE_CUSTOMER_PRODUCTS = 'bulk-create-customer-products',
}

// Temporary interface for compilation
interface BulkCreateCustomerProductsJobData {
  userId: number;
  products: any[];
  timestamp: number;
  trackingId?: string;
}

/**
 * Processor xử lý các job trong queue DATA_PROCESS
 */
@Processor(QueueName.DATA_PROCESS)
export class DataProcessProcessor extends WorkerHost {
  private readonly logger = new Logger(DataProcessProcessor.name);

  constructor(private readonly dataProcessService: DataProcessService) {
    super();
  }

  /**
   * X<PERSON> lý job bulk create customer products
   * @param job Job chứa dữ liệu bulk create
   */
  async process(job: Job<BulkCreateCustomerProductsJobData, any, string>): Promise<any> {
    this.logger.log(`Bắt đầu xử lý job ${job.name} với ID: ${job.id}`);

    try {
      switch (job.name) {
        case DataProcessJobName.BULK_CREATE_CUSTOMER_PRODUCTS:
          return await this.processBulkCreateCustomerProducts(job);
        
        default:
          this.logger.warn(`Job name không được hỗ trợ: ${job.name}`);
          throw new Error(`Job name không được hỗ trợ: ${job.name}`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý job ${job.name} (ID: ${job.id}): ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xử lý job tạo nhiều sản phẩm khách hàng
   * @param job Job chứa dữ liệu bulk create products
   */
  private async processBulkCreateCustomerProducts(
    job: Job<BulkCreateCustomerProductsJobData>
  ): Promise<any> {
    const { userId, products, timestamp, trackingId } = job.data;
    
    this.logger.log(
      `Xử lý bulk create ${products.length} sản phẩm cho userId=${userId}, trackingId=${trackingId}`
    );

    // Cập nhật progress
    await job.updateProgress(0);

    try {
      const result = await this.dataProcessService.bulkCreateCustomerProducts(
        userId,
        products,
        trackingId
      );

      await job.updateProgress(100);
      
      this.logger.log(
        `Hoàn thành bulk create: ${result.totalCreated}/${result.totalRequested} thành công`
      );

      return result;
    } catch (error) {
      this.logger.error(`Lỗi trong processBulkCreateCustomerProducts: ${error.message}`, error.stack);
      throw error;
    }
  }
}
