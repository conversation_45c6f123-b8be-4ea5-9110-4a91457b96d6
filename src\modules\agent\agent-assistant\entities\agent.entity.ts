import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

/**
 * Agent entity
 * Stores general information for all agents in the system
 */
@Entity('agents')
@Index('idx_agents_vector_store_id', ['vectorStoreId'])
@Index('idx_agents_deleted_at', ['deletedAt'])
export class Agent {
  /**
   * UUID unique identifier for each agent
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Agent display name
   */
  @Column({ name: 'name', type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * S3 key for agent avatar
   */
  @Column({ name: 'avatar', type: 'varchar', length: 255, nullable: true })
  avatar?: string;

  /**
   * AI model configuration in JSONB format (e.g., {"temperature": 0.7, "max_tokens": 1000})
   */
  @Column({ name: 'model_config', type: 'jsonb', default: '{}' })
  modelConfig: any;

  /**
   * Instructions or system prompt for agent
   */
  @Column({ name: 'instruction', type: 'text', nullable: true })
  instruction?: string;

  /**
   * Vector store ID used by agent
   */
  @Column({ name: 'vector_store_id', type: 'varchar', length: 100, nullable: true })
  vectorStoreId?: string;

  /**
   * Creation timestamp (timestamp millis)
   */
  @Column({ 
    name: 'created_at', 
    type: 'bigint', 
    nullable: false,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint'
  })
  createdAt: number;

  /**
   * Last update timestamp (timestamp millis)
   */
  @Column({ 
    name: 'updated_at', 
    type: 'bigint', 
    nullable: false,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint'
  })
  updatedAt: number;

  /**
   * Soft delete timestamp (timestamp millis)
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt?: number;
}
