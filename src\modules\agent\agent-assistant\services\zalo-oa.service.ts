import { Injectable, Logger } from '@nestjs/common';
import { ZaloOAAgentAdapterService } from './zalo-oa-agent-adapter.service';

/**
 * Service for managing Zalo Official Account data and tokens
 * Handles fetching OA credentials and agent configurations
 */
@Injectable()
export class ZaloOaService {
  private readonly logger = new Logger(ZaloOaService.name);

  constructor(
    private readonly zaloOAAdapter: ZaloOAAgentAdapterService,
  ) {}



  /**
   * Get OA by OA ID
   * @param oaId Zalo OA ID
   * @returns OA data if found and active
   */
  async getOaByOaId(oaId: string): Promise<any | null> {
    try {
      const oa = await this.zaloOAAdapter.getOaByOaId(oaId);

      if (!oa) {
        this.logger.warn(`No active OA ${oaId} found`);
        return null;
      }

      return oa;

    } catch (error) {
      this.logger.error(`Failed to fetch OA ${oaId}:`, error);
      return null;
    }
  }

  /**
   * Check if access token is valid (not expired)
   * @param oa OA data to check
   * @returns true if token is valid
   */
  isTokenValid(oa: any): boolean {
    return this.zaloOAAdapter.isTokenValid(oa);
  }
}
