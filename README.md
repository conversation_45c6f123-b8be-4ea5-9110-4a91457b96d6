# RedAI Worker

## Overview
RedAI Worker là một ứng dụng NestJS được thiết kế để xử lý các công việc bất đồng bộ thông qua hệ thống queue. Ứng dụng này sử dụng Bull và Redis để quản lý và xử lý các công việc.

## Cấu trúc dự án
```
src/
├── common/              # Các thành phần dùng chung
│   ├── constants/       # Các hằng số
│   ├── decorators/      # Custom decorators
│   ├── dto/             # Data Transfer Objects
│   ├── exceptions/      # Custom exceptions
│   ├── filters/         # Exception filters
│   ├── interceptors/    # Interceptors
│   └── middlewares/     # Middlewares
├── config/              # Cấu hình ứng dụng
├── guards/              # Guards bảo vệ API
├── health/              # Health checks
├── logger/              # Logging service
├── modules/             # Các module nghiệp vụ
│   └── example/         # Module ví dụ
├── queue/               # Queue infrastructure
│   ├── processors/      # Job processors
│   └── strategies/      # Queue strategies
└── main.ts              # Entry point
```

## Các module chính
- **Common**: <PERSON>ung cấp các thành phần dùng chung
- **Config**: <PERSON><PERSON><PERSON>n lý cấu hình ứng dụng
- **Guards**: Bảo vệ API endpoints
- **Health**: Kiểm tra sức khỏe ứng dụng
- **Logger**: Dịch vụ ghi log
- **Queue**: Quản lý và xử lý queue
- **Example**: Module ví dụ triển khai xử lý công việc

## Cài đặt

```bash
# Cài đặt dependencies
$ npm install

# Tạo file .env từ .env.example
$ cp .env.example .env
```

## Chạy ứng dụng

```bash
# Development
$ npm run start

# Watch mode
$ npm run start:dev

# Production mode
$ npm run start:prod
```

## Kiểm tra sức khỏe

```bash
# Kiểm tra sức khỏe ứng dụng
$ curl http://localhost:3000/health
```

## API Endpoints

### Example Module
- **POST /example/process**: Thêm công việc vào queue
- **GET /example/status/:jobId**: Lấy trạng thái của công việc

## Cấu hình Redis
Ứng dụng sử dụng Redis làm backend storage cho Bull queue. Cấu hình Redis được định nghĩa trong file .env:

```
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
```

## Xử lý lỗi
Ứng dụng sử dụng AllExceptionsFilter để xử lý tất cả các lỗi và trả về response với format chuẩn:

```json
{
  "statusCode": 400,
  "errorCode": "EXAMPLE_2000",
  "message": "Invalid input data",
  "details": { "received": null },
  "timestamp": "2023-05-01T12:00:00.000Z"
}
```

## Logging
LoggerService được sử dụng để ghi log với các mức độ khác nhau. Trong môi trường development, tất cả các log sẽ được hiển thị. Trong môi trường production, chỉ log, warn và error được hiển thị.

## Graceful Shutdown
Ứng dụng xử lý graceful shutdown khi nhận được tín hiệu SIGTERM hoặc SIGINT, đảm bảo tất cả các kết nối được đóng đúng cách và các công việc đang xử lý được hoàn thành.

## License

[MIT licensed](LICENSE)
