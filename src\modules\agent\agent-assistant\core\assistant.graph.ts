import { RunnableConfig } from '@langchain/core/runnables';
import {
  Annotation,
  END,
  messagesStateReducer,
  START,
  StateGraph,
} from '@langchain/langgraph';
import { BaseMessage, SystemMessage } from '@langchain/core/messages';
import { AgentAssistantCustomConfigurable } from './configurable-interfaces';
import { initChatModel } from 'langchain/chat_models/universal';
import { Logger } from '@nestjs/common';
import { backOff } from 'exponential-backoff';
import { memorySaver } from './checkpoint-saver';
import { apiKeyEncryption } from '../../helpers/api-key-encryption.helper';

type CustomRunnableConfig = RunnableConfig<AgentAssistantCustomConfigurable>;

const logger = new Logger('AssistantGraph');


const ZaloAssistantGraphState = Annotation.Root({
  messages: Annotation<BaseMessage[], BaseMessage[]>({
    reducer: messagesStateReducer,
    default: () => [],
  }),
});

export type AgentAssistantState = (typeof ZaloAssistantGraphState)['State'];

const callMainAgent = async (
  state: AgentAssistantState,
  config?: CustomRunnableConfig,
) => {
  const agentConfig = config?.configurable?.mainAgent;
  if (!agentConfig) {
    throw new Error('No agent configuration found');
  }
  const modelConfig = agentConfig.model;
  const apiKeys = modelConfig.apiKeys;
  let currentKeyIndex = 0;
  const apiCall = async () => {
    try {
      const encryptedApiKey = apiKeys[currentKeyIndex];

      // Decrypt the API key based on model type
      let decryptedApiKey: string;
      if (modelConfig.type === 'SYSTEM') {
        decryptedApiKey = apiKeyEncryption.decryptAdminApiKey(encryptedApiKey);
      } else {
        // For USER and FINE_TUNE models, use user-specific decryption
        const userId = config?.configurable?.userId;
        if (!userId) {
          throw new Error('User ID is required for user/fine-tune model decryption');
        }
        decryptedApiKey = apiKeyEncryption.decryptUserApiKey(encryptedApiKey, userId);
      }

      const keyIdentifier = `...${encryptedApiKey.slice(-4)}`;
      logger.debug(
        `Attempting API call for agent ${agentConfig.id} with key #${currentKeyIndex} (${keyIdentifier})`,
      );
      const dynamicLLM = await initChatModel(
        `${modelConfig.provider.toLowerCase()}:${modelConfig.name}`,
        {
          configurableFields: modelConfig.samplingParameters,
          ...modelConfig.parameters,
          apiKey: decryptedApiKey,
        },
      );

      // Use agent's instruction instead of hardcoded system message
      const systemPrompt = agentConfig.instruction || 'You are a helpful AI assistant.';
      const input = [
        new SystemMessage({
          content: systemPrompt,
        }),
        ...state.messages,
      ];
      return await dynamicLLM.invoke(input, config);
    } catch (error) {
      logger.error(`API call failed: ${error.message}`);
      throw error;
    }
  };

  const result = await backOff(apiCall, {
    numOfAttempts: apiKeys.length,
    startingDelay: 200, // A small delay between key swaps
    timeMultiple: 1.5, // You can adjust the backoff strategy
    retry: (error: any, attemptNumber: number) => {
      logger.error(`API call failed: ${error.message}`);
      if (attemptNumber >= apiKeys.length) {
        logger.error('All API keys have been tried and failed.');
        return false;
      }
      ++currentKeyIndex;
      logger.log(`Retrying with key #${currentKeyIndex}`);
      return true;
    },
  });

  return {
    messages: [result],
  };
};

const agentAssistantWorkflow = new StateGraph(ZaloAssistantGraphState)
  .addNode('mainAgent', callMainAgent)
  .addEdge(START, 'mainAgent')
  .addEdge('mainAgent', END)
  .compile({
    checkpointer: memorySaver,
  });

export { agentAssistantWorkflow };
