/**
 * Service cho các API truy xuất thông tin Zalo Notification Service (ZNS)
 *
 * <PERSON><PERSON><PERSON>u kiện sử dụng ZNS Info API:
 * - Official Account phải được duyệt và có quyền gửi ZNS
 * - Access token hợp lệ của Official Account
 * - <PERSON><PERSON> thủ giới hạn tần suất gọi API
 *
 * Tài liệu tham khảo: https://developers.zalo.me/docs/zalo-notification-service/truy-xuat-thong-tin/
 */

import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { lastValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { AppException, ErrorCode } from '@common/exceptions';
import {
  ZaloZnsStatusInfo,
  ZaloZnsQuotaInfo,
  ZaloZnsAllowedContent,
  ZaloZnsTemplateSampleData,
  ZaloZnsCustomerRating,
  ZaloZnsCustomerRatingList,
  ZaloZnsQualityInfo,
  ZaloZnsQualityHistoryList,
  ZaloZnsTemplateList,
  ZaloZnsTemplate,
} from './zalo.interface';

@Injectable()
export class ZaloZnsInfoService {
  private readonly logger = new Logger(ZaloZnsInfoService.name);
  private readonly znsApiUrl = 'https://business.openapi.zalo.me/message';
  private readonly templateApiUrl = 'https://business.openapi.zalo.me/template';
  private readonly appId: string;
  private readonly appSecret: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    const appId = this.configService.get<string>('ZALO_APP_ID');
    const appSecret = this.configService.get<string>('ZALO_APP_SECRET');

    if (!appId || !appSecret) {
      throw new Error(
        'ZALO_APP_ID or ZALO_APP_SECRET is not defined in configuration',
      );
    }

    this.appId = appId;
    this.appSecret = appSecret;
  }

  /**
   * Lấy thông tin trạng thái ZNS của Official Account
   * Endpoint: GET https://business.openapi.zalo.me/message/status
   * @param accessToken Access token của Official Account
   * @returns Thông tin trạng thái ZNS
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsStatus(accessToken: string): Promise<ZaloZnsStatusInfo> {
    try {
      const url = `${this.znsApiUrl}/status`;
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsStatusInfo;
        }>(url, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy trạng thái ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy trạng thái ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS status: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy trạng thái ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy trạng thái ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin quota ZNS của Official Account
   * Endpoint: GET https://business.openapi.zalo.me/message/quota
   * @param accessToken Access token của Official Account
   * @returns Thông tin quota ZNS
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsQuota(accessToken: string): Promise<ZaloZnsQuotaInfo> {
    try {
      const url = `${this.znsApiUrl}/quota`;
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsQuotaInfo;
        }>(url, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy quota ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy quota ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS quota: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy quota ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy quota ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin loại nội dung ZNS được phép gửi
   * Endpoint: GET https://business.openapi.zalo.me/message/content_types
   * @param accessToken Access token của Official Account
   * @returns Thông tin loại nội dung được phép gửi
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsAllowedContentTypes(
    accessToken: string,
  ): Promise<ZaloZnsAllowedContent> {
    try {
      const url = `${this.znsApiUrl}/content_types`;
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsAllowedContent;
        }>(url, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy loại nội dung ZNS được phép: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy loại nội dung ZNS được phép: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS allowed content types: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy loại nội dung ZNS được phép: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy loại nội dung ZNS được phép: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách template ZNS với các tùy chọn lọc
   * Endpoint: GET https://business.openapi.zalo.me/template/all
   * @param accessToken Access token của Official Account
   * @param options Tùy chọn lọc và phân trang
   * @returns Danh sách template ZNS
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsTemplateList(
    accessToken: string,
    options?: {
      offset?: number;
      limit?: number;
      status?: 'PENDING' | 'APPROVED' | 'REJECTED' | 'DISABLED';
      templateType?: 'text' | 'media' | 'list' | 'button';
    },
  ): Promise<ZaloZnsTemplateList> {
    try {
      const url = `${this.templateApiUrl}/all`;
      const params: any = {};
      if (options?.offset !== undefined) params.offset = options.offset;
      if (options?.limit !== undefined) params.limit = options.limit;
      if (options?.status !== undefined) params.status = options.status;
      if (options?.templateType !== undefined)
        params.template_type = options.templateType;

      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsTemplateList;
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy danh sách template ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy danh sách template ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS template list: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy danh sách template ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy danh sách template ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết template ZNS
   * Endpoint: GET https://business.openapi.zalo.me/template/info
   * @param accessToken Access token của Official Account
   * @param templateId ID của template
   * @returns Chi tiết template ZNS
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsTemplateDetail(
    accessToken: string,
    templateId: string,
  ): Promise<ZaloZnsTemplate> {
    try {
      const url = `${this.templateApiUrl}/info`;
      const params = { template_id: templateId };
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsTemplate;
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy chi tiết template ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy chi tiết template ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS template detail: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy chi tiết template ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy chi tiết template ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy dữ liệu mẫu của template ZNS
   * Endpoint: GET https://business.openapi.zalo.me/template/sample_data
   * @param accessToken Access token của Official Account
   * @param templateId ID của template
   * @returns Dữ liệu mẫu của template
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsTemplateSampleData(
    accessToken: string,
    templateId: string,
  ): Promise<ZaloZnsTemplateSampleData> {
    try {
      const url = `${this.templateApiUrl}/sample_data`;
      const params = { template_id: templateId };
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsTemplateSampleData;
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy dữ liệu mẫu template ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy dữ liệu mẫu template ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS template sample data: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy dữ liệu mẫu template ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy dữ liệu mẫu template ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin đánh giá của khách hàng
   * Endpoint: GET https://business.openapi.zalo.me/message/rating
   * @param accessToken Access token của Official Account
   * @param startTime Thời gian bắt đầu (Unix timestamp, tùy chọn)
   * @param endTime Thời gian kết thúc (Unix timestamp, tùy chọn)
   * @returns Thông tin đánh giá của khách hàng
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsCustomerRating(
    accessToken: string,
    startTime?: number,
    endTime?: number,
  ): Promise<ZaloZnsCustomerRating> {
    try {
      const url = `${this.znsApiUrl}/rating`;
      const params: any = {};
      if (startTime !== undefined) params.start_time = startTime;
      if (endTime !== undefined) params.end_time = endTime;

      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsCustomerRating;
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy đánh giá khách hàng ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy đánh giá khách hàng ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS customer rating: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy đánh giá khách hàng ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy đánh giá khách hàng ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách chi tiết đánh giá của khách hàng
   * Endpoint: GET https://business.openapi.zalo.me/message/rating/details
   * @param accessToken Access token của Official Account
   * @param options Tùy chọn lọc và phân trang
   * @returns Danh sách chi tiết đánh giá
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsCustomerRatingDetails(
    accessToken: string,
    options?: {
      startTime?: number;
      endTime?: number;
      offset?: number;
      limit?: number;
      rating?: number;
      templateId?: string;
    },
  ): Promise<ZaloZnsCustomerRatingList> {
    try {
      const url = `${this.znsApiUrl}/rating/details`;
      const params: any = {};
      if (options?.startTime !== undefined)
        params.start_time = options.startTime;
      if (options?.endTime !== undefined) params.end_time = options.endTime;
      if (options?.offset !== undefined) params.offset = options.offset;
      if (options?.limit !== undefined) params.limit = options.limit;
      if (options?.rating !== undefined) params.rating = options.rating;
      if (options?.templateId !== undefined)
        params.template_id = options.templateId;

      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsCustomerRatingList;
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy chi tiết đánh giá khách hàng ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy chi tiết đánh giá khách hàng ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS customer rating details: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy chi tiết đánh giá khách hàng ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy chi tiết đánh giá khách hàng ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chất lượng gửi ZNS hiện tại của Official Account
   * Endpoint: GET https://business.openapi.zalo.me/message/quality
   * @param accessToken Access token của Official Account
   * @returns Thông tin chất lượng gửi ZNS
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsQuality(accessToken: string): Promise<ZaloZnsQualityInfo> {
    try {
      const url = `${this.znsApiUrl}/quality`;
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsQualityInfo;
        }>(url, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy thông tin chất lượng ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy thông tin chất lượng ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS quality info: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy thông tin chất lượng ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy thông tin chất lượng ZNS: ${error.message}`,
      );
    }
  }

  /**
   * Lấy lịch sử chất lượng gửi ZNS theo thời gian
   * Endpoint: GET https://business.openapi.zalo.me/message/quality/history
   * @param accessToken Access token của Official Account
   * @param options Tùy chọn thời gian và khoảng đánh giá
   * @returns Lịch sử chất lượng gửi ZNS
   * @throws AppException nếu có lỗi xảy ra
   */
  async getZnsQualityHistory(
    accessToken: string,
    options?: {
      startTime?: number;
      endTime?: number;
      interval?: 'daily' | 'weekly' | 'monthly';
    },
  ): Promise<ZaloZnsQualityHistoryList> {
    try {
      const url = `${this.znsApiUrl}/quality/history`;
      const params: any = {};
      if (options?.startTime !== undefined)
        params.start_time = options.startTime;
      if (options?.endTime !== undefined) params.end_time = options.endTime;
      if (options?.interval !== undefined) params.interval = options.interval;

      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZaloZnsQualityHistoryList;
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy lịch sử chất lượng ZNS: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy lịch sử chất lượng ZNS: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting ZNS quality history: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy lịch sử chất lượng ZNS: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy lịch sử chất lượng ZNS: ${error.message}`,
      );
    }
  }
}
