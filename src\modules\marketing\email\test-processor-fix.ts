/**
 * Test script để kiểm tra processor hoạt động đúng với cả template và non-template campaigns
 * Chạy: npx ts-node src/modules/marketing/email/test-processor-fix.ts
 */

import { BatchEmailMarketingJobDto, EmailRecipientDto } from './dto';

/**
 * Test data cho campaign không dùng template (templateId = 0)
 */
const testNonTemplateCampaign: BatchEmailMarketingJobDto = {
  campaignId: 123,
  templateId: 0, // Không dùng template
  templateVariables: {}, // Object rỗng
  recipients: [
    { audience: { name: 'User 1', email: '<EMAIL>' }, email: '<EMAIL>' },
    { audience: { name: 'User 2', email: '<EMAIL>' }, email: '<EMAIL>' },
  ],
  server: {
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    user: '<EMAIL>',
    password: 'password123',
    from: '<EMAIL>',
  },
  createdAt: Date.now(),
};

/**
 * Test data cho campaign dùng template (templateId > 0)
 */
const testTemplateCampaign: BatchEmailMarketingJobDto = {
  campaignId: 124,
  templateId: 15, // Dùng template ID 15
  templateVariables: {
    companyName: 'RedAI',
    discountPercent: '50',
    validUntil: '31/12/2024',
  },
  recipients: [
    { audience: { name: 'User 1', email: '<EMAIL>' }, email: '<EMAIL>' },
    { audience: { name: 'User 2', email: '<EMAIL>' }, email: '<EMAIL>' },
  ],
  server: {
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    user: '<EMAIL>',
    password: 'password123',
    from: '<EMAIL>',
  },
  createdAt: Date.now(),
};

/**
 * Test data cho campaign với email list (templateId = 0)
 */
const testEmailListCampaign: BatchEmailMarketingJobDto = {
  campaignId: 125,
  templateId: 0, // Không dùng template
  templateVariables: {}, // Object rỗng
  recipients: [
    { audience: { name: 'Customer 1', email: '<EMAIL>' }, email: '<EMAIL>' },
    { audience: { name: 'Customer 2', email: '<EMAIL>' }, email: '<EMAIL>' },
    { audience: { name: 'Customer 3', email: '<EMAIL>' }, email: '<EMAIL>' },
  ],
  createdAt: Date.now(),
  // Không có server config - sẽ dùng default
};

/**
 * Validate job data function (copy từ processor để test)
 */
function validateBatchJobData(jobData: BatchEmailMarketingJobDto): boolean {
  console.log(`\n🔍 Validating job data for campaign ${jobData.campaignId}...`);

  if (!jobData.campaignId) {
    console.error('❌ Missing campaignId');
    return false;
  }

  // templateId có thể = 0 (không dùng template) hoặc > 0 (dùng template)
  if (jobData.templateId === undefined || jobData.templateId === null) {
    console.error('❌ Missing templateId');
    return false;
  }

  if (jobData.templateId < 0) {
    console.error('❌ Invalid templateId: must be >= 0');
    return false;
  }

  if (
    !jobData.recipients ||
    !Array.isArray(jobData.recipients) ||
    jobData.recipients.length === 0
  ) {
    console.error('❌ Missing or empty recipients array');
    return false;
  }

  // Validate từng recipient
  for (const recipient of jobData.recipients) {
    if (!recipient.audience) {
      console.error(`❌ Missing audience for recipient: ${recipient.email}`);
      return false;
    }

    if (!recipient.email || !isValidEmail(recipient.email)) {
      console.error(`❌ Invalid email: ${recipient.email}`);
      return false;
    }
  }

  console.log('✅ Job data validation passed');
  return true;
}

/**
 * Email validation helper
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Test logic xử lý templateId
 */
function testTemplateLogic(jobData: BatchEmailMarketingJobDto): void {
  console.log(
    `\n📧 Testing template logic for campaign ${jobData.campaignId}...`,
  );

  if (jobData.templateId === 0) {
    console.log('📄 templateId = 0 → Sẽ lấy subject/content từ campaign');
    console.log('   - Cần gọi: emailMarketingService.getCampaignById()');
    console.log('   - Sử dụng: campaign.subject và campaign.content');
  } else {
    console.log(
      `📄 templateId = ${jobData.templateId} → Sẽ lấy subject/content từ template`,
    );
    console.log('   - Cần gọi: emailMarketingService.getTemplateById()');
    console.log('   - Sử dụng: template.subject và template.content');
  }

  console.log(
    `📝 Template variables: ${JSON.stringify(jobData.templateVariables)}`,
  );
  console.log(`👥 Recipients count: ${jobData.recipients.length}`);
}

/**
 * Main test function
 */
function runTests(): void {
  console.log('🚀 Testing Email Marketing Processor Fix\n');
  console.log('='.repeat(60));

  // Test 1: Non-template campaign
  console.log('\n📋 TEST 1: Non-template Campaign (API thông thường)');
  console.log('-'.repeat(50));
  if (validateBatchJobData(testNonTemplateCampaign)) {
    testTemplateLogic(testNonTemplateCampaign);
  }

  // Test 2: Template campaign
  console.log('\n📋 TEST 2: Template Campaign (API với template)');
  console.log('-'.repeat(50));
  if (validateBatchJobData(testTemplateCampaign)) {
    testTemplateLogic(testTemplateCampaign);
  }

  // Test 3: Email list campaign
  console.log('\n📋 TEST 3: Email List Campaign (API với email list)');
  console.log('-'.repeat(50));
  if (validateBatchJobData(testEmailListCampaign)) {
    testTemplateLogic(testEmailListCampaign);
  }

  // Test 4: Invalid data
  console.log('\n📋 TEST 4: Invalid Data');
  console.log('-'.repeat(50));
  const invalidData = { ...testNonTemplateCampaign, templateId: -1 };
  validateBatchJobData(invalidData);

  console.log('\n' + '='.repeat(60));
  console.log('✅ All tests completed!');
  console.log('\n📝 Summary:');
  console.log('   - templateId = 0: Lấy từ campaign ✅');
  console.log('   - templateId > 0: Lấy từ template ✅');
  console.log('   - Validation chấp nhận templateId = 0 ✅');
  console.log('   - Validation từ chối templateId < 0 ✅');
}

// Chạy tests
if (require.main === module) {
  runTests();
}

export { runTests, validateBatchJobData, testTemplateLogic };
