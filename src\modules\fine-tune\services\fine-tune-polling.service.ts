import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { QueueName } from '../../../queue';
import { FineTuneJobName } from '../constants/fine-tune-job-name.enum';
import { ProviderFineTuneEnum } from '../constants/provider.enum';
import { FineTunePollingJobData } from '../interfaces/fine-tune-polling.interface';
import { FineTunePollingRepository } from '../repositories/fine-tune-polling.repository';
import { FineTuneLoggingService } from './fine-tune-logging.service';

/**
 * Service để quản lý fine-tune polling operations
 */
@Injectable()
export class FineTunePollingService {
  private readonly logger = new Logger(FineTunePollingService.name);
  private readonly INITIAL_DELAY = 30000; // 30 giây
  private readonly POLLING_INTERVAL = 5 * 60 * 1000; // 5 phút

  constructor(
    @InjectQueue(QueueName.FINE_TUNE) private readonly fineTuneQueue: Queue,
    private readonly repository: FineTunePollingRepository,
    private readonly loggingService: FineTuneLoggingService,
  ) {}

  /**
   * Bắt đầu polling cho một fine-tune job
   */
  async startPolling(
    userId: string,
    modelFineTuneId: string,
    provider: ProviderFineTuneEnum,
  ): Promise<boolean> {
    try {
      this.logger.log('Starting fine-tune polling', {
        userId,
        modelFineTuneId,
        provider,
      });

      // Kiểm tra xem job có cần polling không
      const needsPolling = await this.repository.isJobBeingPolled(
        modelFineTuneId,
        provider,
      );

      if (!needsPolling) {
        this.logger.log('Job does not need polling', {
          modelFineTuneId,
          provider,
        });
        return false;
      }

      // Tạo job data
      const jobData: FineTunePollingJobData = {
        userId,
        modelFineTuneId,
        provider,
        timestamp: Date.now(),
      };

      // Thêm job vào queue với delay ban đầu
      await this.fineTuneQueue.add(
        FineTuneJobName.FINE_TUNE_POLLING,
        jobData,
        {
          delay: this.INITIAL_DELAY,
          attempts: 5,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
          removeOnComplete: 10,
          removeOnFail: 10,
        },
      );

      this.logger.log('Fine-tune polling job added to queue', {
        modelFineTuneId,
        provider,
        delay: this.INITIAL_DELAY,
      });

      return true;
    } catch (error) {
      this.logger.error('Failed to start fine-tune polling', {
        error: error.message,
        userId,
        modelFineTuneId,
        provider,
      });
      return false;
    }
  }

  /**
   * Dừng polling cho một fine-tune job
   */
  async stopPolling(
    modelFineTuneId: string,
    provider: ProviderFineTuneEnum,
  ): Promise<boolean> {
    try {
      this.logger.log('Stopping fine-tune polling', {
        modelFineTuneId,
        provider,
      });

      // Cập nhật status để ngừng polling
      const success = await this.repository.updateFineTuneStatus({
        modelFineTuneId,
        status: 'cancelled',
        isSuccess: false,
        error: 'Polling stopped manually',
      });

      if (success) {
        this.logger.log('Fine-tune polling stopped successfully', {
          modelFineTuneId,
          provider,
        });
      }

      return success;
    } catch (error) {
      this.logger.error('Failed to stop fine-tune polling', {
        error: error.message,
        modelFineTuneId,
        provider,
      });
      return false;
    }
  }

  /**
   * Lấy trạng thái của tất cả jobs đang polling
   */
  async getPollingStatus(provider?: ProviderFineTuneEnum): Promise<any[]> {
    try {
      this.logger.debug('Getting polling status', { provider });

      const pendingJobs = await this.repository.getPendingJobs(provider);

      const statusList = pendingJobs.map(job => ({
        modelFineTuneId: job.modelFineTuneId,
        userId: job.userId,
        provider: job.provider,
        jobId: job.jobId,
        currentStatus: job.currentStatus,
        systemModelId: job.systemModelId,
        userModelId: job.userModelId,
      }));

      this.logger.debug(`Found ${statusList.length} jobs being polled`);

      return statusList;
    } catch (error) {
      this.logger.error('Failed to get polling status', {
        error: error.message,
        provider,
      });
      return [];
    }
  }

  /**
   * Restart polling cho các jobs bị stuck
   */
  async restartStuckJobs(provider?: ProviderFineTuneEnum): Promise<number> {
    try {
      this.logger.log('Restarting stuck jobs', { provider });

      const pendingJobs = await this.repository.getPendingJobs(provider);
      let restartedCount = 0;

      for (const job of pendingJobs) {
        try {
          // Kiểm tra xem job có bị stuck không (ví dụ: không có update trong 1 giờ)
          // Logic này có thể được customize tùy theo yêu cầu

          const success = await this.startPolling(
            job.userId,
            job.modelFineTuneId,
            job.provider,
          );

          if (success) {
            restartedCount++;
          }
        } catch (error) {
          this.logger.error('Failed to restart job', {
            error: error.message,
            modelFineTuneId: job.modelFineTuneId,
            provider: job.provider,
          });
        }
      }

      this.logger.log(`Restarted ${restartedCount} stuck jobs`);
      return restartedCount;
    } catch (error) {
      this.logger.error('Failed to restart stuck jobs', {
        error: error.message,
        provider,
      });
      return 0;
    }
  }

  /**
   * Cleanup completed jobs từ queue
   */
  async cleanupCompletedJobs(): Promise<number> {
    try {
      this.logger.log('Cleaning up completed jobs');

      // Lấy các jobs đã hoàn thành
      const completedJobs = await this.fineTuneQueue.getCompleted();
      const failedJobs = await this.fineTuneQueue.getFailed();

      let cleanedCount = 0;

      // Cleanup completed jobs
      for (const job of completedJobs) {
        try {
          await job.remove();
          cleanedCount++;
        } catch (error) {
          this.logger.warn('Failed to remove completed job', {
            jobId: job.id,
            error: error.message,
          });
        }
      }

      // Cleanup failed jobs
      for (const job of failedJobs) {
        try {
          await job.remove();
          cleanedCount++;
        } catch (error) {
          this.logger.warn('Failed to remove failed job', {
            jobId: job.id,
            error: error.message,
          });
        }
      }

      this.logger.log(`Cleaned up ${cleanedCount} completed/failed jobs`);
      return cleanedCount;
    } catch (error) {
      this.logger.error('Failed to cleanup completed jobs', {
        error: error.message,
      });
      return 0;
    }
  }

  /**
   * Lấy thống kê về polling jobs
   */
  async getPollingStatistics(): Promise<any> {
    try {
      this.logger.debug('Getting polling statistics');

      const [waiting, active, completed, failed] = await Promise.all([
        this.fineTuneQueue.getWaiting(),
        this.fineTuneQueue.getActive(),
        this.fineTuneQueue.getCompleted(),
        this.fineTuneQueue.getFailed(),
      ]);

      const stats = {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        total: waiting.length + active.length + completed.length + failed.length,
      };

      this.loggingService.logJobStatistics({
        totalJobs: stats.total,
        successfulJobs: stats.completed,
        failedJobs: stats.failed,
        pendingJobs: stats.waiting + stats.active,
        averagePollingTime: 0, // Có thể tính toán từ job data
      });

      return stats;
    } catch (error) {
      this.logger.error('Failed to get polling statistics', {
        error: error.message,
      });
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        total: 0,
      };
    }
  }

  /**
   * Health check cho polling system
   */
  async healthCheck(): Promise<boolean> {
    try {
      this.logger.debug('Performing health check');

      // Kiểm tra queue connection
      const queueHealth = await this.fineTuneQueue.isPaused();
      
      // Kiểm tra database connection
      const pendingJobs = await this.repository.getPendingJobs();
      
      // Kiểm tra có jobs bị stuck không
      const stats = await this.getPollingStatistics();

      const healthy = !queueHealth && Array.isArray(pendingJobs) && stats.total >= 0;

      this.loggingService.logHealthCheck(healthy, {
        queuePaused: queueHealth,
        pendingJobsCount: pendingJobs.length,
        queueStats: stats,
      });

      return healthy;
    } catch (error) {
      this.logger.error('Health check failed', {
        error: error.message,
      });

      this.loggingService.logHealthCheck(false, {
        error: error.message,
      });

      return false;
    }
  }

  /**
   * Force update status cho một job
   */
  async forceUpdateStatus(
    modelFineTuneId: string,
    status: string,
    isSuccess: boolean,
    modelId?: string,
    error?: string,
  ): Promise<boolean> {
    try {
      this.logger.log('Force updating job status', {
        modelFineTuneId,
        status,
        isSuccess,
        modelId,
      });

      const success = await this.repository.updateFineTuneStatus({
        modelFineTuneId,
        status,
        isSuccess,
        modelId,
        error,
      });

      if (success) {
        this.logger.log('Job status force updated successfully', {
          modelFineTuneId,
          status,
          isSuccess,
        });
      }

      return success;
    } catch (error) {
      this.logger.error('Failed to force update job status', {
        error: error.message,
        modelFineTuneId,
        status,
        isSuccess,
      });
      return false;
    }
  }
}
