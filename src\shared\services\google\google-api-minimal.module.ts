import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { GoogleOAuthService } from './auth/google-oauth.service';
// import { GoogleGmailOAuthService } from './auth/google-gmail-oauth.service';
import { GoogleSheetsService } from './sheets/google-sheets.service';
import { GoogleCalendarService } from './calendar/google-calendar.service';
import { GoogleAnalyticsService } from './analytics/google-analytics.service';
import { GoogleAdsService } from './ads/google-ads.service';

/**
 * Minimal Google API Module - chỉ với services cần thiết cho workflow
 * Tránh dependency conflicts từ các services khác
 */
@Module({
  imports: [ConfigModule],
  providers: [
    GoogleOAuthService,
    // GoogleGmailOAuthService, // Disabled due to dependency issues
    GoogleSheetsService,
    GoogleCalendarService,
    GoogleAnalyticsService,
    GoogleAdsService,
  ],
  exports: [
    GoogleOAuthService,
    // GoogleGmailOAuthService, // Disabled due to dependency issues
    GoogleSheetsService,
    GoogleCalendarService,
    GoogleAnalyticsService,
    GoogleAdsService,
  ],
})
export class GoogleApiMinimalModule {}
