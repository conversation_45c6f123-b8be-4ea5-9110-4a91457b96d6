import { OnQueueEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger, Inject } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName } from '../../../queue/queue-name.enum';
import { FptSmsBrandnameService, FptSmsConfig } from '../../../shared/services/sms/fpt-sms-brandname.service';
import { HttpService } from '@nestjs/axios';
import Redis from 'ioredis';
import { env } from 'src/config';

/**
 * Interface cho cấu hình FPT SMS tùy chỉnh
 */
interface FptSmsCustomConfig {
  clientId?: string;
  clientSecret?: string;
  brandName?: string;
  apiUrl?: string;
}

/**
 * Interface cho job data
 */
interface FptTestConnectionJobData {
  timestamp: number;
  requestId: string;
  config?: FptSmsCustomConfig;
}

/**
 * Interface cho job result
 */
interface FptTestConnectionResult {
  success: boolean;
  message: string;
  timestamp: number;
  duration: number;
  tokenInfo?: {
    hasToken: boolean;
    tokenLength?: number;
    expiresIn?: number;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

/**
 * Processor xử lý job test kết nối FPT SMS Brandname
 */
@Injectable()
@Processor(QueueName.INTEGRATION, {
  concurrency: 2, // Xử lý tối đa 2 job đồng thời
  stalledInterval: 30 * 1000, // 30 giây
  maxStalledCount: 3,
})
export class FptSmsBrandnameTestProcessor extends WorkerHost {
  private readonly logger = new Logger(FptSmsBrandnameTestProcessor.name);
  private readonly defaultConfig: FptSmsConfig = {
    apiUrl: env.fpt.FPT_SMS_API_URL,
    clientId: env.fpt.FPT_SMS_CLIENT_ID,
    clientSecret: env.fpt.FPT_SMS_CLIENT_SECRET,
    brandName: env.fpt.FPT_SMS_BRANDNAME,
  }

  constructor(
    private readonly fprSmsBrandnameService: FptSmsBrandnameService,
    private readonly httpService: HttpService,
    private readonly redis: Redis,
  ) {
    super();
    this.logger.log('🚀 FPT SMS Brandname Test Processor initialized');
  }

  /**
   * Xử lý job test kết nối FPT SMS Brandname
   */
  async process(job: Job<FptTestConnectionJobData>): Promise<FptTestConnectionResult> {
    const startTime = Date.now();
    const { requestId, timestamp, config } = job.data;

    this.logger.log(`🔍 Processing FPT SMS Brandname test connection job: ${job.id} (${requestId})`);

    try {
      // Tạo service instance với config tùy chỉnh hoặc sử dụng service mặc định
      let tokenResponse;

      if (config && (config.clientId || config.clientSecret || config.apiUrl || config.brandName)) {
        this.logger.log('🔧 Using custom configuration for test...');

        // Tạo config tùy chỉnh
        const customConfig: FptSmsConfig = {
          apiUrl: config.apiUrl || this.defaultConfig.apiUrl,
          clientId: config.clientId || this.defaultConfig.clientId,
          clientSecret: config.clientSecret || this.defaultConfig.clientSecret,
          brandName: config.brandName || this.defaultConfig.brandName,
        };

        // Tạo service instance tùy chỉnh
        const customService = FptSmsBrandnameService.createWithCustomConfig(
          this.httpService,
          customConfig,
          this.redis
        );

        this.logger.log('📞 Calling FPT SMS Brandname getAccessToken with custom config...');
        tokenResponse = await customService.getAccessToken();
      } else {
        this.logger.log('📞 Calling FPT SMS Brandname getAccessToken with default config...');
        tokenResponse = await this.fprSmsBrandnameService.getAccessToken();
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      this.logger.log(`✅ FPT SMS Brandname test connection successful in ${duration}ms`);
      this.logger.log(`📊 Token info: ${tokenResponse.access_token.substring(0, 20)}...`);

      const result: FptTestConnectionResult = {
        success: true,
        message: 'Kết nối FPT SMS Brandname thành công',
        timestamp: endTime,
        duration,
        tokenInfo: {
          hasToken: !!tokenResponse.access_token,
          tokenLength: tokenResponse.access_token?.length || 0,
          expiresIn: tokenResponse.expires_in,
        },
      };

      // Log kết quả để có thể theo dõi
      this.logger.log(`🎉 Job ${job.id} completed successfully:`, JSON.stringify(result, null, 2));

      return result;

    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;

      this.logger.error(`❌ FPT SMS Brandname test connection failed: ${error.message}`, error.stack);

      const result: FptTestConnectionResult = {
        success: false,
        message: `Kết nối FPT SMS Brandname thất bại: ${error.message}`,
        timestamp: endTime,
        duration,
        error: {
          code: error.code || 'UNKNOWN_ERROR',
          message: error.message,
          details: error.response?.data || error.details,
        },
      };

      // Log lỗi chi tiết
      this.logger.error(`💥 Job ${job.id} failed:`, JSON.stringify(result, null, 2));

      return result;
    }
  }

  /**
   * Event handler khi job hoàn thành
   */
  @OnQueueEvent('completed')
  onCompleted(job: Job<FptTestConnectionJobData>, result: FptTestConnectionResult) {
    this.logger.log(`🏁 Job completed: ${job.id}`);
    this.logger.log(`📊 Result: ${result.success ? 'SUCCESS' : 'FAILED'} in ${result.duration}ms`);
    
    if (result.success) {
      this.logger.log(`✅ FPT SMS connection test passed`);
    } else {
      this.logger.warn(`⚠️ FPT SMS connection test failed: ${result.message}`);
    }
  }

  /**
   * Event handler khi job thất bại
   */
  @OnQueueEvent('failed')
  onFailed(job: Job<FptTestConnectionJobData>, error: Error) {
    this.logger.error(`💥 Job failed: ${job.id}`);
    this.logger.error(`❌ Error: ${error.message}`, error.stack);
    
    // Log thông tin job để debug
    this.logger.error(`🔍 Job data:`, JSON.stringify(job.data, null, 2));
    this.logger.error(`🔍 Job attempts: ${job.attemptsMade}/${job.opts.attempts}`);
  }

  /**
   * Event handler khi job bị stalled
   */
  @OnQueueEvent('stalled')
  onStalled(job: Job<FptTestConnectionJobData>) {
    this.logger.warn(`⏰ Job stalled: ${job.id}`);
    this.logger.warn(`🔍 Job data:`, JSON.stringify(job.data, null, 2));
  }

  /**
   * Event handler khi job được retry
   */
  @OnQueueEvent('retries-exhausted')
  onRetriesExhausted(job: Job<FptTestConnectionJobData>, error: Error) {
    this.logger.error(`🔄 Job retries exhausted: ${job.id}`);
    this.logger.error(`❌ Final error: ${error.message}`, error.stack);
    
    // Có thể gửi notification hoặc alert ở đây
    this.logger.error(`🚨 FPT SMS Brandname connection test failed after all retries`);
  }

  /**
   * Event handler khi job đang được xử lý
   */
  @OnQueueEvent('active')
  onActive(job: Job<FptTestConnectionJobData>) {
    this.logger.log(`🔄 Job started: ${job.id} (${job.data.requestId})`);
  }

  /**
   * Event handler khi job được thêm vào queue
   */
  @OnQueueEvent('waiting')
  onWaiting(job: Job<FptTestConnectionJobData>) {
    this.logger.log(`⏳ Job waiting: ${job.id} (${job.data.requestId})`);
  }
}
