import { Injectable, Logger } from '@nestjs/common';
import { MonitoringJobData, MonitoringResult } from '../dto/fine-tune-job.dto';
import { ModelsMonitoringRepository, ModelMonitoringInfo } from '../repositories/models-monitoring.repository';
import { OpenAIFineTuneService } from './openai-fine-tune.service';
import { GoogleFineTuneService } from './google-fine-tune.service';
import { ProviderFineTuneEnum } from '../constants/provider.enum';
import { KeyPairEncryptionService } from '../../../shared/services/encryption/key-pair-encryption.service';

/**
 * Service để monitoring fine-tune status từ providers
 */
@Injectable()
export class FineTuneMonitoringService {
  private readonly logger = new Logger(FineTuneMonitoringService.name);

  constructor(
    private readonly modelsRepository: ModelsMonitoringRepository,
    private readonly openaiService: OpenAIFineTuneService,
    private readonly googleService: GoogleFineTuneService,
    private readonly keyPairEncryptionService: KeyPairEncryptionService,
  ) {}

  /**
   * Thực hiện monitoring một job
   */
  async monitorJob(jobData: MonitoringJobData): Promise<MonitoringResult> {
    try {
      this.logger.debug('Starting job monitoring', {
        historyId: jobData.historyId,
        providerJobId: jobData.providerJobId,
        provider: jobData.provider,
        userId: jobData.userId,
      });

      // Lấy thông tin model từ database
      const modelInfo = await this.modelsRepository.getModelMonitoringInfo(jobData.historyId);
      
      if (!modelInfo) {
        this.logger.error(`Model not found for historyId: ${jobData.historyId}`);
        return {
          shouldContinuePolling: false,
          status: 'failed',
          error: 'Model not found in database',
        };
      }

      // Lấy API key để gọi provider
      const apiKey = await this.getApiKey(modelInfo, jobData.provider);
      
      if (!apiKey) {
        this.logger.error(`API key not found for provider: ${jobData.provider}`);
        return {
          shouldContinuePolling: false,
          status: 'failed',
          error: 'API key not found',
        };
      }

      // Gọi provider để check status
      const pollingResult = await this.pollProviderStatus(
        jobData.providerJobId,
        apiKey,
        jobData.provider,
      );

      // Cập nhật database dựa trên kết quả
      await this.updateModelStatus(jobData.historyId, pollingResult);

      // Chuyển đổi PollingResult thành MonitoringResult
      return {
        shouldContinuePolling: pollingResult.shouldContinuePolling,
        status: pollingResult.status,
        fineTunedModelId: pollingResult.modelId,
        error: pollingResult.error,
        metadata: {
          success: pollingResult.success,
          timestamp: Date.now(),
        },
      };
    } catch (error) {
      this.logger.error('Error monitoring job', {
        error: error.message,
        stack: error.stack,
        jobData,
      });

      return {
        shouldContinuePolling: true, // Tiếp tục thử lại
        status: 'error',
        error: error.message,
      };
    }
  }

  /**
   * Lấy API key từ model info sử dụng KeyPairEncryptionService
   */
  private async getApiKey(modelInfo: ModelMonitoringInfo, provider: ProviderFineTuneEnum): Promise<string | null> {
    try {
      // Lấy thông tin API key từ metadata
      const metadata = modelInfo.metadata;

      // Tìm encrypted data và public key tương ứng với provider
      let encryptedApiKey: string | null = null;
      let publicKey: string | null = null;

      if (provider === ProviderFineTuneEnum.OPENAI) {
        encryptedApiKey = metadata.openaiEncryptedApiKey || metadata.encryptedApiKey;
        publicKey = metadata.openaiPublicKey || metadata.publicKey;
      } else if (provider === ProviderFineTuneEnum.GEMINI) {
        encryptedApiKey = metadata.googleEncryptedApiKey || metadata.encryptedApiKey;
        publicKey = metadata.googlePublicKey || metadata.publicKey;
      }

      if (!encryptedApiKey || !publicKey) {
        this.logger.warn(`No encrypted API key or public key found for provider ${provider} in metadata`, {
          hasEncryptedApiKey: !!encryptedApiKey,
          hasPublicKey: !!publicKey,
        });
        return null;
      }

      // Decrypt API key using KeyPairEncryptionService
      const decryptedApiKey = this.keyPairEncryptionService.decryptObject<string>(
        encryptedApiKey,
        publicKey
      );

      return decryptedApiKey;
    } catch (error) {
      this.logger.error('Error getting API key', {
        error: error.message,
        provider,
        modelId: modelInfo.modelId,
      });
      return null;
    }
  }

  /**
   * Poll status từ provider tương ứng
   */
  private async pollProviderStatus(
    jobId: string,
    apiKey: string,
    provider: ProviderFineTuneEnum,
  ): Promise<any> {
    this.logger.debug('Polling provider status', {
      jobId,
      provider,
    });

    try {
      switch (provider) {
        case ProviderFineTuneEnum.OPENAI:
          return await this.openaiService.getFineTuneJobStatus(jobId, apiKey);
          
        case ProviderFineTuneEnum.GEMINI:
          return await this.googleService.getFineTuneJobStatus(jobId, apiKey);
          
        default:
          this.logger.error('Unsupported provider', { provider });
          return {
            success: false,
            status: 'failed',
            error: `Unsupported provider: ${provider}`,
            shouldContinuePolling: false,
          };
      }
    } catch (error) {
      this.logger.error('Error polling provider status', {
        error: error.message,
        jobId,
        provider,
      });
      
      return {
        success: false,
        status: 'error',
        error: error.message,
        shouldContinuePolling: true, // Tiếp tục thử lại
      };
    }
  }

  /**
   * Cập nhật status trong database
   */
  private async updateModelStatus(historyId: string, pollingResult: any): Promise<void> {
    try {
      if (pollingResult.success && pollingResult.modelId) {
        // Thành công - cập nhật model_id
        await this.modelsRepository.updateModelIdOnSuccess(
          historyId,
          pollingResult.modelId,
          {
            status: pollingResult.status,
            completedAt: Date.now(),
          },
        );
        
        this.logger.log(`Fine-tune completed successfully for model ${historyId}`, {
          fineTunedModelId: pollingResult.modelId,
          status: pollingResult.status,
        });
      } else if (!pollingResult.shouldContinuePolling) {
        // Thất bại hoặc cancelled
        await this.modelsRepository.updateModelOnFailure(
          historyId,
          pollingResult.error || 'Fine-tune failed',
          {
            status: pollingResult.status,
            failedAt: Date.now(),
          },
        );
        
        this.logger.warn(`Fine-tune failed for model ${historyId}`, {
          status: pollingResult.status,
          error: pollingResult.error,
        });
      } else {
        // Đang chạy - cập nhật status
        await this.modelsRepository.updatePollingStatus(
          historyId,
          pollingResult.status,
          {
            lastPolledAt: Date.now(),
          },
        );
        
        this.logger.debug(`Updated polling status for model ${historyId}`, {
          status: pollingResult.status,
        });
      }
    } catch (error) {
      this.logger.error('Error updating model status', {
        error: error.message,
        historyId,
        pollingResult,
      });
    }
  }

  /**
   * Kiểm tra xem job có cần tiếp tục monitoring không
   */
  async shouldContinueMonitoring(historyId: string): Promise<boolean> {
    try {
      return await this.modelsRepository.isModelBeingMonitored(historyId);
    } catch (error) {
      this.logger.error('Error checking if should continue monitoring', {
        error: error.message,
        historyId,
      });
      return false;
    }
  }
}
