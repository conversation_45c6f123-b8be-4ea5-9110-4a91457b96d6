import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EncryptionService } from './encryption.service';
import * as crypto from 'crypto';

/**
 * Interface cho kết quả mã hóa
 */
export interface EncryptionResult {
  encryptedData: string;
  publicKey: string;
}

/**
 * Interface cho kết quả giải mã
 */
export interface DecryptionResult {
  decryptedData: string;
  success: boolean;
}

/**
 * Interface cho Zalo OA token payload
 */
export interface ZaloOAPayload {
  accessToken: string;
  refreshToken: string;
}

/**
 * Service mã hóa tương thích với KeyPairEncryptionService cũ
 * Sử dụng EncryptionService bên trong với interface tương thích
 */
@Injectable()
export class KeyPairEncryptionService {
  private readonly logger = new Logger(KeyPairEncryptionService.name);
  private readonly privateKey: string;

  constructor(
    private readonly encryptionService: EncryptionService,
    private readonly configService: ConfigService,
  ) {
    // Lấy private key từ environment variable
    this.privateKey = this.configService.get<string>('KEY_PAIR_PRIVATE_KEY') || '';

    if (!this.privateKey) {
      this.logger.error('KEY_PAIR_PRIVATE_KEY không được định nghĩa trong file .env');
      throw new Error('KEY_PAIR_PRIVATE_KEY không được định nghĩa');
    }

    this.logger.log('KeyPairEncryptionService đã được khởi tạo');
  }

  /**
   * Mã hóa dữ liệu với public key tự động tạo hoặc sử dụng public key có sẵn
   * @param data Dữ liệu cần mã hóa
   * @param publicKey Public key (optional, sẽ tự tạo nếu không có)
   * @returns Kết quả mã hóa với encryptedData và publicKey
   */
  encrypt(data: any, publicKey?: string): EncryptionResult {
    try {
      // Tạo public key mới nếu không có
      const secretKeyPublic = publicKey || this.generatePublicKey();
      
      // Sử dụng EncryptionService để mã hóa
      const encryptedData = this.encryptionService.encrypt(
        secretKeyPublic,
        this.privateKey,
        data
      );

      return {
        encryptedData,
        publicKey: secretKeyPublic,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi mã hóa dữ liệu: ${error.message}`);
      throw error;
    }
  }

  /**
   * Giải mã dữ liệu với public key
   * @param encryptedData Dữ liệu đã mã hóa
   * @param publicKey Public key để giải mã
   * @returns Kết quả giải mã
   */
  decrypt(encryptedData: string, publicKey: string): DecryptionResult {
    try {
      // Sử dụng EncryptionService để giải mã
      const decryptedData = this.encryptionService.decrypt(
        publicKey,
        this.privateKey,
        encryptedData
      );

      return {
        decryptedData: JSON.stringify(decryptedData),
        success: true,
      };
    } catch (error) {
      this.logger.warn(`Lỗi khi giải mã dữ liệu: ${error.message}`);
      
      // Fallback strategy: trả về dữ liệu gốc
      return {
        decryptedData: encryptedData,
        success: false,
      };
    }
  }

  /**
   * Mã hóa object thành chuỗi JSON đã mã hóa
   * @param obj Object cần mã hóa
   * @param publicKey Public key (optional)
   * @returns Kết quả mã hóa
   */
  encryptObject(obj: any, publicKey?: string): EncryptionResult {
    return this.encrypt(obj, publicKey);
  }

  /**
   * Giải mã chuỗi thành object
   * @param encryptedData Dữ liệu đã mã hóa
   * @param publicKey Public key để giải mã
   * @returns Object đã giải mã
   */
  decryptObject<T>(encryptedData: string, publicKey: string): T {
    const result = this.decrypt(encryptedData, publicKey);
    
    if (!result.success) {
      throw new Error('Không thể giải mã object');
    }

    try {
      return JSON.parse(result.decryptedData) as T;
    } catch (error) {
      this.logger.error(`Lỗi khi parse JSON: ${error.message}`);
      throw new Error('Dữ liệu giải mã không phải JSON hợp lệ');
    }
  }

  /**
   * Tạo public key ngẫu nhiên
   * @returns Public key mới
   */
  generatePublicKey(): string {
    // Tạo public key 32 bytes ngẫu nhiên
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Kiểm tra tính hợp lệ của public key
   * @param publicKey Public key cần kiểm tra
   * @returns true nếu hợp lệ
   */
  isValidPublicKey(publicKey: string): boolean {
    if (!publicKey || typeof publicKey !== 'string') {
      return false;
    }

    // Kiểm tra độ dài và format hex
    return /^[a-fA-F0-9]{64}$/.test(publicKey);
  }

  /**
   * Tạo hash một chiều cho dữ liệu
   * @param data Dữ liệu cần hash
   * @returns Hash string
   */
  createHash(data: string): string {
    return crypto
      .createHash('sha256')
      .update(data)
      .digest('hex');
  }

  /**
   * Xác thực hash
   * @param data Dữ liệu gốc
   * @param hash Hash cần xác thực
   * @returns true nếu hash khớp
   */
  verifyHash(data: string, hash: string): boolean {
    const computedHash = this.createHash(data);
    return computedHash === hash;
  }
}
