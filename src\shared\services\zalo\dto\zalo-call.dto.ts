import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsNumber,
  IsObject,
  Min,
  Max,
} from 'class-validator';

/**
 * Enum định nghĩa loại cuộc gọi
 */
export enum ZaloCallType {
  AUDIO = 'audio',
  VIDEO = 'video',
}

/**
 * Enum định nghĩa trạng thái agent
 */
export enum ZaloAgentStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  BUSY = 'busy',
}

/**
 * Enum định nghĩa trạng thái cấp quyền gọi
 */
export enum ZaloCallPermissionStatusEnum {
  GRANTED = 'granted',
  DENIED = 'denied',
  PENDING = 'pending',
}

/**
 * DTO cho yêu cầu cấp quyền gọi thoại
 */
export class ZaloCallPermissionRequestDto {
  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: '123456789',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: '<PERSON><PERSON> do yêu cầu cấp quyền gọi',
    example: 'Yêu cầu cấp quyền gọi thoại để hỗ trợ tốt hơn',
    required: false,
  })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiProperty({
    description: 'Thông tin bổ sung',
    example: { department: 'support', priority: 'high' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho phản hồi yêu cầu cấp quyền gọi
 */
export class ZaloCallPermissionResponseDto {
  @ApiProperty({
    description: 'Trạng thái yêu cầu',
    enum: ['success', 'failed'],
    example: 'success',
  })
  status: 'success' | 'failed';

  @ApiProperty({
    description: 'ID yêu cầu',
    example: 'req_123456789',
    required: false,
  })
  requestId?: string;

  @ApiProperty({
    description: 'Thông báo',
    example: 'Yêu cầu cấp quyền đã được gửi thành công',
    required: false,
  })
  message?: string;
}

/**
 * DTO cho trạng thái cấp quyền gọi
 */
export class ZaloCallPermissionStatusDto {
  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: '123456789',
  })
  userId: string;

  @ApiProperty({
    description: 'Trạng thái cấp quyền',
    enum: ZaloCallPermissionStatusEnum,
    example: ZaloCallPermissionStatusEnum.GRANTED,
  })
  permissionStatus: ZaloCallPermissionStatusEnum;

  @ApiProperty({
    description: 'Thời gian cấp quyền (Unix timestamp)',
    example: 1625097600000,
    required: false,
  })
  grantedTime?: number;

  @ApiProperty({
    description: 'Thời gian hết hạn quyền (Unix timestamp)',
    example: 1625184000000,
    required: false,
  })
  expiresTime?: number;

  @ApiProperty({
    description: 'Lý do từ chối (nếu có)',
    example: 'Người dùng từ chối cấp quyền',
    required: false,
  })
  denyReason?: string;
}

/**
 * DTO cho yêu cầu tạo link gọi thoại
 */
export class ZaloCallLinkRequestDto {
  @ApiProperty({
    description: 'ID của người dùng Zalo',
    example: '123456789',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'Loại cuộc gọi',
    enum: ZaloCallType,
    example: ZaloCallType.AUDIO,
  })
  @IsEnum(ZaloCallType)
  callType: ZaloCallType;

  @ApiProperty({
    description: 'ID agent thực hiện cuộc gọi',
    example: 'agent_001',
    required: false,
  })
  @IsOptional()
  @IsString()
  agentId?: string;

  @ApiProperty({
    description: 'ID branch',
    example: 'branch_001',
    required: false,
  })
  @IsOptional()
  @IsString()
  branchId?: string;

  @ApiProperty({
    description: 'Thời gian hết hạn link (giây)',
    example: 3600,
    minimum: 300,
    maximum: 86400,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(300) // Tối thiểu 5 phút
  @Max(86400) // Tối đa 24 giờ
  expiresIn?: number;

  @ApiProperty({
    description: 'Thông tin bổ sung',
    example: { purpose: 'support', priority: 'high' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO cho phản hồi tạo link gọi thoại
 */
export class ZaloCallLinkResponseDto {
  @ApiProperty({
    description: 'Link gọi thoại',
    example: 'https://zalo.me/call/abc123def456',
  })
  callLink: string;

  @ApiProperty({
    description: 'ID cuộc gọi',
    example: 'call_123456789',
  })
  callId: string;

  @ApiProperty({
    description: 'Thời gian hết hạn link (Unix timestamp)',
    example: 1625101200000,
  })
  expiresTime: number;

  @ApiProperty({
    description: 'Trạng thái tạo link',
    enum: ['success', 'failed'],
    example: 'success',
  })
  status: 'success' | 'failed';

  @ApiProperty({
    description: 'Thông báo',
    example: 'Link gọi thoại đã được tạo thành công',
    required: false,
  })
  message?: string;
}

/**
 * DTO cho thông tin Agent
 */
export class ZaloAgentInfoDto {
  @ApiProperty({
    description: 'ID của agent',
    example: 'agent_001',
  })
  agentId: string;

  @ApiProperty({
    description: 'Tên agent',
    example: 'Nguyễn Văn A',
  })
  agentName: string;

  @ApiProperty({
    description: 'Email agent',
    example: '<EMAIL>',
    required: false,
  })
  email?: string;

  @ApiProperty({
    description: 'Số điện thoại agent',
    example: '0912345678',
    required: false,
  })
  phone?: string;

  @ApiProperty({
    description: 'Trạng thái agent',
    enum: ZaloAgentStatus,
    example: ZaloAgentStatus.ONLINE,
  })
  status: ZaloAgentStatus;

  @ApiProperty({
    description: 'ID branch mà agent thuộc về',
    example: 'branch_001',
    required: false,
  })
  branchId?: string;

  @ApiProperty({
    description: 'Thông tin bổ sung',
    example: { department: 'support', level: 'senior' },
    required: false,
  })
  metadata?: Record<string, any>;
}

/**
 * DTO cho thông tin Branch
 */
export class ZaloBranchInfoDto {
  @ApiProperty({
    description: 'ID của branch',
    example: 'branch_001',
  })
  branchId: string;

  @ApiProperty({
    description: 'Tên branch',
    example: 'Chi nhánh Hà Nội',
  })
  branchName: string;

  @ApiProperty({
    description: 'Mô tả branch',
    example: 'Chi nhánh chính tại Hà Nội',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Địa chỉ branch',
    example: '123 Đường ABC, Quận XYZ, Hà Nội',
    required: false,
  })
  address?: string;

  @ApiProperty({
    description: 'Số điện thoại branch',
    example: '024-1234-5678',
    required: false,
  })
  phone?: string;

  @ApiProperty({
    description: 'Email branch',
    example: '<EMAIL>',
    required: false,
  })
  email?: string;

  @ApiProperty({
    description: 'Trạng thái branch',
    enum: ['active', 'inactive'],
    example: 'active',
  })
  status: 'active' | 'inactive';

  @ApiProperty({
    description: 'Danh sách agent trong branch',
    type: [ZaloAgentInfoDto],
    required: false,
  })
  agents?: ZaloAgentInfoDto[];

  @ApiProperty({
    description: 'Thông tin bổ sung',
    example: { timezone: 'Asia/Ho_Chi_Minh', capacity: 50 },
    required: false,
  })
  metadata?: Record<string, any>;
}

/**
 * Enum định nghĩa trạng thái cuộc gọi
 */
export enum ZaloCallStatus {
  INITIATED = 'initiated',
  RINGING = 'ringing',
  ANSWERED = 'answered',
  ENDED = 'ended',
  FAILED = 'failed',
}

/**
 * DTO cho thông tin cuộc gọi
 */
export class ZaloCallInfoDto {
  @ApiProperty({
    description: 'ID cuộc gọi',
    example: 'call_123456789',
  })
  callId: string;

  @ApiProperty({
    description: 'ID người gọi',
    example: 'caller_123',
  })
  callerId: string;

  @ApiProperty({
    description: 'ID người nhận',
    example: 'callee_456',
  })
  calleeId: string;

  @ApiProperty({
    description: 'Loại cuộc gọi',
    enum: ZaloCallType,
    example: ZaloCallType.AUDIO,
  })
  callType: ZaloCallType;

  @ApiProperty({
    description: 'Trạng thái cuộc gọi',
    enum: ZaloCallStatus,
    example: ZaloCallStatus.ENDED,
  })
  status: ZaloCallStatus;

  @ApiProperty({
    description: 'Thời gian bắt đầu cuộc gọi (Unix timestamp)',
    example: 1625097600000,
  })
  startTime: number;

  @ApiProperty({
    description: 'Thời gian kết thúc cuộc gọi (Unix timestamp)',
    example: 1625098200000,
    required: false,
  })
  endTime?: number;

  @ApiProperty({
    description: 'Thời lượng cuộc gọi (giây)',
    example: 600,
    required: false,
  })
  duration?: number;

  @ApiProperty({
    description: 'Lý do kết thúc cuộc gọi',
    example: 'Cuộc gọi kết thúc bình thường',
    required: false,
  })
  endReason?: string;

  @ApiProperty({
    description: 'ID agent xử lý cuộc gọi',
    example: 'agent_001',
    required: false,
  })
  agentId?: string;

  @ApiProperty({
    description: 'ID branch',
    example: 'branch_001',
    required: false,
  })
  branchId?: string;

  @ApiProperty({
    description: 'Thông tin bổ sung',
    example: { quality: 'good', recording_url: 'https://...' },
    required: false,
  })
  metadata?: Record<string, any>;
}

/**
 * DTO cho query lịch sử cuộc gọi
 */
export class ZaloCallHistoryQueryDto {
  @ApiProperty({
    description: 'ID của người dùng',
    example: '123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiProperty({
    description: 'ID của agent',
    example: 'agent_001',
    required: false,
  })
  @IsOptional()
  @IsString()
  agentId?: string;

  @ApiProperty({
    description: 'Thời gian bắt đầu (Unix timestamp)',
    example: 1625097600000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  fromTime?: number;

  @ApiProperty({
    description: 'Thời gian kết thúc (Unix timestamp)',
    example: 1625184000000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  toTime?: number;

  @ApiProperty({
    description: 'Số lượng kết quả tối đa',
    example: 20,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiProperty({
    description: 'Offset cho phân trang',
    example: 0,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  offset?: number;
}

/**
 * DTO cho phản hồi lịch sử cuộc gọi
 */
export class ZaloCallHistoryResponseDto {
  @ApiProperty({
    description: 'Danh sách cuộc gọi',
    type: [ZaloCallInfoDto],
  })
  calls: ZaloCallInfoDto[];

  @ApiProperty({
    description: 'Tổng số cuộc gọi',
    example: 150,
  })
  total: number;

  @ApiProperty({
    description: 'Có còn dữ liệu tiếp theo không',
    example: true,
  })
  hasMore: boolean;
}

/**
 * DTO cho yêu cầu kết thúc cuộc gọi
 */
export class ZaloEndCallRequestDto {
  @ApiProperty({
    description: 'ID của cuộc gọi',
    example: 'call_123456789',
  })
  @IsString()
  @IsNotEmpty()
  callId: string;

  @ApiProperty({
    description: 'Lý do kết thúc cuộc gọi',
    example: 'Kết thúc cuộc gọi theo yêu cầu',
    required: false,
  })
  @IsOptional()
  @IsString()
  reason?: string;
}

/**
 * DTO cho yêu cầu cập nhật trạng thái agent
 */
export class ZaloUpdateAgentStatusRequestDto {
  @ApiProperty({
    description: 'ID của agent',
    example: 'agent_001',
  })
  @IsString()
  @IsNotEmpty()
  agentId: string;

  @ApiProperty({
    description: 'Trạng thái mới',
    enum: ZaloAgentStatus,
    example: ZaloAgentStatus.ONLINE,
  })
  @IsEnum(ZaloAgentStatus)
  status: ZaloAgentStatus;
}

/**
 * DTO cho query thống kê cuộc gọi
 */
export class ZaloCallStatisticsQueryDto {
  @ApiProperty({
    description: 'Ngày bắt đầu (YYYY-MM-DD)',
    example: '2023-07-01',
  })
  @IsString()
  @IsNotEmpty()
  fromDate: string;

  @ApiProperty({
    description: 'Ngày kết thúc (YYYY-MM-DD)',
    example: '2023-07-31',
  })
  @IsString()
  @IsNotEmpty()
  toDate: string;

  @ApiProperty({
    description: 'ID của agent',
    example: 'agent_001',
    required: false,
  })
  @IsOptional()
  @IsString()
  agentId?: string;

  @ApiProperty({
    description: 'ID của branch',
    example: 'branch_001',
    required: false,
  })
  @IsOptional()
  @IsString()
  branchId?: string;
}

/**
 * DTO cho thống kê cuộc gọi theo ngày
 */
export class ZaloCallDailyStatDto {
  @ApiProperty({
    description: 'Ngày (YYYY-MM-DD)',
    example: '2023-07-15',
  })
  date: string;

  @ApiProperty({
    description: 'Tổng số cuộc gọi',
    example: 25,
  })
  totalCalls: number;

  @ApiProperty({
    description: 'Số cuộc gọi thành công',
    example: 20,
  })
  successfulCalls: number;

  @ApiProperty({
    description: 'Thời lượng trung bình (giây)',
    example: 450,
  })
  averageDuration: number;
}

/**
 * DTO cho thống kê loại cuộc gọi
 */
export class ZaloCallTypeStatDto {
  @ApiProperty({
    description: 'Số cuộc gọi thoại',
    example: 80,
  })
  audio: number;

  @ApiProperty({
    description: 'Số cuộc gọi video',
    example: 20,
  })
  video: number;
}

/**
 * DTO cho phản hồi thống kê cuộc gọi
 */
export class ZaloCallStatisticsResponseDto {
  @ApiProperty({
    description: 'Tổng số cuộc gọi',
    example: 100,
  })
  totalCalls: number;

  @ApiProperty({
    description: 'Số cuộc gọi thành công',
    example: 85,
  })
  successfulCalls: number;

  @ApiProperty({
    description: 'Số cuộc gọi thất bại',
    example: 15,
  })
  failedCalls: number;

  @ApiProperty({
    description: 'Thời lượng trung bình (giây)',
    example: 420,
  })
  averageDuration: number;

  @ApiProperty({
    description: 'Tổng thời lượng (giây)',
    example: 35700,
  })
  totalDuration: number;

  @ApiProperty({
    description: 'Thống kê theo loại cuộc gọi',
    type: ZaloCallTypeStatDto,
  })
  callTypes: ZaloCallTypeStatDto;

  @ApiProperty({
    description: 'Thống kê theo ngày',
    type: [ZaloCallDailyStatDto],
  })
  dailyStats: ZaloCallDailyStatDto[];
}

/**
 * DTO cho mã lỗi Call API
 */
export class ZaloCallErrorCodeDto {
  @ApiProperty({
    description: 'Mã lỗi',
    example: 1001,
  })
  errorCode: number;

  @ApiProperty({
    description: 'Tên lỗi',
    example: 'PERMISSION_DENIED',
  })
  errorName: string;

  @ApiProperty({
    description: 'Mô tả lỗi',
    example: 'Người dùng chưa cấp quyền gọi thoại',
  })
  errorDescription: string;
}

/**
 * DTO cho phản hồi chung
 */
export class ZaloCallCommonResponseDto {
  @ApiProperty({
    description: 'Trạng thái thành công',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Thông báo',
    example: 'Thao tác thành công',
    required: false,
  })
  message?: string;
}
