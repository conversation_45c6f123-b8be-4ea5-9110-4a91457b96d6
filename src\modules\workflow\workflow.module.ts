import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { AdminWorkflowController } from './controllers/admin-workflow.controller';
import { UserWorkflowController } from './controllers/user-workflow.controller';
import {
  Connection,
  Execution,
  ExecutionNodeData,
  Node,
  NodeDefinition,
  WebhookRegistry,
  Workflow,
} from './entities';
import {
  ConnectionRepository,
  ExecutionNodeDataRepository,
  ExecutionRepository,
  NodeDefinitionRepository,
  NodeRepository,
  WebhookRegistryRepository,
  WorkflowRepository,
} from './repositories';
import { AdminWorkflowService } from './service/admin-workflow.service';
import { UserWorkflowService } from './service/user-workflow.service';
import { AdminWorkflowProcessor, UserWorkflowProcessor } from './processors';
import { QueueName } from '../../queue/queue-name.enum';

/**
 * Workflow Module
 * Chứa tất cả entities, repositories và services cho workflow system
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Workflow,
      Node,
      Connection,
      Execution,
      ExecutionNodeData,
      NodeDefinition,
      WebhookRegistry,
    ]),
    BullModule.registerQueue({
      name: QueueName.WORKFLOW_EXECUTION,
    }),
  ],
  controllers: [
    UserWorkflowController,
    AdminWorkflowController,
  ],
  providers: [
    WorkflowRepository,
    NodeRepository,
    ConnectionRepository,
    ExecutionRepository,
    ExecutionNodeDataRepository,
    NodeDefinitionRepository,
    WebhookRegistryRepository,
    UserWorkflowService,
    AdminWorkflowService,
    AdminWorkflowProcessor,
    UserWorkflowProcessor,
  ],
  exports: [
    TypeOrmModule,
    WorkflowRepository,
    NodeRepository,
    ConnectionRepository,
    ExecutionRepository,
    ExecutionNodeDataRepository,
    NodeDefinitionRepository,
    WebhookRegistryRepository,
  ],
})
export class WorkflowModule { }