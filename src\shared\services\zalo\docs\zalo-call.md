# Zalo Call Service Documentation

## Tổng quan

Zalo Call Service cung cấp các API để quản lý tính năng gọi thoại của Zalo Official Account, bao gồm:

- Gửi yêu cầu cấp quyền gọi thoại
- Kiểm tra trạng thái cấp quyền gọi
- Tạo link gọi thoại outbound từ OA đến User
- Quản lý thông tin Agent và Branch
- <PERSON> dõi lịch sử và thống kê cuộc gọi

## Điều kiện sử dụng

Dựa trên tài liệu chính thức của Zalo:

### Cấp quyền gọi
- OA cần được phê duyệt tính năng gọi thoại từ Zalo
- Chỉ có thể gửi yêu cầu cấp quyền đến user đã tương tác với OA
- User có quyền từ chối hoặc chấp nhận yêu cầu

### Kiểm tra quyền
- Chỉ kiểm tra được quyền của user đã tương tác với OA
- Quyền gọi thoại có thể có thời gian hết hạn

### Tạo link gọi
- Chỉ tạo được link cho user đã cấp quyền gọi thoại
- Link gọi thoại có thời gian hết hạn tối đa 24 giờ
- Cần cấu hình agent và branch trước khi sử dụng

### Giới hạn
- Mỗi OA có giới hạn số cuộc gọi đồng thời
- Thời gian hết hạn link: tối thiểu 5 phút, tối đa 24 giờ

## API Endpoints

### 1. Gửi yêu cầu cấp quyền gọi

```typescript
async requestCallPermission(
  accessToken: string,
  request: ZaloCallPermissionRequest
): Promise<ZaloCallPermissionResponse>
```

**Tham số:**
- `accessToken`: Access token của Official Account
- `request`: Thông tin yêu cầu cấp quyền

**Ví dụ:**
```typescript
const request = {
  user_id: '*********',
  reason: 'Yêu cầu cấp quyền gọi thoại để hỗ trợ tốt hơn',
  metadata: { department: 'support', priority: 'high' }
};

const result = await zaloCallService.requestCallPermission(accessToken, request);
```

### 2. Kiểm tra trạng thái cấp quyền gọi

```typescript
async checkCallPermission(
  accessToken: string,
  userId: string
): Promise<ZaloCallPermissionStatus>
```

**Ví dụ:**
```typescript
const status = await zaloCallService.checkCallPermission(accessToken, '*********');
console.log(status.permission_status); // 'granted', 'denied', 'pending'
```

### 3. Tạo link gọi thoại

```typescript
async createCallLink(
  accessToken: string,
  request: ZaloCallLinkRequest
): Promise<ZaloCallLinkResponse>
```

**Ví dụ:**
```typescript
const request = {
  user_id: '*********',
  call_type: 'audio', // hoặc 'video'
  agent_id: 'agent_001',
  branch_id: 'branch_001',
  expires_in: 3600 // 1 giờ
};

const result = await zaloCallService.createCallLink(accessToken, request);
console.log(result.call_link); // Link để thực hiện cuộc gọi
```

### 4. Lấy thông tin Agent

```typescript
// Lấy thông tin một agent cụ thể
async getAgentInfo(accessToken: string, agentId: string): Promise<ZaloAgentInfo>

// Lấy thông tin tất cả agent
async getAgentInfo(accessToken: string): Promise<ZaloAgentInfo[]>
```

### 5. Lấy thông tin Branch

```typescript
// Lấy thông tin một branch cụ thể
async getBranchInfo(accessToken: string, branchId: string): Promise<ZaloBranchInfo>

// Lấy thông tin tất cả branch
async getBranchInfo(accessToken: string): Promise<ZaloBranchInfo[]>
```

### 6. Lấy thông tin cuộc gọi

```typescript
async getCallInfo(accessToken: string, callId: string): Promise<ZaloCallInfo>
```

### 7. Lấy lịch sử cuộc gọi

```typescript
async getCallHistory(
  accessToken: string,
  options?: {
    userId?: string;
    agentId?: string;
    fromTime?: number;
    toTime?: number;
    limit?: number;
    offset?: number;
  }
): Promise<{ calls: ZaloCallInfo[]; total: number; has_more: boolean }>
```

### 8. Kết thúc cuộc gọi

```typescript
async endCall(
  accessToken: string,
  callId: string,
  reason?: string
): Promise<{ success: boolean; message?: string }>
```

### 9. Lấy thống kê cuộc gọi

```typescript
async getCallStatistics(
  accessToken: string,
  fromDate: string,
  toDate: string,
  agentId?: string,
  branchId?: string
): Promise<ZaloCallStatisticsResponse>
```

### 10. Cập nhật trạng thái agent

```typescript
async updateAgentStatus(
  accessToken: string,
  agentId: string,
  status: 'online' | 'offline' | 'busy'
): Promise<{ success: boolean; message?: string }>
```

## Mã lỗi

Service cung cấp danh sách mã lỗi thông qua method `getCallErrorCodes()`:

| Mã lỗi | Tên lỗi | Mô tả |
|---------|---------|-------|
| 1001 | PERMISSION_DENIED | Người dùng chưa cấp quyền gọi thoại |
| 1002 | PERMISSION_EXPIRED | Quyền gọi thoại đã hết hạn |
| 1003 | USER_NOT_FOUND | Không tìm thấy người dùng |
| 1004 | AGENT_NOT_AVAILABLE | Agent không có sẵn |
| 1005 | CALL_LIMIT_EXCEEDED | Vượt quá giới hạn số cuộc gọi đồng thời |
| 1006 | INVALID_CALL_TYPE | Loại cuộc gọi không hợp lệ |
| 1007 | CALL_LINK_EXPIRED | Link gọi thoại đã hết hạn |
| 1008 | BRANCH_NOT_FOUND | Không tìm thấy branch |
| 1009 | AGENT_NOT_FOUND | Không tìm thấy agent |
| 1010 | CALL_FEATURE_NOT_ENABLED | Tính năng gọi thoại chưa được kích hoạt cho OA |

## Sử dụng trong Controller

```typescript
import { ZaloCallService } from '@shared/services/zalo';

@Controller('zalo/call')
export class ZaloCallController {
  constructor(private readonly zaloCallService: ZaloCallService) {}

  @Post('permission/request')
  async requestPermission(@Body() request: ZaloCallPermissionRequestDto) {
    return this.zaloCallService.requestCallPermission(accessToken, request);
  }

  @Get('permission/check/:userId')
  async checkPermission(@Param('userId') userId: string) {
    return this.zaloCallService.checkCallPermission(accessToken, userId);
  }

  @Post('link/create')
  async createLink(@Body() request: ZaloCallLinkRequestDto) {
    return this.zaloCallService.createCallLink(accessToken, request);
  }
}
```

## Lưu ý quan trọng

1. **Access Token**: Cần access token hợp lệ của Official Account
2. **Quyền gọi**: User phải cấp quyền trước khi có thể tạo link gọi
3. **Agent/Branch**: Cần cấu hình agent và branch trước khi sử dụng
4. **Thời gian hết hạn**: Link gọi có thời gian hết hạn, cần kiểm tra trước khi sử dụng
5. **Giới hạn**: Mỗi OA có giới hạn số cuộc gọi đồng thời
6. **Error Handling**: Luôn xử lý exception khi gọi API

## Tài liệu tham khảo

- [Zalo Official Account Call API - Tổng quan](https://developers.zalo.me/docs/official-account/goi-thoai/tong-quan)
- [Cấp quyền gọi](https://developers.zalo.me/docs/official-account/goi-thoai/cap-quyen-goi)
- [Tạo link gọi thoại](https://developers.zalo.me/docs/official-account/goi-thoai/tao-link-goi-thoai)
- [Thông tin Agent, Branch](https://developers.zalo.me/docs/official-account/goi-thoai/tao-link-goi-thoai/lay-thong-tin-agent-branch)
