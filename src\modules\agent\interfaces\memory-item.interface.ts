/**
 * @file Memory Item Interface
 *
 * Defines the core structure for memory items used throughout the long-term memory system.
 * This interface standardizes memory data representation across different components.
 */

/**
 * Core memory item structure used throughout the long-term memory system
 *
 * @interface MemoryItem
 * @description Represents a single memory item with its content and metadata.
 * This interface serves as the foundation for all memory-related operations in the system.
 *
 * @example
 * ```typescript
 * const memoryItem: MemoryItem = {
 *   id: "mem_123456",
 *   content: "User prefers dark mode for the interface",
 *   timestamp: new Date(),
 *   metadata: { category: "preferences", importance: "high" }
 * };
 * ```
 */
export interface MemoryItem {
  /**
   * Unique identifier for the memory item
   *
   * @type {string | number}
   * @description Can be a UUID string for new memories or a numeric ID for database records.
   * String format is preferred for new memories (e.g., "mem_123456" or UUID).
   * Numeric format is used when retrieving existing records from the database.
   */
  id: string | number;

  /**
   * Text content of the memory
   *
   * @type {string}
   * @description The actual memory content that will be stored and retrieved.
   * This should be meaningful, concise text that captures the essence of what needs to be remembered.
   * Examples: user preferences, conversation context, learned behaviors, etc.
   */
  content: string;

  /**
   * Timestamp when the memory was created or last updated
   *
   * @type {Date}
   * @description Used for chronological ordering, expiration policies, and temporal context.
   * Should be set to the current time when creating new memories.
   */
  timestamp: Date;

  /**
   * Additional metadata for the memory (optional)
   *
   * @type {Record<string, any> | undefined}
   * @description Flexible object for storing additional context or metadata.
   * Can include information like:
   * - source: where the memory originated (e.g., "conversation", "user_input", "system")
   * - importance: numerical score indicating memory importance (1-10)
   * - tags: array of string tags for categorization
   * - context: additional contextual information
   * - userId: associated user ID for user-specific memories
   * - agentId: associated agent ID for agent-specific memories
   */
  metadata?: Record<string, any>;
}

/**
 * Type guard to check if an object is a valid MemoryItem
 *
 * @param obj - Object to check
 * @returns True if the object conforms to the MemoryItem interface
 *
 * @example
 * ```typescript
 * if (isMemoryItem(someObject)) {
 *   // TypeScript now knows someObject is a MemoryItem
 *   console.log(someObject.content);
 * }
 * ```
 */
export function isMemoryItem(obj: any): obj is MemoryItem {
  return (
    obj &&
    typeof obj === 'object' &&
    (typeof obj.id === 'string' || typeof obj.id === 'number') &&
    typeof obj.content === 'string' &&
    obj.timestamp instanceof Date &&
    (obj.metadata === undefined ||
      (typeof obj.metadata === 'object' && obj.metadata !== null))
  );
}

/**
 * Utility type for creating a new MemoryItem with optional fields
 *
 * @description Makes id and timestamp optional for easier memory creation.
 * The system will generate these fields automatically if not provided.
 */
export type CreateMemoryItem = Omit<MemoryItem, 'id' | 'timestamp'> & {
  id?: string | number;
  timestamp?: Date;
};

/**
 * Utility type for updating an existing MemoryItem
 *
 * @description Makes all fields except id optional for partial updates.
 * The id is required to identify which memory to update.
 */
export type UpdateMemoryItem = Pick<MemoryItem, 'id'> &
  Partial<Omit<MemoryItem, 'id'>>;
