import { 
  NodeExecutionContext, 
  DetailedNodeExecutionResult, 
  NodeExecutionConfig,
  NodeExecutionStatus,
  NodeExecutionPriority 
} from '../../types';
import { NodeGroupEnum } from '../../../../enums/node-group.enum';

/**
 * Interface contract cho tất cả node executors
 * Mỗi node type sẽ implement interface này
 */
export interface INodeExecutor {
  /**
   * Node group mà executor này xử lý
   */
  readonly nodeGroup: NodeGroupEnum;
  
  /**
   * Danh sách node types cụ thể mà executor này hỗ trợ
   */
  readonly supportedNodeTypes: string[];
  
  /**
   * Tên của executor (để logging và debugging)
   */
  readonly executorName: string;
  
  /**
   * Version của executor
   */
  readonly version: string;
  
  /**
   * Kiểm tra xem executor có thể xử lý node type này không
   * @param nodeType - Type của node cần kiểm tra
   * @returns true nếu có thể xử lý
   */
  canHandle(nodeType: string): boolean;
  
  /**
   * Validate input data trước khi thực thi
   * @param context - Node execution context
   * @returns Promise<ValidationResult>
   */
  validateInput(context: NodeExecutionContext): Promise<ValidationResult>;
  
  /**
   * Thực thi node với context đã cho
   * @param context - Node execution context
   * @param config - Execution configuration
   * @returns Promise<DetailedNodeExecutionResult>
   */
  execute(
    context: NodeExecutionContext, 
    config?: NodeExecutionConfig
  ): Promise<DetailedNodeExecutionResult>;
  
  /**
   * Validate output data sau khi thực thi
   * @param outputData - Dữ liệu output cần validate
   * @param context - Node execution context
   * @returns Promise<ValidationResult>
   */
  validateOutput(outputData: any, context: NodeExecutionContext): Promise<ValidationResult>;
  
  /**
   * Cleanup resources sau khi thực thi (optional)
   * @param context - Node execution context
   */
  cleanup?(context: NodeExecutionContext): Promise<void>;
  
  /**
   * Estimate execution time (optional, để scheduling)
   * @param context - Node execution context
   * @returns Estimated time in milliseconds
   */
  estimateExecutionTime?(context: NodeExecutionContext): number;
  
  /**
   * Get default configuration cho node type này
   * @returns Default NodeExecutionConfig
   */
  getDefaultConfig(): NodeExecutionConfig;
  
  /**
   * Handle retry logic (optional, override default retry)
   * @param context - Node execution context
   * @param error - Error từ lần thực thi trước
   * @param retryCount - Số lần đã retry
   * @returns Promise<boolean> - true nếu nên retry
   */
  shouldRetry?(
    context: NodeExecutionContext, 
    error: Error, 
    retryCount: number
  ): Promise<boolean>;
}

/**
 * Kết quả validation
 */
export interface ValidationResult {
  /** Validation có thành công không */
  isValid: boolean;
  
  /** Danh sách lỗi nếu có */
  errors: ValidationError[];
  
  /** Danh sách warnings nếu có */
  warnings: ValidationWarning[];
  
  /** Có thể auto-fix không */
  canAutoFix: boolean;
  
  /** Dữ liệu đã được fix (nếu canAutoFix = true) */
  fixedData?: any;
}

/**
 * Validation error
 */
export interface ValidationError {
  /** Mã lỗi */
  code: string;
  
  /** Thông báo lỗi */
  message: string;
  
  /** Field bị lỗi */
  field?: string;
  
  /** Giá trị hiện tại */
  currentValue?: any;
  
  /** Giá trị mong đợi */
  expectedValue?: any;
  
  /** Severity của lỗi */
  severity: 'error' | 'warning' | 'info';
}

/**
 * Validation warning
 */
export interface ValidationWarning {
  /** Mã warning */
  code: string;
  
  /** Thông báo warning */
  message: string;
  
  /** Field có warning */
  field?: string;
  
  /** Suggestion để fix */
  suggestion?: string;
}

/**
 * Execution context mở rộng với thông tin runtime
 */
export interface ExecutorContext extends NodeExecutionContext {
  /** Execution ID để tracking */
  executionId: string;
  
  /** Start time của execution */
  startTime: number;
  
  /** Current retry count */
  currentRetryCount: number;
  
  /** Execution status */
  status: NodeExecutionStatus;
  
  /** Priority của execution */
  priority: NodeExecutionPriority;
  
  /** Có đang trong retry mode không */
  isRetrying: boolean;
  
  /** Có đang trong debug mode không */
  isDebugMode: boolean;
  
  /** Logger instance */
  logger: any;
  
  /** Event emitter để gửi events */
  eventEmitter: any;
  
  /** Metrics collector */
  metricsCollector?: any;
  
  /** Cache service */
  cacheService?: any;
  
  /** HTTP client service */
  httpService?: any;
  
  /** Database service */
  databaseService?: any;
  
  /** File system service */
  fileService?: any;
  
  /** Encryption service */
  encryptionService?: any;
  
  /** Custom services */
  customServices?: Record<string, any>;
}

/**
 * Base execution metrics
 */
export interface ExecutionMetrics {
  /** Thời gian bắt đầu */
  startTime: number;
  
  /** Thời gian kết thúc */
  endTime?: number;
  
  /** Tổng thời gian thực thi (ms) */
  executionTime?: number;
  
  /** Memory usage (bytes) */
  memoryUsage?: number;
  
  /** CPU usage (percentage) */
  cpuUsage?: number;
  
  /** Network I/O (bytes) */
  networkIO?: {
    bytesReceived: number;
    bytesSent: number;
  };
  
  /** Disk I/O (bytes) */
  diskIO?: {
    bytesRead: number;
    bytesWritten: number;
  };
  
  /** Custom metrics */
  customMetrics?: Record<string, number>;
}

/**
 * Executor capabilities
 */
export interface ExecutorCapabilities {
  /** Có hỗ trợ async execution không */
  supportsAsync: boolean;
  
  /** Có hỗ trợ streaming không */
  supportsStreaming: boolean;
  
  /** Có hỗ trợ caching không */
  supportsCaching: boolean;
  
  /** Có hỗ trợ retry không */
  supportsRetry: boolean;
  
  /** Có hỗ trợ timeout không */
  supportsTimeout: boolean;
  
  /** Có hỗ trợ cancellation không */
  supportsCancellation: boolean;
  
  /** Có hỗ trợ progress tracking không */
  supportsProgress: boolean;
  
  /** Có thread-safe không */
  isThreadSafe: boolean;
  
  /** Có stateless không */
  isStateless: boolean;
  
  /** Resource requirements */
  resourceRequirements?: {
    minMemory?: number;
    maxMemory?: number;
    minCpu?: number;
    maxCpu?: number;
    requiresNetwork?: boolean;
    requiresFileSystem?: boolean;
    requiresDatabase?: boolean;
  };
}

/**
 * Abstract base interface với common methods
 */
export interface IBaseNodeExecutor extends INodeExecutor {
  /** Executor capabilities */
  readonly capabilities: ExecutorCapabilities;
  
  /**
   * Initialize executor (được gọi khi khởi tạo)
   */
  initialize(): Promise<void>;
  
  /**
   * Destroy executor (được gọi khi shutdown)
   */
  destroy(): Promise<void>;
  
  /**
   * Health check cho executor
   */
  healthCheck(): Promise<boolean>;
  
  /**
   * Get execution metrics
   */
  getMetrics(): ExecutionMetrics;
  
  /**
   * Reset metrics
   */
  resetMetrics(): void;
}

/**
 * Factory interface để tạo executors
 */
export interface INodeExecutorFactory {
  /**
   * Tạo executor cho node type
   * @param nodeType - Type của node
   * @returns INodeExecutor instance hoặc null nếu không hỗ trợ
   */
  createExecutor(nodeType: string): INodeExecutor | null;
  
  /**
   * Đăng ký executor mới
   * @param executor - Executor instance
   */
  registerExecutor(executor: INodeExecutor): void;
  
  /**
   * Hủy đăng ký executor
   * @param nodeType - Type của node
   */
  unregisterExecutor(nodeType: string): void;
  
  /**
   * Lấy danh sách tất cả supported node types
   */
  getSupportedNodeTypes(): string[];
  
  /**
   * Kiểm tra xem có hỗ trợ node type không
   * @param nodeType - Type của node
   */
  isSupported(nodeType: string): boolean;
}

/**
 * Executor registry để quản lý tất cả executors
 */
export interface IExecutorRegistry {
  /** Map từ node type đến executor */
  readonly executors: Map<string, INodeExecutor>;
  
  /**
   * Đăng ký executor
   */
  register(executor: INodeExecutor): void;
  
  /**
   * Lấy executor cho node type
   */
  get(nodeType: string): INodeExecutor | undefined;
  
  /**
   * Kiểm tra xem có executor cho node type không
   */
  has(nodeType: string): boolean;
  
  /**
   * Hủy đăng ký executor
   */
  unregister(nodeType: string): void;
  
  /**
   * Lấy tất cả executors
   */
  getAll(): INodeExecutor[];
  
  /**
   * Clear tất cả executors
   */
  clear(): void;
}
