# Zalo Notification Service (ZNS) API

Tài liệu hướng dẫn sử dụng Zalo Notification Service (ZNS) API trong hệ thống RedAI.

## Tổng quan

Zalo Notification Service (ZNS) là dịch vụ gửi tin nhắn thông báo từ Official Account đến người dùng <PERSON>alo. ZNS cho phép gửi các loại tin nhắn có cấu trúc với template được duyệt trước.

## Điều kiện sử dụng

- Official Account phải được duyệt và có quyền gửi ZNS
- Access token hợp lệ của Official Account
- Template ZNS phải được duyệt trước khi sử dụng
- Số điện thoại người nhận phải đúng định dạng và hợp lệ
- Dữ liệu template phải khớp với các tham số đã định nghĩa
- <PERSON><PERSON> thủ giới hạn số lượng tin nhắn theo gói dịch vụ

## Các tính năng chính

### 1. <PERSON><PERSON><PERSON><PERSON> lý Template

#### Tạo template mới
```typescript
const templateData: ZaloCreateZnsTemplateRequest = {
  templateName: 'Thông báo đơn hàng',
  templateType: ZnsTemplateType.TEXT,
  templateContent: 'Xin chào {{customer_name}}, đơn hàng {{order_id}} của bạn đã được xác nhận.',
  listButton: [
    {
      type: ZnsButtonType.OPEN_URL,
      title: 'Xem chi tiết',
      payload: 'https://example.com/order/{{order_id}}'
    }
  ]
};

const result = await znsService.createZnsTemplate(accessToken, templateData);
```

#### Cập nhật template
```typescript
const updateData: ZaloUpdateZnsTemplateRequest = {
  templateId: 'template_123',
  templateName: 'Thông báo đơn hàng (cập nhật)',
  templateContent: 'Xin chào {{customer_name}}, đơn hàng {{order_id}} đã được cập nhật trạng thái.'
};

const result = await znsService.updateZnsTemplate(accessToken, updateData);
```

#### Lấy danh sách template
```typescript
const templates = await znsService.getZnsTemplates(accessToken, 0, 20);
```

#### Lấy chi tiết template
```typescript
const template = await znsService.getZnsTemplateDetail(accessToken, 'template_123');
```

### 2. Upload ảnh cho template

```typescript
const uploadResult = await znsService.uploadZnsImage(accessToken, file);
console.log('URL ảnh:', uploadResult.url);
```

### 3. Gửi tin nhắn ZNS

#### Gửi tin nhắn thông thường
```typescript
const message: ZaloZnsMessage = {
  phone: '0987654321',
  template_id: 'template_123',
  template_data: {
    customer_name: 'Nguyễn Văn A',
    order_id: 'ORD001'
  },
  tracking_id: 'track_001'
};

const result = await znsService.sendZnsMessage(accessToken, message);
```

#### Gửi tin nhắn với hash phone
```typescript
const hashMessage: ZaloZnsHashPhoneMessage = {
  phone_hash: 'hashed_phone_number',
  template_id: 'template_123',
  template_data: {
    customer_name: 'Nguyễn Văn A',
    order_id: 'ORD001'
  }
};

const result = await znsService.sendZnsHashPhoneMessage(accessToken, hashMessage);
```

#### Gửi tin nhắn development mode
```typescript
const devMessage: ZaloZnsDevModeMessage = {
  phone: '0987654321',
  template_id: 'template_123',
  template_data: {
    customer_name: 'Nguyễn Văn A',
    order_id: 'ORD001'
  },
  mode: true
};

const result = await znsService.sendZnsDevModeMessage(accessToken, devMessage);
```

#### Gửi tin nhắn với mã hóa RSA
```typescript
const rsaMessage: ZaloZnsRsaMessage = {
  phone: 'rsa_encrypted_phone',
  template_id: 'template_123',
  template_data: {
    customer_name: 'Nguyễn Văn A',
    order_id: 'ORD001'
  }
};

const result = await znsService.sendZnsRsaMessage(accessToken, rsaMessage);
```

#### Gửi tin nhắn Journey
```typescript
const journeyMessage: ZaloZnsJourneyMessage = {
  phone: '0987654321',
  template_id: 'template_123',
  template_data: {
    customer_name: 'Nguyễn Văn A',
    order_id: 'ORD001'
  },
  journey_id: 'journey_123'
};

const result = await znsService.sendZnsJourneyMessage(accessToken, journeyMessage);
```

### 4. Kiểm tra trạng thái tin nhắn

```typescript
const status = await znsService.checkZnsMessageStatus(accessToken, 'message_id');
console.log('Trạng thái:', status.status);
```

## Các loại template

### 1. Text Template
Template chỉ chứa văn bản và button (nếu có).

### 2. Media Template
Template có thể chứa hình ảnh, văn bản và button.

### 3. List Template
Template hiển thị danh sách các item với hình ảnh và button.

### 4. Button Template
Template tập trung vào các button hành động.

## Các loại button

- `oa.open.url`: Mở URL
- `oa.query.show`: Hiển thị query
- `oa.query.hide`: Ẩn query
- `oa.open.phone`: Mở ứng dụng gọi điện

## Lưu ý quan trọng

1. **Template phải được duyệt**: Template chỉ có thể sử dụng khi có trạng thái APPROVED
2. **Giới hạn tin nhắn**: Tuân thủ giới hạn số lượng tin nhắn theo gói dịch vụ
3. **Định dạng số điện thoại**: Số điện thoại phải đúng định dạng Việt Nam
4. **Dữ liệu template**: Phải cung cấp đầy đủ các tham số được định nghĩa trong template
5. **Development mode**: Chỉ sử dụng cho môi trường test

## Truy xuất thông tin ZNS

### 1. Lấy thông tin trạng thái ZNS

```typescript
const status = await znsInfoService.getZnsStatus(accessToken);
console.log('Trạng thái ZNS:', status.status);
```

### 2. Lấy thông tin quota ZNS

```typescript
const quota = await znsInfoService.getZnsQuota(accessToken);
console.log('Quota hàng ngày:', quota.daily_quota);
console.log('Đã sử dụng:', quota.daily_sent);
console.log('Còn lại:', quota.daily_remaining);
```

### 3. Lấy loại nội dung được phép gửi

```typescript
const allowedContent = await znsInfoService.getZnsAllowedContentTypes(accessToken);
console.log('Có thể gửi giao dịch:', allowedContent.can_send_transaction);
console.log('Có thể gửi khuyến mãi:', allowedContent.can_send_promotion);
```

### 4. Lấy danh sách template với lọc

```typescript
const templates = await znsInfoService.getZnsTemplateList(accessToken, {
  offset: 0,
  limit: 20,
  status: 'APPROVED',
  templateType: 'text'
});
```

### 5. Lấy dữ liệu mẫu của template

```typescript
const sampleData = await znsInfoService.getZnsTemplateSampleData(accessToken, 'template_123');
console.log('Dữ liệu mẫu:', sampleData.sample_data);
console.log('Nội dung preview:', sampleData.preview_content);
```

### 6. Lấy đánh giá khách hàng

```typescript
const rating = await znsInfoService.getZnsCustomerRating(accessToken);
console.log('Điểm trung bình:', rating.average_rating);
console.log('Tỷ lệ tích cực:', rating.positive_rating_percentage);

// Lấy chi tiết đánh giá
const ratingDetails = await znsInfoService.getZnsCustomerRatingDetails(accessToken, {
  startTime: Date.now() - 30 * 24 * 60 * 60 * 1000, // 30 ngày trước
  endTime: Date.now(),
  rating: 5, // Chỉ lấy đánh giá 5 sao
  limit: 10
});
```

### 7. Lấy thông tin chất lượng ZNS

```typescript
const quality = await znsInfoService.getZnsQuality(accessToken);
console.log('Mức chất lượng:', quality.current_quality_level);
console.log('Điểm chất lượng:', quality.quality_score);
console.log('Tỷ lệ gửi thành công:', quality.delivery_rate);

// Lấy lịch sử chất lượng
const qualityHistory = await znsInfoService.getZnsQualityHistory(accessToken, {
  startTime: Date.now() - 90 * 24 * 60 * 60 * 1000, // 90 ngày trước
  endTime: Date.now(),
  interval: 'weekly'
});
```

## Services

Dự án cung cấp 2 service chính cho ZNS:

1. **ZaloZnsService**: Quản lý template và gửi tin nhắn
2. **ZaloZnsInfoService**: Truy xuất thông tin và thống kê

## Tài liệu tham khảo

### Quản lý và gửi tin nhắn
- [Giới thiệu ZNS](https://developers.zalo.me/docs/zalo-notification-service/bat-dau/gioi-thieu-zalo-notification-service-api)
- [Tạo template](https://developers.zalo.me/docs/zalo-notification-service/quan-ly-tai-san/tao-template)
- [Chỉnh sửa template](https://developers.zalo.me/docs/zalo-notification-service/quan-ly-tai-san/chinh-sua-template)
- [Upload ảnh](https://developers.zalo.me/docs/zalo-notification-service/quan-ly-tai-san/upload-anh)
- [Gửi ZNS](https://developers.zalo.me/docs/zalo-notification-service/gui-tin-zns/gui-zns)

### Truy xuất thông tin
- [Lấy trạng thái ZNS](https://developers.zalo.me/docs/zalo-notification-service/truy-xuat-thong-tin/lay-thong-tin-trang-thai-zns)
- [Lấy quota ZNS](https://developers.zalo.me/docs/zalo-notification-service/truy-xuat-thong-tin/lay-thong-tin-quota-zns)
- [Lấy loại nội dung được phép](https://developers.zalo.me/docs/zalo-notification-service/truy-xuat-thong-tin/lay-thong-tin-loai-noi-dung-zns-duoc-phep-gui)
- [Lấy danh sách template](https://developers.zalo.me/docs/zalo-notification-service/truy-xuat-thong-tin/lay-danh-sach-template)
- [Lấy chi tiết template](https://developers.zalo.me/docs/zalo-notification-service/truy-xuat-thong-tin/lay-thong-tin-chi-tiet-template)
- [Lấy dữ liệu mẫu template](https://developers.zalo.me/docs/zalo-notification-service/truy-xuat-thong-tin/lay-du-lieu-mau-cua-template)
- [Lấy đánh giá khách hàng](https://developers.zalo.me/docs/zalo-notification-service/truy-xuat-thong-tin/lay-thong-tin-danh-gia-cua-khach-hang)
- [Lấy chất lượng ZNS](https://developers.zalo.me/docs/zalo-notification-service/truy-xuat-thong-tin/lay-thong-tin-chat-luong-gui-zns-hien-tai-cua-oa)
