import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserAgentRun } from '../entities/user-agent-run.entity';

@Injectable()
export class UserAgentRunRepository {
  constructor(
    @InjectRepository(UserAgentRun)
    private readonly repository: Repository<UserAgentRun>,
  ) {}

  /**
   * Create new user agent run record
   * @param runData Run data to create
   * @returns Created run record
   */
  async createRun(runData: Partial<UserAgentRun>): Promise<UserAgentRun> {
    const run = this.repository.create(runData);
    return this.repository.save(run);
  }
}
