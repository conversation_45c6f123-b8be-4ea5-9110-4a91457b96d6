import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { UserAudienceCustomField } from '../entities/user-audience-custom-field.entity';

/**
 * Repository cho UserAudienceCustomField entity
 */
@Injectable()
export class UserAudienceCustomFieldRepository extends Repository<UserAudienceCustomField> {
  private readonly logger = new Logger(UserAudienceCustomFieldRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserAudienceCustomField, dataSource.createEntityManager());
  }

  /**
   * Tìm custom field theo audience ID và field name
   */
  async findByAudienceIdAndFieldName(
    audienceId: number,
    fieldName: string,
  ): Promise<UserAudienceCustomField | null> {
    try {
      return await this.findOne({
        where: {
          audienceId,
          fieldName,
        },
      });
    } catch (error) {
      this.logger.error(`Error finding custom field: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Tạo hoặc cập nhật custom field
   */
  async upsertCustomField(data: Partial<UserAudienceCustomField>): Promise<UserAudienceCustomField> {
    try {
      const existing = await this.findByAudienceIdAndFieldName(
        data.audienceId!,
        data.fieldName!,
      );

      if (existing) {
        // Cập nhật
        existing.fieldValue = data.fieldValue;
        existing.updatedAt = Date.now();
        return await this.save(existing);
      } else {
        // Tạo mới
        const newField = this.create({
          ...data,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
        return await this.save(newField);
      }
    } catch (error) {
      this.logger.error(`Error upserting custom field: ${error.message}`, error.stack);
      throw error;
    }
  }
}
