import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserModel } from '../entities/user-model.entity';

@Injectable()
export class UserModelRepository {
  constructor(
    @InjectRepository(UserModel)
    private readonly repository: Repository<UserModel>,
  ) {}

  async findById(id: string): Promise<UserModel | null> {
    return this.repository.findOne({ 
      where: { id }
    });
  }
}
