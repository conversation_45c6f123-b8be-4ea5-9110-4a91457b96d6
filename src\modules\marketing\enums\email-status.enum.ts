/**
 * Enum trạng thái email của audience
 */
export enum EmailStatus {
  VALID = 'VALID', // Email hợp lệ và có thể gửi
  INVALID = 'INVALID', // Email không hợp lệ (format sai)
  BOUNCED = 'BOUNCED', // Email bị bounce (hard bounce)
  SOFT_BOUNCED = 'SOFT_BOUNCED', // Email bị soft bounce (tạm thời)
  BLOCKED = 'BLOCKED', // Email bị block bởi server
  UNSUBSCRIBED = 'UNSUBSCRIBED', // User đã unsubscribe
  SPAM_COMPLAINT = 'SPAM_COMPLAINT', // User báo spam
}

/**
 * Enum loại lỗi email
 */
export enum EmailErrorType {
  // Domain errors
  DOMAIN_NOT_FOUND = 'DOMAIN_NOT_FOUND', // Không tìm thấy domain
  DOMAIN_NOT_EXIST = 'DOMAIN_NOT_EXIST', // Domain không tồn tại
  MX_RECORD_NOT_FOUND = 'MX_RECORD_NOT_FOUND', // Không tìm thấy MX record

  // Mailbox errors
  MAILBOX_NOT_FOUND = 'MAILBOX_NOT_FOUND', // Mailbox không tồn tại
  MAILBOX_FULL = 'MAILBOX_FULL', // Mailbox đầy
  MAILBOX_DISABLED = 'MAILBOX_DISABLED', // Mailbox bị vô hiệu hóa

  // Format errors
  INVALID_EMAIL_FORMAT = 'INVALID_EMAIL_FORMAT', // Format email không hợp lệ
  MISSING_AT_SYMBOL = 'MISSING_AT_SYMBOL', // Thiếu ký tự @
  INVALID_CHARACTERS = 'INVALID_CHARACTERS', // Ký tự không hợp lệ

  // Server errors
  SMTP_ERROR = 'SMTP_ERROR', // Lỗi SMTP server
  CONNECTION_TIMEOUT = 'CONNECTION_TIMEOUT', // Timeout kết nối
  CONNECTION_REFUSED = 'CONNECTION_REFUSED', // Từ chối kết nối
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED', // Xác thực thất bại

  // Policy errors
  BLOCKED_BY_POLICY = 'BLOCKED_BY_POLICY', // Bị chặn bởi policy
  SPAM_DETECTED = 'SPAM_DETECTED', // Phát hiện spam
  RATE_LIMITED = 'RATE_LIMITED', // Bị giới hạn tốc độ
  BLACKLISTED = 'BLACKLISTED', // Bị blacklist

  // Content errors
  CONTENT_REJECTED = 'CONTENT_REJECTED', // Nội dung bị từ chối
  ATTACHMENT_REJECTED = 'ATTACHMENT_REJECTED', // Attachment bị từ chối

  // Other errors
  TEMPORARY_FAILURE = 'TEMPORARY_FAILURE', // Lỗi tạm thời
  PERMANENT_FAILURE = 'PERMANENT_FAILURE', // Lỗi vĩnh viễn
  UNKNOWN_ERROR = 'UNKNOWN_ERROR', // Lỗi không xác định
}

/**
 * Mapping từ error message sang error type
 */
export const EMAIL_ERROR_PATTERNS: Record<string, EmailErrorType> = {
  // Domain errors
  'domain not found': EmailErrorType.DOMAIN_NOT_FOUND,
  'không thể tìm được miền': EmailErrorType.DOMAIN_NOT_FOUND,
  'domain does not exist': EmailErrorType.DOMAIN_NOT_EXIST,
  'no mx record': EmailErrorType.MX_RECORD_NOT_FOUND,
  'mx record not found': EmailErrorType.MX_RECORD_NOT_FOUND,

  // Mailbox errors
  'user unknown': EmailErrorType.MAILBOX_NOT_FOUND,
  'mailbox not found': EmailErrorType.MAILBOX_NOT_FOUND,
  'recipient not found': EmailErrorType.MAILBOX_NOT_FOUND,
  'mailbox full': EmailErrorType.MAILBOX_FULL,
  'quota exceeded': EmailErrorType.MAILBOX_FULL,
  'mailbox disabled': EmailErrorType.MAILBOX_DISABLED,
  'account disabled': EmailErrorType.MAILBOX_DISABLED,

  // Format errors
  'invalid email': EmailErrorType.INVALID_EMAIL_FORMAT,
  'invalid address': EmailErrorType.INVALID_EMAIL_FORMAT,
  'malformed address': EmailErrorType.INVALID_EMAIL_FORMAT,

  // Server errors
  'connection timeout': EmailErrorType.CONNECTION_TIMEOUT,
  'connection refused': EmailErrorType.CONNECTION_REFUSED,
  'authentication failed': EmailErrorType.AUTHENTICATION_FAILED,
  'smtp error': EmailErrorType.SMTP_ERROR,

  // Policy errors
  blocked: EmailErrorType.BLOCKED_BY_POLICY,
  'spam detected': EmailErrorType.SPAM_DETECTED,
  'rate limited': EmailErrorType.RATE_LIMITED,
  blacklisted: EmailErrorType.BLACKLISTED,

  // Content errors
  'content rejected': EmailErrorType.CONTENT_REJECTED,
  'message rejected': EmailErrorType.CONTENT_REJECTED,

  // Temporary vs Permanent
  'temporary failure': EmailErrorType.TEMPORARY_FAILURE,
  'permanent failure': EmailErrorType.PERMANENT_FAILURE,
};

/**
 * Hàm phân loại error type từ error message
 */
export function classifyEmailError(errorMessage: string): EmailErrorType {
  if (!errorMessage) {
    return EmailErrorType.UNKNOWN_ERROR;
  }

  const lowerMessage = errorMessage.toLowerCase();

  // Tìm pattern phù hợp
  for (const [pattern, errorType] of Object.entries(EMAIL_ERROR_PATTERNS)) {
    if (lowerMessage.includes(pattern)) {
      return errorType;
    }
  }

  // Nếu không tìm thấy pattern nào, phân loại dựa trên SMTP code
  if (lowerMessage.includes('5.')) {
    return EmailErrorType.PERMANENT_FAILURE;
  } else if (lowerMessage.includes('4.')) {
    return EmailErrorType.TEMPORARY_FAILURE;
  }

  return EmailErrorType.UNKNOWN_ERROR;
}

/**
 * Hàm xác định email status từ error type
 */
export function getEmailStatusFromError(
  errorType: EmailErrorType,
): EmailStatus {
  switch (errorType) {
    case EmailErrorType.DOMAIN_NOT_FOUND:
    case EmailErrorType.DOMAIN_NOT_EXIST:
    case EmailErrorType.MAILBOX_NOT_FOUND:
    case EmailErrorType.INVALID_EMAIL_FORMAT:
    case EmailErrorType.MISSING_AT_SYMBOL:
    case EmailErrorType.INVALID_CHARACTERS:
    case EmailErrorType.PERMANENT_FAILURE:
      return EmailStatus.INVALID;

    case EmailErrorType.MAILBOX_FULL:
    case EmailErrorType.TEMPORARY_FAILURE:
    case EmailErrorType.CONNECTION_TIMEOUT:
    case EmailErrorType.RATE_LIMITED:
      return EmailStatus.SOFT_BOUNCED;

    case EmailErrorType.MAILBOX_DISABLED:
    case EmailErrorType.BLOCKED_BY_POLICY:
    case EmailErrorType.BLACKLISTED:
      return EmailStatus.BLOCKED;

    case EmailErrorType.SPAM_DETECTED:
      return EmailStatus.SPAM_COMPLAINT;

    default:
      return EmailStatus.BOUNCED;
  }
}
