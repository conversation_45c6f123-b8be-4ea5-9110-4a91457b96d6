/**
 * Interface cho cấu hình Google Drive API
 */
export interface GoogleDriveConfig {
  /**
   * Client ID từ Google Cloud Console
   */
  clientId: string;

  /**
   * Client Secret từ Google Cloud Console
   */
  clientSecret: string;

  /**
   * Redirect URI cho OAuth
   */
  redirectUri: string;

  /**
   * Folder ID mặc định
   */
  defaultFolderId?: string;
}

/**
 * Interface cho thông tin xác thực Google Drive
 */
export interface GoogleDriveCredentials {
  /**
   * Access Token
   */
  accessToken: string;

  /**
   * Refresh Token
   */
  refreshToken?: string;

  /**
   * Thời gian hết hạn của Access Token (Unix timestamp)
   */
  expiresAt?: number;
}

/**
 * Interface cho thông tin file
 */
export interface DriveFileInfo {
  /**
   * ID của file
   */
  id: string;

  /**
   * Tên file
   */
  name: string;

  /**
   * MIME type
   */
  mimeType: string;

  /**
   * Kích thước file (bytes)
   */
  size?: string;

  /**
   * Thời gian tạo
   */
  createdTime: string;

  /**
   * Thời gian cập nhật cuối
   */
  modifiedTime: string;

  /**
   * URL xem file
   */
  webViewLink?: string;

  /**
   * URL download file
   */
  webContentLink?: string;

  /**
   * Danh sách parent folders
   */
  parents?: string[];

  /**
   * Thông tin owner
   */
  owners?: DriveUser[];

  /**
   * Có phải folder không
   */
  isFolder: boolean;

  /**
   * Trạng thái trashed
   */
  trashed: boolean;
}

/**
 * Interface cho thông tin user
 */
export interface DriveUser {
  /**
   * Display name
   */
  displayName: string;

  /**
   * Email address
   */
  emailAddress: string;

  /**
   * Photo link
   */
  photoLink?: string;

  /**
   * Có phải owner không
   */
  me?: boolean;
}

/**
 * Interface cho upload file request
 */
export interface UploadFileRequest {
  /**
   * Tên file
   */
  name: string;

  /**
   * Nội dung file (Buffer hoặc stream)
   */
  content: Buffer | NodeJS.ReadableStream;

  /**
   * MIME type
   */
  mimeType: string;

  /**
   * Parent folder ID
   */
  parentId?: string;

  /**
   * Mô tả file
   */
  description?: string;
}

/**
 * Interface cho kết quả upload
 */
export interface UploadFileResult {
  /**
   * ID của file đã upload
   */
  fileId: string;

  /**
   * Tên file
   */
  name: string;

  /**
   * URL xem file
   */
  webViewLink: string;

  /**
   * URL download file
   */
  webContentLink?: string;

  /**
   * Kích thước file
   */
  size: string;
}

/**
 * Interface cho tạo folder request
 */
export interface CreateFolderRequest {
  /**
   * Tên folder
   */
  name: string;

  /**
   * Parent folder ID
   */
  parentId?: string;

  /**
   * Mô tả folder
   */
  description?: string;
}

/**
 * Interface cho search files request
 */
export interface SearchFilesRequest {
  /**
   * Query string
   */
  query?: string;

  /**
   * Folder ID để search trong đó
   */
  folderId?: string;

  /**
   * MIME type filter
   */
  mimeType?: string;

  /**
   * Số lượng kết quả tối đa
   */
  pageSize?: number;

  /**
   * Page token cho pagination
   */
  pageToken?: string;

  /**
   * Có bao gồm trashed files không
   */
  includeTrashed?: boolean;

  /**
   * Sắp xếp theo
   */
  orderBy?: string;
}

/**
 * Interface cho kết quả search
 */
export interface SearchFilesResult {
  /**
   * Danh sách files
   */
  files: DriveFileInfo[];

  /**
   * Next page token
   */
  nextPageToken?: string;

  /**
   * Có incomplete search không
   */
  incompleteSearch: boolean;
}

/**
 * Interface cho permission
 */
export interface DrivePermission {
  /**
   * ID của permission
   */
  id?: string;

  /**
   * Loại permission
   */
  type: 'user' | 'group' | 'domain' | 'anyone';

  /**
   * Role
   */
  role: 'owner' | 'organizer' | 'fileOrganizer' | 'writer' | 'commenter' | 'reader';

  /**
   * Email address (cho type user/group)
   */
  emailAddress?: string;

  /**
   * Domain (cho type domain)
   */
  domain?: string;

  /**
   * Allow file discovery
   */
  allowFileDiscovery?: boolean;
}

/**
 * Interface cho share file request
 */
export interface ShareFileRequest {
  /**
   * File ID
   */
  fileId: string;

  /**
   * Danh sách permissions
   */
  permissions: DrivePermission[];

  /**
   * Gửi notification email không
   */
  sendNotificationEmail?: boolean;

  /**
   * Message trong email
   */
  emailMessage?: string;
}

/**
 * Interface cho copy file request
 */
export interface CopyFileRequest {
  /**
   * File ID nguồn
   */
  sourceFileId: string;

  /**
   * Tên file mới
   */
  name: string;

  /**
   * Parent folder ID
   */
  parentId?: string;

  /**
   * Mô tả file mới
   */
  description?: string;
}

/**
 * Interface cho move file request
 */
export interface MoveFileRequest {
  /**
   * File ID
   */
  fileId: string;

  /**
   * Parent folder ID mới
   */
  newParentId: string;

  /**
   * Parent folder ID cũ (optional)
   */
  oldParentId?: string;
}

/**
 * Interface cho batch operation
 */
export interface BatchOperationRequest {
  /**
   * Loại operation
   */
  operation: 'delete' | 'move' | 'copy' | 'share';

  /**
   * Danh sách file IDs
   */
  fileIds: string[];

  /**
   * Tham số cho operation
   */
  parameters?: Record<string, unknown>;
}

/**
 * Interface cho kết quả batch operation
 */
export interface BatchOperationResult {
  /**
   * Số lượng thành công
   */
  successCount: number;

  /**
   * Số lượng thất bại
   */
  failureCount: number;

  /**
   * Danh sách lỗi
   */
  errors: Array<{
    fileId: string;
    error: string;
  }>;
}

/**
 * Interface cho file metadata update
 */
export interface UpdateFileMetadataRequest {
  /**
   * File ID
   */
  fileId: string;

  /**
   * Tên mới
   */
  name?: string;

  /**
   * Mô tả mới
   */
  description?: string;

  /**
   * Starred status
   */
  starred?: boolean;

  /**
   * Trashed status
   */
  trashed?: boolean;
}

/**
 * Interface cho export file request
 */
export interface ExportFileRequest {
  /**
   * File ID
   */
  fileId: string;

  /**
   * MIME type để export
   */
  mimeType: string;
}

/**
 * Interface cho revision info
 */
export interface FileRevision {
  /**
   * ID của revision
   */
  id: string;

  /**
   * Thời gian tạo
   */
  modifiedTime: string;

  /**
   * Kích thước
   */
  size?: string;

  /**
   * Thông tin user chỉnh sửa cuối
   */
  lastModifyingUser?: DriveUser;

  /**
   * Có keep forever không
   */
  keepForever: boolean;
}
