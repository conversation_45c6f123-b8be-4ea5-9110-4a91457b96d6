import { Injectable, Logger } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import { env } from '../../config/env';
import { SystemEmailConfigurationService, EmailServerConfig } from './system-email-configuration.service';

/**
 * Service xử lý gửi email
 */
@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter: nodemailer.Transporter;

  constructor(
    private readonly systemEmailConfigService: SystemEmailConfigurationService,
  ) {
    // Khởi tạo transporter mặc định từ env (fallback)
    this.initializeDefaultTransporter();
  }

  /**
   * Khởi tạo transporter mặc định từ environment variables
   */
  private initializeDefaultTransporter(): void {
    this.transporter = nodemailer.createTransport({
      host: env.email.MAIL_HOST,
      port: Number(env.email.MAIL_PORT),
      auth: {
        user: env.email.MAIL_USERNAME,
        pass: env.email.MAIL_PASSWORD,
      },
    });
  }

  /**
   * <PERSON><PERSON><PERSON> email
   * @param to Địa chỉ email người nhận
   * @param subject Tiêu đề email
   * @param html Nội dung email dạng HTML
   * @param from Địa chỉ email người gửi (mặc định lấy từ biến môi trường)
   * @returns Kết quả gửi email
   */
  async sendEmail(
    to: string | string[],
    subject: string,
    html: string,
    from?: string,
  ): Promise<boolean> {
    try {
      // Lấy cấu hình email từ system configuration
      const emailConfig = await this.getEmailConfiguration();

      // Tạo transporter với cấu hình mới nếu có
      const transporter = emailConfig
        ? this.createTransporterFromConfig(emailConfig)
        : this.transporter;

      const mailOptions = {
        from: from || emailConfig?.username || env.email.MAIL_DEFAULT_FROM || '<EMAIL>',
        to,
        subject,
        html,
      };

      const info = await transporter.sendMail(mailOptions);
      this.logger.log(`Email sent: ${info.messageId}`);
      return true;
    } catch (error) {
      this.logger.error('Failed to send email', error);
      return false;
    }
  }

  /**
   * Lấy cấu hình email từ system configuration
   */
  private async getEmailConfiguration(): Promise<EmailServerConfig | null> {
    try {
      return await this.systemEmailConfigService.getEmailServerConfig();
    } catch (error) {
      this.logger.warn(`Không thể lấy cấu hình email từ system configuration: ${error.message}`);
      return null;
    }
  }

  /**
   * Tạo transporter từ cấu hình email
   */
  private createTransporterFromConfig(config: EmailServerConfig): nodemailer.Transporter {
    return nodemailer.createTransport({
      host: config.host,
      port: config.port,
      auth: {
        user: config.username,
        pass: config.password,
      },
    });
  }

  /**
   * Test kết nối email với cấu hình tùy chỉnh
   * @param config Cấu hình email để test
   * @returns Kết quả test kết nối
   */
  async testConnectionWithConfig(config: {
    host: string;
    port: number;
    secure: boolean;
    auth: {
      user: string;
      pass: string;
    };
  }): Promise<boolean> {
    try {
      const testTransporter = nodemailer.createTransport(config);
      await testTransporter.verify();
      this.logger.log('Email connection test successful');
      return true;
    } catch (error) {
      this.logger.error('Email connection test failed', error);
      return false;
    }
  }
}
