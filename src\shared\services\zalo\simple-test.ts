/**
 * Script test đơn giản cho Zalo Token Utils
 * Chạy trực tiếp trong console hoặc qua npm script
 */

import { Logger } from '@nestjs/common';

// Mock test để kiểm tra cấu trúc code
export class SimpleZaloTokenTest {
  private readonly logger = new Logger('SimpleZaloTokenTest');

  /**
   * Test cơ bản để kiểm tra cấu trúc service
   */
  async runBasicTest(): Promise<void> {
    this.logger.log('🚀 Starting Simple Zalo Token Test...');
    
    try {
      // Test 1: Kiểm tra import
      this.logger.log('📋 Test 1: Checking imports...');
      
      const { ZaloTokenUtilsService } = await import('./zalo-token-utils.service');
      this.logger.log('✅ ZaloTokenUtilsService imported successfully');
      
      // Test 2: Kiểm tra interface
      this.logger.log('📋 Test 2: Checking service structure...');
      
      const servicePrototype = ZaloTokenUtilsService.prototype;
      const methods = Object.getOwnPropertyNames(servicePrototype);
      
      this.logger.log(`Found ${methods.length} methods in service:`);
      methods.forEach(method => {
        if (method !== 'constructor') {
          this.logger.log(`  - ${method}`);
        }
      });
      
      // Test 3: Kiểm tra các method cần thiết
      this.logger.log('📋 Test 3: Checking required methods...');
      
      const requiredMethods = [
        'getValidAccessTokenWithRetry',
        'getValidAccessToken',
        'isOfficialAccountActive',
        'getOfficialAccount',
        'executeWithTokenRetry',
        'executeWithTokenRetryByUserAndId',
        'cleanupExpiredTokens'
      ];
      
      const missingMethods = requiredMethods.filter(method => !methods.includes(method));
      
      if (missingMethods.length === 0) {
        this.logger.log('✅ All required methods are present');
      } else {
        this.logger.error(`❌ Missing methods: ${missingMethods.join(', ')}`);
      }
      
      // Test 4: Kiểm tra dependencies
      this.logger.log('📋 Test 4: Checking dependencies...');
      
      try {
        await import('@nestjs/common');
        this.logger.log('✅ @nestjs/common available');
        
        await import('@nestjs/typeorm');
        this.logger.log('✅ @nestjs/typeorm available');
        
        await import('@nestjs/axios');
        this.logger.log('✅ @nestjs/axios available');
        
        await import('typeorm');
        this.logger.log('✅ typeorm available');
        
        await import('rxjs');
        this.logger.log('✅ rxjs available');
        
      } catch (error: any) {
        this.logger.error(`❌ Dependency error: ${error.message}`);
      }
      
      this.logger.log('🏁 Basic test completed successfully!');
      
    } catch (error: any) {
      this.logger.error(`❌ Test failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Test configuration để kiểm tra env vars
   */
  async testConfiguration(): Promise<void> {
    this.logger.log('🔧 Testing Configuration...');
    
    const requiredEnvVars = ['ZALO_APP_ID', 'ZALO_APP_SECRET'];
    
    requiredEnvVars.forEach(envVar => {
      const value = process.env[envVar];
      if (value) {
        this.logger.log(`✅ ${envVar}: ${value.substring(0, 10)}...`);
      } else {
        this.logger.warn(`⚠️ ${envVar}: Not configured`);
      }
    });
  }

  /**
   * Test database connection (mock)
   */
  async testDatabaseConnection(): Promise<void> {
    this.logger.log('🗄️ Testing Database Connection...');
    
    try {
      // Kiểm tra entity import
      const { ZaloOfficialAccount } = await import('../../../modules/marketing/entities/zalo-official-account.entity');
      this.logger.log('✅ ZaloOfficialAccount entity imported successfully');
      
      // Kiểm tra entity metadata
      const entityMetadata = Reflect.getMetadata('design:paramtypes', ZaloOfficialAccount) || [];
      this.logger.log(`Entity has ${entityMetadata.length} constructor parameters`);
      
    } catch (error: any) {
      this.logger.error(`❌ Database test failed: ${error.message}`);
    }
  }

  /**
   * Chạy tất cả tests
   */
  async runAllTests(): Promise<void> {
    this.logger.log('🚀 Running All Simple Tests...');
    this.logger.log('='.repeat(50));
    
    await this.runBasicTest();
    this.logger.log('='.repeat(50));
    
    await this.testConfiguration();
    this.logger.log('='.repeat(50));
    
    await this.testDatabaseConnection();
    this.logger.log('='.repeat(50));
    
    this.logger.log('🏁 All simple tests completed!');
  }
}

// Hàm helper để chạy test
export async function runSimpleTest(): Promise<void> {
  const test = new SimpleZaloTokenTest();
  await test.runAllTests();
}

// Chạy test nếu file này được execute trực tiếp
if (require.main === module) {
  runSimpleTest().catch(console.error);
}
