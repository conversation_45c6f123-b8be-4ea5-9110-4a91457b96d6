import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng zalo_zns_messages trong cơ sở dữ liệu
 * Lưu trữ lịch sử các tin nhắn ZNS đã gửi
 */
@Entity('zalo_zns_messages')
export class ZaloZnsMessage {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID người dùng gửi tin nhắn
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * ID của Official Account
   */
  @Column({ name: 'oa_id', length: 50 })
  oaId: string;

  /**
   * ID của template
   */
  @Column({ name: 'template_id', length: 50 })
  templateId: string;

  /**
   * Số điện thoại người nhận
   */
  @Column({ name: 'phone', length: 20 })
  phone: string;

  /**
   * ID của tin nhắn trên Zalo
   */
  @Column({ name: 'message_id', length: 50, nullable: true })
  messageId: string;

  /**
   * ID giao dịch
   */
  @Column({ name: 'tracking_id', length: 50 })
  trackingId: string;

  /**
   * Dữ liệu đã gửi cho template (JSON)
   */
  @Column({ name: 'template_data', type: 'jsonb' })
  templateData: any;

  /**
   * Trạng thái tin nhắn (pending, delivered, failed)
   */
  @Column({ name: 'status', length: 20, default: 'pending' })
  status: string;

  /**
   * Thời điểm gửi thành công (Unix timestamp)
   */
  @Column({ name: 'delivered_time', type: 'bigint', nullable: true })
  deliveredTime: number;

  /**
   * Thời điểm tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời điểm cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
