import { Injectable, Logger } from '@nestjs/common';
import { ChatDatabaseService } from '../database.service';
import { ContentBlock } from '../interfaces/message.interface';

/**
 * ✅ UPDATED: Proper typing for message content (synced with backend DTOs)
 */
export interface MessageContent {
  // For user messages with content blocks
  contentBlocks?: ContentBlock[];

  // For simple text messages (assistant responses)
  text?: string;

  // Message metadata
  metadata?: {
    edited?: boolean;
    editedAt?: number;
    partial?: boolean;
    cancelled?: boolean;
    tokenUsage?: {
      promptTokens: number;
      completionTokens: number;
      totalTokens: number;
      pointCost: number;
    };
  };
}

/**
 * ✅ UPDATED: Interface for user message data with proper typing
 */
export interface UserMessageData {
  message_id?: string;
  thread_id: string;
  role: 'user' | 'assistant';
  content: MessageContent; // ✅ Properly typed instead of any
  timestamp?: number;
  created_by: number;
  agent_id?: string; // ✅ NEW: Agent ID for the message
}

/**
 * ✅ UPDATED: Interface for creating user messages with proper typing
 */
export interface CreateUserMessageData {
  thread_id: string;
  role: 'user' | 'assistant';
  content: MessageContent; // ✅ Properly typed instead of any
  created_by: number;
  agent_id?: string; // ✅ NEW: Agent ID for the message
}

/**
 * User Messages Queries Service (Worker)
 *
 * Handles database operations for the user_messages table using raw SQL queries.
 * This service is used in the worker to persist AI assistant responses.
 */
@Injectable()
export class UserMessagesQueries {
  private readonly logger = new Logger(UserMessagesQueries.name);

  constructor(private readonly databaseService: ChatDatabaseService) {}

  /**
   * Create a new user message
   * @param messageData Message data to create
   * @returns Promise<string> Created message ID
   */
  async createMessage(messageData: CreateUserMessageData): Promise<string> {
    const query = `
      INSERT INTO user_messages (thread_id, role, content, timestamp, created_by, agent_id)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING message_id
    `;

    const timestamp = Date.now();
    const values = [
      messageData.thread_id,
      messageData.role,
      JSON.stringify(messageData.content),
      timestamp,
      messageData.created_by,
      messageData.agent_id || null,
    ];

    try {
      this.logger.debug(
        `Creating message for thread ${messageData.thread_id}, role: ${messageData.role}`,
      );

      const result = await this.databaseService.query(query, values);
      const messageId = result[0]?.message_id;

      if (!messageId) {
        throw new Error('Failed to create message - no ID returned');
      }

      this.logger.log(
        `Created message ${messageId} for thread ${messageData.thread_id}`,
      );
      return messageId;
    } catch (error) {
      this.logger.error(
        `Failed to create message for thread ${messageData.thread_id}:`,
        error,
      );
      throw new Error(`Message creation failed: ${error.message}`);
    }
  }

  /**
   * ✅ NEW: Get a single message by ID (for reply context loading)
   * @param messageId Message ID to get
   * @returns Promise<UserMessageData | null> Message data or null if not found
   */
  async getMessageById(messageId: string): Promise<UserMessageData | null> {
    const query = `
      SELECT message_id, thread_id, role, content, timestamp, created_by
      FROM user_messages
      WHERE message_id = $1
    `;

    try {
      this.logger.debug(`Getting message by ID: ${messageId}`);

      const result = await this.databaseService.query(query, [messageId]);

      if (result.length === 0) {
        this.logger.debug(`No message found with ID: ${messageId}`);
        return null;
      }

      const message: UserMessageData = {
        message_id: result[0].message_id,
        thread_id: result[0].thread_id,
        role: result[0].role,
        content: result[0].content, // Already parsed by PostgreSQL
        timestamp: result[0].timestamp,
        created_by: result[0].created_by,
      };

      this.logger.debug(
        `Found message ${messageId} in thread ${message.thread_id}`,
      );
      return message;
    } catch (error) {
      this.logger.error(`Failed to get message ${messageId}:`, error);
      throw new Error(`Message retrieval failed: ${error.message}`);
    }
  }

  /**
   * Get messages by thread ID
   * @param threadId Thread ID to get messages for
   * @param limit Optional limit on number of messages
   * @returns Promise<UserMessageData[]> Array of messages
   */
  async getMessagesByThreadId(
    threadId: string,
    limit?: number,
  ): Promise<UserMessageData[]> {
    let query = `
      SELECT message_id, thread_id, role, content, timestamp, created_by
      FROM user_messages
      WHERE thread_id = $1
      ORDER BY timestamp ASC
    `;

    const values: any[] = [threadId];

    if (limit) {
      query += ` LIMIT $2`;
      values.push(limit);
    }

    try {
      this.logger.debug(
        `Getting messages for thread ${threadId}${limit ? ` (limit: ${limit})` : ''}`,
      );

      const result = await this.databaseService.query(query, values);

      const messages: UserMessageData[] = result.map((row) => ({
        message_id: row.message_id,
        thread_id: row.thread_id,
        role: row.role,
        content: row.content, // Already parsed by PostgreSQL
        timestamp: row.timestamp,
        created_by: row.created_by,
      }));

      this.logger.debug(
        `Found ${messages.length} messages for thread ${threadId}`,
      );
      return messages;
    } catch (error) {
      this.logger.error(
        `Failed to get messages for thread ${threadId}:`,
        error,
      );
      throw new Error(`Message retrieval failed: ${error.message}`);
    }
  }

  /**
   * Update message content by ID
   * @param messageId Message ID to update
   * @param content New content for the message
   * @returns Promise<boolean> True if update was successful
   */
  async updateMessageContent(
    messageId: string,
    content: any,
  ): Promise<boolean> {
    const query = `
      UPDATE user_messages
      SET content = $1, timestamp = $2
      WHERE message_id = $3
    `;

    const timestamp = Date.now();
    const values = [JSON.stringify(content), timestamp, messageId];

    try {
      this.logger.debug(`Updating message ${messageId}`);

      const result = await this.databaseService.query(query, values);

      this.logger.log(`Updated message ${messageId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to update message ${messageId}:`, error);
      throw new Error(`Message update failed: ${error.message}`);
    }
  }

  /**
   * Delete message by ID
   * @param messageId Message ID to delete
   * @returns Promise<boolean> True if deletion was successful
   */
  async deleteMessage(messageId: string): Promise<boolean> {
    const query = `
      DELETE FROM user_messages
      WHERE message_id = $1
    `;

    try {
      this.logger.debug(`Deleting message ${messageId}`);

      await this.databaseService.query(query, [messageId]);

      this.logger.log(`Deleted message ${messageId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete message ${messageId}:`, error);
      throw new Error(`Message deletion failed: ${error.message}`);
    }
  }

  // ✅ REMOVED: extractTextFromMessageContent() and getMessageTextById()
  // These methods contained business logic and should be in a separate service
  // This service should only handle pure DB queries

  /**
   * Delete messages by thread ID
   * @param threadId Thread ID to delete messages for
   * @returns Promise<boolean> True if deletion was successful
   */
  async deleteMessagesByThreadId(threadId: string): Promise<boolean> {
    const query = `
      DELETE FROM user_messages
      WHERE thread_id = $1
    `;

    try {
      this.logger.debug(`Deleting messages for thread ${threadId}`);

      await this.databaseService.query(query, [threadId]);

      this.logger.log(`Deleted messages for thread ${threadId}`);
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to delete messages for thread ${threadId}:`,
        error,
      );
      throw new Error(`Message deletion failed: ${error.message}`);
    }
  }
}
