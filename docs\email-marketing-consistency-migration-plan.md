# Email Marketing Consistency Migration Plan

## Tổng quan

Sau khi kiểm tra tính nhất quán giữa app main và worker cho phần email marketing, đã phát hiện một số vấn đề cần được đồng bộ để đảm bảo consistency.

## Các vấn đề phát hiện

### 1. ❌ **Missing Admin Email Campaign Support trong Worker**

**Vấn đề:**
- Worker không có AdminEmailCampaign entity
- Worker không có AdminEmailCampaignJobDto processor
- Worker chỉ xử lý EmailMarketingJobDto (user campaigns)

**Tác động:**
- Admin email campaigns không thể được xử lý bởi worker
- Job queue sẽ fail khi app main gửi admin email campaign jobs

### 2. ❌ **Inconsistent Job DTO Structure**

**Vấn đề:**
- `AdminEmailCampaignJobDto` có `htmlContent/textContent` riêng biệt
- `EmailMarketingJobDto` có `content` duy nhất
- Cấu trúc server config khác nhau giữa admin và user jobs

**Tác động:**
- Worker không thể xử lý admin jobs với cấu trúc hiện tại
- Logic xử lý email khác nhau cho admin vs user

### 3. ❌ **Missing Admin Entities trong Worker**

**Vấn đề:**
- Worker thiếu các admin entities: AdminEmailCampaign, AdminAudience, AdminSegment
- Worker không thể truy cập admin data để tracking và reporting

**Tác động:**
- Không thể track email status cho admin campaigns
- Không thể update campaign status sau khi gửi

### 4. ⚠️ **Different Campaign Structure**

**Vấn đề:**
- App main đã migrate sang JSONB structure (audiences, segment, content)
- Worker vẫn sử dụng old structure trong một số nơi
- BatchAdminEmailCampaignJobDto vẫn có `templateId` (đã bỏ trong app main)

**Tác động:**
- Data inconsistency giữa app main và worker
- Potential errors khi process jobs

## Migration Plan

### Phase 1: Sync Entity Structure (Priority: HIGH)

#### 1.1 Copy Admin Entities từ App Main
```bash
# Copy admin entities
cp redai-v201-be-app/src/modules/marketing/admin/entities/admin-email-campaign.entity.ts \
   redai-v201-be-worker/src/modules/marketing/entities/

cp redai-v201-be-app/src/modules/marketing/admin/entities/admin-audience.entity.ts \
   redai-v201-be-worker/src/modules/marketing/entities/

cp redai-v201-be-app/src/modules/marketing/admin/entities/admin-segment.entity.ts \
   redai-v201-be-worker/src/modules/marketing/entities/
```

#### 1.2 Update Worker Entity Index
```typescript
// redai-v201-be-worker/src/modules/marketing/entities/index.ts
export * from './admin-email-campaign.entity';
export * from './admin-audience.entity';
export * from './admin-segment.entity';
```

#### 1.3 Update TypeORM Module
```typescript
// redai-v201-be-worker/src/modules/marketing/email/email-marketing.module.ts
TypeOrmModule.forFeature([
  // Existing entities
  UserCampaign,
  UserAudience,
  // Add admin entities
  AdminEmailCampaign,
  AdminAudience,
  AdminSegment,
])
```

### Phase 2: Sync Job DTO Structure (Priority: HIGH)

#### 2.1 Update AdminEmailCampaignJobDto
```typescript
// Sync với app main structure
export interface AdminEmailCampaignJobDto {
  campaignId: number;
  email: string;
  subject: string;
  
  // Thay đổi: sử dụng content object thay vì htmlContent/textContent
  content?: {
    html?: string;
    text?: string;
  };
  
  // Bỏ templateId (đã remove trong app main)
  // templateId?: number;
  
  templateVariables?: Record<string, any>;
  emailServerConfig?: EmailServerConfig;
  trackingId: string;
  createdAt: number;
}
```

#### 2.2 Update BatchAdminEmailCampaignJobDto
```typescript
export interface BatchAdminEmailCampaignJobDto {
  campaignId: number;
  
  // Bỏ templateId
  // templateId?: number;
  
  subject?: string;
  
  // Thay đổi: sử dụng content object
  content?: {
    html?: string;
    text?: string;
  };
  
  templateVariables?: Record<string, any>;
  recipients: AdminEmailRecipientDto[];
  emailServerConfig?: EmailServerConfig;
  createdAt: number;
}
```

### Phase 3: Update Worker Processing Logic (Priority: HIGH)

#### 3.1 Extend EmailMarketingProcessor
```typescript
// Thêm support cho admin email campaigns
@Process(EmailMarketingJobName.SEND_ADMIN_EMAIL)
async processAdminEmail(job: Job<AdminEmailCampaignJobDto>): Promise<void> {
  // Logic tương tự processSingleEmail nhưng cho admin campaigns
}

@Process(EmailMarketingJobName.SEND_BATCH_ADMIN_EMAIL)
async processBatchAdminEmail(job: Job<BatchAdminEmailCampaignJobDto>): Promise<void> {
  // Logic xử lý batch admin emails
}
```

#### 3.2 Update Email Tracking Service
```typescript
// Thêm support tracking cho admin campaigns
async trackAdminEmailSent(
  campaignId: number,
  audience: { name: string; email: string },
  trackingId: string,
): Promise<void> {
  // Implementation
}
```

### Phase 4: Sync Campaign Types và Enums (Priority: MEDIUM)

#### 4.1 Copy Admin Types
```bash
cp redai-v201-be-app/src/modules/marketing/admin/types/admin-email-campaign.types.ts \
   redai-v201-be-worker/src/modules/marketing/types/
```

#### 4.2 Copy Admin Enums
```bash
cp redai-v201-be-app/src/modules/marketing/admin/enums/campaign-target-type.enum.ts \
   redai-v201-be-worker/src/modules/marketing/enums/
```

### Phase 5: Update Database Access (Priority: MEDIUM)

#### 5.1 Add Admin Repositories
```typescript
// Create admin repositories trong worker
export class AdminEmailCampaignRepository {
  // Implementation tương tự app main
}

export class AdminAudienceRepository {
  // Implementation tương tự app main
}
```

#### 5.2 Update Services
```typescript
// Inject admin repositories vào services
constructor(
  private readonly adminEmailCampaignRepository: AdminEmailCampaignRepository,
  private readonly adminAudienceRepository: AdminAudienceRepository,
) {}
```

## Implementation Steps

### Step 1: Backup và Preparation
```bash
# Backup worker codebase
git checkout -b feature/email-marketing-consistency-sync
git add .
git commit -m "Backup before email marketing consistency sync"
```

### Step 2: Execute Phase 1 (Entities)
- Copy admin entities
- Update imports
- Test compilation

### Step 3: Execute Phase 2 (DTOs)
- Update job DTOs
- Sync với app main structure
- Update job names enum

### Step 4: Execute Phase 3 (Processing Logic)
- Extend processor
- Add admin email processing
- Update tracking services

### Step 5: Testing
- Unit tests cho admin email processing
- Integration tests với queue
- End-to-end tests với app main

### Step 6: Deployment
- Deploy worker với new structure
- Monitor queue processing
- Verify admin email campaigns work

## Validation Checklist

- [ ] Worker có thể compile với admin entities
- [ ] Worker có thể process AdminEmailCampaignJobDto
- [ ] Worker có thể process BatchAdminEmailCampaignJobDto
- [ ] Admin email campaigns được gửi thành công
- [ ] Email tracking hoạt động cho admin campaigns
- [ ] Campaign status được update đúng
- [ ] Không có breaking changes cho user campaigns
- [ ] Queue processing performance không bị ảnh hưởng

## Rollback Plan

Nếu có vấn đề:
```bash
git checkout main
git branch -D feature/email-marketing-consistency-sync
# Redeploy worker từ main branch
```

## Timeline

- **Phase 1-2**: 1-2 ngày (entities và DTOs)
- **Phase 3**: 2-3 ngày (processing logic)
- **Phase 4-5**: 1-2 ngày (types và repositories)
- **Testing**: 2-3 ngày
- **Total**: 6-10 ngày

## Risk Assessment

**High Risk:**
- Breaking existing user email campaigns
- Queue processing failures

**Medium Risk:**
- Performance degradation
- Memory usage increase

**Low Risk:**
- Minor compatibility issues
- Logging inconsistencies

**Mitigation:**
- Thorough testing trước deployment
- Gradual rollout với monitoring
- Quick rollback plan sẵn sàng
