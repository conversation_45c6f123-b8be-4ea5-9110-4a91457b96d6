import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName } from '../../queue';
import {
  AffiliateClickJobData,
  AffiliateClickBatchJobData,
} from './dto/affiliate-click-job.dto';
import { AffiliateClickRepository } from './repositories/affiliate-click.repository';
import { AffiliateClickService } from './services/affiliate-click.service';
import { AffiliateClick } from './entities/affiliate-click.entity';
import { AffiliateJobName } from './constants';

/**
 * Processor xử lý queue affiliate click
 */
@Injectable()
@Processor(QueueName.AFFILIATE_CLICK)
export class AffiliateClickProcessor extends WorkerHost {
  private readonly logger = new Logger(AffiliateClickProcessor.name);

  constructor(
    private readonly affiliateClickRepository: AffiliateClickRepository,
    private readonly affiliateClickService: AffiliateClickService,
  ) {
    super();
  }

  /**
   * Xử lý job từ queue
   * @param job Job chứa dữ liệu affiliate click
   */
  async process(job: Job<any, any, string>): Promise<void> {
    this.logger.log(
      `Bắt đầu xử lý job affiliate click: ${job.id} - Type: ${job.name}`,
    );

    try {
      switch (job.name) {
        case 'affiliate-click':
          await this.processAffiliateClickJob(
            job as Job<AffiliateClickJobData>,
          );
          break;
        case AffiliateJobName.AFFILIATE_CLICK_BATCH:
          await this.processAffiliateClickBatchJob(
            job as Job<AffiliateClickBatchJobData>,
          );
          break;
        default:
          this.logger.warn(`Job name không được hỗ trợ: ${job.name}`);
          throw new Error(`Job name không được hỗ trợ: ${job.name}`);
      }

      this.logger.log(`Đã xử lý thành công job affiliate click: ${job.id}`);
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý job affiliate click: ${job.id} - ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý job affiliate click đơn lẻ
   * @param job Job chứa dữ liệu affiliate click đơn lẻ
   */
  private async processAffiliateClickJob(
    job: Job<AffiliateClickJobData>,
  ): Promise<void> {
    const { clickData } = job.data;

    this.logger.log(
      `Xử lý affiliate click - Account: ${clickData.affiliateAccountId}, Code: ${clickData.referralCode}`,
    );

    try {
      // Tạo đối tượng AffiliateClick từ dữ liệu nhận được
      const affiliateClick = new AffiliateClick();
      affiliateClick.affiliateAccountId = clickData.affiliateAccountId;
      affiliateClick.referralCode = clickData.referralCode;
      // Xử lý các trường có thể là undefined
      if (clickData.ipAddress) {
        affiliateClick.ipAddress = clickData.ipAddress;
      } else {
        affiliateClick.ipAddress = ''; // Hoặc giá trị mặc định khác
      }

      if (clickData.userAgent) {
        affiliateClick.userAgent = clickData.userAgent;
      } else {
        affiliateClick.userAgent = ''; // Hoặc giá trị mặc định khác
      }

      if (clickData.referrerUrl) {
        affiliateClick.referrerUrl = clickData.referrerUrl;
      } else {
        affiliateClick.referrerUrl = ''; // Hoặc giá trị mặc định khác
      }

      if (clickData.landingPage) {
        affiliateClick.landingPage = clickData.landingPage;
      } else {
        affiliateClick.landingPage = ''; // Hoặc giá trị mặc định khác
      }

      affiliateClick.clickTime = clickData.clickTime;

      // Thêm vào danh sách chờ xử lý batch
      await this.affiliateClickService.processAffiliateClick(affiliateClick);

      this.logger.log(
        `Đã xử lý thành công affiliate click - Account: ${clickData.affiliateAccountId}`,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý affiliate click - Account: ${clickData.affiliateAccountId}, Error: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý job batch affiliate click
   * @param job Job chứa dữ liệu batch affiliate click
   */
  private async processAffiliateClickBatchJob(
    job: Job<AffiliateClickBatchJobData>,
  ): Promise<void> {
    const { clicks, timestamp } = job.data;

    this.logger.log(
      `Xử lý batch affiliate click - Count: ${clicks.length}, Timestamp: ${new Date(timestamp).toISOString()}`,
    );

    if (!clicks || clicks.length === 0) {
      this.logger.warn('Batch rỗng, không có affiliate click nào để xử lý');
      return;
    }

    try {
      // Lưu batch vào database
      await this.affiliateClickService.saveBatch(clicks);

      this.logger.log(
        `Đã lưu thành công batch ${clicks.length} affiliate clicks vào database`,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý batch affiliate click - Count: ${clicks.length}, Error: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
