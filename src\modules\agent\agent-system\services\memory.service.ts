import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { MemoryQueriesService } from './memory-queries.service';
import {
  MemoryItem,
  SaveMemoryEvent,
  MemoryEventType,
  UserMemorySearchResult,
  AgentMemorySearchResult,
  InsertUserMemorySQL,
  InsertAgentMemorySQL,
} from '../../interfaces';

/**
 * Memory Service - Business Logic for Long-Term Memory Operations
 *
 * @description Central service for managing memory operations including:
 * - Saving memories for users and agents (simple content storage)
 * - Retrieving memories by user/agent ID (full load, no similarity search)
 * - Event handling for 'memory.save' events from LangChain tools
 * - Error handling and retry logic
 *
 * This service orchestrates with MemoryQueriesService to provide a complete
 * memory management solution. It acts as both a direct service for memory
 * operations and an event listener for asynchronous processing.
 */
@Injectable()
export class MemoryService {
  private readonly logger = new Logger(MemoryService.name);

  constructor(private readonly memoryQueriesService: MemoryQueriesService) {
    this.logger.log('MemoryService initialized with dependencies');
  }

  /**
   * Save a memory item for a user (simple content storage)
   *
   * @param memoryItem - The memory item to save
   * @param userId - User ID (number) for user memories
   * @param options - Additional options for saving
   * @returns Promise<{ id: string; success: boolean }>
   */
  async saveUserMemory(
    memoryItem: MemoryItem,
    userId: number,
  ): Promise<{ id: string; success: boolean }> {
    const startTime = Date.now();

    this.logger.debug(`Saving user memory for user ${userId}`, {
      userId,
      contentLength: memoryItem.content?.length || 0,
      hasMetadata: !!memoryItem.metadata,
    });

    try {
      // 1. Prepare memory data for database insertion (no embedding)
      const memoryData: InsertUserMemorySQL = {
        userId,
        structuredContent: {
          title: memoryItem.metadata?.title || 'Untitled Memory',
          content: memoryItem.content,
          reason: memoryItem.metadata?.reason || 'No reason provided',
        },
        metadata: {
          // agentId removed - not used for filtering and adds unnecessary data
        },
      };

      // 2. Save to database
      this.logger.debug(`Saving user memory to database`, { userId });

      const insertResults =
        await this.memoryQueriesService.bulkInsertUserMemories([memoryData]);

      if (!insertResults || insertResults.length === 0) {
        throw new Error('Failed to insert user memory - no results returned');
      }

      const savedMemoryId = insertResults[0]; // insertResults is array of string IDs

      // Note: Event emission removed - LangChain tools will emit events instead

      const duration = Date.now() - startTime;

      this.logger.log(`Successfully saved user memory`, {
        userId,
        savedMemoryId,
        duration,
      });

      return {
        id: savedMemoryId,
        success: true,
      };
    } catch (error) {
      const duration = Date.now() - startTime;

      this.logger.error(`Failed to save user memory for user ${userId}`, {
        userId,
        error: error.message,
        stack: error.stack,
        duration,
        contentLength: memoryItem.content?.length || 0,
      });

      throw new Error(`Failed to save user memory: ${error.message}`);
    }
  }

  /**
   * Save a memory item for an agent (no embedding generation)
   *
   * @param memoryItem - The memory item to save
   * @param agentId - Agent ID (UUID string) for agent memories
   * @param options - Additional options for saving
   * @returns Promise<{ id: string; success: boolean }>
   */
  async saveAgentMemory(
    memoryItem: MemoryItem,
    agentId: string,
  ): Promise<{ id: string; success: boolean }> {
    const startTime = Date.now();

    this.logger.debug(`Saving agent memory for agent ${agentId}`, {
      agentId,
      contentLength: memoryItem.content?.length || 0,
      hasMetadata: !!memoryItem.metadata,
    });

    try {
      // 1. Prepare memory data for database insertion (no embedding for agents)
      const memoryData: InsertAgentMemorySQL = {
        agentId,
        structuredContent: {
          title: memoryItem.metadata?.title || 'Untitled Memory',
          content: memoryItem.content,
          reason: memoryItem.metadata?.reason || 'No reason provided',
        },
        metadata: {},
      };

      // 2. Save to database
      this.logger.debug(`Saving agent memory to database`, { agentId });

      const insertResults =
        await this.memoryQueriesService.bulkInsertAgentMemories([memoryData]);

      if (!insertResults || insertResults.length === 0) {
        throw new Error('Failed to insert agent memory - no results returned');
      }

      const savedMemoryId = insertResults[0]; // insertResults is array of string IDs

      // Note: Event emission removed - LangChain tools will emit events instead

      const duration = Date.now() - startTime;

      this.logger.log(`Successfully saved agent memory`, {
        agentId,
        savedMemoryId,
        duration,
      });

      return {
        id: savedMemoryId,
        success: true,
      };
    } catch (error) {
      const duration = Date.now() - startTime;

      this.logger.error(`Failed to save agent memory for agent ${agentId}`, {
        agentId,
        error: error.message,
        stack: error.stack,
        duration,
        contentLength: memoryItem.content?.length || 0,
      });

      throw new Error(`Failed to save agent memory: ${error.message}`);
    }
  }

  /**
   * Retrieve all user memories (full load by user ID)
   *
   * @param userId - User ID to retrieve memories for
   * @param options - Retrieval options (limit, etc.)
   * @returns Promise<UserMemorySearchResult[]>
   */
  async retrieveUserMemories(
    userId: number,
    options?: {
      limit?: number;
    },
  ): Promise<UserMemorySearchResult[]> {
    const startTime = Date.now();
    const limit = options?.limit || 100; // Default higher limit for full load

    this.logger.debug(`Retrieving all user memories`, {
      userId,
      limit,
    });

    try {
      // Simple full load by user ID
      const memories = await this.memoryQueriesService.getAllUserMemories(
        userId,
        limit,
      );

      const duration = Date.now() - startTime;

      this.logger.log(`Retrieved ${memories.length} user memories`, {
        userId,
        resultCount: memories.length,
        duration,
        limit,
      });

      return memories;
    } catch (error) {
      const duration = Date.now() - startTime;

      this.logger.error(`Failed to retrieve user memories for user ${userId}`, {
        userId,
        error: error.message,
        stack: error.stack,
        duration,
      });

      throw new Error(`Failed to retrieve user memories: ${error.message}`);
    }
  }

  /**
   * Retrieve all agent memories (full load by agent ID)
   *
   * @param agentId - Agent ID to retrieve memories for
   * @param options - Retrieval options (limit, etc.)
   * @returns Promise<AgentMemorySearchResult[]>
   */
  async retrieveAgentMemories(
    agentId: string,
    options?: {
      limit?: number;
    },
  ): Promise<AgentMemorySearchResult[]> {
    const startTime = Date.now();
    const limit = options?.limit || 100; // Default higher limit for full load

    this.logger.debug(`Retrieving all agent memories`, {
      agentId,
      limit,
    });

    try {
      // Simple full load by agent ID
      const memories = await this.memoryQueriesService.getAllAgentMemories(
        agentId,
        limit,
      );

      const duration = Date.now() - startTime;

      this.logger.log(`Retrieved ${memories.length} agent memories`, {
        agentId,
        resultCount: memories.length,
        duration,
        limit,
      });

      return memories;
    } catch (error) {
      const duration = Date.now() - startTime;

      this.logger.error(
        `Failed to retrieve agent memories for agent ${agentId}`,
        {
          agentId,
          error: error.message,
          stack: error.stack,
          duration,
        },
      );

      throw new Error(`Failed to retrieve agent memories: ${error.message}`);
    }
  }

  /**
   * Batch save multiple user memories (simple content storage)
   *
   * @param memoryItems - Array of memory items to save
   * @param userId - User ID for all memories
   * @param options - Batch save options
   * @returns Promise<{ ids: string[]; success: boolean; errors: string[] }>
   */
  async batchSaveUserMemories(
    memoryItems: MemoryItem[],
    userId: number,
    options?: {
      skipEvent?: boolean;
      metadata?: Record<string, any>;
    },
  ): Promise<{ ids: string[]; success: boolean; errors: string[] }> {
    const startTime = Date.now();

    this.logger.debug(`Batch saving ${memoryItems.length} user memories`, {
      userId,
      memoryCount: memoryItems.length,
      skipEvent: options?.skipEvent,
    });

    try {
      // Convert to SQL format for bulk insert
      const sqlMemories = memoryItems.map((item) => ({
        userId,
        structuredContent: {
          title: item.metadata?.title || 'Untitled Memory',
          content: item.content,
          reason: item.metadata?.reason || 'No reason provided',
        },
        metadata: {
          // agentId removed - not used for filtering and adds unnecessary data
        },
      }));

      // Single bulk insert
      const insertResults =
        await this.memoryQueriesService.bulkInsertUserMemories(sqlMemories);

      const duration = Date.now() - startTime;
      const success = insertResults && insertResults.length > 0;

      this.logger.log(`Batch save completed`, {
        userId,
        totalMemories: memoryItems.length,
        savedCount: insertResults?.length || 0,
        success,
        duration,
      });

      return {
        ids: insertResults || [],
        success,
        errors: success ? [] : ['Bulk insert failed'],
      };
    } catch (error) {
      const duration = Date.now() - startTime;

      this.logger.error(`Batch save failed for user ${userId}`, {
        userId,
        memoryCount: memoryItems.length,
        error: error.message,
        stack: error.stack,
        duration,
      });

      throw new Error(`Batch save failed: ${error.message}`);
    }
  }

  /**
   * Batch save multiple agent memories using single bulk insert
   *
   * @param memoryItems - Array of memory items to save
   * @param agentId - Agent ID for all memories
   * @returns Promise<{ ids: string[]; success: boolean; errors: string[] }>
   */
  async batchSaveAgentMemories(
    memoryItems: any[],
    agentId: string,
  ): Promise<{ ids: string[]; success: boolean; errors: string[] }> {
    const startTime = Date.now();

    this.logger.debug(`Batch saving ${memoryItems.length} agent memories`, {
      agentId,
      memoryCount: memoryItems.length,
    });

    try {
      // Convert to SQL format for bulk insert
      const sqlMemories = memoryItems.map((item) => ({
        agentId,
        structuredContent: {
          title: item.metadata?.title || 'Untitled Memory',
          content: item.content,
          reason: item.metadata?.reason || 'No reason provided',
        },
        metadata: {},
      }));

      // Single bulk insert
      const insertResults =
        await this.memoryQueriesService.bulkInsertAgentMemories(sqlMemories);

      const duration = Date.now() - startTime;
      const success = insertResults && insertResults.length > 0;

      this.logger.log(`Batch save completed`, {
        agentId,
        totalMemories: memoryItems.length,
        savedCount: insertResults?.length || 0,
        success,
        duration,
      });

      return {
        ids: insertResults || [],
        success,
        errors: success ? [] : ['Bulk insert failed'],
      };
    } catch (error) {
      const duration = Date.now() - startTime;

      this.logger.error(`Batch save failed for agent ${agentId}`, {
        agentId,
        memoryCount: memoryItems.length,
        error: error.message,
        stack: error.stack,
        duration,
      });

      return {
        ids: [],
        success: false,
        errors: [error.message],
      };
    }
  }

  /**
   * Update an existing memory by ID
   * Supports both user and agent memories with granular field updates
   * @param memoryId - ID of the memory to update
   * @param memoryType - Type of memory (user or agent)
   * @param operation - What to update (content, title, reason, or all)
   * @param updates - Object containing the new values
   * @param agentId - ID of the agent performing the update
   * @param userId - ID of the user (required for user memory updates)
   * @returns Promise with success status and message
   */
  async updateMemory(
    memoryId: string,
    memoryType: 'user' | 'agent',
    operation: 'content' | 'title' | 'reason' | 'all',
    updates: {
      content?: string;
      title?: string;
      reason?: string;
    },
    agentId: string,
    userId?: number,
  ): Promise<{ success: boolean; message: string; updatedMemory?: any }> {
    try {
      if (memoryType === 'user') {
        if (!userId) {
          return {
            success: false,
            message: 'User ID required for user memory updates',
          };
        }
        return await this.updateUserMemoryInternal(
          memoryId,
          operation,
          updates,
          userId,
          agentId,
        );
      } else {
        return await this.updateAgentMemoryInternal(
          memoryId,
          operation,
          updates,
          agentId,
        );
      }
    } catch (error) {
      this.logger.error(`Failed to update ${memoryType} memory ${memoryId}`, {
        memoryId,
        memoryType,
        agentId,
        userId,
        operation,
        error: error.message,
      });

      return { success: false, message: `Update failed: ${error.message}` };
    }
  }

  /**
   * Internal method to update user memory
   * @private
   */
  private async updateUserMemoryInternal(
    memoryId: string,
    operation: 'content' | 'title' | 'reason' | 'all',
    updates: { content?: string; title?: string; reason?: string },
    userId: number,
    agentId: string,
  ): Promise<{ success: boolean; message: string; updatedMemory?: any }> {
    try {
      // 1. Verify memory exists and get current data
      const existingMemory =
        await this.memoryQueriesService.getUserMemoryById(memoryId);
      if (!existingMemory) {
        return { success: false, message: 'User memory not found' };
      }

      // 2. Verify ownership (memory belongs to this user)
      if (existingMemory.userId !== userId) {
        return {
          success: false,
          message: 'Cannot update memory owned by another user',
        };
      }

      // 3. Build update object based on operation
      const newStructuredContent = { ...existingMemory.structuredContent };

      if (operation === 'content' || operation === 'all') {
        newStructuredContent.content = updates.content;
      }

      if (operation === 'title' || operation === 'all') {
        newStructuredContent.title = updates.title;
      }

      if (operation === 'reason' || operation === 'all') {
        newStructuredContent.reason = updates.reason;
      }

      // 4. Update in database
      const success = await this.memoryQueriesService.updateUserMemory(
        memoryId,
        {
          structuredContent: newStructuredContent,
        },
      );

      if (success) {
        // 5. Get updated memory for response
        const updatedMemory =
          await this.memoryQueriesService.getUserMemoryById(memoryId);

        this.logger.log(`Updated user memory ${memoryId}`, {
          memoryId,
          userId,
          agentId,
          operation,
          updatedFields: Object.keys(updates),
        });

        return {
          success: true,
          message: `Successfully updated ${operation} for user memory`,
          updatedMemory,
        };
      } else {
        return {
          success: false,
          message: 'Failed to update memory in database',
        };
      }
    } catch (error) {
      this.logger.error(`Failed to update user memory ${memoryId}`, {
        memoryId,
        userId,
        agentId,
        operation,
        error: error.message,
      });

      throw error;
    }
  }

  /**
   * Internal method to update agent memory
   * @private
   */
  private async updateAgentMemoryInternal(
    memoryId: string,
    operation: 'content' | 'title' | 'reason' | 'all',
    updates: { content?: string; title?: string; reason?: string },
    agentId: string,
  ): Promise<{ success: boolean; message: string; updatedMemory?: any }> {
    try {
      // 1. Verify memory exists and get current data
      const existingMemory =
        await this.memoryQueriesService.getAgentMemoryById(memoryId);
      if (!existingMemory) {
        return { success: false, message: 'Agent memory not found' };
      }

      // 2. Verify ownership (memory belongs to this agent)
      if (existingMemory.agentId !== agentId) {
        return {
          success: false,
          message: 'Cannot update memory owned by another agent',
        };
      }

      // 3. Build update object based on operation
      const newStructuredContent = { ...existingMemory.structuredContent };

      if (operation === 'content' || operation === 'all') {
        newStructuredContent.content = updates.content;
      }

      if (operation === 'title' || operation === 'all') {
        newStructuredContent.title = updates.title;
      }

      if (operation === 'reason' || operation === 'all') {
        newStructuredContent.reason = updates.reason;
      }

      // 4. Update in database
      const success = await this.memoryQueriesService.updateAgentMemory(
        memoryId,
        {
          structuredContent: newStructuredContent,
        },
      );

      if (success) {
        // 5. Get updated memory for response
        const updatedMemory =
          await this.memoryQueriesService.getAgentMemoryById(memoryId);

        this.logger.log(`Updated agent memory ${memoryId}`, {
          memoryId,
          agentId,
          operation,
          updatedFields: Object.keys(updates),
        });

        return {
          success: true,
          message: `Successfully updated ${operation} for agent memory`,
          updatedMemory,
        };
      } else {
        return {
          success: false,
          message: 'Failed to update memory in database',
        };
      }
    } catch (error) {
      this.logger.error(`Failed to update agent memory ${memoryId}`, {
        memoryId,
        agentId,
        operation,
        error: error.message,
      });

      throw error;
    }
  }

  /**
   * Event handler for batch memory save events emitted by LangChain tools
   *
   * @description This method listens for 'memory.save.batch' events and processes
   * multiple memories in a single batch operation.
   *
   * @param event - Batch save event containing array of memory items
   */
  @OnEvent('memory.save.batch')
  async handleMemoryBatchSaveEvent(event: any): Promise<void> {
    const startTime = Date.now();

    this.logger.debug(`Received memory batch save event`, {
      userId: event.userId,
      agentId: event.agentId,
      reason: event.reason,
      memoryCount: event.memoryItems?.length || 0,
      requestId: event.requestId,
    });

    try {
      // Separate user and agent memories
      const userMemories = event.memoryItems.filter(
        (item) => item.metadata?.type === 'user',
      );
      const agentMemories = event.memoryItems.filter(
        (item) => item.metadata?.type !== 'user',
      );

      let userResults: { ids: string[]; success: boolean; errors: string[] } = {
        ids: [],
        success: true,
        errors: [],
      };
      let agentResults: { ids: string[]; success: boolean; errors: string[] } =
        { ids: [], success: true, errors: [] };

      // Bulk insert user memories if any
      if (userMemories.length > 0) {
        userResults = await this.batchSaveUserMemories(
          userMemories,
          parseInt(event.userId, 10),
        );
      }

      // Bulk insert agent memories if any
      if (agentMemories.length > 0) {
        agentResults = await this.batchSaveAgentMemories(
          agentMemories,
          event.agentId,
        );
      }

      const successCount = userResults.ids.length + agentResults.ids.length;
      const errorCount = userResults.errors.length + agentResults.errors.length;

      this.logger.log(`Successfully processed memory batch save event`, {
        userId: event.userId,
        agentId: event.agentId,
        reason: event.reason,
        totalMemories: event.memoryItems.length,
        successCount,
        errorCount,
        duration: Date.now() - startTime,
      });
    } catch (error) {
      const duration = Date.now() - startTime;

      this.logger.error(`Failed to process memory batch save event`, {
        userId: event.userId,
        agentId: event.agentId,
        requestId: event.requestId,
        error: error.message,
        stack: error.stack,
        duration,
      });
    }
  }

  /**
   * Event handler for bulk memory update events from LangChain tools
   * Processes multiple memory update operations asynchronously
   * @param event UpdateMemoryEvent containing array of memory update items
   */
  @OnEvent('memory.update.batch')
  async handleMemoryUpdateBatchEvent(event: any): Promise<void> {
    const startTime = Date.now();

    this.logger.debug(`Received memory update batch event`, {
      agentId: event.agentId,
      userId: event.userId,
      memoryCount: event.memoryItems?.length || 0,
      requestId: event.requestId,
    });

    try {
      let successCount = 0;
      let errorCount = 0;
      const errors: string[] = [];

      // Process each memory update item
      for (const memoryItem of event.memoryItems) {
        try {
          const result = await this.updateMemory(
            memoryItem.memoryId,
            memoryItem.memoryType,
            memoryItem.operation,
            memoryItem.updates,
            event.agentId,
            event.userId ? parseInt(event.userId, 10) : undefined,
          );

          if (result.success) {
            successCount++;
            this.logger.debug(
              `Successfully updated memory ${memoryItem.memoryId}`,
              {
                memoryId: memoryItem.memoryId,
                memoryType: memoryItem.memoryType,
                operation: memoryItem.operation,
              },
            );
          } else {
            errorCount++;
            errors.push(`Memory ${memoryItem.memoryId}: ${result.message}`);
            this.logger.warn(`Failed to update memory ${memoryItem.memoryId}`, {
              memoryId: memoryItem.memoryId,
              error: result.message,
            });
          }
        } catch (itemError) {
          errorCount++;
          errors.push(`Memory ${memoryItem.memoryId}: ${itemError.message}`);
          this.logger.error(`Error updating memory ${memoryItem.memoryId}`, {
            memoryId: memoryItem.memoryId,
            error: itemError.message,
          });
        }
      }

      const duration = Date.now() - startTime;

      this.logger.log(`Successfully processed memory update batch event`, {
        agentId: event.agentId,
        userId: event.userId,
        totalMemories: event.memoryItems.length,
        successCount,
        errorCount,
        duration,
        requestId: event.requestId,
      });

      if (errors.length > 0) {
        this.logger.warn(`Some memory updates failed`, {
          agentId: event.agentId,
          userId: event.userId,
          errors,
          requestId: event.requestId,
        });
      }
    } catch (error) {
      const duration = Date.now() - startTime;

      this.logger.error(`Failed to process memory update batch event`, {
        agentId: event.agentId,
        userId: event.userId,
        requestId: event.requestId,
        error: error.message,
        stack: error.stack,
        duration,
      });
    }
  }
}
