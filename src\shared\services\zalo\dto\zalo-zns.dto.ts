/**
 * DTO cho Zalo Notification Service (ZNS)
 * Dựa trên tài liệu chính thức: https://developers.zalo.me/docs/zalo-notification-service/
 */

import {
  IsString,
  IsOptional,
  IsObject,
  IsEnum,
  IsArray,
  ValidateNested,
  IsNumber,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Enum cho các loại template ZNS
 */
export enum ZnsTemplateType {
  TEXT = 'text',
  MEDIA = 'media',
  LIST = 'list',
  BUTTON = 'button',
}

/**
 * Enum cho trạng thái template ZNS
 */
export enum ZnsTemplateStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  DISABLED = 'DISABLED',
}

/**
 * Enum cho loại button trong template
 */
export enum ZnsButtonType {
  OPEN_URL = 'oa.open.url',
  QUERY_SHOW = 'oa.query.show',
  QUERY_HIDE = 'oa.query.hide',
  PHONE_CODE = 'oa.open.phone',
}

/**
 * DTO cho button trong template ZNS
 */
export class ZnsTemplateButtonDto {
  @ApiProperty({ description: 'Loại button', enum: ZnsButtonType })
  @IsEnum(ZnsButtonType)
  type: ZnsButtonType;

  @ApiProperty({ description: 'Tiêu đề button' })
  @IsString()
  title: string;

  @ApiPropertyOptional({ description: 'URL hoặc payload của button' })
  @IsOptional()
  @IsString()
  payload?: string;

  @ApiPropertyOptional({ description: 'URL hình ảnh icon button' })
  @IsOptional()
  @IsString()
  image_icon?: string;
}

/**
 * DTO cho element trong template list
 */
export class ZnsTemplateListElementDto {
  @ApiProperty({ description: 'Tiêu đề element' })
  @IsString()
  title: string;

  @ApiPropertyOptional({ description: 'Mô tả element' })
  @IsOptional()
  @IsString()
  subtitle?: string;

  @ApiPropertyOptional({ description: 'URL hình ảnh element' })
  @IsOptional()
  @IsString()
  image_url?: string;

  @ApiPropertyOptional({
    description: 'Danh sách button của element',
    type: [ZnsTemplateButtonDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ZnsTemplateButtonDto)
  default_action?: ZnsTemplateButtonDto;
}

/**
 * DTO cho tạo template ZNS
 */
export class CreateZnsTemplateDto {
  @ApiProperty({ description: 'Tên template' })
  @IsString()
  templateName: string;

  @ApiProperty({ description: 'Loại template', enum: ZnsTemplateType })
  @IsEnum(ZnsTemplateType)
  templateType: ZnsTemplateType;

  @ApiPropertyOptional({ description: 'Ngôn ngữ template (mặc định: vi)' })
  @IsOptional()
  @IsString()
  lang?: string = 'vi';

  @ApiPropertyOptional({
    description: 'Timeout template (giây, mặc định: 86400)',
  })
  @IsOptional()
  @IsNumber()
  timeout?: number = 86400;

  @ApiPropertyOptional({ description: 'URL hình ảnh preview' })
  @IsOptional()
  @IsString()
  previewUrl?: string;

  @ApiProperty({ description: 'Nội dung template' })
  @IsString()
  templateContent: string;

  @ApiPropertyOptional({
    description: 'Danh sách button',
    type: [ZnsTemplateButtonDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ZnsTemplateButtonDto)
  listButton?: ZnsTemplateButtonDto[];

  @ApiPropertyOptional({
    description: 'Danh sách element cho template list',
    type: [ZnsTemplateListElementDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ZnsTemplateListElementDto)
  listElement?: ZnsTemplateListElementDto[];
}

/**
 * DTO cho cập nhật template ZNS
 */
export class UpdateZnsTemplateDto {
  @ApiProperty({ description: 'ID template' })
  @IsString()
  templateId: string;

  @ApiPropertyOptional({ description: 'Tên template mới' })
  @IsOptional()
  @IsString()
  templateName?: string;

  @ApiPropertyOptional({ description: 'Timeout template (giây)' })
  @IsOptional()
  @IsNumber()
  timeout?: number;

  @ApiPropertyOptional({ description: 'URL hình ảnh preview mới' })
  @IsOptional()
  @IsString()
  previewUrl?: string;

  @ApiPropertyOptional({ description: 'Nội dung template mới' })
  @IsOptional()
  @IsString()
  templateContent?: string;

  @ApiPropertyOptional({
    description: 'Danh sách button mới',
    type: [ZnsTemplateButtonDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ZnsTemplateButtonDto)
  listButton?: ZnsTemplateButtonDto[];

  @ApiPropertyOptional({
    description: 'Danh sách element mới cho template list',
    type: [ZnsTemplateListElementDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ZnsTemplateListElementDto)
  listElement?: ZnsTemplateListElementDto[];
}

/**
 * DTO cho upload ảnh ZNS
 */
export class ZnsUploadImageDto {
  @ApiProperty({
    description: 'File ảnh cần upload',
    type: 'string',
    format: 'binary',
  })
  file: any;
}

/**
 * DTO cho gửi tin nhắn ZNS
 */
export class SendZnsMessageDto {
  @ApiProperty({ description: 'Số điện thoại người nhận' })
  @IsString()
  phone: string;

  @ApiProperty({ description: 'ID template ZNS' })
  @IsString()
  template_id: string;

  @ApiProperty({ description: 'Dữ liệu cho template' })
  @IsObject()
  template_data: Record<string, string>;

  @ApiPropertyOptional({ description: 'ID tracking (tùy chọn)' })
  @IsOptional()
  @IsString()
  tracking_id?: string;
}

/**
 * DTO cho gửi ZNS sử dụng hash phone
 */
export class SendZnsHashPhoneDto {
  @ApiProperty({ description: 'Hash của số điện thoại' })
  @IsString()
  phone_hash: string;

  @ApiProperty({ description: 'ID template ZNS' })
  @IsString()
  template_id: string;

  @ApiProperty({ description: 'Dữ liệu cho template' })
  @IsObject()
  template_data: Record<string, string>;

  @ApiPropertyOptional({ description: 'ID tracking (tùy chọn)' })
  @IsOptional()
  @IsString()
  tracking_id?: string;
}

/**
 * DTO cho gửi ZNS development mode
 */
export class SendZnsDevModeDto {
  @ApiProperty({ description: 'Số điện thoại người nhận' })
  @IsString()
  phone: string;

  @ApiProperty({ description: 'ID template ZNS' })
  @IsString()
  template_id: string;

  @ApiProperty({ description: 'Dữ liệu cho template' })
  @IsObject()
  template_data: Record<string, string>;

  @ApiProperty({ description: 'Chế độ development', default: true })
  @IsBoolean()
  mode: boolean = true;

  @ApiPropertyOptional({ description: 'ID tracking (tùy chọn)' })
  @IsOptional()
  @IsString()
  tracking_id?: string;
}

/**
 * DTO cho gửi ZNS với mã hóa RSA
 */
export class SendZnsRsaDto {
  @ApiProperty({ description: 'Số điện thoại người nhận (đã mã hóa RSA)' })
  @IsString()
  phone: string;

  @ApiProperty({ description: 'ID template ZNS' })
  @IsString()
  template_id: string;

  @ApiProperty({ description: 'Dữ liệu cho template' })
  @IsObject()
  template_data: Record<string, string>;

  @ApiPropertyOptional({ description: 'ID tracking (tùy chọn)' })
  @IsOptional()
  @IsString()
  tracking_id?: string;
}

/**
 * DTO cho gửi ZNS Journey
 */
export class SendZnsJourneyDto {
  @ApiProperty({ description: 'Số điện thoại người nhận' })
  @IsString()
  phone: string;

  @ApiProperty({ description: 'ID template ZNS' })
  @IsString()
  template_id: string;

  @ApiProperty({ description: 'Dữ liệu cho template' })
  @IsObject()
  template_data: Record<string, string>;

  @ApiProperty({ description: 'ID journey' })
  @IsString()
  journey_id: string;

  @ApiPropertyOptional({ description: 'ID tracking (tùy chọn)' })
  @IsOptional()
  @IsString()
  tracking_id?: string;
}

/**
 * Response DTO cho tạo template ZNS
 */
export class ZnsTemplateResponseDto {
  @ApiProperty({ description: 'ID template được tạo' })
  templateId: string;

  @ApiProperty({ description: 'Trạng thái template', enum: ZnsTemplateStatus })
  status: ZnsTemplateStatus;

  @ApiPropertyOptional({ description: 'Thông báo từ hệ thống' })
  message?: string;
}

/**
 * Response DTO cho upload ảnh ZNS
 */
export class ZnsUploadImageResponseDto {
  @ApiProperty({ description: 'URL ảnh đã upload' })
  url: string;

  @ApiProperty({ description: 'ID attachment' })
  attachment_id: string;

  @ApiPropertyOptional({ description: 'Kích thước file (bytes)' })
  size?: number;

  @ApiPropertyOptional({ description: 'Loại file' })
  type?: string;
}

/**
 * Response DTO cho gửi tin nhắn ZNS
 */
export class ZnsSendResponseDto {
  @ApiProperty({ description: 'ID tin nhắn đã gửi' })
  message_id: string;

  @ApiProperty({ description: 'ID tracking' })
  tracking_id: string;

  @ApiPropertyOptional({ description: 'Trạng thái gửi' })
  status?: string;

  @ApiPropertyOptional({ description: 'Thông báo' })
  message?: string;
}

/**
 * DTO cho chi tiết template ZNS
 */
export class ZnsTemplateDetailDto {
  @ApiProperty({ description: 'ID template' })
  template_id: string;

  @ApiProperty({ description: 'Tên template' })
  template_name: string;

  @ApiProperty({ description: 'Trạng thái template', enum: ZnsTemplateStatus })
  status: ZnsTemplateStatus;

  @ApiProperty({ description: 'Nội dung template' })
  template_content: string;

  @ApiProperty({ description: 'Loại template', enum: ZnsTemplateType })
  template_type: ZnsTemplateType;

  @ApiPropertyOptional({ description: 'Thời gian tạo' })
  created_time?: number;

  @ApiPropertyOptional({ description: 'Thời gian cập nhật' })
  updated_time?: number;

  @ApiPropertyOptional({ description: 'URL preview' })
  preview_url?: string;

  @ApiPropertyOptional({ description: 'Danh sách tham số template' })
  params?: Array<{
    key: string;
    label: string;
    type: string;
  }>;
}

/**
 * DTO cho danh sách template ZNS
 */
export class ZnsTemplateListDto {
  @ApiProperty({
    description: 'Danh sách template',
    type: [ZnsTemplateDetailDto],
  })
  templates: ZnsTemplateDetailDto[];

  @ApiProperty({ description: 'Tổng số template' })
  total: number;

  @ApiPropertyOptional({ description: 'Có còn dữ liệu tiếp theo không' })
  has_more?: boolean;

  @ApiPropertyOptional({ description: 'Offset tiếp theo' })
  next_offset?: number;
}

// ===== DTO CHO CÁC API TRUY XUẤT THÔNG TIN ZNS =====

/**
 * Enum cho trạng thái ZNS
 */
export enum ZnsStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
}

/**
 * Enum cho loại nội dung ZNS được phép gửi
 */
export enum ZnsContentType {
  TRANSACTION = 'TRANSACTION',
  PROMOTION = 'PROMOTION',
  CUSTOMER_CARE = 'CUSTOMER_CARE',
  NOTIFICATION = 'NOTIFICATION',
}

/**
 * Enum cho mức độ chất lượng ZNS
 */
export enum ZnsQualityLevel {
  HIGH = 'HIGH',
  MEDIUM = 'MEDIUM',
  LOW = 'LOW',
}

/**
 * DTO cho thông tin trạng thái ZNS
 */
export class ZnsStatusInfoDto {
  @ApiProperty({ description: 'Trạng thái ZNS', enum: ZnsStatus })
  status: ZnsStatus;

  @ApiProperty({ description: 'Thời gian cập nhật trạng thái' })
  updated_time: number;

  @ApiPropertyOptional({ description: 'Lý do thay đổi trạng thái' })
  reason?: string;

  @ApiPropertyOptional({ description: 'Thông tin bổ sung' })
  additional_info?: string;
}

/**
 * DTO cho thông tin quota ZNS
 */
export class ZnsQuotaInfoDto {
  @ApiProperty({ description: 'Hạn mức tin nhắn hàng ngày' })
  daily_quota: number;

  @ApiProperty({ description: 'Số tin nhắn đã gửi trong ngày' })
  daily_sent: number;

  @ApiProperty({ description: 'Số tin nhắn còn lại trong ngày' })
  daily_remaining: number;

  @ApiProperty({ description: 'Hạn mức tin nhắn hàng tháng' })
  monthly_quota: number;

  @ApiProperty({ description: 'Số tin nhắn đã gửi trong tháng' })
  monthly_sent: number;

  @ApiProperty({ description: 'Số tin nhắn còn lại trong tháng' })
  monthly_remaining: number;

  @ApiProperty({
    description: 'Thời gian reset quota hàng ngày (Unix timestamp)',
  })
  daily_reset_time: number;

  @ApiProperty({
    description: 'Thời gian reset quota hàng tháng (Unix timestamp)',
  })
  monthly_reset_time: number;

  @ApiPropertyOptional({ description: 'Loại gói dịch vụ' })
  package_type?: string;
}

/**
 * DTO cho thông tin loại nội dung ZNS được phép gửi
 */
export class ZnsAllowedContentDto {
  @ApiProperty({
    description: 'Danh sách loại nội dung được phép',
    enum: ZnsContentType,
    isArray: true,
  })
  allowed_content_types: ZnsContentType[];

  @ApiProperty({ description: 'Có được phép gửi tin nhắn giao dịch không' })
  can_send_transaction: boolean;

  @ApiProperty({ description: 'Có được phép gửi tin nhắn khuyến mãi không' })
  can_send_promotion: boolean;

  @ApiProperty({
    description: 'Có được phép gửi tin nhắn chăm sóc khách hàng không',
  })
  can_send_customer_care: boolean;

  @ApiProperty({ description: 'Có được phép gửi tin nhắn thông báo không' })
  can_send_notification: boolean;

  @ApiPropertyOptional({
    description: 'Giới hạn đặc biệt cho từng loại nội dung',
  })
  content_limits?: Record<string, number>;
}

/**
 * DTO cho tham số lấy danh sách template
 */
export class GetZnsTemplateListDto {
  @ApiPropertyOptional({ description: 'Offset cho phân trang' })
  @IsOptional()
  @IsNumber()
  offset?: number;

  @ApiPropertyOptional({
    description: 'Số lượng template tối đa trả về (1-100)',
  })
  @IsOptional()
  @IsNumber()
  limit?: number;

  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái template',
    enum: ZnsTemplateStatus,
  })
  @IsOptional()
  @IsEnum(ZnsTemplateStatus)
  status?: ZnsTemplateStatus;

  @ApiPropertyOptional({
    description: 'Lọc theo loại template',
    enum: ZnsTemplateType,
  })
  @IsOptional()
  @IsEnum(ZnsTemplateType)
  template_type?: ZnsTemplateType;
}

/**
 * DTO cho dữ liệu mẫu của template
 */
export class ZnsTemplateSampleDataDto {
  @ApiProperty({ description: 'ID template' })
  template_id: string;

  @ApiProperty({ description: 'Dữ liệu mẫu cho template' })
  sample_data: Record<string, string>;

  @ApiProperty({
    description: 'Nội dung template sau khi thay thế dữ liệu mẫu',
  })
  preview_content: string;

  @ApiPropertyOptional({ description: 'URL preview (nếu có hình ảnh)' })
  preview_url?: string;
}

/**
 * DTO cho thông tin đánh giá của khách hàng
 */
export class ZnsCustomerRatingDto {
  @ApiProperty({ description: 'Tổng số đánh giá' })
  total_ratings: number;

  @ApiProperty({ description: 'Điểm đánh giá trung bình (1-5)' })
  average_rating: number;

  @ApiProperty({ description: 'Số đánh giá 5 sao' })
  five_star_count: number;

  @ApiProperty({ description: 'Số đánh giá 4 sao' })
  four_star_count: number;

  @ApiProperty({ description: 'Số đánh giá 3 sao' })
  three_star_count: number;

  @ApiProperty({ description: 'Số đánh giá 2 sao' })
  two_star_count: number;

  @ApiProperty({ description: 'Số đánh giá 1 sao' })
  one_star_count: number;

  @ApiProperty({ description: 'Phần trăm đánh giá tích cực (4-5 sao)' })
  positive_rating_percentage: number;

  @ApiProperty({ description: 'Thời gian cập nhật cuối cùng' })
  last_updated: number;

  @ApiPropertyOptional({
    description: 'Xu hướng đánh giá (tăng/giảm so với tháng trước)',
  })
  rating_trend?: 'up' | 'down' | 'stable';
}

/**
 * DTO cho chi tiết đánh giá khách hàng
 */
export class ZnsCustomerRatingDetailDto {
  @ApiProperty({ description: 'ID đánh giá' })
  rating_id: string;

  @ApiProperty({ description: 'ID tin nhắn được đánh giá' })
  message_id: string;

  @ApiProperty({ description: 'ID template' })
  template_id: string;

  @ApiProperty({ description: 'Điểm đánh giá (1-5)' })
  rating: number;

  @ApiPropertyOptional({ description: 'Nhận xét của khách hàng' })
  comment?: string;

  @ApiProperty({ description: 'Thời gian đánh giá' })
  rating_time: number;

  @ApiPropertyOptional({ description: 'ID khách hàng (nếu có)' })
  customer_id?: string;

  @ApiPropertyOptional({ description: 'Thông tin bổ sung' })
  metadata?: Record<string, any>;
}

/**
 * DTO cho tham số lấy đánh giá khách hàng
 */
export class GetZnsCustomerRatingDto {
  @ApiPropertyOptional({ description: 'Thời gian bắt đầu (Unix timestamp)' })
  @IsOptional()
  @IsNumber()
  start_time?: number;

  @ApiPropertyOptional({ description: 'Thời gian kết thúc (Unix timestamp)' })
  @IsOptional()
  @IsNumber()
  end_time?: number;

  @ApiPropertyOptional({ description: 'Offset cho phân trang' })
  @IsOptional()
  @IsNumber()
  offset?: number;

  @ApiPropertyOptional({ description: 'Số lượng đánh giá tối đa trả về' })
  @IsOptional()
  @IsNumber()
  limit?: number;

  @ApiPropertyOptional({ description: 'Lọc theo điểm đánh giá' })
  @IsOptional()
  @IsNumber()
  rating?: number;

  @ApiPropertyOptional({ description: 'Lọc theo ID template' })
  @IsOptional()
  @IsString()
  template_id?: string;
}

/**
 * DTO cho thông tin chất lượng gửi ZNS
 */
export class ZnsQualityInfoDto {
  @ApiProperty({
    description: 'Mức độ chất lượng hiện tại',
    enum: ZnsQualityLevel,
  })
  current_quality_level: ZnsQualityLevel;

  @ApiProperty({ description: 'Điểm chất lượng (0-100)' })
  quality_score: number;

  @ApiProperty({ description: 'Tỷ lệ gửi thành công (%)' })
  delivery_rate: number;

  @ApiProperty({ description: 'Tỷ lệ khách hàng đọc tin nhắn (%)' })
  read_rate: number;

  @ApiProperty({ description: 'Tỷ lệ khách hàng tương tác (%)' })
  engagement_rate: number;

  @ApiProperty({ description: 'Tỷ lệ khiếu nại (%)' })
  complaint_rate: number;

  @ApiProperty({ description: 'Điểm đánh giá trung bình từ khách hàng' })
  average_customer_rating: number;

  @ApiProperty({ description: 'Thời gian đánh giá' })
  evaluation_time: number;

  @ApiPropertyOptional({
    description: 'Xu hướng chất lượng so với tháng trước',
  })
  quality_trend?: 'improving' | 'declining' | 'stable';

  @ApiPropertyOptional({ description: 'Gợi ý cải thiện chất lượng' })
  improvement_suggestions?: string[];
}

/**
 * DTO cho chi tiết chất lượng theo thời gian
 */
export class ZnsQualityHistoryDto {
  @ApiProperty({ description: 'Thời gian đánh giá' })
  evaluation_time: number;

  @ApiProperty({ description: 'Điểm chất lượng tại thời điểm đó' })
  quality_score: number;

  @ApiProperty({
    description: 'Mức độ chất lượng tại thời điểm đó',
    enum: ZnsQualityLevel,
  })
  quality_level: ZnsQualityLevel;

  @ApiProperty({ description: 'Tỷ lệ gửi thành công (%)' })
  delivery_rate: number;

  @ApiProperty({ description: 'Tỷ lệ đọc tin nhắn (%)' })
  read_rate: number;

  @ApiProperty({ description: 'Tỷ lệ khiếu nại (%)' })
  complaint_rate: number;
}

/**
 * DTO cho tham số lấy lịch sử chất lượng
 */
export class GetZnsQualityHistoryDto {
  @ApiPropertyOptional({ description: 'Thời gian bắt đầu (Unix timestamp)' })
  @IsOptional()
  @IsNumber()
  start_time?: number;

  @ApiPropertyOptional({ description: 'Thời gian kết thúc (Unix timestamp)' })
  @IsOptional()
  @IsNumber()
  end_time?: number;

  @ApiPropertyOptional({
    description: 'Khoảng thời gian đánh giá (daily, weekly, monthly)',
  })
  @IsOptional()
  @IsString()
  interval?: 'daily' | 'weekly' | 'monthly';
}

// ===== DTO CHO COMPONENT ZNS =====

/**
 * Enum cho loại component ZNS
 */
export enum ZnsComponentType {
  TEXT = 'text',
  HEADER = 'header',
  BODY = 'body',
  FOOTER = 'footer',
  BUTTON = 'button',
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  FILE = 'file',
  LOCATION = 'location',
  CONTACT = 'contact',
  LIST = 'list',
  CAROUSEL = 'carousel',
  QUICK_REPLY = 'quick_reply',
  INTERACTIVE = 'interactive',
}

/**
 * Enum cho định dạng component
 */
export enum ZnsComponentFormat {
  TEXT = 'TEXT',
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  DOCUMENT = 'DOCUMENT',
  LOCATION = 'LOCATION',
  DATETIME = 'DATETIME',
  CURRENCY = 'CURRENCY',
}

/**
 * DTO cho component text
 */
export class ZnsTextComponentDto {
  @ApiProperty({ description: 'Loại component', enum: ZnsComponentType })
  @IsEnum(ZnsComponentType)
  type: ZnsComponentType.TEXT;

  @ApiProperty({ description: 'Nội dung text' })
  @IsString()
  text: string;

  @ApiPropertyOptional({
    description: 'Định dạng text',
    enum: ZnsComponentFormat,
  })
  @IsOptional()
  @IsEnum(ZnsComponentFormat)
  format?: ZnsComponentFormat;

  @ApiPropertyOptional({ description: 'Tham số động trong text' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  parameters?: string[];
}

/**
 * DTO cho component header
 */
export class ZnsHeaderComponentDto {
  @ApiProperty({ description: 'Loại component', enum: ZnsComponentType })
  @IsEnum(ZnsComponentType)
  type: ZnsComponentType.HEADER;

  @ApiProperty({ description: 'Định dạng header', enum: ZnsComponentFormat })
  @IsEnum(ZnsComponentFormat)
  format: ZnsComponentFormat;

  @ApiPropertyOptional({
    description: 'Nội dung text cho header (nếu format là TEXT)',
  })
  @IsOptional()
  @IsString()
  text?: string;

  @ApiPropertyOptional({
    description: 'URL media cho header (nếu format là IMAGE/VIDEO/DOCUMENT)',
  })
  @IsOptional()
  @IsString()
  media_url?: string;

  @ApiPropertyOptional({ description: 'Tham số động trong header' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  parameters?: string[];
}

/**
 * DTO cho component body
 */
export class ZnsBodyComponentDto {
  @ApiProperty({ description: 'Loại component', enum: ZnsComponentType })
  @IsEnum(ZnsComponentType)
  type: ZnsComponentType.BODY;

  @ApiProperty({ description: 'Nội dung body' })
  @IsString()
  text: string;

  @ApiPropertyOptional({ description: 'Tham số động trong body' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  parameters?: string[];
}

/**
 * DTO cho component footer
 */
export class ZnsFooterComponentDto {
  @ApiProperty({ description: 'Loại component', enum: ZnsComponentType })
  @IsEnum(ZnsComponentType)
  type: ZnsComponentType.FOOTER;

  @ApiProperty({ description: 'Nội dung footer' })
  @IsString()
  text: string;
}

/**
 * DTO cho component button
 */
export class ZnsButtonComponentDto {
  @ApiProperty({ description: 'Loại component', enum: ZnsComponentType })
  @IsEnum(ZnsComponentType)
  type: ZnsComponentType.BUTTON;

  @ApiProperty({ description: 'Loại button', enum: ZnsButtonType })
  @IsEnum(ZnsButtonType)
  sub_type: ZnsButtonType;

  @ApiProperty({ description: 'Tiêu đề button' })
  @IsString()
  text: string;

  @ApiPropertyOptional({ description: 'URL hoặc payload của button' })
  @IsOptional()
  @IsString()
  url?: string;

  @ApiPropertyOptional({ description: 'Số điện thoại (cho button gọi điện)' })
  @IsOptional()
  @IsString()
  phone_number?: string;

  @ApiPropertyOptional({ description: 'Tham số động trong button' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  parameters?: string[];
}

/**
 * DTO cho component interactive
 */
export class ZnsInteractiveComponentDto {
  @ApiProperty({ description: 'Loại component', enum: ZnsComponentType })
  @IsEnum(ZnsComponentType)
  type: ZnsComponentType.INTERACTIVE;

  @ApiProperty({ description: 'Loại interactive (button, list, product)' })
  @IsString()
  interactive_type: 'button' | 'list' | 'product';

  @ApiPropertyOptional({ description: 'Header của interactive' })
  @IsOptional()
  @ValidateNested()
  @Type(() => ZnsHeaderComponentDto)
  header?: ZnsHeaderComponentDto;

  @ApiPropertyOptional({ description: 'Body của interactive' })
  @IsOptional()
  @ValidateNested()
  @Type(() => ZnsBodyComponentDto)
  body?: ZnsBodyComponentDto;

  @ApiPropertyOptional({ description: 'Footer của interactive' })
  @IsOptional()
  @ValidateNested()
  @Type(() => ZnsFooterComponentDto)
  footer?: ZnsFooterComponentDto;

  @ApiPropertyOptional({
    description: 'Danh sách button',
    type: [ZnsButtonComponentDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ZnsButtonComponentDto)
  buttons?: ZnsButtonComponentDto[];
}

/**
 * DTO cho template component tổng hợp
 */
export class ZnsTemplateComponentDto {
  @ApiProperty({ description: 'Loại component', enum: ZnsComponentType })
  @IsEnum(ZnsComponentType)
  type: ZnsComponentType;

  @ApiPropertyOptional({ description: 'Component text' })
  @IsOptional()
  @ValidateNested()
  @Type(() => ZnsTextComponentDto)
  text?: ZnsTextComponentDto;

  @ApiPropertyOptional({ description: 'Component header' })
  @IsOptional()
  @ValidateNested()
  @Type(() => ZnsHeaderComponentDto)
  header?: ZnsHeaderComponentDto;

  @ApiPropertyOptional({ description: 'Component body' })
  @IsOptional()
  @ValidateNested()
  @Type(() => ZnsBodyComponentDto)
  body?: ZnsBodyComponentDto;

  @ApiPropertyOptional({ description: 'Component footer' })
  @IsOptional()
  @ValidateNested()
  @Type(() => ZnsFooterComponentDto)
  footer?: ZnsFooterComponentDto;

  @ApiPropertyOptional({ description: 'Component button' })
  @IsOptional()
  @ValidateNested()
  @Type(() => ZnsButtonComponentDto)
  button?: ZnsButtonComponentDto;

  @ApiPropertyOptional({ description: 'Component interactive' })
  @IsOptional()
  @ValidateNested()
  @Type(() => ZnsInteractiveComponentDto)
  interactive?: ZnsInteractiveComponentDto;
}

// ===== DTO CHO MÃ LỖI ZNS =====

/**
 * Enum cho các mã lỗi ZNS phổ biến
 */
export enum ZnsErrorCode {
  // Lỗi chung
  SUCCESS = 0,
  INVALID_PARAMETER = -1,
  INVALID_ACCESS_TOKEN = -2,
  PERMISSION_DENIED = -3,
  RATE_LIMIT_EXCEEDED = -4,
  INTERNAL_SERVER_ERROR = -5,

  // Lỗi template
  TEMPLATE_NOT_FOUND = -100,
  TEMPLATE_NOT_APPROVED = -101,
  TEMPLATE_EXPIRED = -102,
  TEMPLATE_DISABLED = -103,
  TEMPLATE_INVALID_FORMAT = -104,
  TEMPLATE_PARAMETER_MISMATCH = -105,

  // Lỗi tin nhắn
  INVALID_PHONE_NUMBER = -200,
  PHONE_NUMBER_BLOCKED = -201,
  MESSAGE_QUOTA_EXCEEDED = -202,
  MESSAGE_TOO_LONG = -203,
  MESSAGE_INVALID_CONTENT = -204,
  MESSAGE_DUPLICATE = -205,

  // Lỗi Official Account
  OA_NOT_FOUND = -300,
  OA_NOT_APPROVED = -301,
  OA_SUSPENDED = -302,
  OA_INSUFFICIENT_BALANCE = -303,
  OA_FEATURE_NOT_ENABLED = -304,

  // Lỗi upload file
  FILE_TOO_LARGE = -400,
  FILE_INVALID_FORMAT = -401,
  FILE_UPLOAD_FAILED = -402,
  FILE_NOT_FOUND = -403,
}

/**
 * DTO cho thông tin lỗi ZNS
 */
export class ZnsErrorInfoDto {
  @ApiProperty({ description: 'Mã lỗi', enum: ZnsErrorCode })
  @IsEnum(ZnsErrorCode)
  error_code: ZnsErrorCode;

  @ApiProperty({ description: 'Thông báo lỗi' })
  @IsString()
  error_message: string;

  @ApiPropertyOptional({ description: 'Mô tả chi tiết lỗi' })
  @IsOptional()
  @IsString()
  error_description?: string;

  @ApiPropertyOptional({ description: 'Gợi ý khắc phục' })
  @IsOptional()
  @IsString()
  suggestion?: string;

  @ApiPropertyOptional({ description: 'Thông tin bổ sung' })
  @IsOptional()
  @IsObject()
  additional_info?: Record<string, any>;
}

/**
 * DTO cho response lỗi ZNS
 */
export class ZnsErrorResponseDto {
  @ApiProperty({ description: 'Mã lỗi', enum: ZnsErrorCode })
  error: ZnsErrorCode;

  @ApiProperty({ description: 'Thông báo lỗi' })
  message: string;

  @ApiPropertyOptional({ description: 'Dữ liệu bổ sung (nếu có)' })
  @IsOptional()
  data?: any;

  @ApiPropertyOptional({ description: 'Timestamp của lỗi' })
  @IsOptional()
  @IsNumber()
  timestamp?: number;
}

// ===== DTO CHO BIN CODE =====

/**
 * Enum cho các BIN code ngân hàng Việt Nam
 */
export enum ZnsBankBinCode {
  // Ngân hàng Nhà nước
  VIETCOMBANK = '970436',
  VIETINBANK = '970415',
  BIDV = '970418',
  AGRIBANK = '970405',

  // Ngân hàng thương mại cổ phần
  TECHCOMBANK = '970407',
  MB_BANK = '970422',
  ACB = '970416',
  VPB = '970432',
  TPB = '970423',
  SACOMBANK = '970403',
  EXIMBANK = '970431',
  HDBANK = '970437',
  SHB = '970443',
  OCB = '970448',
  MSB = '970426',
  SEABANK = '970440',
  VIB = '970441',
  LPB = '970449',
  KIENLONGBANK = '970452',
  DONGABANK = '970406',
  NAMABANK = '970428',
  PGBANK = '970430',
  VIETBANK = '970433',
  NCB = '970419',
  BACABANK = '970409',
  OCEANBANK = '970414',
  GPBANK = '970408',
  ABBANK = '970425',
  VCCB = '970454',
  BAOVIETBANK = '970438',
  CBBANK = '970444',
  WOORIBANK = '970457',
  SHINHANBANK = '970424',
  STANDARDCHARTERED = '970410',
  PUBLICBANK = '970439',
  NONGHYUP = '970427',
  INDOVINABANK = '970434',
  CIMB = '970456',
  UOB = '970458',
  HSBC = '970412',
  HONGLEONG = '970442',
}

/**
 * DTO cho thông tin BIN code
 */
export class ZnsBinCodeInfoDto {
  @ApiProperty({ description: 'Mã BIN', enum: ZnsBankBinCode })
  @IsEnum(ZnsBankBinCode)
  bin_code: ZnsBankBinCode;

  @ApiProperty({ description: 'Tên ngân hàng' })
  @IsString()
  bank_name: string;

  @ApiProperty({ description: 'Tên viết tắt ngân hàng' })
  @IsString()
  bank_short_name: string;

  @ApiProperty({ description: 'Logo ngân hàng' })
  @IsString()
  bank_logo: string;

  @ApiProperty({ description: 'Loại thẻ (DEBIT/CREDIT)' })
  @IsString()
  card_type: 'DEBIT' | 'CREDIT';

  @ApiProperty({ description: 'Thương hiệu thẻ (VISA/MASTERCARD/JCB/NAPAS)' })
  @IsString()
  card_brand: 'VISA' | 'MASTERCARD' | 'JCB' | 'NAPAS' | 'AMEX';

  @ApiPropertyOptional({ description: 'Có hỗ trợ thanh toán online không' })
  @IsOptional()
  @IsBoolean()
  supports_online_payment?: boolean;

  @ApiPropertyOptional({ description: 'Có hỗ trợ ZNS không' })
  @IsOptional()
  @IsBoolean()
  supports_zns?: boolean;
}

/**
 * DTO cho danh sách BIN code
 */
export class ZnsBinCodeListDto {
  @ApiProperty({ description: 'Danh sách BIN code', type: [ZnsBinCodeInfoDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ZnsBinCodeInfoDto)
  bin_codes: ZnsBinCodeInfoDto[];

  @ApiProperty({ description: 'Tổng số BIN code' })
  @IsNumber()
  total: number;

  @ApiPropertyOptional({ description: 'Thời gian cập nhật cuối cùng' })
  @IsOptional()
  @IsNumber()
  last_updated?: number;
}

// ===== DTO CHO CƠ CHẾ ĐÁNH GIÁ CHẤT LƯỢNG =====

/**
 * Enum cho mức độ ưu tiên gửi ZNS
 */
export enum ZnsPriorityLevel {
  HIGH = 'HIGH',
  MEDIUM = 'MEDIUM',
  LOW = 'LOW',
  BLOCKED = 'BLOCKED',
}

/**
 * Enum cho loại vi phạm chất lượng
 */
export enum ZnsViolationType {
  SPAM_CONTENT = 'SPAM_CONTENT',
  MISLEADING_CONTENT = 'MISLEADING_CONTENT',
  INAPPROPRIATE_CONTENT = 'INAPPROPRIATE_CONTENT',
  EXCESSIVE_FREQUENCY = 'EXCESSIVE_FREQUENCY',
  LOW_ENGAGEMENT = 'LOW_ENGAGEMENT',
  HIGH_COMPLAINT_RATE = 'HIGH_COMPLAINT_RATE',
  INVALID_TEMPLATE = 'INVALID_TEMPLATE',
  POLICY_VIOLATION = 'POLICY_VIOLATION',
}

/**
 * DTO cho thông tin vi phạm chất lượng
 */
export class ZnsQualityViolationDto {
  @ApiProperty({ description: 'ID vi phạm' })
  @IsString()
  violation_id: string;

  @ApiProperty({ description: 'Loại vi phạm', enum: ZnsViolationType })
  @IsEnum(ZnsViolationType)
  violation_type: ZnsViolationType;

  @ApiProperty({ description: 'Mô tả vi phạm' })
  @IsString()
  description: string;

  @ApiProperty({ description: 'Mức độ nghiêm trọng (1-5)' })
  @IsNumber()
  severity_level: number;

  @ApiProperty({ description: 'Thời gian vi phạm' })
  @IsNumber()
  violation_time: number;

  @ApiProperty({
    description: 'Trạng thái vi phạm (ACTIVE/RESOLVED/DISMISSED)',
  })
  @IsString()
  status: 'ACTIVE' | 'RESOLVED' | 'DISMISSED';

  @ApiPropertyOptional({ description: 'ID template liên quan (nếu có)' })
  @IsOptional()
  @IsString()
  template_id?: string;

  @ApiPropertyOptional({ description: 'ID tin nhắn liên quan (nếu có)' })
  @IsOptional()
  @IsString()
  message_id?: string;

  @ApiPropertyOptional({ description: 'Hành động khắc phục được đề xuất' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  recommended_actions?: string[];

  @ApiPropertyOptional({ description: 'Thời hạn khắc phục' })
  @IsOptional()
  @IsNumber()
  resolution_deadline?: number;
}

/**
 * DTO cho quyền lợi gửi ZNS
 */
export class ZnsSendingPrivilegeDto {
  @ApiProperty({ description: 'Mức độ ưu tiên gửi', enum: ZnsPriorityLevel })
  @IsEnum(ZnsPriorityLevel)
  priority_level: ZnsPriorityLevel;

  @ApiProperty({ description: 'Hạn mức tin nhắn hàng ngày' })
  @IsNumber()
  daily_quota: number;

  @ApiProperty({ description: 'Hạn mức tin nhắn hàng tháng' })
  @IsNumber()
  monthly_quota: number;

  @ApiProperty({ description: 'Tỷ lệ gửi tối đa (tin nhắn/giây)' })
  @IsNumber()
  max_sending_rate: number;

  @ApiProperty({ description: 'Có được phép gửi tin nhắn khuyến mãi không' })
  @IsBoolean()
  can_send_promotional: boolean;

  @ApiProperty({ description: 'Có được phép gửi tin nhắn giao dịch không' })
  @IsBoolean()
  can_send_transactional: boolean;

  @ApiProperty({
    description: 'Có được phép gửi tin nhắn chăm sóc khách hàng không',
  })
  @IsBoolean()
  can_send_customer_care: boolean;

  @ApiProperty({ description: 'Thời gian hiệu lực' })
  @IsNumber()
  valid_until: number;

  @ApiPropertyOptional({ description: 'Lý do hạn chế (nếu có)' })
  @IsOptional()
  @IsString()
  restriction_reason?: string;

  @ApiPropertyOptional({ description: 'Điều kiện để nâng cấp quyền lợi' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  upgrade_conditions?: string[];
}

/**
 * DTO cho chi tiết đánh giá chất lượng
 */
export class ZnsQualityAssessmentDto {
  @ApiProperty({ description: 'ID đánh giá' })
  @IsString()
  assessment_id: string;

  @ApiProperty({ description: 'Thời gian đánh giá' })
  @IsNumber()
  assessment_time: number;

  @ApiProperty({ description: 'Điểm chất lượng tổng thể (0-100)' })
  @IsNumber()
  overall_score: number;

  @ApiProperty({ description: 'Mức độ chất lượng', enum: ZnsQualityLevel })
  @IsEnum(ZnsQualityLevel)
  quality_level: ZnsQualityLevel;

  @ApiProperty({ description: 'Chi tiết điểm số theo từng tiêu chí' })
  @IsObject()
  score_breakdown: {
    content_quality: number;
    delivery_rate: number;
    engagement_rate: number;
    complaint_rate: number;
    customer_satisfaction: number;
    template_compliance: number;
  };

  @ApiProperty({
    description: 'Danh sách vi phạm (nếu có)',
    type: [ZnsQualityViolationDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ZnsQualityViolationDto)
  violations: ZnsQualityViolationDto[];

  @ApiProperty({ description: 'Quyền lợi gửi hiện tại' })
  @ValidateNested()
  @Type(() => ZnsSendingPrivilegeDto)
  current_privileges: ZnsSendingPrivilegeDto;

  @ApiPropertyOptional({ description: 'Gợi ý cải thiện' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  improvement_recommendations?: string[];

  @ApiPropertyOptional({ description: 'Thời gian đánh giá tiếp theo' })
  @IsOptional()
  @IsNumber()
  next_assessment_time?: number;
}

/**
 * DTO cho lịch sử đánh giá chất lượng
 */
export class ZnsQualityAssessmentHistoryDto {
  @ApiProperty({
    description: 'Danh sách đánh giá',
    type: [ZnsQualityAssessmentDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ZnsQualityAssessmentDto)
  assessments: ZnsQualityAssessmentDto[];

  @ApiProperty({ description: 'Tổng số đánh giá' })
  @IsNumber()
  total: number;

  @ApiPropertyOptional({ description: 'Xu hướng chất lượng' })
  @IsOptional()
  @IsString()
  quality_trend?: 'improving' | 'declining' | 'stable';

  @ApiPropertyOptional({ description: 'Điểm trung bình trong 30 ngày qua' })
  @IsOptional()
  @IsNumber()
  average_score_30_days?: number;

  @ApiPropertyOptional({ description: 'Điểm trung bình trong 90 ngày qua' })
  @IsOptional()
  @IsNumber()
  average_score_90_days?: number;
}

/**
 * DTO cho tham số lấy đánh giá chất lượng
 */
export class GetZnsQualityAssessmentDto {
  @ApiPropertyOptional({ description: 'Thời gian bắt đầu (Unix timestamp)' })
  @IsOptional()
  @IsNumber()
  start_time?: number;

  @ApiPropertyOptional({ description: 'Thời gian kết thúc (Unix timestamp)' })
  @IsOptional()
  @IsNumber()
  end_time?: number;

  @ApiPropertyOptional({ description: 'Offset cho phân trang' })
  @IsOptional()
  @IsNumber()
  offset?: number;

  @ApiPropertyOptional({ description: 'Số lượng đánh giá tối đa trả về' })
  @IsOptional()
  @IsNumber()
  limit?: number;

  @ApiPropertyOptional({
    description: 'Lọc theo mức độ chất lượng',
    enum: ZnsQualityLevel,
  })
  @IsOptional()
  @IsEnum(ZnsQualityLevel)
  quality_level?: ZnsQualityLevel;

  @ApiPropertyOptional({ description: 'Chỉ lấy đánh giá có vi phạm' })
  @IsOptional()
  @IsBoolean()
  violations_only?: boolean;
}
