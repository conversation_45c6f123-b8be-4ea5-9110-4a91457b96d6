import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ZaloAiMessage } from '../entities/zalo-ai-message.entity';

@Injectable()
export class ZaloAiMessageRepository {
  constructor(
    @InjectRepository(ZaloAiMessage)
    private readonly repository: Repository<ZaloAiMessage>,
  ) {}

  /**
   * Save incoming message to database with duplicate prevention
   * @param messageData Message data to save
   * @returns Created or existing message record
   */
  async saveZaloMessage(messageData: Partial<ZaloAiMessage>): Promise<ZaloAiMessage> {
    try {
      // Try to create and save the message directly
      const message = this.repository.create(messageData);
      return await this.repository.save(message);
    } catch (error) {
      // Handle duplicate key constraint violation (race condition)
      if (error.code === '23505' || error.message?.includes('duplicate key')) {
        // Duplicate detected, fetch and return the existing message
        if (messageData.messageId && messageData.threadId) {
          const existingMessage = await this.repository.findOne({
            where: {
              messageId: messageData.messageId,
              threadId: messageData.threadId
            }
          });

          if (existingMessage) {
            return existingMessage;
          }
        }
      }

      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Find message by ID
   * @param id Message ID
   * @returns Message record or null
   */
  async findById(id: string): Promise<ZaloAiMessage | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Find message by thread ID and Zalo message ID
   * @param threadId Thread ID
   * @param zaloMessageId Zalo message ID
   * @returns Message record or null
   */
  async findByThreadIdAndZaloMessageId(threadId: string, zaloMessageId: string): Promise<ZaloAiMessage | null> {
    return this.repository.findOne({ where: { threadId, messageId: zaloMessageId } });
  }

  /**
   * Find message by external Zalo message ID within a specific thread
   * @param messageId External Zalo message ID
   * @param threadId Thread ID to scope the search (prevents ID conflicts across different interactions)
   * @returns Message record or null
   */
  async findByMessageId(messageId: string, threadId: string): Promise<ZaloAiMessage | null> {
    return this.repository.findOne({ where: { messageId, threadId } });
  }
}
