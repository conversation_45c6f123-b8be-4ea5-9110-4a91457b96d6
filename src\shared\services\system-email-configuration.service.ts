import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SystemConfiguration } from '../../modules/system-configuration/entities/system-configuration.entity';
import { Integration } from '../entities/integration.entity';
import { IntegrationProvider } from '../entities/integration-provider.entity';
import { KeyPairEncryptionService } from './encryption/key-pair-encryption.service';

/**
 * Interface cho cấu hình email server
 */
export interface EmailServerConfig {
  host: string;
  port: number;
  username: string;
  password: string;
  useSsl?: boolean;
  serverName?: string;
}

/**
 * Service để lấy cấu hình email từ system configuration
 */
@Injectable()
export class SystemEmailConfigurationService {
  private readonly logger = new Logger(SystemEmailConfigurationService.name);

  constructor(
    @InjectRepository(SystemConfiguration)
    private readonly systemConfigRepository: Repository<SystemConfiguration>,
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
    @InjectRepository(IntegrationProvider)
    private readonly integrationProviderRepository: Repository<IntegrationProvider>,
    private readonly keyPairEncryptionService: KeyPairEncryptionService,
  ) {}

  /**
   * Lấy cấu hình email server từ system configuration
   * @returns Cấu hình email server hoặc null nếu không tìm thấy
   */
  async getEmailServerConfig(): Promise<EmailServerConfig | null> {
    try {
      // Lấy system configuration đang active
      const systemConfig = await this.systemConfigRepository.findOne({
        where: { active: true },
      });

      if (!systemConfig || !systemConfig.emailNotificationSystemId) {
        this.logger.warn(
          'Không tìm thấy system configuration hoặc emailNotificationSystemId',
        );
        return null;
      }

      // Lấy integration theo UUID
      const integration = await this.integrationRepository
        .createQueryBuilder('integration')
        .leftJoin(
          'integration_providers',
          'provider',
          'provider.id = integration.type_id',
        )
        .where('integration.id = :integrationId', {
          integrationId: systemConfig.emailNotificationSystemId,
        })
        .andWhere('provider.type = :providerType', {
          providerType: 'EMAIL_SMTP',
        })
        .andWhere('integration.owned_type = :ownedType', { ownedType: 'ADMIN' })
        .select([
          'integration.id',
          'integration.integrationName',
          'integration.encryptedConfig',
          'integration.secretKey',
          'integration.metadata',
        ])
        .getOne();

      if (!integration) {
        this.logger.warn(
          `Không tìm thấy integration với ID: ${systemConfig.emailNotificationSystemId}`,
        );
        return null;
      }

      // Giải mã cấu hình email
      return this.decryptEmailConfig(integration);
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy cấu hình email server: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * Giải mã cấu hình email từ integration
   */
  private decryptEmailConfig(
    integration: Integration,
  ): EmailServerConfig | null {
    try {
      const metadata = integration.metadata || {};
      let encryptedConfig: any = {};

      // Giải mã encryptedConfig nếu có
      if (integration.encryptedConfig && integration.secretKey) {
        try {
          console.log('Giải mã encryptedConfig:', integration);
          const decryptedData = this.keyPairEncryptionService.decrypt(
            integration.encryptedConfig,
            integration.secretKey,
          );
          encryptedConfig = JSON.parse(decryptedData.decryptedData);
        } catch (error) {
          this.logger.warn(
            `Không thể giải mã encryptedConfig cho integration ${integration.id}: ${error.message}`,
          );
          encryptedConfig = {};
        }
      }

      // Tạo cấu hình email server
      const emailConfig: EmailServerConfig = {
        host: metadata.host || '',
        port: metadata.port || 587,
        username: encryptedConfig.username || '',
        password: encryptedConfig.password || '',
        useSsl: metadata.useSsl || false,
        serverName:
          metadata.serverName ||
          integration.integrationName ||
          'Default Email Server',
      };

      // Kiểm tra các trường bắt buộc
      if (!emailConfig.host || !emailConfig.username || !emailConfig.password) {
        this.logger.warn('Cấu hình email không đầy đủ thông tin bắt buộc');
        return null;
      }

      return emailConfig;
    } catch (error) {
      this.logger.error(
        `Lỗi khi giải mã cấu hình email: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }
}
