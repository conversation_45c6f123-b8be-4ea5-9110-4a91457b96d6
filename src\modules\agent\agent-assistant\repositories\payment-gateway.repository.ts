import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PaymentGateway } from '../entities/payment-gateway.entity';

@Injectable()
export class PaymentGatewayRepository {
  constructor(
    @InjectRepository(PaymentGateway)
    private readonly repository: Repository<PaymentGateway>,
  ) {}

  async findById(id: string): Promise<PaymentGateway | null> {
    return this.repository.findOne({
      where: { id }
    });
  }
}
