/**
 * @file Interface cho Wait node
 * 
 * Định nghĩa type-safe interface cho node Wait bao gồm:
 * - Parameters structure với validation
 * - Properties definition
 * - Input/Output data types
 * - 2 wait types: Duration và Until Time
 * - Advanced timing features
 */

import {
    IBaseNodeInput,
    IBaseNodeOutput,
    ITypedNodeExecution
} from '../../execute.interface';

// =================================================================
// SECTION 1: ENUMS & TYPES
// Định nghĩa các enum và type cho Wait node
// =================================================================

/**
 * Wait types - các loại wait được hỗ trợ
 */
export enum EWaitType {
    /** Chờ một khoảng thời gian */
    DURATION = 'duration',
    
    /** Chờ đến thời điểm cụ thể */
    UNTIL_TIME = 'until_time'
}

/**
 * Duration types
 */
export enum EDurationType {
    /** Fixed duration */
    STATIC = 'static',
    
    /** Duration từ input data */
    DYNAMIC = 'dynamic'
}

/**
 * Until time types
 */
export enum EUntilType {
    /** Fixed datetime */
    STATIC = 'static',
    
    /** Datetime từ input data */
    DYNAMIC = 'dynamic'
}

/**
 * Time units cho duration
 */
export enum ETimeUnit {
    MILLISECONDS = 'milliseconds',
    SECONDS = 'seconds',
    MINUTES = 'minutes',
    HOURS = 'hours',
    DAYS = 'days'
}

/**
 * Timeout behavior khi wait time > max_wait_time
 */
export enum ETimeoutBehavior {
    /** Throw error */
    ERROR = 'error',
    
    /** Truncate wait time to max */
    TRUNCATE = 'truncate',
    
    /** Skip wait entirely */
    SKIP = 'skip'
}

/**
 * Past time behavior khi target time đã qua
 */
export enum EPastTimeBehavior {
    /** Skip wait, continue immediately */
    SKIP = 'skip',
    
    /** Throw error */
    ERROR = 'error',
    
    /** Wait minimum duration */
    WAIT_MINIMUM = 'wait_minimum'
}

/**
 * Date formats cho parsing
 */
export enum EDateFormat {
    ISO8601 = 'ISO8601',
    UNIX_TIMESTAMP = 'unix_timestamp',
    CUSTOM = 'custom'
}

// =================================================================
// SECTION 2: CONFIGURATION STRUCTURES
// =================================================================

/**
 * Configuration cho duration wait
 */
export interface IDurationConfig {
    /** Duration type */
    type: EDurationType;
    
    /** Static duration value */
    value?: number;
    
    /** Field name cho dynamic duration */
    field?: string;
    
    /** Time unit */
    unit: ETimeUnit;
    
    /** Default value nếu dynamic field không có */
    default_value?: number;
    
    /** Minimum duration (milliseconds) */
    min_duration?: number;
    
    /** Maximum duration (milliseconds) */
    max_duration?: number;
}

/**
 * Configuration cho until time wait
 */
export interface IUntilConfig {
    /** Until type */
    type: EUntilType;
    
    /** Static datetime string */
    datetime?: string;
    
    /** Field name cho dynamic datetime */
    field?: string;
    
    /** Date format */
    format: EDateFormat;
    
    /** Custom date format pattern (cho CUSTOM format) */
    custom_format?: string;
    
    /** Timezone */
    timezone?: string;
    
    /** Default datetime nếu dynamic field không có */
    default_datetime?: string;
}

/**
 * Wait progress information
 */
export interface IWaitProgress {
    /** Current time */
    current_time: number;
    
    /** Target time */
    target_time: number;
    
    /** Elapsed time (milliseconds) */
    elapsed_time: number;
    
    /** Remaining time (milliseconds) */
    remaining_time: number;
    
    /** Progress percentage (0-100) */
    progress_percentage: number;
    
    /** Wait status */
    status: 'waiting' | 'completed' | 'cancelled' | 'error';
}

// =================================================================
// SECTION 3: PARAMETERS INTERFACE
// =================================================================

/**
 * Interface cho parameters của Wait node
 */
export interface IWaitParameters {
    /** Wait type */
    wait_type: EWaitType;
    
    /** Duration configuration (cho DURATION type) */
    duration_config?: IDurationConfig;
    
    /** Until time configuration (cho UNTIL_TIME type) */
    until_config?: IUntilConfig;
    
    /** Maximum wait time (milliseconds) */
    max_wait_time?: number;
    
    /** Timeout behavior */
    timeout_behavior: ETimeoutBehavior;
    
    /** Past time behavior */
    past_time_behavior: EPastTimeBehavior;
    
    /** Minimum wait duration cho WAIT_MINIMUM behavior (milliseconds) */
    minimum_wait_duration?: number;
    
    /** Có emit progress events không */
    emit_progress?: boolean;
    
    /** Progress interval (milliseconds) */
    progress_interval?: number;
    
    /** Có allow cancellation không */
    allow_cancellation?: boolean;
    
    /** Custom variables có thể sử dụng trong calculations */
    variables?: Record<string, any>;
}

// =================================================================
// SECTION 4: INPUT/OUTPUT INTERFACES
// =================================================================

/**
 * Interface cho input data của Wait node
 */
export interface IWaitInput extends IBaseNodeInput {
    /** Data chứa dynamic duration/datetime values */
    data: Record<string, any>;
    
    /** Context variables */
    variables?: Record<string, any>;
    
    /** Override wait configuration */
    wait_override?: {
        duration?: number;
        until_time?: string;
        skip_wait?: boolean;
    };
}

/**
 * Interface cho output data của Wait node
 */
export interface IWaitOutput extends IBaseNodeOutput {
    /** Original data passed through */
    data: Record<string, any>;
    
    /** Wait execution metadata */
    wait_metadata: {
        /** Wait type đã sử dụng */
        wait_type: EWaitType;
        
        /** Planned wait time (milliseconds) */
        planned_wait_time: number;
        
        /** Actual wait time (milliseconds) */
        actual_wait_time: number;
        
        /** Target time (timestamp) */
        target_time: number;
        
        /** Start time (timestamp) */
        start_time: number;
        
        /** Completion time (timestamp) */
        completed_at: number;
        
        /** Wait status */
        status: 'completed' | 'skipped' | 'truncated' | 'cancelled' | 'error';
        
        /** Reason for status */
        status_reason?: string;
        
        /** Có bị timeout không */
        timed_out: boolean;
        
        /** Có bị cancel không */
        cancelled: boolean;
        
        /** Progress snapshots (nếu emit_progress = true) */
        progress_snapshots?: IWaitProgress[];
    };
}

// =================================================================
// SECTION 5: PROPERTIES DEFINITION
// =================================================================

/**
 * Properties definition cho Wait node
 */
// ❌ REMOVED: Properties constants moved to database
// ✅ Use NodeDefinitionService.findByTypeName() to load properties

// =================================================================
// SECTION 6: TYPE ALIASES - USING EXISTING ENTITIES
// Sử dụng entities có sẵn thay vì duplicate interfaces
// =================================================================

/**
 * Type alias cho Wait node definition
 * ✅ Sử dụng entity có sẵn thay vì duplicate interface
 * Xem: src/modules/workflow/entities/node-definition.entity.ts
 */
export type IWaitNodeDefinition = {
    id: string;
    type_name: string;
    version: number;
    display_name: string;
    description?: string;
    group_name: string;
    icon?: string;
    properties: any[]; // Dynamic properties từ database
    inputs?: string[];
    outputs?: string[];
    credentials?: any[];
    created_at: number;
    updated_at: number;
};

/**
 * Type alias cho Wait node instance
 * ✅ Sử dụng entity có sẵn với type-safe parameters
 * Xem: src/modules/workflow/interfaces/workflow.interface.ts
 */
export type IWaitNodeInstance = {
    id: string;
    workflow_id: string;
    name: string;
    type: string;
    type_version: string;
    position: { x: number; y: number };
    parameters: IWaitParameters; // Type-safe parameters
    disabled?: boolean;
    notes?: string;
    retry_on_fail?: boolean;
    max_tries?: number;
    wait_between_tries?: number;
    on_error?: string;
    integration_id?: string;
    node_definition_id: string;
};

/**
 * Type-safe node execution cho Wait
 */
export type IWaitNodeExecution = ITypedNodeExecution<
    IWaitInput,
    IWaitOutput,
    IWaitParameters
>;

// =================================================================
// SECTION 7: HELPER FUNCTIONS
// =================================================================

/**
 * Helper function để convert time unit to milliseconds
 */
export function convertToMilliseconds(value: number, unit: ETimeUnit): number {
    switch (unit) {
        case ETimeUnit.MILLISECONDS:
            return value;
        case ETimeUnit.SECONDS:
            return value * 1000;
        case ETimeUnit.MINUTES:
            return value * 60 * 1000;
        case ETimeUnit.HOURS:
            return value * 60 * 60 * 1000;
        case ETimeUnit.DAYS:
            return value * 24 * 60 * 60 * 1000;
        default:
            return value;
    }
}

/**
 * Helper function để parse datetime string
 */
export function parseDateTime(
    dateTimeStr: string,
    format: EDateFormat,
    customFormat?: string,
    timezone?: string
): number {
    switch (format) {
        case EDateFormat.ISO8601:
            return new Date(dateTimeStr).getTime();

        case EDateFormat.UNIX_TIMESTAMP:
            const timestamp = parseInt(dateTimeStr);
            return timestamp < 10000000000 ? timestamp * 1000 : timestamp; // Handle seconds vs milliseconds

        case EDateFormat.CUSTOM:
            // Implementation would depend on date parsing library
            // For now, fallback to ISO8601
            return new Date(dateTimeStr).getTime();

        default:
            return new Date(dateTimeStr).getTime();
    }
}

/**
 * Helper function để calculate duration từ config và input
 */
export function calculateDuration(config: IDurationConfig, inputData: any): number {
    let durationValue: number;

    switch (config.type) {
        case EDurationType.STATIC:
            durationValue = config.value || 0;
            break;

        case EDurationType.DYNAMIC:
            const fieldValue = getNestedValue(inputData, config.field || '');
            durationValue = fieldValue !== undefined ? fieldValue : (config.default_value || 0);
            break;

        default:
            durationValue = 0;
    }

    // Convert to milliseconds
    let durationMs = convertToMilliseconds(durationValue, config.unit);

    // Apply min/max constraints
    if (config.min_duration !== undefined) {
        durationMs = Math.max(durationMs, config.min_duration);
    }

    if (config.max_duration !== undefined) {
        durationMs = Math.min(durationMs, config.max_duration);
    }

    return durationMs;
}

/**
 * Helper function để calculate target time từ config và input
 */
export function calculateTargetTime(config: IUntilConfig, inputData: any): number {
    let dateTimeStr: string;

    switch (config.type) {
        case EUntilType.STATIC:
            dateTimeStr = config.datetime || '';
            break;

        case EUntilType.DYNAMIC:
            const fieldValue = getNestedValue(inputData, config.field || '');
            dateTimeStr = fieldValue !== undefined ? fieldValue : (config.default_datetime || '');
            break;

        default:
            dateTimeStr = '';
    }

    if (!dateTimeStr) {
        throw new Error('No datetime value provided');
    }

    return parseDateTime(dateTimeStr, config.format, config.custom_format, config.timezone);
}

/**
 * Helper function để get nested value từ object
 */
function getNestedValue(obj: any, path: string): any {
    if (!path) return undefined;
    return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * Helper function để create progress snapshot
 */
export function createProgressSnapshot(
    startTime: number,
    targetTime: number,
    currentTime: number = Date.now()
): IWaitProgress {
    const elapsedTime = currentTime - startTime;
    const totalTime = targetTime - startTime;
    const remainingTime = Math.max(0, targetTime - currentTime);
    const progressPercentage = totalTime > 0 ? Math.min(100, (elapsedTime / totalTime) * 100) : 100;

    return {
        current_time: currentTime,
        target_time: targetTime,
        elapsed_time: elapsedTime,
        remaining_time: remainingTime,
        progress_percentage: progressPercentage,
        status: remainingTime > 0 ? 'waiting' : 'completed'
    };
}

/**
 * Helper function để validate wait parameters
 */
export function validateWaitParameters(params: Partial<IWaitParameters>): {
    isValid: boolean;
    errors: string[];
} {
    const errors: string[] = [];

    if (!params.wait_type) {
        errors.push('Wait type is required');
    }

    switch (params.wait_type) {
        case EWaitType.DURATION:
            if (!params.duration_config) {
                errors.push('Duration configuration is required for DURATION wait type');
            } else {
                const config = params.duration_config;

                if (config.type === EDurationType.STATIC && (config.value === undefined || config.value < 0)) {
                    errors.push('Static duration value must be >= 0');
                }

                if (config.type === EDurationType.DYNAMIC && !config.field) {
                    errors.push('Duration field is required for dynamic duration');
                }

                if (config.min_duration !== undefined && config.max_duration !== undefined) {
                    if (config.min_duration > config.max_duration) {
                        errors.push('Min duration must be <= max duration');
                    }
                }
            }
            break;

        case EWaitType.UNTIL_TIME:
            if (!params.until_config) {
                errors.push('Until configuration is required for UNTIL_TIME wait type');
            } else {
                const config = params.until_config;

                if (config.type === EUntilType.STATIC && !config.datetime) {
                    errors.push('Static datetime is required for static until time');
                }

                if (config.type === EUntilType.DYNAMIC && !config.field) {
                    errors.push('DateTime field is required for dynamic until time');
                }

                if (config.format === EDateFormat.CUSTOM && !config.custom_format) {
                    errors.push('Custom format is required when using CUSTOM date format');
                }
            }
            break;
    }

    if (params.max_wait_time !== undefined && params.max_wait_time < 0) {
        errors.push('Max wait time must be >= 0');
    }

    if (params.minimum_wait_duration !== undefined && params.minimum_wait_duration < 0) {
        errors.push('Minimum wait duration must be >= 0');
    }

    if (params.progress_interval !== undefined && params.progress_interval < 100) {
        errors.push('Progress interval must be >= 100ms');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

// =================================================================
// SECTION 8: FACTORY & VALIDATION
// =================================================================

/**
 * Factory function để tạo Wait node definition
 * ❌ DEPRECATED: Properties hiện tại được load từ database
 * ✅ Sử dụng NodeDefinitionService.findByTypeName('wait') thay thế
 *
 * @deprecated Chỉ dùng cho testing hoặc seeding data
 */
export function createWaitNodeDefinition(): Omit<IWaitNodeDefinition, 'properties'> {
    return {
        id: 'wait-v1',
        type_name: 'wait',
        version: 1,
        display_name: 'Wait',
        description: 'Wait for specified duration hoặc until specific time',
        group_name: 'Utility',
        icon: 'clock',
        inputs: ['main'],
        outputs: ['main', 'error'],
        // properties: Load từ database qua NodeDefinitionService
        created_at: Date.now(),
        updated_at: Date.now()
    };
}
