import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { google, gmail_v1, Auth } from 'googleapis';
import { AppException } from '@common/exceptions';
import { GOOGLE_ERROR_CODES, handleGoogleApiError } from '../exceptions/google.exception';
import {
  GoogleGmailConfig,
  GmailTokens,
  GmailUserInfo,
  GmailMessage,
  GmailSendResult,
  GmailSearchOptions,
  GmailSearchResult,
  GmailMessageInfo,
  GMAIL_SCOPES,
} from '../interfaces/google-gmail.interface';

/**
 * Service xử lý Gmail API operations
 */
@Injectable()
export class GoogleGmailApiService {
  private readonly logger = new Logger(GoogleGmailApiService.name);
  private oauth2Client: Auth.OAuth2Client;
  private gmailConfig: GoogleGmailConfig;

  constructor(private readonly configService: ConfigService) {
    this.initializeConfig();
    this.initializeOAuth2Client();
  }

  /**
   * Khởi tạo cấu hình từ environment variables
   */
  private initializeConfig(): void {
    this.gmailConfig = {
      clientId: this.configService.get<string>('GOOGLE_CLIENT_ID') || '',
      clientSecret: this.configService.get<string>('GOOGLE_CLIENT_SECRET') || '',
      redirectUri: this.configService.get<string>('GOOGLE_REDIRECT_URI') || '',
    };

    if (!this.gmailConfig.clientId || !this.gmailConfig.clientSecret) {
      this.logger.warn('Gmail configuration is incomplete');
    }
  }

  /**
   * Khởi tạo OAuth2 client
   */
  private initializeOAuth2Client(): void {
    this.oauth2Client = new google.auth.OAuth2(
      this.gmailConfig.clientId,
      this.gmailConfig.clientSecret,
      this.gmailConfig.redirectUri,
    );
  }


  /**
   * Thiết lập credentials cho OAuth2 client
   * @param tokens Gmail tokens
   */
  setCredentials(tokens: GmailTokens): void {
    this.oauth2Client.setCredentials({
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token,
      expiry_date: tokens.expiry_date,
      token_type: tokens.token_type,
      scope: tokens.scope,
    });
  }

  /**
   * Lấy Gmail API instance
   * @param accessToken Access token (optional)
   * @returns Gmail API instance
   */
  private getGmailInstance(accessToken?: string): gmail_v1.Gmail {
    if (accessToken) {
      // Use access token directly
      return google.gmail({ version: 'v1', auth: accessToken });
    }
    // Use OAuth2Client
    return google.gmail({ version: 'v1', auth: this.oauth2Client });
  }

  /**
   * Thiết lập access token từ ngoài vào
   * @param accessToken Access token
   */
  setAccessToken(accessToken: string): void {
    this.oauth2Client.setCredentials({
      access_token: accessToken,
    });
  }

  /**
   * Tạo URL xác thực OAuth2
   * @param scopes Danh sách quyền cần xin
   * @param state State token để bảo mật
   * @returns URL xác thực
   */
  generateAuthUrl(scopes: string[], state?: string): string {
    try {
      return this.oauth2Client.generateAuthUrl({
        access_type: 'offline',
        scope: scopes,
        state,
        prompt: 'consent', // Force consent screen to get refresh token
      });
    } catch (error) {
      this.logger.error(`Error generating auth URL: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        GOOGLE_ERROR_CODES.GOOGLE_API_CONFIGURATION_ERROR,
        'Không thể tạo URL xác thực Gmail',
      );
    }
  }

  /**
   * Lấy tokens từ authorization code
   * @param code Authorization code từ OAuth callback
   * @returns Tokens
   */
  async getTokensFromCode(code: string): Promise<GmailTokens> {
    try {
      const { tokens } = await this.oauth2Client.getToken(code);
      
      this.logger.log('Successfully obtained tokens from authorization code');
      
      return {
        access_token: tokens.access_token || '',
        refresh_token: tokens.refresh_token ?? undefined,
        expiry_date: tokens.expiry_date ?? undefined,
        token_type: tokens.token_type ?? undefined,
        scope: tokens.scope ?? undefined,
      };
    } catch (error) {
      this.logger.error(`Error getting tokens from code: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      const errorCode = handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN);
      throw new AppException(
        errorCode,
        'Không thể lấy tokens từ authorization code',
      );
    }
  }

  /**
   * Refresh access token
   * @param refreshToken Refresh token
   * @returns New tokens
   */
  async refreshAccessToken(refreshToken: string): Promise<GmailTokens> {
    try {
      this.oauth2Client.setCredentials({ refresh_token: refreshToken });
      const { credentials } = await this.oauth2Client.refreshAccessToken();
      
      this.logger.log('Successfully refreshed access token');
      
      return {
        access_token: credentials.access_token || '',
        refresh_token: credentials.refresh_token ?? refreshToken,
        expiry_date: credentials.expiry_date ?? undefined,
        token_type: credentials.token_type ?? undefined,
        scope: credentials.scope ?? undefined,
      };
    } catch (error) {
      this.logger.error(`Error refreshing access token: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      const errorCode = handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN);
      throw new AppException(
        errorCode,
        'Không thể refresh access token',
      );
    }
  }

  /**
   * Lấy thông tin user từ Google
   * @param accessToken Access token (optional)
   * @returns User info
   */
  async getUserInfo(accessToken?: string): Promise<GmailUserInfo> {
    try {
      const auth = accessToken || this.oauth2Client;
      const oauth2 = google.oauth2({ version: 'v2', auth });
      const { data } = await oauth2.userinfo.get();

      this.logger.log(`Retrieved user info for: ${data.email}`);

      return {
        email: data.email || '',
        name: data.name || undefined,
        picture: data.picture || undefined,
        verified_email: data.verified_email || false,
        id: data.id || undefined,
        locale: data.locale || undefined,
      };
    } catch (error) {
      this.logger.error(`Error getting user info: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      const errorCode = handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN);
      throw new AppException(
        errorCode,
        'Không thể lấy thông tin user',
      );
    }
  }

  /**
   * Gửi email qua Gmail API
   * @param message Thông tin email
   * @param accessToken Access token (optional)
   * @returns Kết quả gửi email
   */
  async sendEmail(message: GmailMessage, accessToken?: string): Promise<GmailSendResult> {
    try {
      const gmail = this.getGmailInstance(accessToken);

      const emailContent = this.buildEmailContent(message);
      const encodedEmail = Buffer.from(emailContent)
        .toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '');

      const response = await gmail.users.messages.send({
        userId: 'me',
        requestBody: {
          raw: encodedEmail,
        },
      });

      const result: GmailSendResult = {
        messageId: response.data.id || '',
        threadId: response.data.threadId || '',
        success: true,
        metadata: {
          sentAt: new Date(),
          size: emailContent.length,
          labelIds: response.data.labelIds ?? undefined,
        },
      };

      this.logger.log(`Email sent successfully to ${message.to}, messageId: ${result.messageId}`);

      return result;
    } catch (error) {
      this.logger.error(`Error sending email: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      const errorCode = handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_API_UNKNOWN_ERROR);
      throw new AppException(
        errorCode,
        `Không thể gửi email đến ${message.to}`,
      );
    }
  }

  /**
   * Lấy danh sách email
   * @param options Search options
   * @param accessToken Access token (optional)
   * @returns Danh sách emails
   */
  async listEmails(options: GmailSearchOptions = {}, accessToken?: string): Promise<GmailSearchResult> {
    try {
      const gmail = this.getGmailInstance(accessToken);

      const response = await gmail.users.messages.list({
        userId: 'me',
        q: options.query,
        maxResults: options.maxResults || 10,
        pageToken: options.pageToken,
        labelIds: options.labelIds,
        includeSpamTrash: options.includeSpamTrash || false,
      });

      const messages: GmailMessageInfo[] = (response.data.messages || []).map(msg => ({
        id: msg.id || '',
        threadId: msg.threadId || '',
        labelIds: msg.labelIds ?? undefined,
        snippet: msg.snippet ?? undefined,
        historyId: msg.historyId ?? undefined,
        internalDate: msg.internalDate ?? undefined,
        sizeEstimate: msg.sizeEstimate ?? undefined,
      }));

      this.logger.log(`Retrieved ${messages.length} emails`);

      return {
        messages,
        nextPageToken: response.data.nextPageToken || undefined,
        resultSizeEstimate: response.data.resultSizeEstimate || 0,
      };
    } catch (error) {
      this.logger.error(`Error listing emails: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      const errorCode = handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_API_UNKNOWN_ERROR);
      throw new AppException(
        errorCode,
        'Không thể lấy danh sách email',
      );
    }
  }

  /**
   * Lấy chi tiết email
   * @param messageId ID của email
   * @param accessToken Access token (optional)
   * @returns Chi tiết email
   */
  async getEmail(messageId: string, accessToken?: string): Promise<GmailMessageInfo> {
    try {
      const gmail = this.getGmailInstance(accessToken);

      const response = await gmail.users.messages.get({
        userId: 'me',
        id: messageId,
      });

      const message = response.data;

      return {
        id: message.id || '',
        threadId: message.threadId || '',
        labelIds: message.labelIds ?? undefined,
        snippet: message.snippet ?? undefined,
        historyId: message.historyId ?? undefined,
        internalDate: message.internalDate ?? undefined,
        sizeEstimate: message.sizeEstimate ?? undefined,
        payload: message.payload ? {
          partId: message.payload.partId ?? undefined,
          mimeType: message.payload.mimeType ?? undefined,
          filename: message.payload.filename ?? undefined,
          headers: message.payload.headers?.map(header => ({
            name: header.name || '',
            value: header.value || '',
          })),
          body: message.payload.body ? {
            attachmentId: message.payload.body.attachmentId ?? undefined,
            size: message.payload.body.size ?? undefined,
            data: message.payload.body.data ?? undefined,
          } : undefined,
          parts: message.payload.parts?.map(part => ({
            partId: part.partId ?? undefined,
            mimeType: part.mimeType ?? undefined,
            filename: part.filename ?? undefined,
            headers: part.headers?.map(header => ({
              name: header.name || '',
              value: header.value || '',
            })),
            body: part.body ? {
              attachmentId: part.body.attachmentId ?? undefined,
              size: part.body.size ?? undefined,
              data: part.body.data ?? undefined,
            } : undefined,
          })),
        } : undefined,
        raw: message.raw ?? undefined,
      };
    } catch (error) {
      this.logger.error(`Error getting email: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      if (error.response?.status === 404) {
        throw new AppException(
          GOOGLE_ERROR_CODES.GOOGLE_API_UNKNOWN_ERROR,
          'Không tìm thấy email',
        );
      }

      const errorCode = handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_API_UNKNOWN_ERROR);
      throw new AppException(
        errorCode,
        'Không thể lấy chi tiết email',
      );
    }
  }

  /**
   * Xây dựng nội dung email
   * @param message Thông tin email
   * @returns Email content string
   */
  private buildEmailContent(message: GmailMessage): string {
    const lines: string[] = [];
    
    // Headers
    lines.push(`To: ${message.to}`);
    if (message.cc && message.cc.length > 0) {
      lines.push(`Cc: ${message.cc.join(', ')}`);
    }
    if (message.bcc && message.bcc.length > 0) {
      lines.push(`Bcc: ${message.bcc.join(', ')}`);
    }
    if (message.replyTo) {
      lines.push(`Reply-To: ${message.replyTo}`);
    }
    lines.push(`Subject: ${message.subject}`);
    
    // Priority
    if (message.priority && message.priority !== 'normal') {
      const priorityValue = message.priority === 'high' ? '1' : '5';
      lines.push(`X-Priority: ${priorityValue}`);
    }

    // Custom headers
    if (message.headers) {
      Object.entries(message.headers).forEach(([key, value]) => {
        lines.push(`${key}: ${value}`);
      });
    }
    
    // Content type
    if (message.isHtml) {
      lines.push('Content-Type: text/html; charset=utf-8');
    } else {
      lines.push('Content-Type: text/plain; charset=utf-8');
    }
    
    lines.push(''); // Empty line between headers and body
    lines.push(message.body);
    
    return lines.join('\n');
  }

  /**
   * Kiểm tra kết nối Gmail
   * @param accessToken Access token (optional)
   * @returns True nếu kết nối thành công
   */
  async testConnection(accessToken?: string): Promise<boolean> {
    try {
      const gmail = this.getGmailInstance(accessToken);
      await gmail.users.getProfile({ userId: 'me' });

      this.logger.log('Gmail connection test successful');
      return true;
    } catch (error) {
      this.logger.error(`Gmail connection test failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Lấy Gmail API instance công khai
   * @param accessToken Access token (optional)
   * @returns Gmail API instance
   */
  getGmailApi(accessToken?: string): gmail_v1.Gmail {
    return this.getGmailInstance(accessToken);
  }

  /**
   * Lấy scopes mặc định cho các use case khác nhau
   */
  static getDefaultScopes(useCase: 'send' | 'read' | 'full' = 'send'): string[] {
    switch (useCase) {
      case 'send':
        return [
          GMAIL_SCOPES.SEND,
          GMAIL_SCOPES.EMAIL,
          GMAIL_SCOPES.PROFILE,
        ];
      case 'read':
        return [
          GMAIL_SCOPES.READONLY,
          GMAIL_SCOPES.EMAIL,
          GMAIL_SCOPES.PROFILE,
        ];
      case 'full':
        return [
          GMAIL_SCOPES.SEND,
          GMAIL_SCOPES.READONLY,
          GMAIL_SCOPES.MODIFY,
          GMAIL_SCOPES.COMPOSE,
          GMAIL_SCOPES.LABELS,
          GMAIL_SCOPES.EMAIL,
          GMAIL_SCOPES.PROFILE,
        ];
      default:
        return [
          GMAIL_SCOPES.SEND,
          GMAIL_SCOPES.EMAIL,
          GMAIL_SCOPES.PROFILE,
        ];
    }
  }
}
