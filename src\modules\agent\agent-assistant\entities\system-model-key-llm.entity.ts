import { Entity, PrimaryColumn } from 'typeorm';

/**
 * SystemModelKeyLlm entity
 * Junction table linking system models to system API keys (many-to-many)
 */
@Entity('system_model_key_llm')
export class SystemModelKeyLlm {
  /**
   * System model ID reference to system_models
   */
  @PrimaryColumn({ name: 'model_id', type: 'uuid' })
  modelId: string;

  /**
   * System LLM key ID reference to system_key_llm
   */
  @PrimaryColumn({ name: 'llm_key_id', type: 'uuid' })
  llmKeyId: string;
}
