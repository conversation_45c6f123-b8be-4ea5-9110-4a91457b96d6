import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { ProviderEnum } from '../enums/model-capabilities.enum';

/**
 * SystemKeyLlm entity
 * Stores system LLM API keys for load balancing
 */
@Entity('system_key_llm')
export class SystemKeyLlm {
  /**
   * UUID unique identifier for system key
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Identifier name for the key
   */
  @Column({ name: 'name', type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * LLM provider
   */
  @Column({ name: 'provider', type: 'varchar', default: 'OPENAI', nullable: true })
  provider?: ProviderEnum;

  /**
   * Encrypted API key
   */
  @Column({ name: 'api_key', type: 'text', nullable: false })
  apiKey: string;

  /**
   * Creation timestamp
   */
  @Column({ 
    name: 'created_at', 
    type: 'bigint', 
    nullable: true,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint'
  })
  createdAt?: number;

  /**
   * Employee who created this key
   */
  @Column({ name: 'created_by', type: 'int', nullable: true })
  createdBy?: number;

  /**
   * Update timestamp
   */
  @Column({ 
    name: 'updated_at', 
    type: 'bigint', 
    nullable: true,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint'
  })
  updatedAt?: number;

  /**
   * Employee who updated this key
   */
  @Column({ name: 'updated_by', type: 'int', nullable: true })
  updatedBy?: number;

  /**
   * Soft delete timestamp
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt?: number;

  /**
   * Employee who deleted this key
   */
  @Column({ name: 'deleted_by', type: 'bigint', nullable: true })
  deletedBy?: number;
}
