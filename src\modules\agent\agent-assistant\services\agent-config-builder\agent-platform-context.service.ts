import { Injectable, Logger } from '@nestjs/common';
import { ZaloAiMessageRepository } from '../../repositories';
import { PlatformContextEnum } from '../../enums/platform-context.enum';
import { MessageTypeEnum } from '../../enums/message.enum';
import {
  ReplyContext,
  PlatformContext,
  PlatformContextInput
} from '../../interfaces';
import { ZaloMessageContentParserService } from '../zalo-message-content-parser.service';

/**
 * Agent Platform Context Service
 * 
 * Handles platform-specific context and reply context for agent conversations.
 * Responsible for: "What platform context does this agent need?"
 */
@Injectable()
export class AgentPlatformContextService {
  private readonly logger = new Logger(AgentPlatformContextService.name);

  constructor(
    private readonly zaloAiMessageRepository: ZaloAiMessageRepository,
    private readonly messageContentParser: ZaloMessageContentParserService,
  ) {}

  /**
   * Get platform context (reply context is handled separately)
   */
  async getPlatformContext(input: PlatformContextInput): Promise<PlatformContext> {
    // Create clean ZaloOfficialAccount without redundant fields
    const cleanZaloOA = input.zaloOfficialAccount ? {
      id: input.zaloOfficialAccount.id,
      oaId: input.zaloOfficialAccount.oaId,
      name: input.zaloOfficialAccount.name,
      description: input.zaloOfficialAccount.description,
      avatarUrl: input.zaloOfficialAccount.avatarUrl,
      accessToken: input.zaloOfficialAccount.accessToken,
      refreshToken: input.zaloOfficialAccount.refreshToken,
      expiresAt: input.zaloOfficialAccount.expiresAt,
      status: input.zaloOfficialAccount.status,
      createdAt: input.zaloOfficialAccount.createdAt,
      updatedAt: input.zaloOfficialAccount.updatedAt,
      // Exclude redundant userId and agentId fields
    } : undefined;

    return {
      zaloOfficialAccount: cleanZaloOA,
      zaloCustomerId: input.zaloCustomerId,
      platformContext: input.platform || PlatformContextEnum.ZALO,
      // replyToContext is handled separately in agent config builder
      replyToContext: undefined,
    };
  }

  /**
   * Get reply context for a message using external Zalo message ID
   * @param threadId Thread ID to scope the search
   * @param replyToMessageId External Zalo message ID to find
   */
  async getReplyContext(threadId: string, replyToMessageId?: string): Promise<ReplyContext | undefined> {
    try {
      if (!replyToMessageId) {
        this.logger.debug(`No reply message ID provided for thread ${threadId}`);
        return undefined;
      }

      // Validate input parameters
      if (!threadId || threadId.trim().length === 0) {
        this.logger.error(`Invalid thread ID provided: ${threadId}`);
        return undefined;
      }

      if (replyToMessageId.trim().length === 0) {
        this.logger.warn(`Empty reply message ID provided for thread ${threadId}`);
        return undefined;
      }

      this.logger.debug(`Looking for reply message ${replyToMessageId} in thread ${threadId}`);

      // Find the original message by external Zalo message ID within the thread
      const originalMessage = await this.zaloAiMessageRepository.findByMessageId(replyToMessageId, threadId);
      if (!originalMessage) {
        this.logger.warn(`Reply message ${replyToMessageId} not found in thread ${threadId}. This could mean:
          1. The message was not saved to our database
          2. The message is from a different thread
          3. The message ID is incorrect`);
        return undefined;
      }

      // Skip sticker messages - we don't want to handle sticker replies
      if (originalMessage.messageType === MessageTypeEnum.STICKER) {
        this.logger.debug(`Skipping reply context for sticker message ${replyToMessageId} - sticker replies not supported`);
        return undefined;
      }

      this.logger.debug(`Found reply message ${originalMessage.id} of type ${originalMessage.messageType} created at ${originalMessage.createdAt}`);

      // Validate message data
      if (!originalMessage.messageType) {
        this.logger.error(`Reply message ${originalMessage.id} has no message type`);
        return this.createFallbackReplyContext(replyToMessageId, originalMessage, 'Missing message type');
      }

      // Parse the message content based on its type
      const parsedContent = await this.messageContentParser.parseMessageContent(originalMessage);
      if (!parsedContent) {
        this.logger.warn(`Failed to parse content for reply message ${originalMessage.id} of type ${originalMessage.messageType}`);
        return this.createFallbackReplyContext(replyToMessageId, originalMessage, 'Content parsing failed');
      }

      this.logger.debug(`Successfully parsed reply message ${originalMessage.id}: "${parsedContent.summary}"`);

      return {
        messageId: replyToMessageId,
        originalMessageData: {
          content: parsedContent.summary, // Use parsed summary for better context
          messageId: originalMessage.messageId || '',
          createdAt: originalMessage.createdAt,
          mediaIds: originalMessage.mediaIds || [],
        },
      };

    } catch (error) {
      this.logger.error(`Unexpected error while getting reply context for message ${replyToMessageId} in thread ${threadId}:`, {
        error: error.message,
        stack: error.stack,
        threadId,
        replyToMessageId,
      });
      return undefined;
    }
  }

  /**
   * Create fallback reply context when parsing fails
   */
  private createFallbackReplyContext(replyToMessageId: string, originalMessage: any, reason: string): ReplyContext {
    this.logger.debug(`Creating fallback reply context for message ${originalMessage.id}: ${reason}`);

    return {
      messageId: replyToMessageId,
      originalMessageData: {
        content: originalMessage.content || `[${originalMessage.messageType || 'unknown'} message]`,
        messageId: originalMessage.messageId || '',
        createdAt: originalMessage.createdAt,
        mediaIds: originalMessage.mediaIds || [],
      },
    };
  }
}
