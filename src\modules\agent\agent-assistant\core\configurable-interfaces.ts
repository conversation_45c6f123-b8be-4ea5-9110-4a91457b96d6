import {
  InputModalityEnum,
  OutputModalityEnum,
  SamplingParameterEnum,
  ModelFeatureEnum,
  ProviderEnum,
  PaymentMethodEnum,
  ProviderShipmentTypeEnum,
  GenderEnum,
  PlatformContextEnum,
  TrimmingTypeEnum,
  ModelTypeEnum,
} from '../enums';

/**
 * Interface corresponding to the GenderEnumSchema.
 */
export type Gender = GenderEnum;

/**
 * Interface corresponding to the PaymentMethodEnumSchema.
 */
export type PaymentMethod = PaymentMethodEnum;

/**
 * Interface corresponding to the ProviderShipmentTypeEnumSchema.
 */
export type ProviderShipmentType = ProviderShipmentTypeEnum;

/**
 * Interface for profile agent data, corresponding to ProfileAgentSchema.
 */
export interface ProfileAgent {
  gender?: GenderEnum;
  dateOfBirth?: string | Date;
  position?: string;
  education?: string;
  skills?: string[];
  personality?: string[];
  languages?: string[];
  nations?: string;
}

/**
 * Interface for trimming types, corresponding to TrimmingTypeSchema.
 */
export type TrimmingType = TrimmingTypeEnum;

/**
 * Interface for model parameters, corresponding to ModelParametersSchema.
 */
export interface ModelParameters {
  temperature?: number;
  topP?: number;
  topK?: number;
  maxTokens?: number;
  maxOutputTokens?: number;
}

/**
 * Interface for model pricing, corresponding to ModelPricingSchema.
 */
export interface ModelPricing {
  inputRate: number;
  outputRate: number;
}

/**
 * Interface for assistant model configuration, corresponding to AssistantModelConfigSchema.
 */
export interface AssistantModelConfig {
  name: string;
  provider: ProviderEnum;
  inputModalities: InputModalityEnum[];
  outputModalities: OutputModalityEnum[];
  samplingParameters: SamplingParameterEnum[];
  features: ModelFeatureEnum[];
  parameters?: ModelParameters;
  pricing: ModelPricing;
  type: ModelTypeEnum;
  apiKeys: string[];
}

/**
 * Interface for trimming configuration, corresponding to TrimmingConfigSchema.
 */
export interface TrimmingConfig {
  type: TrimmingType;
  threshold: number;
}

/**
 * Interface for agent assistant configuration, corresponding to AgentAssistantConfigSchema.
 */
export interface AgentAssistantConfig {
  id: string;
  name: string;
  description?: string;
  instruction?: string;
  mcpConfig: any;
  vectorStoreId?: string | null;
  isSupervisor: boolean;
  profile?: ProfileAgent | null;
  trimmingConfig: TrimmingConfig;
  model: AssistantModelConfig;

  // Agent assistant specific fields for customer-facing agents
  paymentGatewayId?: number | null;
  userProviderShipmentId?: string | null;
  receiverPayShippingFee?: boolean;
  paymentMethods?: PaymentMethodEnum[];
}

/**
 * Interface for strategy content step, corresponding to StrategyContentStepSchema.
 */
export interface StrategyContentStep {
  stepOrder: number;
  content: string;
}

/**
 * Interface for strategist agent configuration, corresponding to AgentStrategistConfigSchema.
 */
export interface AgentStrategistConfig {
  // Core identification
  id: string;
  name: string;
  description: string;

  // Strategy-specific instruction
  instruction: string;

  // Strategy content steps
  content?: StrategyContentStep[];

  // Single example field
  example?: StrategyContentStep[];

  // Model configuration
  model: AssistantModelConfig;

  // Trimming configuration
  trimming: TrimmingConfig;

  // Vector store ID
  vectorStoreId: string | null;
}

/**
 * Interface for agent assistant custom configurable type, corresponding to AgentAssistantCustomConfigurableTypeSchema.
 */
export interface AgentAssistantCustomConfigurable {
  thread_id?: string;
  checkpoint_id?: string;
  userId?: number;
  mainAgent?: AgentAssistantConfig;
  strategistAgent?: AgentStrategistConfig | null;
  attachmentImageMap?: Record<string, string>;
  contextualTools?: any[];

  // Context data fields
  replyToContext?: {
    messageId: string;
    originalMessageData: any;
  };
  attachmentContext?: any[];

  // Agent assistant specific context
  zaloOfficialAccountId?: string;
  zaloCustomerId?: string;
  platformContext?: PlatformContextEnum;
}