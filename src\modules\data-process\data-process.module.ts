import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';

// TODO: Import entities từ app chính khi app module được fix
// import { CustomerProduct } from '../../../../redai-v201-be-app/src/modules/business/entities/customer-product.entity';
// import { CustomField } from '../../../../redai-v201-be-app/src/modules/business/entities/custom-field.entity';

// Import queue constants
import { QueueName } from '../../queue/queue-name.enum';

// Import services and processors
import { DataProcessService } from './data-process.service';
import { DataProcessProcessor } from './data-process.processor';

/**
 * Module xử lý các job data process
 */
@Module({
  imports: [
    // TODO: TypeORM entities khi app module được fix
    // TypeOrmModule.forFeature([
    //   CustomerProduct,
    //   CustomField,
    // ]),
    
    // BullMQ queue
    BullModule.registerQueue({
      name: QueueName.DATA_PROCESS,
    }),
  ],
  providers: [
    DataProcessService,
    DataProcessProcessor,
  ],
  exports: [
    DataProcessService,
  ],
})
export class DataProcessModule {}
