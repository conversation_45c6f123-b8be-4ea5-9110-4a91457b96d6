/**
 * Enum cho loại đối tượng nhận của email campaign
 */
export enum CampaignTargetType {
  /**
   * Gửi cho Admin Audience
   * Sử dụng bảng admin_audience
   */
  ADMIN_AUDIENCE = 'ADMIN_AUDIENCE',

  /**
   * <PERSON><PERSON><PERSON> cho User (từ bảng users)
   * Sử dụng bảng users thông qua segment criteria
   */
  USER = 'USER',

  /**
   * Gửi cho User Audience
   * Sử dụng bảng user_audience của một user cụ thể
   */
  USER_AUDIENCE = 'USER_AUDIENCE',

  /**
   * <PERSON><PERSON><PERSON> cho danh sách email tùy chỉnh
   * Không liên quan đến audience hay user
   */
  CUSTOM_EMAIL_LIST = 'CUSTOM_EMAIL_LIST'
}

/**
 * <PERSON><PERSON> tả chi tiết cho từng loại target
 */
export const CAMPAIGN_TARGET_TYPE_DESCRIPTIONS = {
  [CampaignTargetType.ADMIN_AUDIENCE]: {
    name: 'Admin Audience',
    description: 'G<PERSON>i email đến danh sách audience được quản lý bởi admin',
    table: 'admin_audience',
    useCases: [
      'Gửi email marketing đến khách hàng tiềm năng',
      'Thông báo sản phẩm mới đến audience đã phân loại',
      'Campaign email theo segment audience'
    ]
  },
  [CampaignTargetType.USER]: {
    name: 'User',
    description: 'Gửi email đến user thực tế trong hệ thống thông qua segment criteria',
    table: 'users',
    useCases: [
      'Gửi email đến user VIP (points > 1000)',
      'Thông báo hệ thống đến user active',
      'Campaign theo phân khúc user (type, location, etc.)'
    ]
  },
  [CampaignTargetType.USER_AUDIENCE]: {
    name: 'User Audience',
    description: 'Gửi email đến audience của một user cụ thể',
    table: 'user_audience',
    useCases: [
      'User gửi email marketing đến audience của họ',
      'Thông báo từ user đến khách hàng của họ',
      'Campaign cá nhân của user'
    ]
  },
  [CampaignTargetType.CUSTOM_EMAIL_LIST]: {
    name: 'Custom Email List',
    description: 'Gửi email đến danh sách email được chỉ định trực tiếp',
    table: 'none',
    useCases: [
      'Gửi email đến danh sách email cụ thể',
      'Test campaign với email nội bộ',
      'Gửi email đến partner/vendor'
    ]
  }
} as const;

/**
 * Kiểm tra xem target type có hợp lệ không
 */
export function isValidCampaignTargetType(type: string): type is CampaignTargetType {
  return Object.values(CampaignTargetType).includes(type as CampaignTargetType);
}

/**
 * Lấy mô tả cho target type
 */
export function getCampaignTargetTypeDescription(type: CampaignTargetType) {
  return CAMPAIGN_TARGET_TYPE_DESCRIPTIONS[type];
}

/**
 * Lấy tất cả target types có thể sử dụng
 */
export function getAllCampaignTargetTypes() {
  return Object.values(CampaignTargetType);
}
