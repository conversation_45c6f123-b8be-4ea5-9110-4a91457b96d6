import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName, ZaloAudienceSyncJobName } from '../../../queue/queue-name.enum';
import { ZaloAudienceSyncJobData } from '../../../queue/queue.types';
import { ZaloAudienceSyncService } from './zalo-audience-sync.service';

/**
 * Processor xử lý queue đồng bộ Zalo audience
 */
@Injectable()
@Processor(QueueName.ZALO_AUDIENCE_SYNC)
export class ZaloAudienceSyncProcessor extends WorkerHost {
  private readonly logger = new Logger(ZaloAudienceSyncProcessor.name);

  constructor(
    private readonly zaloAudienceSyncService: ZaloAudienceSyncService,
  ) {
    super();
  }

  /**
   * Xử lý job đồng bộ người dùng Zalo vào audience
   * @param job Job chứa dữ liệu đồng bộ
   */
  async process(job: Job<ZaloAudienceSyncJobData, any, string>): Promise<void> {
    this.logger.log(
      `Bắt đầu x<PERSON> lý job đồng bộ Zalo audience: ${job.id} - syncId: ${job.data.syncId}`,
    );

    try {
      const { userId, oaId, syncDto, syncId } = job.data;

      // Gửi event bắt đầu đồng bộ
      await this.zaloAudienceSyncService.emitSyncProgress(syncId, {
        status: 'started',
        message: 'Bắt đầu đồng bộ người dùng Zalo vào audience',
        progress: 0,
        timestamp: Date.now(),
      });

      // Thực hiện đồng bộ
      const result = await this.zaloAudienceSyncService.syncZaloUsersToAudience(
        userId,
        oaId,
        syncDto,
        syncId,
      );

      // Gửi event hoàn thành
      await this.zaloAudienceSyncService.emitSyncProgress(syncId, {
        status: 'completed',
        message: 'Đồng bộ hoàn thành thành công',
        progress: 100,
        result,
        timestamp: Date.now(),
      });

      this.logger.log(
        `Hoàn thành job đồng bộ Zalo audience: ${job.id} - syncId: ${syncId}. ` +
        `Processed: ${result.processedCount}, Created: ${result.newAudienceCreated}, ` +
        `Updated: ${result.existingAudienceUpdated}, Errors: ${result.errorCount}`,
      );

    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý job đồng bộ Zalo audience: ${job.id} - syncId: ${job.data.syncId}: ${error.message}`,
        error.stack,
      );

      // Gửi event lỗi
      await this.zaloAudienceSyncService.emitSyncProgress(job.data.syncId, {
        status: 'failed',
        message: `Đồng bộ thất bại: ${error.message}`,
        progress: 0,
        error: error.message,
        timestamp: Date.now(),
      });

      throw error;
    }
  }
}
