import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { GoogleSheetsService } from './google-sheets.service';
import { CreateSpreadsheetRequest, CreateSheetRequest } from '../interfaces/google-sheets.interface';

// Mock googleapis
jest.mock('googleapis', () => ({
  google: {
    auth: {
      OAuth2: jest.fn().mockImplementation(() => ({
        setCredentials: jest.fn(),
      })),
    },
    sheets: jest.fn().mockImplementation(() => ({
      spreadsheets: {
        create: jest.fn(),
        get: jest.fn(),
        batchUpdate: jest.fn(),
        values: {
          get: jest.fn(),
          update: jest.fn(),
          append: jest.fn(),
          clear: jest.fn(),
        },
      },
    })),
    drive: jest.fn().mockImplementation(() => ({
      files: {
        delete: jest.fn(),
      },
    })),
  },
}));

describe('GoogleSheetsService', () => {
  let service: GoogleSheetsService;
  let configService: ConfigService;

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GoogleSheetsService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<GoogleSheetsService>(GoogleSheetsService);
    configService = module.get<ConfigService>(ConfigService);

    // Setup default config values
    mockConfigService.get.mockImplementation((key: string) => {
      switch (key) {
        case 'GOOGLE_CLIENT_ID':
          return 'test-client-id';
        case 'GOOGLE_CLIENT_SECRET':
          return 'test-client-secret';
        case 'GOOGLE_REDIRECT_URI':
          return 'http://localhost:3000/callback';
        default:
          return undefined;
      }
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createSpreadsheet', () => {
    it('should create a new spreadsheet successfully', async () => {
      const mockResponse = {
        data: {
          spreadsheetId: 'test-spreadsheet-id',
          properties: {
            title: 'Test Spreadsheet',
          },
          spreadsheetUrl: 'https://docs.google.com/spreadsheets/d/test-spreadsheet-id',
          sheets: [
            {
              properties: {
                sheetId: 0,
                title: 'Sheet1',
                index: 0,
                gridProperties: {
                  rowCount: 1000,
                  columnCount: 26,
                },
              },
            },
          ],
        },
      };

      const { google } = require('googleapis');
      const mockSheetsInstance = google.sheets();
      mockSheetsInstance.spreadsheets.create.mockResolvedValue(mockResponse);

      const request: CreateSpreadsheetRequest = {
        title: 'Test Spreadsheet',
        sheets: [
          {
            title: 'Sheet1',
            rowCount: 1000,
            columnCount: 26,
          },
        ],
      };

      const result = await service.createSpreadsheet('test-access-token', request);

      expect(result).toEqual({
        spreadsheetId: 'test-spreadsheet-id',
        title: 'Test Spreadsheet',
        url: 'https://docs.google.com/spreadsheets/d/test-spreadsheet-id',
        sheets: [
          {
            sheetId: 0,
            title: 'Sheet1',
            index: 0,
            rowCount: 1000,
            columnCount: 26,
          },
        ],
      });

      expect(mockSheetsInstance.spreadsheets.create).toHaveBeenCalledWith({
        requestBody: {
          properties: {
            title: 'Test Spreadsheet',
          },
          sheets: [
            {
              properties: {
                title: 'Sheet1',
                gridProperties: {
                  rowCount: 1000,
                  columnCount: 26,
                },
              },
            },
          ],
        },
      });
    });

    it('should handle errors when creating spreadsheet', async () => {
      const { google } = require('googleapis');
      const mockSheetsInstance = google.sheets();
      mockSheetsInstance.spreadsheets.create.mockRejectedValue(new Error('API Error'));

      const request: CreateSpreadsheetRequest = {
        title: 'Test Spreadsheet',
      };

      await expect(
        service.createSpreadsheet('test-access-token', request),
      ).rejects.toThrow('Không thể tạo spreadsheet: API Error');
    });
  });

  describe('readData', () => {
    it('should read data from spreadsheet successfully', async () => {
      const mockResponse = {
        data: {
          range: 'Sheet1!A1:D5',
          values: [
            ['Name', 'Age', 'City', 'Country'],
            ['John', '25', 'New York', 'USA'],
            ['Jane', '30', 'London', 'UK'],
          ],
        },
      };

      const { google } = require('googleapis');
      const mockSheetsInstance = google.sheets();
      mockSheetsInstance.spreadsheets.values.get.mockResolvedValue(mockResponse);

      const result = await service.readData(
        'test-access-token',
        'test-spreadsheet-id',
        'Sheet1!A1:D5',
      );

      expect(result).toEqual({
        range: 'Sheet1!A1:D5',
        values: [
          ['Name', 'Age', 'City', 'Country'],
          ['John', '25', 'New York', 'USA'],
          ['Jane', '30', 'London', 'UK'],
        ],
        rowCount: 3,
        columnCount: 4,
      });

      expect(mockSheetsInstance.spreadsheets.values.get).toHaveBeenCalledWith({
        spreadsheetId: 'test-spreadsheet-id',
        range: 'Sheet1!A1:D5',
      });
    });

    it('should handle empty data', async () => {
      const mockResponse = {
        data: {
          range: 'Sheet1!A1:D5',
          values: [],
        },
      };

      const { google } = require('googleapis');
      const mockSheetsInstance = google.sheets();
      mockSheetsInstance.spreadsheets.values.get.mockResolvedValue(mockResponse);

      const result = await service.readData(
        'test-access-token',
        'test-spreadsheet-id',
        'Sheet1!A1:D5',
      );

      expect(result).toEqual({
        range: 'Sheet1!A1:D5',
        values: [],
        rowCount: 0,
        columnCount: 0,
      });
    });
  });

  describe('writeData', () => {
    it('should write data to spreadsheet successfully', async () => {
      const mockResponse = {
        data: {
          updatedRange: 'Sheet1!A1:B2',
          updatedRows: 2,
          updatedColumns: 2,
          updatedCells: 4,
        },
      };

      const { google } = require('googleapis');
      const mockSheetsInstance = google.sheets();
      mockSheetsInstance.spreadsheets.values.update.mockResolvedValue(mockResponse);

      const values = [
        ['Name', 'Age'],
        ['John', 25],
      ];

      const result = await service.writeData(
        'test-access-token',
        'test-spreadsheet-id',
        'Sheet1!A1:B2',
        values,
      );

      expect(result).toEqual({
        updatedRange: 'Sheet1!A1:B2',
        updatedRows: 2,
        updatedColumns: 2,
        updatedCells: 4,
      });

      expect(mockSheetsInstance.spreadsheets.values.update).toHaveBeenCalledWith({
        spreadsheetId: 'test-spreadsheet-id',
        range: 'Sheet1!A1:B2',
        valueInputOption: 'USER_ENTERED',
        requestBody: {
          values,
        },
      });
    });
  });

  describe('testConnection', () => {
    it('should test connection successfully', async () => {
      const mockCreateResponse = {
        data: {
          spreadsheetId: 'test-spreadsheet-id',
        },
      };

      const { google } = require('googleapis');
      const mockSheetsInstance = google.sheets();
      const mockDriveInstance = google.drive();
      
      mockSheetsInstance.spreadsheets.create.mockResolvedValue(mockCreateResponse);
      mockDriveInstance.files.delete.mockResolvedValue({});

      const result = await service.testConnection('test-access-token');

      expect(result).toBe(true);
      expect(mockSheetsInstance.spreadsheets.create).toHaveBeenCalled();
      expect(mockDriveInstance.files.delete).toHaveBeenCalledWith({
        fileId: 'test-spreadsheet-id',
      });
    });

    it('should handle connection test failure', async () => {
      const { google } = require('googleapis');
      const mockSheetsInstance = google.sheets();
      mockSheetsInstance.spreadsheets.create.mockRejectedValue(new Error('Connection failed'));

      const result = await service.testConnection('test-access-token');

      expect(result).toBe(false);
    });
  });
});
