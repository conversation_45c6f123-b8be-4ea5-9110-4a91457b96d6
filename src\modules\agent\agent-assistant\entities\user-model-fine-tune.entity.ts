import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * UserModelFineTune entity
 * Stores user fine-tuned model configurations
 */
@Entity('user_model_fine_tune')
export class UserModelFineTune {
  /**
   * UUID unique identifier for user model fine tune
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Model identifier
   */
  @Column({ name: 'model_id', type: 'varchar', length: 255, nullable: false })
  modelId: string;

  /**
   * Base model used for fine-tuning
   */
  @Column({ name: 'model_base', type: 'varchar', length: 255, nullable: false })
  modelBase: string;

  /**
   * Model registry ID reference to model_registry
   */
  @Column({ name: 'model_registry_id', type: 'uuid', nullable: true })
  modelRegistryId?: string;

  /**
   * System LLM key ID reference to system_key_llm
   */
  @Column({ name: 'llm_key_id', type: 'uuid', nullable: true })
  llmKeyId?: string;

  /**
   * Fine-tune detail ID reference to fine_tune_histories
   */
  @Column({ name: 'detail_id', type: 'uuid', nullable: true })
  detailId?: string;

  /**
   * Whether the fine-tuning was successful
   */
  @Column({ name: 'is_success', type: 'boolean', nullable: true })
  isSuccess?: boolean;
}
