import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, In } from 'typeorm';
import { Connection } from '../entities/connection.entity';

/**
 * Repository cho Connection entity
 * Chứa các method CRUD và business logic cho workflow connections
 */
@Injectable()
export class ConnectionRepository {
  constructor(
    @InjectRepository(Connection)
    private readonly repository: Repository<Connection>,
  ) {}

  /**
   * Tạo connection mới
   */
  async create(connectionData: Partial<Connection>): Promise<Connection> {
    const connection = this.repository.create(connectionData);
    return await this.repository.save(connection);
  }

  /**
   * Tạo nhiều connections cùng lúc
   */
  async createMany(connectionsData: Partial<Connection>[]): Promise<Connection[]> {
    const connections = connectionsData.map(connectionData => 
      this.repository.create(connectionData)
    );
    return await this.repository.save(connections);
  }

  /**
   * Tìm connection theo ID
   */
  async findById(id: number): Promise<Connection | null> {
    return await this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm nhiều connections theo IDs
   */
  async findByIds(ids: number[]): Promise<Connection[]> {
    return await this.repository.find({
      where: { id: In(ids) },
    });
  }

  /**
   * Tìm tất cả connections của một workflow
   */
  async findByWorkflowId(workflowId: string): Promise<Connection[]> {
    return await this.repository.find({
      where: { workflowId },
      order: { id: 'ASC' },
    });
  }

  /**
   * Tìm connections từ một source node
   */
  async findBySourceNodeId(sourceNodeId: string): Promise<Connection[]> {
    return await this.repository.find({
      where: { sourceNodeId },
      order: { sourceHandleIndex: 'ASC' },
    });
  }

  /**
   * Tìm connections đến một target node
   */
  async findByTargetNodeId(targetNodeId: string): Promise<Connection[]> {
    return await this.repository.find({
      where: { targetNodeId },
      order: { id: 'ASC' },
    });
  }

  /**
   * Tìm connections giữa hai nodes
   */
  async findBetweenNodes(sourceNodeId: string, targetNodeId: string): Promise<Connection[]> {
    return await this.repository.find({
      where: { sourceNodeId, targetNodeId },
    });
  }

  /**
   * Tìm connection cụ thể theo handles
   */
  async findByHandles(
    sourceNodeId: string,
    targetNodeId: string,
    sourceHandle: string,
    targetHandle: string,
  ): Promise<Connection | null> {
    return await this.repository.findOne({
      where: {
        sourceNodeId,
        targetNodeId,
        sourceHandle,
        targetHandle,
      },
    });
  }

  /**
   * Tìm connections theo source handle
   */
  async findBySourceHandle(sourceNodeId: string, sourceHandle: string): Promise<Connection[]> {
    return await this.repository.find({
      where: { sourceNodeId, sourceHandle },
      order: { sourceHandleIndex: 'ASC' },
    });
  }

  /**
   * Tìm connections theo target handle
   */
  async findByTargetHandle(targetNodeId: string, targetHandle: string): Promise<Connection[]> {
    return await this.repository.find({
      where: { targetNodeId, targetHandle },
    });
  }

  /**
   * Cập nhật connection
   */
  async update(id: number, updateData: Partial<Connection>): Promise<Connection | null> {
    const updateResult = await this.repository.update(id, updateData);

    if (updateResult.affected === 0) {
      return null;
    }

    return await this.findById(id);
  }

  /**
   * Xóa connection
   */
  async delete(id: number): Promise<boolean> {
    const deleteResult = await this.repository.delete(id);
    return (deleteResult.affected || 0) > 0;
  }

  /**
   * Xóa tất cả connections của một workflow
   */
  async deleteByWorkflowId(workflowId: string): Promise<number> {
    const deleteResult = await this.repository.delete({ workflowId });
    return deleteResult.affected || 0;
  }

  /**
   * Xóa tất cả connections từ một node
   */
  async deleteBySourceNodeId(sourceNodeId: string): Promise<number> {
    const deleteResult = await this.repository.delete({ sourceNodeId });
    return deleteResult.affected || 0;
  }

  /**
   * Xóa tất cả connections đến một node
   */
  async deleteByTargetNodeId(targetNodeId: string): Promise<number> {
    const deleteResult = await this.repository.delete({ targetNodeId });
    return deleteResult.affected || 0;
  }

  /**
   * Xóa tất cả connections liên quan đến một node
   */
  async deleteByNodeId(nodeId: string): Promise<number> {
    const sourceDeleted = await this.deleteBySourceNodeId(nodeId);
    const targetDeleted = await this.deleteByTargetNodeId(nodeId);
    return sourceDeleted + targetDeleted;
  }

  /**
   * Đếm số lượng connections
   */
  async count(where?: FindOptionsWhere<Connection>): Promise<number> {
    return await this.repository.count({ where });
  }

  /**
   * Đếm connections theo workflow
   */
  async countByWorkflowId(workflowId: string): Promise<number> {
    return await this.repository.count({
      where: { workflowId },
    });
  }

  /**
   * Đếm connections từ một node
   */
  async countBySourceNodeId(sourceNodeId: string): Promise<number> {
    return await this.repository.count({
      where: { sourceNodeId },
    });
  }

  /**
   * Đếm connections đến một node
   */
  async countByTargetNodeId(targetNodeId: string): Promise<number> {
    return await this.repository.count({
      where: { targetNodeId },
    });
  }

  /**
   * Kiểm tra xem có connection giữa hai nodes không
   */
  async existsBetweenNodes(sourceNodeId: string, targetNodeId: string): Promise<boolean> {
    const count = await this.repository.count({
      where: { sourceNodeId, targetNodeId },
    });
    return count > 0;
  }

  /**
   * Kiểm tra xem có connection với handles cụ thể không
   */
  async existsByHandles(
    sourceNodeId: string,
    targetNodeId: string,
    sourceHandle: string,
    targetHandle: string,
  ): Promise<boolean> {
    const count = await this.repository.count({
      where: {
        sourceNodeId,
        targetNodeId,
        sourceHandle,
        targetHandle,
      },
    });
    return count > 0;
  }

  /**
   * Tìm connections với pagination
   */
  async findWithPagination(
    page: number = 1,
    limit: number = 10,
    where?: FindOptionsWhere<Connection>,
  ): Promise<{ connections: Connection[]; total: number; totalPages: number }> {
    const skip = (page - 1) * limit;
    
    const [connections, total] = await this.repository.findAndCount({
      where,
      skip,
      take: limit,
      order: { id: 'ASC' },
    });

    const totalPages = Math.ceil(total / limit);

    return {
      connections,
      total,
      totalPages,
    };
  }

  /**
   * Bulk delete connections
   */
  async bulkDelete(ids: number[]): Promise<number> {
    const deleteResult = await this.repository
      .createQueryBuilder()
      .delete()
      .from(Connection)
      .whereInIds(ids)
      .execute();

    return deleteResult.affected || 0;
  }

  /**
   * Tìm workflow graph structure
   */
  async getWorkflowGraph(workflowId: string): Promise<{
    nodes: string[];
    connections: { source: string; target: string; sourceHandle: string; targetHandle: string }[];
  }> {
    const connections = await this.findByWorkflowId(workflowId);
    
    const nodes = new Set<string>();
    const edges = connections.map(conn => {
      nodes.add(conn.sourceNodeId);
      nodes.add(conn.targetNodeId);
      return {
        source: conn.sourceNodeId,
        target: conn.targetNodeId,
        sourceHandle: conn.sourceHandle,
        targetHandle: conn.targetHandle,
      };
    });

    return {
      nodes: Array.from(nodes),
      connections: edges,
    };
  }

  /**
   * Kiểm tra circular dependency
   */
  async hasCircularDependency(workflowId: string): Promise<boolean> {
    const graph = await this.getWorkflowGraph(workflowId);
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (node: string): boolean => {
      if (recursionStack.has(node)) {
        return true;
      }
      if (visited.has(node)) {
        return false;
      }

      visited.add(node);
      recursionStack.add(node);

      const neighbors = graph.connections
        .filter(conn => conn.source === node)
        .map(conn => conn.target);

      for (const neighbor of neighbors) {
        if (hasCycle(neighbor)) {
          return true;
        }
      }

      recursionStack.delete(node);
      return false;
    };

    for (const node of graph.nodes) {
      if (!visited.has(node)) {
        if (hasCycle(node)) {
          return true;
        }
      }
    }

    return false;
  }
}
