import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { MessageDirectionEnum, MessageTypeEnum } from '../enums/message.enum';

/**
 * Zalo AI Message entity
 * Tracks messages in Zalo conversations
 */
@Entity('zalo_ai_messages')
export class ZaloAiMessage {
  /**
   * Primary key - UUID
   */
  @PrimaryGeneratedColumn('uuid')
  id: string; 
  
  /**
   * Zalo thread ID
   */
  @Column({ name: 'thread_id', type: 'uuid', nullable: false })
  threadId: string; 
  
  /**
   * Zalo message ID
   */
  @Column({ name: 'message_id', type: 'varchar', length: 50, nullable: true })
  messageId?: string; 
  
  /**
   * Message direction (incoming/outgoing)
   */
  @Column({
    name: 'direction',
    type: 'enum',
    enum: MessageDirectionEnum,
    nullable: false
  })
  direction: MessageDirectionEnum;
  
  /**
   * Message type (text, image, sticker, etc.)
   */
  @Column({
    name: 'message_type',
    type: 'enum',
    enum: MessageTypeEnum,
    nullable: false,
  })
  messageType: MessageTypeEnum;
  
  /**
   * Raw webhook data from Zalo
   */
  @Column({ name: 'raw_webhook_data', type: 'jsonb', nullable: true })
  rawWebhookData?: any;

  /**
   * Processed message content in our format (text, structured data, etc.)
   */
  @Column({ name: 'content', type: 'text', nullable: true })
  content?: string;

  /**
   * Our internal media IDs associated with this message
   */
  @Column({ name: 'media_ids', type: 'jsonb', nullable: true })
  mediaIds?: string[];

  /**
   * Creation timestamp
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;
}
