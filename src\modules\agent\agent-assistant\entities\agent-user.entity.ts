import { Entity, PrimaryColumn, Column, Index } from 'typeorm';
import { AgentProfile } from '../interfaces/agent-profile.interface';
import { PaymentMethodEnum } from '../enums/payment-method.enum';

/**
 * AgentUser entity
 * Stores user agent information, linked with users
 */
@Entity('agents_user')
@Index('idx_agents_user_user_id', ['userId'])
@Index('idx_agents_user_type_id', ['typeId'])
@Index('idx_agents_user_source_id', ['sourceId'])
export class AgentUser {
  /**
   * UUID reference from agents.id
   */
  @PrimaryColumn({ name: 'id', type: 'uuid' })
  id: string;

  /**
   * User ID who owns the agent
   */
  @Column({ name: 'user_id', type: 'int', nullable: true })
  userId?: number;

  /**
   * Agent function type ID, references type_agents
   */
  @Column({ name: 'type_id', type: 'int', nullable: false })
  typeId: number;

  /**
   * UUID reference to source agent (if agent was created from another agent)
   */
  @Column({ name: 'source_id', type: 'uuid', nullable: true })
  sourceId?: string;

  /**
   * Profile information in JSONB format (e.g., {"bio": "Assistant for tasks"})
   */
  @Column({ name: 'profile', type: 'jsonb', default: '{}' })
  profile: AgentProfile;

  /**
   * Conversion configuration in JSONB format (e.g., {"format": "json"})
   */
  @Column({ name: 'convert_config', type: 'jsonb', default: '{}' })
  convertConfig: any;

  /**
   * Whether the agent is active
   */
  @Column({ name: 'active', type: 'boolean', default: false, nullable: false })
  active: boolean;

  /**
   * Experience points
   */
  @Column({ name: 'exp', type: 'bigint', default: 0, nullable: false })
  exp: number;

  /**
   * Strategy ID reference to agents_strategy_user
   */
  @Column({ name: 'strategy_id', type: 'uuid', nullable: true })
  strategyId?: string;

  /**
   * Whether the agent is for sale
   */
  @Column({ name: 'is_for_sale', type: 'boolean', default: false, nullable: false })
  isForSale: boolean;

  /**
   * User model ID reference to user_models
   */
  @Column({ name: 'user_model_id', type: 'uuid', nullable: true })
  userModelId?: string;

  /**
   * System model ID reference to system_models
   */
  @Column({ name: 'system_model_id', type: 'uuid', nullable: true })
  systemModelId?: string;

  /**
   * Fine-tuned model ID reference to user_model_fine_tune
   */
  @Column({ name: 'model_fine_tune_id', type: 'uuid', nullable: true })
  modelFineTuneId?: string;

  /**
   * LLM key ID reference to user_key_llm
   */
  @Column({ name: 'key_llm_id', type: 'uuid', nullable: true })
  keyLlmId?: string;

  /**
   * Payment gateway ID reference to payment_gateway
   */
  @Column({ name: 'payment_gateway_id', type: 'int', nullable: true })
  paymentGatewayId?: number;

  /**
   * User provider shipment ID reference to user_provider_shipments
   */
  @Column({ name: 'user_provider_shipment_id', type: 'uuid', nullable: true })
  userProviderShipmentId?: string;

  /**
   * Whether receiver pays shipping fee (default: true)
   */
  @Column({ name: 'receiver_pay_shipping_fee', type: 'boolean', default: true })
  receiverPayShippingFee: boolean;

  /**
   * Supported payment methods (COD, BANKING)
   */
  @Column({ name: 'payment_methods', type: 'jsonb', default: '[]' })
  paymentMethods: PaymentMethodEnum[];
}
