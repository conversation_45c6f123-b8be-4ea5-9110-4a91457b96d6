import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AgentStrategyUser } from '../entities/agent-strategy-user.entity';

@Injectable()
export class AgentStrategyUserRepository {
  constructor(
    @InjectRepository(AgentStrategyUser)
    private readonly repository: Repository<AgentStrategyUser>,
  ) {}

  /**
   * Find agent strategy user by ID
   * @param id Strategy user ID
   * @returns AgentStrategyUser or null if not found
   */
  async findById(id: string): Promise<AgentStrategyUser | null> {
    return this.repository.findOne({
      where: {
        id,
      }
    });
  }

  /**
   * Find agent strategy user by strategy ID and user ID
   * @param agentsStrategyId Strategy ID
   * @param userId User ID
   * @returns AgentStrategyUser or null if not found
   */
  async findByStrategyAndUser(agentsStrategyId: string, userId: number): Promise<AgentStrategyUser | null> {
    return this.repository.findOne({
      where: {
        agentsStrategyId,
        userId,
      }
    });
  }

  /**
   * Find all strategy users by user ID
   * @param userId User ID
   * @returns Array of AgentStrategyUser
   */
  async findByUserId(userId: number): Promise<AgentStrategyUser[]> {
    return this.repository.find({
      where: {
        userId,
      },
      order: {
        ownedAt: 'DESC',
      }
    });
  }
}
