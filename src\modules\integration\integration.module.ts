import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { FptSmsBrandnameTestProcessor } from './processors/fpt-sms-brandname-test.processor';
import { SmsModule } from '../../shared/services/sms/sms.module';

/**
 * Module xử lý các tích hợp (Integration) trong Worker
 */
@Module({
  imports: [
    HttpModule, // Import HttpModule để sử dụng HttpService
    SmsModule, // Import SMS module để sử dụng FprSmsBrandnameService
  ],
  providers: [
    FptSmsBrandnameTestProcessor, // Processor test kết nối FPT SMS Brandname
  ],
  exports: [
    FptSmsBrandnameTestProcessor,
  ],
})
export class IntegrationModule {}
