import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Upload } from '@aws-sdk/lib-storage';
import {
    GetObjectCommand,
    S3Client
} from '@aws-sdk/client-s3';
import { AppException, ErrorCode } from '@common/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import { Readable } from 'typeorm/platform/PlatformTools';
import { env } from '../../config/env';

@Injectable()
export class S3Service {
    private readonly logger = new Logger(S3Service.name);
    private readonly s3Client: S3Client;
    private readonly bucketName: string;

    constructor() {
        // Initialize S3 client with environment configuration
        this.s3Client = new S3Client({
            region: env.s3.S3_REGION,
            endpoint: env.s3.S3_ENDPOINT,
            credentials: {
                accessKeyId: env.s3.S3_ACCESS_KEY,
                secretAccessKey: env.s3.S3_SECRET_KEY,
            },
        });

        this.bucketName = env.s3.S3_BUCKET_NAME;

        this.logger.log(`S3Service initialized with bucket: ${this.bucketName}`);
    }

    /**
   * Tải file từ S3/R2 dưới dạng stream
   * @param key S3 key của file cần tải
   * @returns Readable stream của file
   * @throws AppException nếu có lỗi khi tải file
   */
    async downloadFileAsStream(key: string): Promise<NodeJS.ReadableStream> {
        try {
            // Kiểm tra nếu key rỗng hoặc không hợp lệ
            if (!key || typeof key !== 'string') {
                this.logger.warn(`Invalid key provided for stream download: ${key}`);
                throw new AppException(
                    ErrorCode.CLOUD_FLARE_ERROR_DOWNLOAD,
                    'Invalid key provided for stream download',
                    { key },
                );
            }

            // Chuẩn hóa key (loại bỏ dấu / ở đầu nếu có)
            const normalizedKey = key.startsWith('/') ? key.substring(1) : key;

            // Tạo command để lấy file
            const command = new GetObjectCommand({
                Bucket: this.bucketName,
                Key: normalizedKey,
            });

            // Gọi S3 API để lấy file
            const response = await this.s3Client.send(command);

            // Kiểm tra response
            if (!response.Body) {
                throw new Error('Empty response body');
            }

            // Ghi log thành công
            this.logger.log(
                `Created stream for file: ${normalizedKey}, content-length: ${response.ContentLength || 'unknown'}`,
            );

            // Trả về stream trực tiếp
            return response.Body as NodeJS.ReadableStream;
        } catch (error) {
            this.logger.error(
                `Failed to download file as stream from S3/R2: ${key}: ${error.message}`,
                error.stack,
            );

            // Ném ra exception để caller có thể xử lý
            throw new AppException(
                ErrorCode.CLOUD_FLARE_ERROR_DOWNLOAD,
                `Failed to download file as stream: ${error.message}`,
                process.env.NODE_ENV === 'production'
                    ? undefined
                    : { error: error.message, stack: error.stack },
            );
        }
    }


    /**
     * Tải file từ S3/R2 dưới dạng byte array
     * @param key S3 key của file cần tải
     * @returns Byte array của file
     * @throws AppException nếu có lỗi khi tải file
     */
    async downloadFileAsBytes(key: string): Promise<Uint8Array> {
        try {
            // Kiểm tra nếu key rỗng hoặc không hợp lệ
            if (!key || typeof key !== 'string') {
                this.logger.warn(`Invalid key provided for download: ${key}`);
                throw new AppException(
                    ErrorCode.CLOUD_FLARE_ERROR_DOWNLOAD,
                    'Invalid key provided for download',
                    { key },
                );
            }

            // Chuẩn hóa key (loại bỏ dấu / ở đầu nếu có)
            const normalizedKey = key.startsWith('/') ? key.substring(1) : key;

            // Tạo command để lấy file
            const command = new GetObjectCommand({
                Bucket: this.bucketName,
                Key: normalizedKey,
            });

            // Gọi S3 API để lấy file
            const response = await this.s3Client.send(command);

            // Kiểm tra response
            if (!response.Body) {
                throw new Error('Empty response body');
            }

            // Đọc dữ liệu từ stream
            const chunks: Uint8Array[] = [];
            const stream = response.Body as any;

            // Sử dụng for await để đọc từ stream
            for await (const chunk of stream) {
                chunks.push(chunk);
            }

            // Nối các chunk lại với nhau
            const fileSize = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
            const fileData = new Uint8Array(fileSize);
            let position = 0;

            for (const chunk of chunks) {
                fileData.set(chunk, position);
                position += chunk.length;
            }

            // Ghi log thành công
            this.logger.log(
                `Downloaded file as bytes successfully: ${normalizedKey}, size: ${fileSize} bytes`,
            );

            return fileData;
        } catch (error) {
            this.logger.error(
                `Failed to download file as bytes from S3/R2: ${key}: ${error.message}`,
                error.stack,
            );

            // Ném ra exception để caller có thể xử lý
            throw new AppException(
                ErrorCode.CLOUD_FLARE_ERROR_DOWNLOAD,
                `Failed to download file as bytes: ${error.message}`,
                process.env.NODE_ENV === 'production'
                    ? undefined
                    : { error: error.message, stack: error.stack },
            );
        }
    }

    
  async generateGetPresignedUrl(
    key: string,
    expirationInSeconds: number = 3600,
  ): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      const url = await getSignedUrl(this.s3Client, command, {
        expiresIn: expirationInSeconds,
      });
      return url;
    } catch (error) {
      this.logger.error(
        `Failed to generate presigned URL for ${key}: ${error.message}`,
      );
      throw error;
    }
  }

  async uploadFromUrlStreaming(url: string, key: string): Promise<string> {
    try {
      const res = await fetch(url);
      if (!res.ok) {
        throw new Error(`Failed to fetch from ${url}: ${res.statusText}`);
      }

      const nodeStream =
        res.body instanceof Readable
          ? res.body
          : Readable.fromWeb(res.body as any);

      const upload = new Upload({
        client: this.s3Client,
        params: {
          Bucket: this.bucketName,
          Key: key,
          Body: nodeStream,
        },
      });

      upload.on('httpUploadProgress', (progress) => {
        this.logger.log(`Upload progress: ${progress.loaded}/${progress.total}`);
      });

      await upload.done();
      return key;
    } catch (error) {
      this.logger.error(
        `Failed to upload from ${url} to ${key}: ${error.message}`,
      );
      throw error;
    }
  }
}
