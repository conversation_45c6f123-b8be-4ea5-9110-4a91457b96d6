# Fine-Tune Job Examples

Th<PERSON> mục này chứa các ví dụ về cách tạo và sử dụng fine-tune jobs trong hệ thống.

## 📁 Files

- `fine-tune-job-example.service.ts` - Service chứa các ví dụ tạo job
- `fine-tune-example.controller.ts` - Controller để test qua API
- `README.md` - Tài liệu hướng dẫn

## 🚀 Cách sử dụng

### 1. Import vào module

```typescript
// Trong fine-tune.module.ts hoặc module khác
import { FineTuneJobExampleService } from './examples/fine-tune-job-example.service';
import { FineTuneExampleController } from './examples/fine-tune-example.controller';

@Module({
  // ...
  providers: [
    // ... other providers
    FineTuneJobExampleService,
  ],
  controllers: [
    // ... other controllers  
    FineTuneExampleController,
  ],
})
```

### 2. Test qua API

Sau khi import, bạn có thể test các endpoint:

```bash
# Tạo job với raw data
POST /fine-tune/examples/create-with-raw-data

# Tạo job với file đã upload
POST /fine-tune/examples/create-with-uploaded-files

# Tạo admin job
POST /fine-tune/examples/create-admin-job

# Tạo Google AI job
POST /fine-tune/examples/create-google-job

# Chạy tất cả ví dụ
POST /fine-tune/examples/run-all-examples

# Tạo custom job
POST /fine-tune/examples/create-custom-job
Content-Type: application/json
{
  "context": "USER",
  "userId": 123,
  "provider": "OPENAI",
  "modelType": "SYSTEM",
  "baseModel": "gpt-3.5-turbo-1106",
  "trainingFileId": "file-abc123",
  "pointsToRefund": 1000,
  "modelRegistryId": "uuid-model-registry",
  "userKeyId": "uuid-user-key"
}
```

### 3. Sử dụng trong code

```typescript
import { FineTuneJobExampleService } from './examples/fine-tune-job-example.service';

@Injectable()
export class YourService {
  constructor(
    private readonly fineTuneJobExampleService: FineTuneJobExampleService,
  ) {}

  async createJob() {
    // Tạo job với raw data
    const jobId = await this.fineTuneJobExampleService.createJobWithRawData();
    console.log(`Job created: ${jobId}`);
  }
}
```

## 📋 Các loại job

### 1. Job với Raw Data
- **Mục đích**: Khi bạn có training data dạng text
- **Quy trình**: Upload data → Tạo job → Polling status
- **Ví dụ**: `createJobWithRawData()`

### 2. Job với File đã Upload
- **Mục đích**: Khi bạn đã có file ID từ provider
- **Quy trình**: Tạo job trực tiếp → Polling status
- **Ví dụ**: `createJobWithUploadedFiles()`

### 3. Admin Job
- **Mục đích**: Job được tạo bởi admin
- **Đặc điểm**: Không trừ R-Points
- **Ví dụ**: `createAdminJob()`

### 4. Google AI Job
- **Mục đích**: Sử dụng Google AI provider
- **Đặc điểm**: Format data khác với OpenAI
- **Ví dụ**: `createGoogleFineTuneJob()`

## 🔧 Cấu hình Job

### Context
- `USER`: Người dùng thường (cần userId)
- `ADMIN`: Quản trị viên (cần employeeId)

### Provider
- `OPENAI`: Sử dụng OpenAI API
- `GOOGLE`: Sử dụng Google AI API

### Model Type
- `SYSTEM`: Model hệ thống (có thể hoàn points)
- `USER`: Model người dùng (không hoàn points)

### Training Data Format

**OpenAI Format:**
```json
{"messages": [{"role": "user", "content": "Hello"}, {"role": "assistant", "content": "Hi!"}]}
```

**Google AI Format:**
```json
{"input_text": "Translate: Hello", "output_text": "Xin chào"}
```

## 📊 Monitoring

### Queue Dashboard
- URL: `/queues`
- Username: `admin`
- Password: `redai@123`

### Job Status
- `PENDING`: Đang chờ xử lý
- `QUEUED`: Đã gửi lên provider
- `RUNNING`: Đang fine-tune
- `SUCCEEDED`: Thành công
- `FAILED`: Thất bại
- `CANCELLED`: Đã hủy

### Polling
- Tần suất: Mỗi 5 phút
- Tự động dừng khi job terminal (success/failed)
- Cập nhật model_id khi thành công
- Hoàn R-Points khi thất bại

## ⚠️ Lưu ý

1. **File IDs**: Đảm bảo file đã tồn tại trên provider
2. **Points**: Chỉ hoàn points cho User + System model khi fail
3. **Validation**: Training data phải đúng format JSONL
4. **Timeout**: HTTP timeout 60 seconds cho API calls
5. **Retry**: Tối đa 3 lần retry với exponential backoff

## 🐛 Troubleshooting

### Job không chạy
- Kiểm tra Redis connection
- Xem queue dashboard
- Check logs trong console

### Job thất bại
- Xem error message trong database
- Kiểm tra API keys
- Validate training data format

### Points không được hoàn
- Chỉ hoàn cho User + System model
- Kiểm tra pointsToRefund trong job data
- Xem logs của RPointService

## 📝 Development

Để thêm ví dụ mới:

1. Thêm method vào `FineTuneJobExampleService`
2. Thêm endpoint vào `FineTuneExampleController`
3. Update README này
4. Test qua API hoặc unit test
