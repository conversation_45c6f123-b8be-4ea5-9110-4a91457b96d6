import { Injectable, Logger } from '@nestjs/common';
import { FineTunePollingJobData, PollingResult, StatusUpdateData } from '../interfaces/fine-tune-polling.interface';
import { ProviderFineTuneEnum } from '../constants/provider.enum';

/**
 * Service để quản lý logging cho fine-tune polling
 */
@Injectable()
export class FineTuneLoggingService {
  private readonly logger = new Logger(FineTuneLoggingService.name);

  /**
   * Log khi bắt đầu polling job
   */
  logJobStart(jobData: FineTunePollingJobData, jobId: string, attempt: number): void {
    this.logger.log('Starting fine-tune polling job', {
      jobId,
      userId: jobData.userId,
      modelFineTuneId: jobData.modelFineTuneId,
      provider: jobData.provider,
      attempt,
      timestamp: new Date(jobData.timestamp).toISOString(),
    });
  }

  /**
   * Log khi job hoàn thành thành công
   */
  logJobSuccess(jobData: FineTunePollingJobData, result: PollingResult): void {
    this.logger.log('Fine-tune polling job completed successfully', {
      userId: jobData.userId,
      modelFineTuneId: jobData.modelFineTuneId,
      provider: jobData.provider,
      status: result.status,
      modelId: result.modelId,
      success: result.success,
    });
  }

  /**
   * Log khi job thất bại
   */
  logJobFailure(jobData: FineTunePollingJobData, error: string, attempt: number): void {
    this.logger.error('Fine-tune polling job failed', {
      userId: jobData.userId,
      modelFineTuneId: jobData.modelFineTuneId,
      provider: jobData.provider,
      error,
      attempt,
    });
  }

  /**
   * Log khi polling provider
   */
  logProviderPolling(provider: ProviderFineTuneEnum, jobId: string): void {
    this.logger.debug('Polling provider for job status', {
      provider,
      jobId,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Log kết quả polling từ provider
   */
  logProviderResult(provider: ProviderFineTuneEnum, jobId: string, result: PollingResult): void {
    this.logger.debug('Provider polling result', {
      provider,
      jobId,
      status: result.status,
      success: result.success,
      shouldContinuePolling: result.shouldContinuePolling,
      modelId: result.modelId,
      error: result.error,
    });
  }

  /**
   * Log khi cập nhật database
   */
  logDatabaseUpdate(updateData: StatusUpdateData, success: boolean): void {
    if (success) {
      this.logger.log('Database status updated successfully', {
        modelFineTuneId: updateData.modelFineTuneId,
        status: updateData.status,
        isSuccess: updateData.isSuccess,
        modelId: updateData.modelId,
      });
    } else {
      this.logger.error('Failed to update database status', {
        modelFineTuneId: updateData.modelFineTuneId,
        status: updateData.status,
        isSuccess: updateData.isSuccess,
        error: updateData.error,
      });
    }
  }

  /**
   * Log khi schedule job tiếp theo
   */
  logNextJobScheduled(jobData: FineTunePollingJobData, delay: number): void {
    this.logger.debug('Next polling job scheduled', {
      modelFineTuneId: jobData.modelFineTuneId,
      provider: jobData.provider,
      delayMs: delay,
      nextRunTime: new Date(Date.now() + delay).toISOString(),
    });
  }

  /**
   * Log khi đạt max attempts
   */
  logMaxAttemptsReached(jobData: FineTunePollingJobData, maxAttempts: number): void {
    this.logger.warn('Max polling attempts reached', {
      userId: jobData.userId,
      modelFineTuneId: jobData.modelFineTuneId,
      provider: jobData.provider,
      maxAttempts,
      message: 'Job will be marked as timeout',
    });
  }

  /**
   * Log khi không tìm thấy job info
   */
  logJobInfoNotFound(jobData: FineTunePollingJobData): void {
    this.logger.error('Job info not found in database', {
      userId: jobData.userId,
      modelFineTuneId: jobData.modelFineTuneId,
      provider: jobData.provider,
      message: 'Cannot proceed with polling - missing job information',
    });
  }

  /**
   * Log khi API key decrypt thất bại
   */
  logApiKeyDecryptFailed(jobData: FineTunePollingJobData): void {
    this.logger.error('Failed to decrypt API key', {
      userId: jobData.userId,
      modelFineTuneId: jobData.modelFineTuneId,
      provider: jobData.provider,
      message: 'Cannot proceed with polling - invalid API key',
    });
  }

  /**
   * Log khi job không cần polling nữa
   */
  logJobNoLongerNeeded(jobData: FineTunePollingJobData): void {
    this.logger.log('Job no longer needs polling', {
      userId: jobData.userId,
      modelFineTuneId: jobData.modelFineTuneId,
      provider: jobData.provider,
      message: 'Job has already completed or failed',
    });
  }

  /**
   * Log provider API error
   */
  logProviderApiError(provider: ProviderFineTuneEnum, jobId: string, error: any): void {
    this.logger.error('Provider API error', {
      provider,
      jobId,
      error: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
    });
  }

  /**
   * Log database connection error
   */
  logDatabaseError(operation: string, error: any, context?: any): void {
    this.logger.error('Database operation failed', {
      operation,
      error: error.message,
      context,
    });
  }

  /**
   * Log encryption/decryption error
   */
  logEncryptionError(operation: 'encrypt' | 'decrypt', error: any): void {
    this.logger.error('Encryption operation failed', {
      operation,
      error: error.message,
    });
  }

  /**
   * Log queue operation error
   */
  logQueueError(operation: string, error: any, jobData?: FineTunePollingJobData): void {
    this.logger.error('Queue operation failed', {
      operation,
      error: error.message,
      jobData,
    });
  }

  /**
   * Log performance metrics
   */
  logPerformanceMetrics(
    jobData: FineTunePollingJobData,
    startTime: number,
    endTime: number,
    operation: string,
  ): void {
    const duration = endTime - startTime;
    
    this.logger.debug('Performance metrics', {
      operation,
      modelFineTuneId: jobData.modelFineTuneId,
      provider: jobData.provider,
      durationMs: duration,
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
    });

    // Log warning nếu operation mất quá lâu
    if (duration > 30000) { // 30 giây
      this.logger.warn('Slow operation detected', {
        operation,
        modelFineTuneId: jobData.modelFineTuneId,
        provider: jobData.provider,
        durationMs: duration,
        message: 'Operation took longer than expected',
      });
    }
  }

  /**
   * Log system health check
   */
  logHealthCheck(healthy: boolean, details?: any): void {
    if (healthy) {
      this.logger.log('Fine-tune polling system health check passed', details);
    } else {
      this.logger.error('Fine-tune polling system health check failed', details);
    }
  }

  /**
   * Log configuration issues
   */
  logConfigurationIssue(issue: string, details?: any): void {
    this.logger.warn('Configuration issue detected', {
      issue,
      details,
      message: 'This may affect fine-tune polling functionality',
    });
  }

  /**
   * Log rate limiting
   */
  logRateLimit(provider: ProviderFineTuneEnum, retryAfter?: number): void {
    this.logger.warn('Rate limit encountered', {
      provider,
      retryAfter,
      message: 'Polling will be delayed due to rate limiting',
    });
  }

  /**
   * Log job statistics
   */
  logJobStatistics(stats: {
    totalJobs: number;
    successfulJobs: number;
    failedJobs: number;
    pendingJobs: number;
    averagePollingTime: number;
  }): void {
    this.logger.log('Fine-tune polling job statistics', {
      ...stats,
      successRate: stats.totalJobs > 0 ? (stats.successfulJobs / stats.totalJobs * 100).toFixed(2) + '%' : '0%',
      timestamp: new Date().toISOString(),
    });
  }
}
