import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';

/**
 * Enum cho loại chiến dịch Zalo
 */
export enum ZaloCampaignType {
  MESSAGE = 'message',
  ZNS = 'zns',
}

/**
 * Enum cho trạng thái chiến dịch Zalo
 */
export enum ZaloCampaignStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  RUNNING = 'running',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

/**
 * DTO cho nội dung tin nhắn trong chiến dịch Zalo
 */
export class ZaloCampaignMessageContentDto {
  @ApiProperty({
    description: 'Loại tin nhắn (text, image, file, template)',
    example: 'text',
  })
  @IsString()
  @IsNotEmpty()
  type: string;

  @ApiProperty({
    description: 'Nội dung tin nhắn văn bản',
    example: 'Xin chào! Cảm ơn bạn đã quan tâm đến sản phẩm của chúng tôi.',
    required: false,
  })
  @IsString()
  @IsOptional()
  text?: string;

  @ApiProperty({
    description: 'URL của hình ảnh',
    example: 'https://example.com/image.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  imageUrl?: string;

  @ApiProperty({
    description: 'URL của file',
    example: 'https://example.com/document.pdf',
    required: false,
  })
  @IsString()
  @IsOptional()
  fileUrl?: string;

  @ApiProperty({
    description: 'ID của template',
    example: 'template123',
    required: false,
  })
  @IsString()
  @IsOptional()
  templateId?: string;

  @ApiProperty({
    description: 'Dữ liệu cho template',
    example: { name: '{name}', product: '{product}' },
    required: false,
  })
  @IsObject()
  @IsOptional()
  templateData?: Record<string, any>;
}

/**
 * DTO cho nội dung ZNS trong chiến dịch Zalo
 */
export class ZaloCampaignZnsContentDto {
  @ApiProperty({
    description: 'ID của template ZNS',
    example: 'template*********',
  })
  @IsString()
  @IsNotEmpty()
  templateId: string;

  @ApiProperty({
    description: 'Dữ liệu cho template',
    example: { orderId: '{orderId}', shopName: '{shopName}' },
  })
  @IsObject()
  @IsNotEmpty()
  templateData: Record<string, any>;
}

/**
 * DTO cho việc tạo chiến dịch Zalo
 */
export class CreateZaloCampaignDto {
  @ApiProperty({
    description: 'ID của Official Account',
    example: '*********',
  })
  @IsString()
  @IsNotEmpty()
  oaId: string;

  @ApiProperty({
    description: 'Tên của chiến dịch',
    example: 'Chiến dịch khuyến mãi tháng 7',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Mô tả của chiến dịch',
    example: 'Chiến dịch khuyến mãi dành cho khách hàng VIP',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Loại chiến dịch',
    enum: ZaloCampaignType,
    example: ZaloCampaignType.MESSAGE,
  })
  @IsEnum(ZaloCampaignType)
  @IsNotEmpty()
  type: ZaloCampaignType;

  @ApiProperty({
    description: 'ID của phân đoạn',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  segmentId: number;

  @ApiProperty({
    description: 'Thời điểm bắt đầu (Unix timestamp)',
    example: *************,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  scheduledAt?: number;

  @ApiProperty({
    description: 'Nội dung tin nhắn (chỉ dùng khi type là message)',
    type: ZaloCampaignMessageContentDto,
    required: false,
  })
  @IsObject()
  @IsOptional()
  messageContent?: ZaloCampaignMessageContentDto;

  @ApiProperty({
    description: 'Nội dung ZNS (chỉ dùng khi type là zns)',
    type: ZaloCampaignZnsContentDto,
    required: false,
  })
  @IsObject()
  @IsOptional()
  znsContent?: ZaloCampaignZnsContentDto;
}

/**
 * DTO cho việc cập nhật chiến dịch Zalo
 */
export class UpdateZaloCampaignDto {
  @ApiProperty({
    description: 'Tên của chiến dịch',
    example: 'Chiến dịch khuyến mãi tháng 7 - Cập nhật',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Mô tả của chiến dịch',
    example: 'Chiến dịch khuyến mãi dành cho khách hàng VIP - Cập nhật',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'ID của phân đoạn',
    example: 2,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  segmentId?: number;

  @ApiProperty({
    description: 'Thời điểm bắt đầu (Unix timestamp)',
    example: *************,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  scheduledAt?: number;

  @ApiProperty({
    description: 'Nội dung tin nhắn (chỉ dùng khi type là message)',
    type: ZaloCampaignMessageContentDto,
    required: false,
  })
  @IsObject()
  @IsOptional()
  messageContent?: ZaloCampaignMessageContentDto;

  @ApiProperty({
    description: 'Nội dung ZNS (chỉ dùng khi type là zns)',
    type: ZaloCampaignZnsContentDto,
    required: false,
  })
  @IsObject()
  @IsOptional()
  znsContent?: ZaloCampaignZnsContentDto;
}

/**
 * DTO cho phản hồi thông tin chiến dịch Zalo
 */
export class ZaloCampaignResponseDto {
  @ApiProperty({
    description: 'ID của chiến dịch',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của người dùng',
    example: 123,
  })
  userId: number;

  @ApiProperty({
    description: 'ID của Official Account',
    example: '*********',
  })
  oaId: string;

  @ApiProperty({
    description: 'Tên của chiến dịch',
    example: 'Chiến dịch khuyến mãi tháng 7',
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả của chiến dịch',
    example: 'Chiến dịch khuyến mãi dành cho khách hàng VIP',
    nullable: true,
  })
  description?: string;

  @ApiProperty({
    description: 'Loại chiến dịch',
    enum: ZaloCampaignType,
    example: ZaloCampaignType.MESSAGE,
  })
  type: ZaloCampaignType;

  @ApiProperty({
    description: 'ID của phân đoạn',
    example: 1,
  })
  segmentId: number;

  @ApiProperty({
    description: 'Trạng thái chiến dịch',
    enum: ZaloCampaignStatus,
    example: ZaloCampaignStatus.DRAFT,
  })
  status: ZaloCampaignStatus;

  @ApiProperty({
    description: 'Thời điểm bắt đầu (Unix timestamp)',
    example: *************,
    nullable: true,
  })
  scheduledAt?: number;

  @ApiProperty({
    description: 'Thời điểm bắt đầu thực tế (Unix timestamp)',
    example: *************,
    nullable: true,
  })
  startedAt?: number;

  @ApiProperty({
    description: 'Thời điểm kết thúc (Unix timestamp)',
    example: *************,
    nullable: true,
  })
  completedAt?: number;

  @ApiProperty({
    description: 'Nội dung tin nhắn (chỉ dùng khi type là message)',
    type: ZaloCampaignMessageContentDto,
    nullable: true,
  })
  messageContent?: ZaloCampaignMessageContentDto;

  @ApiProperty({
    description: 'Nội dung ZNS (chỉ dùng khi type là zns)',
    type: ZaloCampaignZnsContentDto,
    nullable: true,
  })
  znsContent?: ZaloCampaignZnsContentDto;

  @ApiProperty({
    description: 'Tổng số người nhận',
    example: 100,
  })
  totalRecipients: number;

  @ApiProperty({
    description: 'Số người đã gửi thành công',
    example: 95,
  })
  successCount: number;

  @ApiProperty({
    description: 'Số người gửi thất bại',
    example: 5,
  })
  failureCount: number;

  @ApiProperty({
    description: 'Thời điểm tạo (Unix timestamp)',
    example: *************,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật (Unix timestamp)',
    example: *************,
  })
  updatedAt: number;
}

/**
 * DTO cho việc truy vấn danh sách chiến dịch Zalo
 */
export class ZaloCampaignQueryDto {
  @ApiProperty({
    description: 'Tìm kiếm theo tên chiến dịch',
    example: 'khuyến mãi',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Lọc theo loại chiến dịch',
    enum: ZaloCampaignType,
    example: ZaloCampaignType.MESSAGE,
    required: false,
  })
  @IsEnum(ZaloCampaignType)
  @IsOptional()
  type?: ZaloCampaignType;

  @ApiProperty({
    description: 'Lọc theo trạng thái chiến dịch',
    enum: ZaloCampaignStatus,
    example: ZaloCampaignStatus.RUNNING,
    required: false,
  })
  @IsEnum(ZaloCampaignStatus)
  @IsOptional()
  status?: ZaloCampaignStatus;

  @ApiProperty({
    description: 'Số trang',
    example: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  page?: number = 1;

  @ApiProperty({
    description: 'Số lượng chiến dịch trên mỗi trang',
    example: 10,
    default: 10,
    required: false,
  })
  @IsOptional()
  limit?: number = 10;
}

/**
 * DTO cho việc thực thi chiến dịch Zalo
 */
export class ExecuteZaloCampaignDto {
  @ApiProperty({
    description: 'Hành động (start, cancel)',
    example: 'start',
  })
  @IsString()
  @IsNotEmpty()
  action: string;
}
