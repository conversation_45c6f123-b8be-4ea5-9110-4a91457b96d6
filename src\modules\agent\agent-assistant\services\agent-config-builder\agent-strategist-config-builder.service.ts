import { Injectable, Logger } from '@nestjs/common';
import {
  AgentRepository,
  AgentStrategyRepository,
  AgentStrategyUserRepository,
  SystemModelsRepository,
  SystemModelKeyLlmRepository,
  ModelRegistryRepository,
} from '../../repositories';
import {
  AgentStrategistConfig,
  StrategistModelConfig,
} from '../../schemas/agent-strategist.schema';
import { AgentUser, Agent, AgentStrategy } from '../../entities';
import {
  ModelTypeEnum,
  ProviderEnum,
  InputModalityEnum,
  OutputModalityEnum,
  SamplingParameterEnum,
  ModelFeatureEnum,
} from '../../enums';
import { IStrategyContentStep } from '../../interfaces/strategy-content-step.interface';

interface ApiKeyConfig {
  keyId: string;
  encryptedKey: string;
  provider: ProviderEnum;
  isActive: boolean;
}

interface ResolvedModel {
  modelId: string;
  modelRegistryId: string;
  type: ModelTypeEnum;
  apiKeys: ApiKeyConfig[];
}

/**
 * Agent Strategist Config Builder Service
 * Main service that orchestrates strategist config building with proper structure
 */
@Injectable()
export class AgentStrategistConfigBuilderService {
  private readonly logger = new Logger(
    AgentStrategistConfigBuilderService.name,
  );

  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly agentStrategyRepository: AgentStrategyRepository,
    private readonly agentStrategyUserRepository: AgentStrategyUserRepository,
    private readonly systemModelsRepository: SystemModelsRepository,
    private readonly systemModelKeyLlmRepository: SystemModelKeyLlmRepository,
    private readonly modelRegistryRepository: ModelRegistryRepository,
  ) {}

  /**
   * Build strategist agent configuration
   * @param strategyId Strategy ID (from agentUser.strategyId)
   * @param userId User ID for user-specific examples
   * @returns AgentStrategistConfig or null if strategy not found
   */
  async buildStrategistConfig(
    strategyId: string,
    userId: number,
  ): Promise<AgentStrategistConfig | null> {
    this.logger.debug(
      `Building strategist config for strategy ${strategyId}, user ${userId}`,
    );

    try {
      // 1. Load and validate core data
      const { agent, strategy, examples } = await this.loadCoreData(
        strategyId,
        userId,
      );

      // 2. Build model configuration
      const modelConfig = await this.buildModelConfig(strategy);

      // 3. Build final strategist configuration
      const strategistConfig: AgentStrategistConfig = {
        id: strategy.id,
        name: agent.name || `Strategy Agent ${strategy.id}`,
        description: 'Strategy Agent for Message Analysis',
        instruction:
          agent.instruction ||
          'You are a strategy agent that analyzes messages before main agent processing.',
        content: strategy.content || [],
        example: examples,
        model: modelConfig,
        trimming: {
          type: 'token' as any, // Will be properly typed from enum
          threshold: 8000, // Smaller threshold for strategist analysis
        },
        vectorStoreId: agent.vectorStoreId || null,
      };

      this.logger.debug(
        `Successfully built strategist config for strategy ${strategyId}`,
      );
      return strategistConfig;
    } catch (error) {
      this.logger.error(
        `Failed to build strategist config for strategy ${strategyId}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Load and validate core data (Agent and AgentStrategy entities)
   * @param strategyId This is AgentUser.strategyId which references AgentStrategyUser.id
   */
  private async loadCoreData(
    strategyId: string,
    userId?: number,
  ): Promise<{
    agent: Agent;
    strategy: AgentStrategy;
    examples: IStrategyContentStep[];
  }> {
    // 1. Find AgentStrategyUser by strategyId (AgentUser.strategyId → AgentStrategyUser.id)
    const agentStrategyUser =
      await this.agentStrategyUserRepository.findById(strategyId);
    if (!agentStrategyUser || !agentStrategyUser.agentsStrategyId) {
      throw new Error(
        `AgentStrategyUser ${strategyId} not found or has no agentsStrategyId`,
      );
    }

    // 2. Find AgentStrategy entity by agentsStrategyId
    const strategy = await this.agentStrategyRepository.findById(
      agentStrategyUser.agentsStrategyId,
    );
    if (!strategy) {
      throw new Error(
        `AgentStrategy ${agentStrategyUser.agentsStrategyId} not found`,
      );
    }

    // 3. Find Agent entity by strategy.id (AgentStrategy.id has 1-1 link with Agent.id)
    const agent = await this.agentRepository.findById(strategy.id);
    if (!agent) {
      throw new Error(`Agent ${strategy.id} not found`);
    }

    // 4. Load user-specific examples if userId provided
    let examples: IStrategyContentStep[] = strategy.exampleDefault || [];
    if (userId) {
      const userStrategy =
        await this.agentStrategyUserRepository.findByStrategyAndUser(
          agentStrategyUser.agentsStrategyId,
          userId,
        );
      if (
        userStrategy &&
        userStrategy.example &&
        userStrategy.example.length > 0
      ) {
        examples = userStrategy.example;
      }
    }

    return { agent, strategy, examples };
  }

  /**
   * Build model configuration for strategist agent
   */
  private async buildModelConfig(
    strategy: AgentStrategy,
  ): Promise<StrategistModelConfig> {
    if (!strategy.systemModelId) {
      throw new Error('Strategy must have a system model ID');
    }

    // Resolve system model configuration using the same pattern as agent-model-config.service.ts
    const resolvedModel = await this.resolveSystemModel(strategy.systemModelId);

    // Get model registry information
    const modelRegistry = await this.modelRegistryRepository.findById(
      resolvedModel.modelRegistryId,
    );
    if (!modelRegistry) {
      throw new Error('Model registry not found');
    }

    // Build strategist model configuration
    const modelConfig: StrategistModelConfig = {
      name: modelRegistry.modelNamePattern || resolvedModel.modelId,
      provider: resolvedModel.apiKeys[0]?.provider || ProviderEnum.OPENAI,
      inputModalities: modelRegistry.inputModalities,
      outputModalities: modelRegistry.outputModalities,
      samplingParameters: modelRegistry.samplingParameters,
      features: modelRegistry.features,
      parameters: {
        temperature: strategy.metadata?.temperature,
        topP: strategy.metadata?.top_p,
        topK: strategy.metadata?.top_k,
        maxTokens: strategy.metadata?.max_tokens,
        maxOutputTokens: strategy.metadata?.max_output_tokens,
      },
      pricing: modelRegistry.basePricing || { inputRate: 0, outputRate: 0 },
      type: ModelTypeEnum.SYSTEM,
      apiKeys: resolvedModel.apiKeys.map((key) => key.encryptedKey),
    };

    return modelConfig;
  }

  /**
   * Resolve system model configuration (copied from agent-model-config.service.ts)
   */
  private async resolveSystemModel(
    systemModelId: string,
  ): Promise<ResolvedModel> {
    // Get system model
    const systemModel =
      await this.systemModelsRepository.findById(systemModelId);
    if (!systemModel) {
      throw new Error('System model not found');
    }

    // Get multiple API keys via JOIN (load balancing)
    const systemKeysData =
      await this.systemModelKeyLlmRepository.findKeysByModelId(systemModelId);

    this.logger.debug(
      `Found ${systemKeysData.length} API keys for system model`,
      JSON.stringify(systemKeysData, null, 2),
    );
    if (systemKeysData.length === 0) {
      throw new Error('No API keys found for system model');
    }

    const apiKeys: ApiKeyConfig[] = systemKeysData.map((keyData) => ({
      keyId: keyData.llmKeyId,
      encryptedKey: keyData.apiKey,
      provider: (keyData.provider as ProviderEnum) || ProviderEnum.OPENAI,
      isActive: !keyData.keyDeletedAt,
    }));

    // Filter out inactive keys
    const activeKeys = apiKeys.filter((key) => key.isActive);
    if (activeKeys.length === 0) {
      throw new Error('No active API keys found for system model');
    }

    return {
      modelId: systemModel.modelId,
      modelRegistryId: systemModel.modelRegistryId as string,
      type: ModelTypeEnum.SYSTEM,
      apiKeys: activeKeys,
    };
  }

  /**
   * Check if strategist should be enabled for the given agent
   * @param agentUser Agent user entity
   * @returns boolean indicating if strategist should be enabled
   */
  async shouldEnableStrategist(agentUser: any): Promise<boolean> {
    // Check if agent has a strategy ID
    if (!agentUser.strategyId) {
      return false;
    }

    // Check if strategy is active by finding it in the repository
    const strategy = await this.agentStrategyRepository.findById(
      agentUser.strategyId,
    );
    return strategy !== null && strategy.using === true;
  }

  /**
   * Validate strategist configuration
   * @param config Strategist configuration to validate
   * @returns boolean indicating if configuration is valid
   */
  private validateStrategistConfig(config: AgentStrategistConfig): boolean {
    // Check required fields
    if (!config.id || !config.name || !config.instruction) {
      return false;
    }

    // Check model configuration
    if (!config.model || !config.model.name) {
      return false;
    }

    // Check trimming configuration
    if (!config.trimming || config.trimming.threshold <= 0) {
      return false;
    }

    // Validate instruction

    return true;
  }

  /**
   * Get strategist agent ID from agent user
   * @param agentUser Agent user entity
   * @returns Strategist agent ID or null
   */
  getStrategistAgentId(agentUser: any): string | null {
    return agentUser.strategyId || null;
  }
}
