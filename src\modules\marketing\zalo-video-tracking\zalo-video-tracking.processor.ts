import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName, ZaloVideoTrackingJobName } from '../../../queue/queue-name.enum';
import { ZaloVideoTrackingJobData } from '../../../queue/queue.types';
import { ZaloVideoTrackingService } from './zalo-video-tracking.service';

/**
 * Processor xử lý queue tracking video Zalo
 */
@Injectable()
@Processor(QueueName.ZALO_VIDEO_TRACKING, {
  concurrency: 5, // Xử lý tối đa 5 job đồng thời
  stalledInterval: 30 * 1000, // 30 giây
  maxStalledCount: 1,
})
export class ZaloVideoTrackingProcessor extends WorkerHost {
  private readonly logger = new Logger(ZaloVideoTrackingProcessor.name);

  constructor(
    private readonly zaloVideoTrackingService: ZaloVideoTrackingService,
  ) {
    super();
  }

  /**
   * X<PERSON> lý job từ queue
   * @param job Job chứa dữ liệu tracking video
   */
  async process(job: Job<ZaloVideoTrackingJobData, any, string>): Promise<void> {
    this.logger.log(
      `Bắt đầu xử lý job video tracking: ${job.id} - Token: ${job.data.token}`,
    );

    try {
      switch (job.name) {
        case ZaloVideoTrackingJobName.CHECK_VIDEO_STATUS:
          await this.processCheckVideoStatus(job);
          break;
        
        default:
          this.logger.warn(`Job name không được hỗ trợ: ${job.name}`);
          throw new Error(`Job name không được hỗ trợ: ${job.name}`);
      }

      this.logger.log(`Đã xử lý thành công job video tracking: ${job.id}`);
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý job video tracking: ${job.id} - ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý job kiểm tra trạng thái video
   * @param job Job chứa dữ liệu tracking
   */
  private async processCheckVideoStatus(job: Job<ZaloVideoTrackingJobData>): Promise<void> {
    const { token, accessToken, userId, integrationId, oaId, checkCount = 0 } = job.data;

    this.logger.debug(`Checking video status - Token: ${token}, Check count: ${checkCount}`);

    // Cập nhật progress
    await job.updateProgress(10);

    try {
      // Gọi service để check video status và cập nhật database
      const result = await this.zaloVideoTrackingService.checkAndUpdateVideoStatus(
        token,
        accessToken,
        userId,
        integrationId,
        oaId
      );

      await job.updateProgress(50);

      this.logger.debug(`Video status result:`, JSON.stringify(result, null, 2));

      // Kiểm tra trạng thái video
      if (result.status === 1) {
        // Video đã xử lý thành công
        this.logger.log(`Video đã xử lý thành công - Token: ${token}, Video ID: ${result.videoId}`);
        await job.updateProgress(100);
        return;
      } else if (result.status === 2 || result.status === 4 || result.status === 5) {
        // Video bị khóa, xử lý thất bại hoặc đã bị xóa - không cần check nữa
        this.logger.warn(`Video ở trạng thái cuối: ${result.status} - Token: ${token}`);
        await job.updateProgress(100);
        return;
      } else if (result.status === 3) {
        // Video vẫn đang xử lý
        const newCheckCount = checkCount + 1;
        const maxChecks = 20; // Tối đa check 20 lần (khoảng 10-15 phút)

        if (newCheckCount >= maxChecks) {
          this.logger.warn(`Đã check tối đa ${maxChecks} lần cho video - Token: ${token}`);
          await job.updateProgress(100);
          return;
        }

        // Tạo job mới để check lại sau một khoảng thời gian
        const delayMs = Math.min(30000 + (newCheckCount * 5000), 120000); // Tăng dần delay, tối đa 2 phút
        
        await job.updateProgress(80);

        // Tạo job mới với delay
        await this.zaloVideoTrackingService.scheduleNextCheck({
          ...job.data,
          checkCount: newCheckCount,
          delayMs,
        });

        this.logger.log(`Đã lên lịch check lần ${newCheckCount} cho video - Token: ${token}, Delay: ${delayMs}ms`);
      }

      await job.updateProgress(100);
    } catch (error) {
      this.logger.error(`Lỗi trong processCheckVideoStatus: ${error.message}`, error.stack);
      throw error;
    }
  }
}
