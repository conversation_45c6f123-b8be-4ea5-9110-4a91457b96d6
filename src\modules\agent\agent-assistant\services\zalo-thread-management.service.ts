import { Injectable, Logger } from '@nestjs/common';
import { ZaloConversationThreadRepository } from '../repositories/zalo-conversation-thread.repository';
import { ZaloConversationThread } from '../entities';
import { ThreadStatusEnum } from '../enums/thread-status.enum';

/**
 * Data structure for creating a new thread
 */
export interface ThreadCreationData {
  userId: number;
  zaloCustomerId: string;
  zaloUserId: string;
  oaId: string;
  status?: ThreadStatusEnum;
  firstMessageAt: number;
  lastMessageAt: number;
  createdAt?: number;
  updatedAt?: number;
}

/**
 * Service for managing Zalo conversation threads
 * Handles thread creation, updates, and lifecycle management
 */
@Injectable()
export class ZaloThreadManagementService {
  private readonly logger = new Logger(ZaloThreadManagementService.name);

  constructor(
    private readonly zaloThreadRepository: ZaloConversationThreadRepository,
  ) {}

  /**
   * Get existing thread or create new one
   * @param userId Internal user ID
   * @param customerId Customer ID
   * @param senderId Zalo sender ID
   * @param recipientId Zalo recipient ID (OA ID)
   * @returns Thread ID in format "zalo:recipientId:senderId"
   */
  async getOrCreateThread(
    userId: number,
    customerId: string,
    senderId: string,
    recipientId: string,
  ): Promise<string> {
    // Check for existing active thread
    const existingThread = await this.zaloThreadRepository.getActiveThread(
      userId,
      customerId,
      senderId,
      recipientId,
    );

    if (existingThread) {
      // Update last message timestamp
      await this.updateThreadLastMessage(existingThread.id);

      this.logger.debug(
        `Found existing thread: ${existingThread.getThreadId()}`,
      );
      return existingThread.getThreadId();
    }

    // Create new thread
    const now = Date.now();
    const threadData: ThreadCreationData = {
      userId,
      zaloCustomerId: customerId,
      zaloUserId: senderId,
      oaId: recipientId,
      status: ThreadStatusEnum.ACTIVE,
      firstMessageAt: now,
      lastMessageAt: now,
      createdAt: now,
      updatedAt: now,
    };

    const newThread = await this.createNewThread(threadData);

    this.logger.log(`Created new thread: ${newThread.getThreadId()}`);
    return newThread.getThreadId();
  }

  /**
   * Update thread's last message timestamp
   * @param senderId Zalo sender ID
   * @param recipientId Zalo recipient ID (OA ID)
   */
  async updateThreadLastMessage(threadId: string): Promise<void> {
    try {
      await this.zaloThreadRepository.updateLastMessage(threadId);
      this.logger.debug(`Updated last message timestamp for thread: ${threadId}`);
    } catch (error) {
      this.logger.error(
        `Failed to update thread last message: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Create a new conversation thread
   * @param threadData Thread creation data
   * @returns Created thread entity
   */
  private async createNewThread(
    threadData: ThreadCreationData,
  ): Promise<ZaloConversationThread> {
    try {
      const thread = await this.zaloThreadRepository.createThread(threadData);

      this.logger.debug(
        `Created new thread with ID: ${thread.id} for user ${threadData.zaloUserId}`,
      );

      return thread;
    } catch (error) {
      this.logger.error(
        `Failed to create new thread: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to create conversation thread: ${error.message}`);
    }
  }

  /**
   * Get thread by ID
   * @param threadId Thread UUID
   * @returns Thread entity or null if not found
   */
  async getThreadById(
    threadId: string,
  ): Promise<ZaloConversationThread | null> {
    try {
      return await this.zaloThreadRepository.findById(threadId);
    } catch (error) {
      this.logger.error(
        `Failed to get thread by ID: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Validate thread ownership
   * @param threadId Thread UUID
   * @param userId Internal user ID
   * @returns True if user owns the thread
   */
  async validateThreadOwnership(
    threadId: string,
    userId: number,
  ): Promise<boolean> {
    try {
      const thread = await this.getThreadById(threadId);
      return thread ? thread.userId === userId : false;
    } catch (error) {
      this.logger.error(
        `Failed to validate thread ownership: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }
}
