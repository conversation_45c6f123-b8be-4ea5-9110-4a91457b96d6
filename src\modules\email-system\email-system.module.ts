import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdminTemplateEmail } from './entities/admin-template-email.entity';
import { EmailSystemService } from './email-system.service';
import { EmailSystemProcessor } from './email-system.processor';
import { EmailSystemController } from './email-system.controller';
import { BullModule } from '@nestjs/bullmq';
import { QueueName } from '../../queue';

/**
 * Module xử lý hệ thống email
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([AdminTemplateEmail]),
    BullModule.registerQueue({
      name: QueueName.EMAIL_SYSTEM,
    }),
  ],
  providers: [EmailSystemService, EmailSystemProcessor],
  controllers: [EmailSystemController],
  exports: [EmailSystemService],
})
export class EmailSystemModule {}
