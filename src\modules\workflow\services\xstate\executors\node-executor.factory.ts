import { Injectable, Logger } from '@nestjs/common';
import { 
  INodeExecutor, 
  INodeExecutorFactory, 
  IExecutorRegistry 
} from './base/node-executor.interface';
import { NodeGroupEnum } from '../../../enums/node-group.enum';

/**
 * Registry để quản lý tất cả node executors
 */
@Injectable()
export class ExecutorRegistry implements IExecutorRegistry {
  private readonly logger = new Logger(ExecutorRegistry.name);
  readonly executors = new Map<string, INodeExecutor>();
  
  /**
   * Đăng ký executor cho các node types
   */
  register(executor: INodeExecutor): void {
    for (const nodeType of executor.supportedNodeTypes) {
      if (this.executors.has(nodeType)) {
        this.logger.warn(`Overriding existing executor for node type: ${nodeType}`);
      }
      
      this.executors.set(nodeType, executor);
      this.logger.debug(`Registered executor ${executor.executorName} for node type: ${nodeType}`);
    }
  }
  
  /**
   * Lấy executor cho node type
   */
  get(nodeType: string): INodeExecutor | undefined {
    return this.executors.get(nodeType);
  }
  
  /**
   * Kiểm tra xem có executor cho node type không
   */
  has(nodeType: string): boolean {
    return this.executors.has(nodeType);
  }
  
  /**
   * Hủy đăng ký executor
   */
  unregister(nodeType: string): void {
    if (this.executors.delete(nodeType)) {
      this.logger.debug(`Unregistered executor for node type: ${nodeType}`);
    }
  }
  
  /**
   * Lấy tất cả executors
   */
  getAll(): INodeExecutor[] {
    return Array.from(new Set(this.executors.values()));
  }
  
  /**
   * Clear tất cả executors
   */
  clear(): void {
    this.executors.clear();
    this.logger.debug('Cleared all executors from registry');
  }
  
  /**
   * Get executors by node group
   */
  getByGroup(nodeGroup: NodeGroupEnum): INodeExecutor[] {
    return this.getAll().filter(executor => executor.nodeGroup === nodeGroup);
  }
  
  /**
   * Get supported node types
   */
  getSupportedNodeTypes(): string[] {
    return Array.from(this.executors.keys());
  }
  
  /**
   * Get registry statistics
   */
  getStats(): {
    totalExecutors: number;
    totalNodeTypes: number;
    executorsByGroup: Record<string, number>;
  } {
    const executorsByGroup: Record<string, number> = {};
    
    for (const executor of this.getAll()) {
      const group = executor.nodeGroup;
      executorsByGroup[group] = (executorsByGroup[group] || 0) + 1;
    }
    
    return {
      totalExecutors: this.getAll().length,
      totalNodeTypes: this.executors.size,
      executorsByGroup,
    };
  }
}

/**
 * Factory để tạo và quản lý node executors
 */
@Injectable()
export class NodeExecutorFactory implements INodeExecutorFactory {
  private readonly logger = new Logger(NodeExecutorFactory.name);
  
  constructor(private readonly registry: ExecutorRegistry) {}
  
  /**
   * Tạo executor cho node type
   */
  createExecutor(nodeType: string): INodeExecutor | null {
    const executor = this.registry.get(nodeType);
    
    if (!executor) {
      this.logger.warn(`No executor found for node type: ${nodeType}`);
      return null;
    }
    
    if (!executor.canHandle(nodeType)) {
      this.logger.error(`Executor ${executor.executorName} cannot handle node type: ${nodeType}`);
      return null;
    }
    
    this.logger.debug(`Created executor ${executor.executorName} for node type: ${nodeType}`);
    return executor;
  }
  
  /**
   * Đăng ký executor mới
   */
  registerExecutor(executor: INodeExecutor): void {
    this.registry.register(executor);
    this.logger.log(`Registered executor: ${executor.executorName} v${executor.version}`);
  }
  
  /**
   * Hủy đăng ký executor
   */
  unregisterExecutor(nodeType: string): void {
    this.registry.unregister(nodeType);
    this.logger.log(`Unregistered executor for node type: ${nodeType}`);
  }
  
  /**
   * Lấy danh sách tất cả supported node types
   */
  getSupportedNodeTypes(): string[] {
    return this.registry.getSupportedNodeTypes();
  }
  
  /**
   * Kiểm tra xem có hỗ trợ node type không
   */
  isSupported(nodeType: string): boolean {
    return this.registry.has(nodeType);
  }
  
  /**
   * Get executor by node group
   */
  getExecutorsByGroup(nodeGroup: NodeGroupEnum): INodeExecutor[] {
    return this.registry.getByGroup(nodeGroup);
  }
  
  /**
   * Initialize tất cả executors
   */
  async initializeAll(): Promise<void> {
    const executors = this.registry.getAll();
    this.logger.log(`Initializing ${executors.length} executors...`);
    
    const initPromises = executors.map(async (executor) => {
      try {
        await executor.initialize();
        this.logger.debug(`Initialized executor: ${executor.executorName}`);
      } catch (error) {
        this.logger.error(`Failed to initialize executor ${executor.executorName}:`, error);
        throw error;
      }
    });
    
    await Promise.all(initPromises);
    this.logger.log('All executors initialized successfully');
  }
  
  /**
   * Destroy tất cả executors
   */
  async destroyAll(): Promise<void> {
    const executors = this.registry.getAll();
    this.logger.log(`Destroying ${executors.length} executors...`);
    
    const destroyPromises = executors.map(async (executor) => {
      try {
        await executor.destroy();
        this.logger.debug(`Destroyed executor: ${executor.executorName}`);
      } catch (error) {
        this.logger.warn(`Failed to destroy executor ${executor.executorName}:`, error);
      }
    });
    
    await Promise.all(destroyPromises);
    this.registry.clear();
    this.logger.log('All executors destroyed');
  }
  
  /**
   * Health check cho tất cả executors
   */
  async healthCheckAll(): Promise<{
    healthy: string[];
    unhealthy: string[];
    total: number;
  }> {
    const executors = this.registry.getAll();
    const healthy: string[] = [];
    const unhealthy: string[] = [];
    
    const healthPromises = executors.map(async (executor) => {
      try {
        const isHealthy = await executor.healthCheck();
        if (isHealthy) {
          healthy.push(executor.executorName);
        } else {
          unhealthy.push(executor.executorName);
        }
      } catch (error) {
        this.logger.error(`Health check failed for ${executor.executorName}:`, error);
        unhealthy.push(executor.executorName);
      }
    });
    
    await Promise.all(healthPromises);
    
    return {
      healthy,
      unhealthy,
      total: executors.length,
    };
  }
  
  /**
   * Get factory statistics
   */
  getFactoryStats(): {
    registry: ReturnType<ExecutorRegistry['getStats']>;
    health: {
      totalExecutors: number;
      lastHealthCheck?: Date;
    };
  } {
    return {
      registry: this.registry.getStats(),
      health: {
        totalExecutors: this.registry.getAll().length,
      },
    };
  }
  
  /**
   * Validate executor configuration
   */
  validateExecutor(executor: INodeExecutor): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Validate required properties
    if (!executor.nodeGroup) {
      errors.push('nodeGroup is required');
    }
    
    if (!executor.supportedNodeTypes || executor.supportedNodeTypes.length === 0) {
      errors.push('supportedNodeTypes must not be empty');
    }
    
    if (!executor.executorName) {
      errors.push('executorName is required');
    }
    
    if (!executor.version) {
      errors.push('version is required');
    }
    
    // Validate methods
    if (typeof executor.canHandle !== 'function') {
      errors.push('canHandle method is required');
    }
    
    if (typeof executor.execute !== 'function') {
      errors.push('execute method is required');
    }
    
    if (typeof executor.validateInput !== 'function') {
      errors.push('validateInput method is required');
    }
    
    if (typeof executor.validateOutput !== 'function') {
      errors.push('validateOutput method is required');
    }
    
    // Validate supported node types
    for (const nodeType of executor.supportedNodeTypes) {
      if (!executor.canHandle(nodeType)) {
        warnings.push(`Executor claims to support ${nodeType} but canHandle returns false`);
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }
  
  /**
   * Auto-register executors từ một array
   */
  autoRegisterExecutors(executors: INodeExecutor[]): {
    registered: string[];
    failed: Array<{ executor: string; error: string }>;
  } {
    const registered: string[] = [];
    const failed: Array<{ executor: string; error: string }> = [];
    
    for (const executor of executors) {
      try {
        const validation = this.validateExecutor(executor);
        
        if (!validation.isValid) {
          failed.push({
            executor: executor.executorName || 'Unknown',
            error: validation.errors.join(', '),
          });
          continue;
        }
        
        if (validation.warnings.length > 0) {
          this.logger.warn(`Warnings for executor ${executor.executorName}:`, validation.warnings);
        }
        
        this.registerExecutor(executor);
        registered.push(executor.executorName);
        
      } catch (error) {
        failed.push({
          executor: executor.executorName || 'Unknown',
          error: error.message,
        });
      }
    }
    
    this.logger.log(`Auto-registration completed: ${registered.length} registered, ${failed.length} failed`);
    
    return { registered, failed };
  }
}
