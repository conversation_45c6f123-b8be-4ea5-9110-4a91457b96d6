import { Injectable, Logger } from '@nestjs/common';
import {
  AgentUserRepository,
  UserModelRepository,
  UserKeyLlmRepository,
  ModelRegistryRepository,
  SystemModelsRepository,
  UserModelFineTuneRepository,
  SystemKeyLlmRepository,
  SystemModelKeyLlmRepository,
} from '../../repositories';
import {
  ProviderEnum,
  ModelFeatureEnum,
  InputModalityEnum,
  OutputModalityEnum,
  SamplingParameterEnum,
} from '../../enums/model-capabilities.enum';
import { ModelTypeEnum } from '../../enums/model-type.enum';
import {
  ApiKeyConfig,
  ModelMetadata,
  ModelConfig,
  ResolvedModel,
} from '../../interfaces';

/**
 * Agent Model Configuration Service
 *
 * Handles complex 3-tier model resolution: User > Fine-tune > System
 * Responsible for: "What AI model should this agent use and with which API keys?"
 */
@Injectable()
export class AgentModelConfigService {
  private readonly logger = new Logger(AgentModelConfigService.name);

  constructor(
    private readonly agentUserRepository: AgentUserRepository,
    private readonly userModelRepository: UserModelRepository,
    private readonly userKeyLlmRepository: UserKeyLlmRepository,
    private readonly modelRegistryRepository: ModelRegistryRepository,
    private readonly systemModelsRepository: SystemModelsRepository,
    private readonly userModelFineTuneRepository: UserModelFineTuneRepository,
    private readonly systemKeyLlmRepository: SystemKeyLlmRepository,
    private readonly systemModelKeyLlmRepository: SystemModelKeyLlmRepository,
  ) {}

  /**
   * Get complete model configuration for an agent
   */
  async getModelConfig(agentId: string, userId: number): Promise<ModelConfig> {
    // 1. Get AgentUser data (with validation)
    const agentUser = await this.agentUserRepository.findByIdAndUser(
      agentId,
      userId,
    );
    if (!agentUser) {
      throw new Error('Agent not found or access denied');
    }

    // 2. Apply 3-tier resolution logic
    const resolvedModel = await this.resolveModel(agentUser);

    // 3. Get model metadata from ModelRegistry
    const modelMetadata = await this.getModelMetadata(
      resolvedModel.modelRegistryId,
    );

    // 4. Return complete configuration
    return {
      modelId: resolvedModel.modelId,
      modelType: resolvedModel.type,
      apiKeys: resolvedModel.apiKeys,
      modelMetadata,
    };
  }

  /**
   * Get model configuration using specific model properties (no database lookup)
   * Use this when agentUser has already been fetched and validated
   */
  async getModelConfigFromValidatedAgent(modelProps: {
    userModelId?: string;
    modelFineTuneId?: string;
    systemModelId?: string;
    keyLlmId?: string;
  }): Promise<ModelConfig> {
    // 1. Resolve model using 3-tier logic (no validation needed)
    const resolvedModel = await this.resolveModel(modelProps);

    // 2. Get model metadata
    const modelMetadata = await this.getModelMetadata(resolvedModel.modelRegistryId);

    return {
      modelId: resolvedModel.modelId,
      modelType: resolvedModel.type,
      apiKeys: resolvedModel.apiKeys,
      modelMetadata,
    };
  }

  /**
   * Apply 3-tier model resolution logic
   * Priority: User Model > Fine-tune Model > System Model
   */
  private async resolveModel(modelProps: {
    userModelId?: string;
    modelFineTuneId?: string;
    systemModelId?: string;
    keyLlmId?: string;
  }): Promise<ResolvedModel> {
    // Validate that only one model type is configured
    const modelCount = [
      modelProps.userModelId,
      modelProps.modelFineTuneId,
      modelProps.systemModelId,
    ].filter(Boolean).length;

    if (modelCount === 0) {
      throw new Error('No model configuration found for agent');
    }

    if (modelCount > 1) {
      throw new Error(
        'Multiple model configurations found. Agent should have exactly one model configured.',
      );
    }

    // Priority 1: User Model (highest)
    if (modelProps.userModelId) {
      return await this.resolveUserModel(
        modelProps.userModelId,
        modelProps.keyLlmId,
      );
    }

    // Priority 2: Fine-tune Model (medium)
    if (modelProps.modelFineTuneId) {
      return await this.resolveFineTuneModel(modelProps.modelFineTuneId);
    }

    // Priority 3: System Model (lowest)
    if (modelProps.systemModelId) {
      return await this.resolveSystemModel(modelProps.systemModelId);
    }

    // This should never happen due to validation above
    throw new Error('No valid model configuration found');
  }

  /**
   * Resolve user model configuration (Priority 1)
   */
  private async resolveUserModel(
    userModelId: string,
    keyLlmId?: string,
  ): Promise<ResolvedModel> {
    // Get user model
    const userModel = await this.userModelRepository.findById(userModelId);
    if (!userModel) {
      throw new Error('User model not found');
    }

    // Get API key (single key from AgentUser.keyLlmId)
    let apiKeys: ApiKeyConfig[] = [];
    if (keyLlmId) {
      const userKey = await this.userKeyLlmRepository.findById(keyLlmId);
      if (userKey) {
        apiKeys = [
          {
            keyId: userKey.id,
            encryptedKey: userKey.apiKey,
            provider: userKey.provider || ProviderEnum.OPENAI,
            isActive: !userKey.deletedAt,
          },
        ];
      }
    }

    if (apiKeys.length === 0) {
      throw new Error('No valid API key found for user model');
    }

    return {
      modelId: userModel.modelId,
      modelRegistryId: userModel.modelRegistryId,
      type: ModelTypeEnum.USER,
      apiKeys,
    };
  }

  /**
   * Resolve fine-tune model configuration (Priority 2)
   */
  private async resolveFineTuneModel(
    modelFineTuneId: string,
  ): Promise<ResolvedModel> {
    // Get fine-tune model
    const fineTuneModel =
      await this.userModelFineTuneRepository.findById(modelFineTuneId);
    if (!fineTuneModel) {
      throw new Error('Fine-tune model not found');
    }

    // Get system API key (single key from fine-tune model)
    let apiKeys: ApiKeyConfig[] = [];
    if (fineTuneModel.llmKeyId) {
      const systemKey = await this.systemKeyLlmRepository.findById(
        fineTuneModel.llmKeyId,
      );
      if (systemKey) {
        apiKeys = [
          {
            keyId: systemKey.id,
            encryptedKey: systemKey.apiKey,
            provider: systemKey.provider || ProviderEnum.OPENAI,
            isActive: !systemKey.deletedAt,
          },
        ];
      }
    }

    if (apiKeys.length === 0) {
      throw new Error('No valid API key found for fine-tune model');
    }

    return {
      modelId: fineTuneModel.modelId,
      modelRegistryId: fineTuneModel.modelRegistryId,
      type: ModelTypeEnum.FINE_TUNE,
      apiKeys,
    };
  }

  /**
   * Resolve system model configuration (Priority 3)
   */
  private async resolveSystemModel(
    systemModelId: string,
  ): Promise<ResolvedModel> {
    // Get system model
    const systemModel =
      await this.systemModelsRepository.findById(systemModelId);
    if (!systemModel) {
      throw new Error('System model not found');
    }

    // Get multiple API keys via JOIN (load balancing)
    const systemKeysData =
      await this.systemModelKeyLlmRepository.findKeysByModelId(systemModelId);

    this.logger.debug(
      `Found ${systemKeysData.length} API keys for system model`,
      JSON.stringify(systemKeysData, null, 2),
    );
    if (systemKeysData.length === 0) {
      throw new Error('No API keys found for system model');
    }

    const apiKeys: ApiKeyConfig[] = systemKeysData.map((keyData) => ({
      keyId: keyData.llmKeyId,
      encryptedKey: keyData.apiKey,
      provider: (keyData.provider as ProviderEnum) || ProviderEnum.OPENAI,
      isActive: !keyData.keyDeletedAt,
    }));

    // Filter out inactive keys
    const activeKeys = apiKeys.filter((key) => key.isActive);
    if (activeKeys.length === 0) {
      throw new Error('No active API keys found for system model');
    }

    return {
      modelId: systemModel.modelId,
      modelRegistryId: systemModel.modelRegistryId,
      type: ModelTypeEnum.SYSTEM,
      apiKeys: activeKeys,
    };
  }

  /**
   * Get model metadata from ModelRegistry - REQUIRED for agent to function
   */
  private async getModelMetadata(
    modelRegistryId?: string,
  ): Promise<ModelMetadata> {
    if (!modelRegistryId) {
      throw new Error(
        'Model registry ID is required - agent cannot function without model metadata',
      );
    }

    const modelRegistry =
      await this.modelRegistryRepository.findById(modelRegistryId);
    if (!modelRegistry) {
      throw new Error(
        `Model registry not found for ID: ${modelRegistryId} - agent cannot function without model metadata`,
      );
    }

    return {
      provider: modelRegistry.provider,
      capabilities: modelRegistry.features || [],
      contextWindow: 8000, // TODO: Add contextWindow field to ModelRegistry entity
      maxTokens: 4000, // TODO: Add maxTokens field to ModelRegistry entity
      inputModalities: modelRegistry.inputModalities || [],
      outputModalities: modelRegistry.outputModalities || [],
      samplingParameters: modelRegistry.samplingParameters || [],
      basePricing: modelRegistry.basePricing,
      fineTunePricing: modelRegistry.fineTunePricing,
    };
  }
}
