import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto';

/**
 * Enum cho hướng tin nhắn
 */
export enum MessageDirection {
  INCOMING = 'incoming',
  OUTGOING = 'outgoing',
  ALL = 'all',
}

/**
 * Enum cho loại tin nhắn
 */
export enum MessageTypeFilter {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  TEMPLATE = 'template',
  ALL = 'all',
}

/**
 * DTO cho việc truy vấn danh sách tin nhắn
 */
export class MessageQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Lọc theo loại tin nhắn',
    enum: MessageTypeFilter,
    example: MessageTypeFilter.ALL,
    required: false,
  })
  @IsOptional()
  @IsEnum(MessageTypeFilter)
  messageType?: MessageTypeFilter;

  @ApiProperty({
    description: '<PERSON>ọ<PERSON> theo hướng tin nhắn',
    enum: MessageDirection,
    example: MessageDirection.ALL,
    required: false,
  })
  @IsOptional()
  @IsEnum(MessageDirection)
  direction?: MessageDirection;

  @ApiProperty({
    description: 'Tìm kiếm theo nội dung tin nhắn',
    example: 'xin chào',
    required: false,
  })
  @IsOptional()
  @IsString()
  content?: string;

  constructor() {
    super();
    this.sortBy = 'timestamp';
    this.sortDirection = SortDirection.DESC;
  }
}
