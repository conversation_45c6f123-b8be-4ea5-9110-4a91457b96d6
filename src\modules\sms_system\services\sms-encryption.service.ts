import { Injectable, Logger } from '@nestjs/common';

/**
 * Service mã hóa nội dung SMS OTP bằng Base64
 */
@Injectable()
export class SmsEncryptionService {
  private readonly logger = new Logger(SmsEncryptionService.name);

  /**
   * Mã hóa nội dung SMS bằng Base64
   * @param content Nội dung SMS cần mã hóa
   * @returns Nội dung đã được mã hóa Base64
   */
  encryptSmsContent(content: string): string {
    try {
      // Mã hóa nội dung thành Base64
      const encodedContent = Buffer.from(content, 'utf8').toString('base64');

      this.logger.debug(
        `Đã mã hóa Base64 nội dung SMS: ${content.substring(0, 20)}...`,
      );
      return encodedContent;
    } catch (error) {
      this.logger.error(
        `Lỗi khi mã hóa Base64 nội dung SMS: ${error.message}`,
        error.stack,
      );
      throw new Error(`<PERSON>h<PERSON>ng thể mã hóa Base64 nội dung SMS: ${error.message}`);
    }
  }

  /**
   * Giải mã nội dung SMS từ Base64
   * @param encryptedContent Nội dung đã được mã hóa Base64
   * @returns Nội dung gốc đã được giải mã
   */
  decryptSmsContent(encryptedContent: string): string {
    try {
      // Giải mã từ Base64
      const decodedContent = Buffer.from(encryptedContent, 'base64').toString(
        'utf8',
      );

      this.logger.debug(`Đã giải mã Base64 nội dung SMS thành công`);
      return decodedContent;
    } catch (error) {
      this.logger.error(
        `Lỗi khi giải mã Base64 nội dung SMS: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Không thể giải mã Base64 nội dung SMS: ${error.message}`,
      );
    }
  }

  /**
   * Mã hóa nội dung OTP bằng Base64
   * @param otpContent Nội dung OTP cần mã hóa
   * @param userId ID người dùng (tùy chọn, để log)
   * @returns Nội dung OTP đã được mã hóa Base64
   */
  encryptOtpContent(otpContent: string, userId?: number): string {
    try {
      const encryptedContent = this.encryptSmsContent(otpContent);

      this.logger.log(
        `Đã mã hóa Base64 nội dung OTP${userId ? ` cho user ${userId}` : ''}`,
      );
      return encryptedContent;
    } catch (error) {
      this.logger.error(
        `Lỗi khi mã hóa Base64 OTP: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Giải mã nội dung OTP từ Base64
   * @param encryptedOtpContent Nội dung OTP đã được mã hóa Base64
   * @param userId ID người dùng (tùy chọn, để log)
   * @returns Nội dung OTP gốc
   */
  decryptOtpContent(encryptedOtpContent: string, userId?: number): string {
    try {
      const decryptedContent = this.decryptSmsContent(encryptedOtpContent);

      this.logger.log(
        `Đã giải mã Base64 nội dung OTP${userId ? ` cho user ${userId}` : ''}`,
      );
      return decryptedContent;
    } catch (error) {
      this.logger.error(
        `Lỗi khi giải mã Base64 OTP: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Kiểm tra xem nội dung có được mã hóa Base64 hay không
   * @param content Nội dung cần kiểm tra
   * @returns true nếu nội dung có thể là Base64
   */
  isBase64Encoded(content: string): boolean {
    try {
      // Kiểm tra xem có thể decode Base64 không
      const decoded = Buffer.from(content, 'base64').toString('utf8');
      const reencoded = Buffer.from(decoded, 'utf8').toString('base64');

      // Nếu encode lại giống với input thì đây là Base64 hợp lệ
      return reencoded === content;
    } catch {
      return false;
    }
  }
}
