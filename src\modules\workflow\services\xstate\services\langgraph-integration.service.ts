import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { 
  NodeExecutionContext, 
  LangGraphExecutionResult,
  DetailedNodeExecutionResult 
} from '../types';
import { Node } from '../../../entities/node.entity';

/**
 * Agent execution request cho LangGraph
 */
export interface AgentExecutionRequest {
  /** ID của agent */
  agentId: string;
  
  /** Input data cho agent */
  inputData: any;
  
  /** Messages history nếu có */
  messages?: any[];
  
  /** Thread ID để continue conversation */
  threadId?: string;
  
  /** Configuration cho agent execution */
  config?: {
    /** Model configuration */
    model?: {
      name: string;
      temperature?: number;
      maxTokens?: number;
      topP?: number;
    };
    
    /** Tools available cho agent */
    tools?: string[];
    
    /** System prompt override */
    systemPrompt?: string;
    
    /** Execution timeout (ms) */
    timeout?: number;
    
    /** Có stream response không */
    streaming?: boolean;
    
    /** Checkpoint configuration */
    checkpoint?: {
      enabled: boolean;
      saveInterval?: number;
    };
  };
  
  /** Metadata từ workflow context */
  workflowMetadata?: {
    workflowId: string;
    executionId: string;
    nodeId: string;
    userId: number;
  };
}

/**
 * Agent execution response từ LangGraph
 */
export interface AgentExecutionResponse {
  /** Thành công hay không */
  success: boolean;
  
  /** Agent result data */
  result?: any;
  
  /** Final messages từ conversation */
  messages?: any[];
  
  /** Thread ID cho future conversations */
  threadId?: string;
  
  /** Checkpoint ID nếu có */
  checkpointId?: string;
  
  /** Error nếu có */
  error?: Error;
  
  /** Execution metadata */
  metadata?: {
    /** Thời gian thực thi (ms) */
    executionTime: number;
    
    /** Token usage */
    tokenUsage?: {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
      cost?: number;
    };
    
    /** Tools used */
    toolsUsed?: string[];
    
    /** Model used */
    modelUsed?: string;
    
    /** Checkpoint info */
    checkpoints?: Array<{
      id: string;
      timestamp: number;
      state: any;
    }>;
    
    /** Custom metadata từ agent */
    agentMetadata?: Record<string, any>;
  };
}

/**
 * Service để tích hợp với LangGraph system
 */
@Injectable()
export class LangGraphIntegrationService {
  private readonly logger = new Logger(LangGraphIntegrationService.name);
  
  constructor(
    private readonly eventEmitter: EventEmitter2,
  ) {}
  
  /**
   * Detect xem node có phải là agent node không
   */
  isAgentNode(node: Node): boolean {
    return !!(node.agentId && node.agentId.trim() !== '');
  }
  
  /**
   * Validate agent node configuration
   */
  async validateAgentNode(node: Node): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Check if has agentId
    if (!node.agentId) {
      errors.push('Agent node must have agentId');
    }
    
    // Validate agent exists (TODO: implement agent lookup)
    if (node.agentId) {
      const agentExists = await this.checkAgentExists(node.agentId);
      if (!agentExists) {
        errors.push(`Agent not found: ${node.agentId}`);
      }
    }
    
    // Check node parameters for agent-specific config
    if (node.parameters) {
      const params = node.parameters as any;
      
      // Validate model configuration
      if (params.model) {
        if (!params.model.name) {
          warnings.push('Model name not specified, will use default');
        }
        
        if (params.model.temperature && (params.model.temperature < 0 || params.model.temperature > 2)) {
          warnings.push('Temperature should be between 0 and 2');
        }
        
        if (params.model.maxTokens && params.model.maxTokens > 4096) {
          warnings.push('MaxTokens is very high, may cause performance issues');
        }
      }
      
      // Validate tools
      if (params.tools && Array.isArray(params.tools)) {
        for (const tool of params.tools) {
          const toolExists = await this.checkToolExists(tool);
          if (!toolExists) {
            warnings.push(`Tool not found: ${tool}`);
          }
        }
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }
  
  /**
   * Execute agent node thông qua LangGraph
   */
  async executeAgentNode(
    context: NodeExecutionContext
  ): Promise<DetailedNodeExecutionResult> {
    const startTime = Date.now();
    
    try {
      this.logger.log(`Executing agent node: ${context.node.id} with agent: ${context.node.agentId}`);
      
      // Validate agent node
      const validation = await this.validateAgentNode(context.node);
      if (!validation.isValid) {
        throw new Error(`Agent validation failed: ${validation.errors.join(', ')}`);
      }
      
      if (validation.warnings.length > 0) {
        this.logger.warn(`Agent warnings for ${context.node.id}:`, validation.warnings);
      }
      
      // Prepare agent execution request
      const agentRequest = await this.prepareAgentRequest(context);
      
      // Execute agent
      const agentResponse = await this.executeAgent(agentRequest);
      
      // Convert agent response to node execution result
      const result = this.convertAgentResponseToNodeResult(agentResponse, startTime);
      
      this.logger.log(`Agent node ${context.node.id} executed successfully in ${result.metadata?.executionTime}ms`);
      
      return result;
      
    } catch (error) {
      this.logger.error(`Agent node ${context.node.id} execution failed:`, error);
      
      return {
        success: false,
        error,
        metadata: {
          executionTime: Date.now() - startTime,
          logs: [`Agent execution failed: ${error.message}`],
        },
      };
    }
  }
  
  /**
   * Get agent execution status
   */
  async getAgentExecutionStatus(threadId: string): Promise<{
    isRunning: boolean;
    currentState?: string;
    progress?: number;
    lastActivity?: number;
  }> {
    try {
      // TODO: Implement với actual LangGraph API
      return {
        isRunning: false,
        lastActivity: Date.now(),
      };
    } catch (error) {
      this.logger.error(`Failed to get agent execution status for thread ${threadId}:`, error);
      return {
        isRunning: false,
      };
    }
  }
  
  /**
   * Cancel agent execution
   */
  async cancelAgentExecution(threadId: string): Promise<boolean> {
    try {
      this.logger.log(`Cancelling agent execution for thread: ${threadId}`);
      
      // TODO: Implement với actual LangGraph API
      // await this.langGraphService.cancelExecution(threadId);
      
      return true;
    } catch (error) {
      this.logger.error(`Failed to cancel agent execution for thread ${threadId}:`, error);
      return false;
    }
  }
  
  /**
   * Get agent conversation history
   */
  async getAgentConversationHistory(
    agentId: string, 
    threadId?: string,
    limit: number = 50
  ): Promise<any[]> {
    try {
      // TODO: Implement với actual LangGraph API
      return [];
    } catch (error) {
      this.logger.error(`Failed to get conversation history for agent ${agentId}:`, error);
      return [];
    }
  }
  
  /**
   * Create new agent thread
   */
  async createAgentThread(
    agentId: string,
    initialMessage?: string,
    metadata?: Record<string, any>
  ): Promise<string> {
    try {
      // TODO: Implement với actual LangGraph API
      const threadId = `thread_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      this.logger.debug(`Created new agent thread: ${threadId} for agent: ${agentId}`);
      
      return threadId;
    } catch (error) {
      this.logger.error(`Failed to create agent thread for agent ${agentId}:`, error);
      throw error;
    }
  }
  
  // Private helper methods
  
  private async prepareAgentRequest(context: NodeExecutionContext): Promise<AgentExecutionRequest> {
    const node = context.node;
    const params = (node.parameters as any) || {};
    
    // Extract previous messages từ context nếu có
    const previousMessages = this.extractPreviousMessages(context);
    
    // Prepare input data
    const inputData = this.prepareInputData(context.inputData, context.previousOutputs);
    
    return {
      agentId: node.agentId!,
      inputData,
      messages: previousMessages,
      threadId: params.threadId,
      config: {
        model: params.model,
        tools: params.tools,
        systemPrompt: params.systemPrompt,
        timeout: context.options?.timeout || 120000, // 2 minutes default
        streaming: params.streaming || false,
        checkpoint: {
          enabled: params.enableCheckpoints !== false,
          saveInterval: params.checkpointInterval || 30000,
        },
      },
      workflowMetadata: {
        workflowId: context.workflowId,
        executionId: context.executionId,
        nodeId: context.node.id,
        userId: context.userId,
      },
    };
  }
  
  private async executeAgent(request: AgentExecutionRequest): Promise<AgentExecutionResponse> {
    const startTime = Date.now();
    
    try {
      // TODO: Replace với actual LangGraph service call
      // const response = await this.langGraphService.executeAgent(request);
      
      // Mock response for now
      const mockResponse: AgentExecutionResponse = {
        success: true,
        result: {
          message: 'Mock agent response',
          data: request.inputData,
        },
        messages: [
          {
            role: 'user',
            content: JSON.stringify(request.inputData),
          },
          {
            role: 'assistant', 
            content: 'Mock agent response',
          },
        ],
        threadId: request.threadId || await this.createAgentThread(request.agentId),
        metadata: {
          executionTime: Date.now() - startTime,
          tokenUsage: {
            inputTokens: 100,
            outputTokens: 50,
            totalTokens: 150,
            cost: 0.001,
          },
          modelUsed: request.config?.model?.name || 'gpt-3.5-turbo',
          toolsUsed: request.config?.tools || [],
        },
      };
      
      // Emit events cho SSE
      if (request.workflowMetadata) {
        this.eventEmitter.emit('agent.execution.completed', {
          ...request.workflowMetadata,
          agentId: request.agentId,
          result: mockResponse.result,
          executionTime: mockResponse.metadata?.executionTime,
        });
      }
      
      return mockResponse;
      
    } catch (error) {
      this.logger.error(`Agent execution failed for agent ${request.agentId}:`, error);
      
      return {
        success: false,
        error,
        metadata: {
          executionTime: Date.now() - startTime,
        },
      };
    }
  }
  
  private convertAgentResponseToNodeResult(
    agentResponse: AgentExecutionResponse,
    startTime: number
  ): DetailedNodeExecutionResult {
    if (!agentResponse.success) {
      return {
        success: false,
        error: agentResponse.error,
        metadata: {
          executionTime: agentResponse.metadata?.executionTime || (Date.now() - startTime),
          logs: [`Agent execution failed: ${agentResponse.error?.message}`],
        },
      };
    }
    
    return {
      success: true,
      outputData: {
        result: agentResponse.result,
        messages: agentResponse.messages,
        threadId: agentResponse.threadId,
        checkpointId: agentResponse.checkpointId,
      },
      metadata: {
        executionTime: agentResponse.metadata?.executionTime || (Date.now() - startTime),
        tokenUsage: agentResponse.metadata?.tokenUsage,
        customMetrics: {
          modelUsed: agentResponse.metadata?.modelUsed,
          toolsUsed: agentResponse.metadata?.toolsUsed?.length || 0,
          checkpointsCreated: agentResponse.metadata?.checkpoints?.length || 0,
        },
        logs: [
          `Agent execution completed successfully`,
          `Model: ${agentResponse.metadata?.modelUsed}`,
          `Tokens: ${agentResponse.metadata?.tokenUsage?.totalTokens || 0}`,
        ],
      },
    };
  }
  
  private extractPreviousMessages(context: NodeExecutionContext): any[] {
    // Extract messages từ previous outputs nếu có
    const messages: any[] = [];
    
    for (const [nodeId, output] of context.previousOutputs.entries()) {
      if (output && output.messages && Array.isArray(output.messages)) {
        messages.push(...output.messages);
      }
    }
    
    return messages;
  }
  
  private prepareInputData(inputData: any, previousOutputs: Map<string, any>): any {
    // Combine input data với relevant previous outputs
    const combinedData = {
      ...inputData,
      previousOutputs: Object.fromEntries(previousOutputs.entries()),
    };
    
    return combinedData;
  }
  
  private async checkAgentExists(agentId: string): Promise<boolean> {
    try {
      // TODO: Implement actual agent lookup
      // const agent = await this.agentRepository.findById(agentId);
      // return !!agent;
      
      return true; // Mock implementation
    } catch (error) {
      this.logger.error(`Failed to check agent existence: ${agentId}`, error);
      return false;
    }
  }
  
  private async checkToolExists(toolName: string): Promise<boolean> {
    try {
      // TODO: Implement actual tool lookup
      // const tool = await this.toolRegistry.getTool(toolName);
      // return !!tool;
      
      return true; // Mock implementation
    } catch (error) {
      this.logger.error(`Failed to check tool existence: ${toolName}`, error);
      return false;
    }
  }
}
