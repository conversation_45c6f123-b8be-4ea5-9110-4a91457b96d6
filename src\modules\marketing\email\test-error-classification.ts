import {
  classifyEmailError,
  EmailErrorType,
  getEmailStatusFromError,
  EmailStatus,
} from '../enums/email-status.enum';

/**
 * Test script để kiểm tra email error classification
 */

// Test cases cho error classification
const testCases = [
  // Domain errors
  {
    error: 'Không thể tìm được miền example.com',
    expectedType: EmailErrorType.DOMAIN_NOT_FOUND,
    expectedStatus: EmailStatus.INVALID,
    description: 'Vietnamese domain not found error',
  },
  {
    error: 'domain not found',
    expectedType: EmailErrorType.DOMAIN_NOT_FOUND,
    expectedStatus: EmailStatus.INVALID,
    description: 'English domain not found error',
  },
  {
    error: 'domain does not exist',
    expectedType: EmailErrorType.DOMAIN_NOT_EXIST,
    expectedStatus: EmailStatus.INVALID,
    description: 'Domain does not exist error',
  },

  // Mailbox errors
  {
    error: 'user unknown',
    expectedType: EmailErrorType.MAILBOX_NOT_FOUND,
    expectedStatus: EmailStatus.INVALID,
    description: 'User unknown error',
  },
  {
    error: 'mailbox full',
    expectedType: EmailErrorType.MAILBOX_FULL,
    expectedStatus: EmailStatus.SOFT_BOUNCED,
    description: 'Mailbox full error',
  },
  {
    error: 'quota exceeded',
    expectedType: EmailErrorType.MAILBOX_FULL,
    expectedStatus: EmailStatus.SOFT_BOUNCED,
    description: 'Quota exceeded error',
  },

  // Format errors
  {
    error: 'invalid email address',
    expectedType: EmailErrorType.INVALID_EMAIL_FORMAT,
    expectedStatus: EmailStatus.INVALID,
    description: 'Invalid email format error',
  },
  {
    error: 'malformed address',
    expectedType: EmailErrorType.INVALID_EMAIL_FORMAT,
    expectedStatus: EmailStatus.INVALID,
    description: 'Malformed address error',
  },

  // Server errors
  {
    error: 'connection timeout',
    expectedType: EmailErrorType.CONNECTION_TIMEOUT,
    expectedStatus: EmailStatus.SOFT_BOUNCED,
    description: 'Connection timeout error',
  },
  {
    error: 'connection refused',
    expectedType: EmailErrorType.CONNECTION_REFUSED,
    expectedStatus: EmailStatus.BOUNCED,
    description: 'Connection refused error',
  },
  {
    error: 'authentication failed',
    expectedType: EmailErrorType.AUTHENTICATION_FAILED,
    expectedStatus: EmailStatus.BOUNCED,
    description: 'Authentication failed error',
  },

  // Policy errors
  {
    error: 'blocked by policy',
    expectedType: EmailErrorType.BLOCKED_BY_POLICY,
    expectedStatus: EmailStatus.BLOCKED,
    description: 'Blocked by policy error',
  },
  {
    error: 'spam detected',
    expectedType: EmailErrorType.SPAM_DETECTED,
    expectedStatus: EmailStatus.SPAM_COMPLAINT,
    description: 'Spam detected error',
  },
  {
    error: 'rate limited',
    expectedType: EmailErrorType.RATE_LIMITED,
    expectedStatus: EmailStatus.SOFT_BOUNCED,
    description: 'Rate limited error',
  },

  // SMTP codes
  {
    error: '5.1.1 User unknown',
    expectedType: EmailErrorType.PERMANENT_FAILURE,
    expectedStatus: EmailStatus.INVALID,
    description: 'SMTP 5xx permanent failure',
  },
  {
    error: '4.2.2 Mailbox full',
    expectedType: EmailErrorType.TEMPORARY_FAILURE,
    expectedStatus: EmailStatus.SOFT_BOUNCED,
    description: 'SMTP 4xx temporary failure',
  },

  // Unknown errors
  {
    error: 'some random error message',
    expectedType: EmailErrorType.UNKNOWN_ERROR,
    expectedStatus: EmailStatus.BOUNCED,
    description: 'Unknown error message',
  },
  {
    error: '',
    expectedType: EmailErrorType.UNKNOWN_ERROR,
    expectedStatus: EmailStatus.BOUNCED,
    description: 'Empty error message',
  },
];

/**
 * Chạy test cases
 */
function runTests() {
  console.log('🧪 Testing Email Error Classification...\n');

  let passedTests = 0;
  const totalTests = testCases.length;

  testCases.forEach((testCase, index) => {
    const actualType = classifyEmailError(testCase.error);
    const actualStatus = getEmailStatusFromError(actualType);

    const typeMatch = actualType === testCase.expectedType;
    const statusMatch = actualStatus === testCase.expectedStatus;
    const passed = typeMatch && statusMatch;

    if (passed) {
      passedTests++;
      console.log(`✅ Test ${index + 1}: ${testCase.description}`);
    } else {
      console.log(`❌ Test ${index + 1}: ${testCase.description}`);
      console.log(`   Error: "${testCase.error}"`);
      console.log(
        `   Expected Type: ${testCase.expectedType}, Got: ${actualType}`,
      );
      console.log(
        `   Expected Status: ${testCase.expectedStatus}, Got: ${actualStatus}`,
      );
    }
  });

  console.log(`\n📊 Test Results: ${passedTests}/${totalTests} passed`);

  if (passedTests === totalTests) {
    console.log('🎉 All tests passed!');
  } else {
    console.log('⚠️  Some tests failed. Please check the implementation.');
  }
}

/**
 * Test specific error messages
 */
function testSpecificErrors() {
  console.log('\n🔍 Testing Specific Error Messages...\n');

  const specificErrors = [
    'Thư của bạn chưa được gửi đến <EMAIL> vì không thể tìm được miền example.com',
    'The email could not be delivered because the domain example.com was not found',
    'SMTP Error: 550 5.1.1 User unknown',
    'SMTP Error: 452 4.2.2 Mailbox full',
    'Connection timed out while connecting to mail server',
    'Invalid email address format',
    'Message rejected due to spam content',
  ];

  specificErrors.forEach((error, index) => {
    const errorType = classifyEmailError(error);
    const emailStatus = getEmailStatusFromError(errorType);

    console.log(`${index + 1}. Error: "${error}"`);
    console.log(`   Type: ${errorType}`);
    console.log(`   Status: ${emailStatus}\n`);
  });
}

// Chạy tests
if (require.main === module) {
  runTests();
  testSpecificErrors();
}

export { runTests, testSpecificErrors };
