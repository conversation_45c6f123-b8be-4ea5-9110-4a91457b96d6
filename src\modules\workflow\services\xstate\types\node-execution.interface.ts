import { Node } from '../../../entities/node.entity';
import { NodeDefinition } from '../../../entities/node-definition.entity';
import { NodeGroupEnum } from '../../../enums/node-group.enum';

/**
 * <PERSON><PERSON><PERSON> quả từ LangGraph agent execution
 */
export interface LangGraphExecutionResult {
  /** Thành công hay không */
  success: boolean;
  
  /** Kết quả từ agent */
  result?: any;
  
  /** Messages từ agent conversation */
  messages?: any[];
  
  /** Lỗi nếu có */
  error?: Error;
  
  /** Metadata từ LangGraph */
  metadata?: {
    /** Thread ID trong LangGraph */
    threadId: string;
    
    /** Checkpoint ID để resume */
    checkpointId?: string;
    
    /** Thời gian thực thi (ms) */
    executionTime: number;
    
    /** Token usage cho AI models */
    tokenUsage?: {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
      cost?: number;
    };
    
    /** Agent configuration used */
    agentConfig?: {
      modelName: string;
      temperature: number;
      maxTokens: number;
    };
    
    /** Custom metadata từ agent */
    [key: string]: any;
  };
}

/**
 * Context cho node execution
 */
export interface NodeExecutionContext {
  /** ID của workflow execution */
  executionId: string;
  
  /** ID của workflow */
  workflowId: string;
  
  /** ID của user thực thi */
  userId: number;
  
  /** Node đang được thực thi */
  node: Node;
  
  /** Node definition */
  nodeDefinition: NodeDefinition;
  
  /** Dữ liệu đầu vào cho node */
  inputData: any;
  
  /** Dữ liệu từ các nodes trước đó */
  previousOutputs: Map<string, any>;
  
  /** Trigger data ban đầu */
  triggerData: any;
  
  /** Environment variables */
  env?: Record<string, string>;
  
  /** Custom headers cho HTTP requests */
  headers?: Record<string, string>;
  
  /** Execution options */
  options?: {
    timeout?: number;
    retryOnFail?: boolean;
    maxRetries?: number;
    retryDelay?: number;
    skipValidation?: boolean;
    enableSSE?: boolean;
  };
  
  /** Services có thể inject vào executor */
  services?: {
    logger?: any;
    httpClient?: any;
    database?: any;
    eventEmitter?: any;
    [serviceName: string]: any;
  };
}

/**
 * Base interface cho tất cả node execution results
 */
export interface BaseNodeExecutionResult {
  /** Thành công hay không */
  success: boolean;

  /** Lỗi nếu có */
  error?: Error;

  /** Có nên retry không */
  shouldRetry?: boolean;

  /** Có nên skip node này không */
  shouldSkip?: boolean;

  /** Có nên pause workflow không */
  shouldPause?: boolean;

  /** Next nodes để thực thi (override default flow) */
  nextNodes?: string[];

  /** Data để pass cho specific nodes */
  nodeSpecificData?: Record<string, any>;

  /** Base metadata có trong tất cả nodes */
  metadata: {
    /** Thời gian thực thi (ms) */
    executionTime: number;

    /** Memory usage (bytes) */
    memoryUsage?: number;

    /** Logs từ node execution */
    logs?: string[];

    /** Warnings nếu có */
    warnings?: string[];

    /** Debug information */
    debug?: Record<string, any>;
  };
}

/**
 * Enhanced node execution result với type discrimination
 * Sử dụng generic approach với optional node-specific metadata
 */
export interface DetailedNodeExecutionResult extends BaseNodeExecutionResult {
  /** Node type để type discrimination */
  nodeType?: 'HTTP_REQUEST' | 'IF_CONDITION' | 'SWITCH' | 'LOOP' | 'WAIT' | string;

  /** Output data từ node - sử dụng any để tương thích với existing interfaces */
  outputData?: any;

  /** Enhanced metadata với node-specific fields */
  metadata: BaseNodeExecutionResult['metadata'] & {
    /** HTTP-specific fields */
    statusCode?: number;
    responseTime?: number;
    requestSize?: number;
    responseSize?: number;
    retryCount?: number;
    httpMethod?: string;
    finalUrl?: string;

    /** Logic node fields */
    conditionsEvaluated?: number;
    evaluationTime?: number;
    branchTaken?: string;
    totalConditionGroups?: number;
    conditionResult?: boolean;

    /** Switch node fields */
    totalCases?: number;
    matchedCaseIndex?: number;
    defaultCaseUsed?: boolean;

    /** Loop node fields */
    loopType?: string;
    iterationsCompleted?: number;
    breakConditionMet?: boolean;
    maxIterationsReached?: boolean;
    breakReason?: string;

    /** Wait node fields */
    waitType?: string;
    actualWaitTime?: number;
    plannedWaitTime?: number;
    timeoutReached?: boolean;
    conditionMet?: boolean;

    /** Custom metrics - flexible structure */
    customMetrics?: Record<string, any>;
  };
}

/**
 * Configuration cho node execution
 */
export interface NodeExecutionConfig {
  /** Timeout cho node (ms) */
  timeout?: number;
  
  /** Có retry khi fail không */
  retryOnFail?: boolean;
  
  /** Số lần retry tối đa */
  maxRetries?: number;
  
  /** Thời gian chờ giữa các retry (ms) */
  retryDelay?: number;
  
  /** Retry strategy */
  retryStrategy?: 'fixed' | 'exponential' | 'linear';
  
  /** Có skip validation không */
  skipValidation?: boolean;
  
  /** Có gửi SSE events không */
  enableSSE?: boolean;
  
  /** Custom headers cho HTTP nodes */
  headers?: Record<string, string>;
  
  /** Environment variables */
  env?: Record<string, string>;
  
  /** Memory limit (bytes) */
  memoryLimit?: number;
  
  /** CPU limit (percentage) */
  cpuLimit?: number;
  
  /** Có cache kết quả không */
  enableCache?: boolean;
  
  /** Cache TTL (seconds) */
  cacheTTL?: number;
  
  /** Có log debug info không */
  enableDebug?: boolean;
  
  /** Custom configuration cho specific node types */
  nodeTypeConfig?: {
    /** HTTP node specific config */
    http?: {
      followRedirects?: boolean;
      maxRedirects?: number;
      validateSSL?: boolean;
      userAgent?: string;
    };
    
    /** AI node specific config */
    ai?: {
      modelName?: string;
      temperature?: number;
      maxTokens?: number;
      topP?: number;
      frequencyPenalty?: number;
      presencePenalty?: number;
    };
    
    /** Logic node specific config */
    logic?: {
      strictMode?: boolean;
      allowUndefined?: boolean;
    };
    
    /** Transform node specific config */
    transform?: {
      preserveTypes?: boolean;
      allowPartialTransform?: boolean;
    };
    
    /** Integration node specific config */
    integration?: {
      apiVersion?: string;
      rateLimitPerSecond?: number;
    };
  };
}

/**
 * Node execution status
 */
export type NodeExecutionStatus = 
  | 'pending'     // Chưa bắt đầu
  | 'waiting'     // Đang chờ dependencies
  | 'running'     // Đang thực thi
  | 'completed'   // Hoàn thành thành công
  | 'failed'      // Thất bại
  | 'skipped'     // Bị skip
  | 'cancelled'   // Bị cancel
  | 'retrying'    // Đang retry
  | 'paused';     // Bị pause

/**
 * Node execution priority
 */
export type NodeExecutionPriority = 'low' | 'normal' | 'high' | 'critical';

/**
 * Node execution mode
 */
export type NodeExecutionMode = 'sync' | 'async' | 'parallel';

/**
 * Interface cho node execution request
 */
export interface NodeExecutionRequest {
  /** Node execution context */
  context: NodeExecutionContext;
  
  /** Execution config */
  config?: NodeExecutionConfig;
  
  /** Priority */
  priority?: NodeExecutionPriority;
  
  /** Execution mode */
  mode?: NodeExecutionMode;
  
  /** Callback khi hoàn thành */
  onComplete?: (result: DetailedNodeExecutionResult) => void;
  
  /** Callback khi có progress update */
  onProgress?: (progress: number, message?: string) => void;
  
  /** Callback khi có error */
  onError?: (error: Error) => void;
}

/**
 * Helper functions để tạo execution objects
 */
export class NodeExecutionHelper {
  /**
   * Tạo execution context từ node và workflow data
   */
  static createContext(
    executionId: string,
    workflowId: string,
    userId: number,
    node: Node,
    nodeDefinition: NodeDefinition,
    inputData: any,
    previousOutputs: Map<string, any>,
    triggerData: any,
    options?: Partial<NodeExecutionContext['options']>
  ): NodeExecutionContext {
    return {
      executionId,
      workflowId,
      userId,
      node,
      nodeDefinition,
      inputData,
      previousOutputs,
      triggerData,
      options,
    };
  }
  
  /**
   * Tạo default config cho node type
   */
  static createDefaultConfig(nodeGroup: NodeGroupEnum): NodeExecutionConfig {
    const baseConfig: NodeExecutionConfig = {
      timeout: 30000, // 30 seconds
      retryOnFail: true,
      maxRetries: 3,
      retryDelay: 1000,
      retryStrategy: 'exponential',
      enableSSE: true,
      enableDebug: false,
    };
    
    switch (nodeGroup) {
      case NodeGroupEnum.HTTP:
        return {
          ...baseConfig,
          timeout: 60000, // 1 minute for HTTP
          nodeTypeConfig: {
            http: {
              followRedirects: true,
              maxRedirects: 5,
              validateSSL: true,
            },
          },
        };
        
      case NodeGroupEnum.AI:
        return {
          ...baseConfig,
          timeout: 120000, // 2 minutes for AI
          nodeTypeConfig: {
            ai: {
              temperature: 0.7,
              maxTokens: 1000,
            },
          },
        };
        
      case NodeGroupEnum.INTEGRATION:
        return {
          ...baseConfig,
          timeout: 90000, // 1.5 minutes for integrations
          nodeTypeConfig: {
            integration: {
              rateLimitPerSecond: 10,
            },
          },
        };
        
      default:
        return baseConfig;
    }
  }
}
