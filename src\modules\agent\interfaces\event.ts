export type Role = 'supervisor' | 'worker';

export interface SupervisorStreamTextTokenEvent {
  type: 'stream_text_token';
  /** Token streaming data with role and text content */
  data: {
    /** Role of the agent streaming the token */
    role: Role;
    /** The actual text token content */
    text: string;
  };
}

export interface SupervisorStreamToolTokenEvent {
  type: 'stream_tool_token';
  /** Tool token streaming data (currently unused) */
  data: {
    /** Role of the agent */
    role: Role;
    /** Tool token content */
    text: string;
  };
}

/** Supervisor bắt đầu gọi tool */
export interface SupervisorToolCallStartEvent {
  type: 'tool_call_start';
  /** Tool call start data */
  data: {
    /** Role of the agent calling the tool */
    role: Role;
    /** Name of the tool being called */
    toolName?: string;
  };
}

/** Supervisor kết thúc gọi tool */
export interface SupervisorToolCallEndEvent {
  type: 'tool_call_end';
  /** Tool call end data */
  data: {
    /** Role of the agent that finished the tool call */
    role: Role;
    /** Name of the tool that was called */
    toolName?: string;
  };
}

export interface SupervisorToolCallInterruptEvent {
  type: 'tool_call_interrupt';
  /** Tool call interrupt data */
  data: {
    /** Role information from the interrupt */
    role?: string;
    /** Prompt or message from the interrupt */
    prompt?: string;
    /** Additional interrupt data */
    [key: string]: any;
  };
}

/** Worker bắt đầu làm việc (chỉ emit một lần khi chạy) */
export interface WorkerWorkingEvent {
  type: 'stream_text_token';
  /** Worker token streaming data */
  data: {
    /** Role of the worker agent */
    role: Role;
    /** The text token content */
    text: string;
  };
}

export interface WorkerStreamToolTokenEvent {
  type: 'stream_tool_token';
  /** Worker tool token streaming data (currently unused) */
  data: {
    /** Role of the worker agent */
    role: Role;
    /** Tool token content */
    text: string;
  };
}

/** Worker bắt đầu gọi tool */
export interface WorkerToolCallStartEvent {
  type: 'tool_call_start';
  /** Worker tool call start data */
  data: {
    /** Role of the worker agent */
    role: Role;
    /** Name of the tool being called */
    toolName?: string;
  };
}

/** Worker kết thúc gọi tool */
export interface WorkerToolCallEndEvent {
  type: 'tool_call_end';
  /** Worker tool call end data */
  data: {
    /** Role of the worker agent */
    role: Role;
    /** Name of the tool that was called */
    toolName?: string;
  };
}

export interface WorkerToolCallInterruptEvent {
  type: 'tool_call_interrupt';
  /** Worker tool call interrupt data */
  data: {
    /** Role information from the interrupt */
    role?: string;
    /** Prompt or message from the interrupt */
    prompt?: string;
    /** Additional interrupt data */
    [key: string]: any;
  };
}

/** Event emitted when user's point balance is updated after token usage */
export interface UpdateRpointEvent {
  type: 'update_rpoint';
  data: {
    /** Cost in points for the LLM operation */
    rPointCost: number;
    /** Updated user balance after deduction */
    updatedBalance: number;
    /** Timestamp when the update occurred */
    timestamp?: number;
  };
}

/** Event emitted when each individual LLM completion finishes (not after entire streaming session) */
export interface LlmStreamEndEvent {
  type: 'llm_stream_end';
  data: {
    /** Role of the agent that completed the LLM call */
    role?: Role;
  };
}

/** Event emitted when the entire streaming session ends (final event to close SSE connection) */
export interface StreamSessionEndEvent {
  type: 'stream_session_end';
  data: {
    /** Reason for session ending */
    reason: string;
    /** Session duration in milliseconds */
    duration?: number;
    /** Timestamp when session ended */
    endTime?: number;
  };
}

/** Event emitted when an error occurs during streaming */
export interface StreamErrorEvent {
  type: 'stream_error';
  data: {
    /** Error message */
    error: string;
    /** Error name/type */
    errorName: string;
    /** Stack trace for debugging */
    stack?: string;
    /** Thread ID where error occurred */
    threadId: string;
    /** Run ID where error occurred */
    runId: string;
    /** Timestamp when error occurred */
    timestamp: number;
  };
}

/** Event emitted when a message is saved to the database */
export interface MessageCreatedEvent {
  type: 'message_created';
  data: {
    /** ID of the created message */
    message_id: string;
    /** Thread ID where message was created */
    thread_id: string;
    /** Role of the message sender */
    role: 'user' | 'assistant';
    /** Preview of the message content */
    content_preview: string;
  };
}

/** Union type cho tất cả các Transformed Events */
export type TransformedEvent =
  | SupervisorStreamTextTokenEvent
  | SupervisorStreamToolTokenEvent
  | SupervisorToolCallStartEvent
  | SupervisorToolCallEndEvent
  | SupervisorToolCallInterruptEvent
  | WorkerWorkingEvent
  | WorkerStreamToolTokenEvent
  | WorkerToolCallStartEvent
  | WorkerToolCallEndEvent
  | WorkerToolCallInterruptEvent
  | UpdateRpointEvent
  | LlmStreamEndEvent
  | StreamSessionEndEvent
  | StreamErrorEvent
  | MessageCreatedEvent;

/**
 * Type-safe callback function for emitting events to Redis Streams
 * Ensures all emitted events conform to the TransformedEvent union type
 *
 * @param event - The event to emit, must be one of the TransformedEvent variants
 * @returns Promise that resolves when the event is successfully emitted
 *
 * @example
 * ```typescript
 * const emitEvent: EmitEventCallback = async (event) => {
 *   await redisPublisher.emit(event);
 * };
 *
 * await emitEvent({
 *   type: 'stream_text_token',
 *   data: { role: 'supervisor', text: 'Hello world' }
 * });
 * ```
 */
export type EmitEventCallback = (event: TransformedEvent) => Promise<void>;
