import { Controller, Get, Param, Query, ParseIntPipe } from '@nestjs/common';
import { AudienceEmailStatusService } from '../services/audience-email-status.service';

/**
 * Controller để quản lý email status của audience
 */
@Controller('audience-email-status')
export class AudienceEmailStatusController {
  constructor(
    private readonly audienceEmailStatusService: AudienceEmailStatusService,
  ) {}

  /**
   * Lấy thống kê email status của user
   * @param userId ID của user
   * @returns Thống kê email status
   */
  @Get('stats/:userId')
  async getEmailStatusStats(@Param('userId', ParseIntPipe) userId: number) {
    const stats =
      await this.audienceEmailStatusService.getEmailStatusStats(userId);

    return {
      success: true,
      data: {
        ...stats,
        validRate:
          stats.total > 0
            ? ((stats.valid / stats.total) * 100).toFixed(2)
            : '0.00',
        invalidRate:
          stats.total > 0
            ? ((stats.invalid / stats.total) * 100).toFixed(2)
            : '0.00',
      },
    };
  }

  /**
   * <PERSON><PERSON>y danh sách audience có email không hợp lệ
   * @param userId ID của user
   * @param limit Số lượng kết quả trả về (default: 50)
   * @returns Danh sách audience có email invalid
   */
  @Get('invalid/:userId')
  async getInvalidEmailAudiences(
    @Param('userId', ParseIntPipe) userId: number,
    @Query('limit', ParseIntPipe) limit: number = 50,
  ) {
    const audiences =
      await this.audienceEmailStatusService.getInvalidEmailAudiences(userId);

    // Giới hạn số lượng kết quả
    const limitedAudiences = audiences.slice(0, limit);

    return {
      success: true,
      data: {
        total: audiences.length,
        limit,
        audiences: limitedAudiences.map((audience) => ({
          id: audience.id,
          email: audience.email,
          emailStatus: audience.emailStatus,
          emailErrorType: audience.emailErrorType,
          emailErrorDetails: audience.emailErrorDetails,
          lastEmailFailedAt: audience.lastEmailFailedAt,
          emailFailureCount: audience.emailFailureCount,
        })),
      },
    };
  }

  /**
   * Reset email status của một audience về VALID
   * @param audienceId ID của audience
   */
  @Get('reset/:audienceId')
  async resetEmailStatus(
    @Param('audienceId', ParseIntPipe) audienceId: number,
  ) {
    await this.audienceEmailStatusService.resetEmailStatus(audienceId);

    return {
      success: true,
      message: `Email status reset for audience ${audienceId}`,
    };
  }
}
