import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  Index
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsEnum, Length, IsNumber, Min, IsArray } from 'class-validator';

/**
 * Enum cho loại chiến dịch SMS admin
 */
export enum SmsCampaignAdminType {
  OTP = 'OTP',
  ADS = 'ADS',
  NOTIFICATION = 'NOTIFICATION'
}

/**
 * Enum cho trạng thái chiến dịch SMS admin
 */
export enum SmsCampaignAdminStatus {
  DRAFT = 'DRAFT',
  SCHEDULED = 'SCHEDULED',
  SENDING = 'SENDING',
  SENT = 'SENT',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

/**
 * Entity đại diện cho bảng sms_campaign_admin trong cơ sở dữ liệu
 * Bảng lưu thông tin chiến dịch SMS marketing cho admin
 */
@Entity('sms_campaign_admin')
@Index(['employeeId'])
@Index(['status'])
@Index(['campaignType'])
@Index(['scheduledAt'])
@Index(['createdAt'])
export class SmsCampaignAdmin {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của employee tạo campaign
   */
  @Column({ name: 'employee_id', type: 'integer' })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  employeeId: number;

  /**
   * Tên chiến dịch
   */
  @Column({ name: 'name', length: 255 })
  @IsNotEmpty()
  @Length(1, 255)
  name: string;

  /**
   * Mô tả chiến dịch
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  @IsOptional()
  description: string;

  /**
   * Thông tin segment (lưu trữ trực tiếp dưới dạng JSON)
   */
  @Column({ name: 'segment', type: 'jsonb', nullable: true })
  @IsOptional()
  segment: Record<string, any> | null;

  /**
   * Danh sách đối tượng nhận SMS (lưu trữ trực tiếp dưới dạng JSON)
   * Bao gồm: name, phoneNumber, countryCode
   */
  @Column({ name: 'audiences', type: 'jsonb', nullable: true })
  @IsOptional()
  @IsArray()
  audiences: Array<{
    name: string;
    phoneNumber: string;
    countryCode: number;
  }> | null;

  /**
   * Loại chiến dịch SMS (OTP, ADS, NOTIFICATION)
   */
  @Column({
    name: 'campaign_type',
    type: 'enum',
    enum: SmsCampaignAdminType,
    default: SmsCampaignAdminType.ADS
  })
  @IsEnum(SmsCampaignAdminType)
  campaignType: SmsCampaignAdminType;

  /**
   * Trạng thái campaign
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: SmsCampaignAdminStatus,
    default: SmsCampaignAdminStatus.DRAFT
  })
  @IsEnum(SmsCampaignAdminStatus)
  status: SmsCampaignAdminStatus;

  /**
   * Thời gian lên lịch gửi (Unix timestamp)
   */
  @Column({ name: 'scheduled_at', type: 'bigint', nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  scheduledAt: number | null;

  /**
   * Thời gian bắt đầu gửi (Unix timestamp)
   */
  @Column({ name: 'started_at', type: 'bigint', nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  startedAt: number | null;

  /**
   * Thời gian hoàn thành (Unix timestamp)
   */
  @Column({ name: 'completed_at', type: 'bigint', nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  completedAt: number | null;

  /**
   * Tổng số người nhận
   */
  @Column({ name: 'total_recipients', type: 'integer', default: 0 })
  @IsNumber()
  @Min(0)
  totalRecipients: number;

  /**
   * Số SMS đã gửi thành công
   */
  @Column({ name: 'sent_count', type: 'integer', default: 0 })
  @IsNumber()
  @Min(0)
  sentCount: number;

  /**
   * Số SMS gửi thất bại
   */
  @Column({ name: 'failed_count', type: 'integer', default: 0 })
  @IsNumber()
  @Min(0)
  failedCount: number;

  /**
   * Danh sách job IDs trong queue
   */
  @Column({ name: 'job_ids', type: 'jsonb', nullable: true })
  @IsOptional()
  @IsArray()
  jobIds: string[] | null;

  /**
   * Mã campaign từ hệ thống bên ngoài (FPT SMS, etc.)
   */
  @Column({ name: 'external_campaign_code', type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @Length(1, 255)
  externalCampaignCode: string | null;

  /**
   * Nội dung SMS (nếu không dùng template)
   */
  @Column({ name: 'content', type: 'text', nullable: true })
  @IsOptional()
  content: string | null;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => 'EXTRACT(EPOCH FROM NOW()) * 1000'
  })
  @IsNumber()
  @Min(0)
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => 'EXTRACT(EPOCH FROM NOW()) * 1000'
  })
  @IsNumber()
  @Min(0)
  updatedAt: number;

  /**
   * Thời gian xóa (Unix timestamp, soft delete)
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  deletedAt: number | null;

  /**
   * Cấu hình SMS server (lưu trữ trực tiếp dưới dạng JSON, bao gồm cả serverId)
   */
  @Column({ name: 'sms_server_config', type: 'jsonb', nullable: true })
  @IsOptional()
  smsServer: Record<string, any> | null;
}
