# Zalo Integration Services

Module này cung cấp các service để tích hợp với Zalo API, cho phép tương tác với Zalo Official Account (OA), gửi tin nhắn thông báo qua Zalo Notification Service (ZNS), và xử lý webhook từ Zalo.

## Tổng quan

Zalo là một nền tảng nhắn tin phổ biến tại Việt Nam với hơn 100 triệu người dùng. Zalo cung cấp nhiều API cho phép doanh nghiệp tương tác với khách hàng thông qua:

1. **Zalo Official Account (OA)**: T<PERSON><PERSON> kho<PERSON>n ch<PERSON>h thức của doanh nghiệp trên <PERSON>, cho phép gửi tin nhắn, tương tác với người dùng.
2. **Zalo Notification Service (ZNS)**: Dịch vụ gửi thông báo đến người dùng <PERSON>alo thông qua số điện thoại, ngay cả khi họ chưa theo dõi Official Account.
3. **Zalo Social API**: API cho phép tích hợp đăng nhập bằng Zalo và truy cập thông tin người dùng.

## Cài đặt

### Cấu hình môi trường

Thêm các biến môi trường sau vào file `.env`:

```env
# Zalo API
ZALO_APP_ID=your_app_id
ZALO_APP_SECRET=your_app_secret
ZALO_WEBHOOK_SECRET=your_webhook_secret
ZALO_WEBHOOK_URL=https://your-domain.com/api/zalo/webhook
```

### Đăng ký module

Module đã được đăng ký trong `ServicesModule`, nên bạn có thể sử dụng các service của nó trong bất kỳ module nào đã import `ServicesModule`.

## Các service

### ZaloService

Service cơ bản cung cấp các phương thức để tương tác với Zalo API.

```typescript
// Lấy access token cho Official Account
const accessToken = await zaloService.getOaAccessToken(
  appId,
  appSecret,
  code,
  redirectUri
);

// Lấy access token cho Social API
const socialToken = await zaloService.getSocialAccessToken(appId, appSecret, code, redirectUri);

// Làm mới Social access token
const refreshedToken = await zaloService.refreshSocialAccessToken(appId, refreshToken);

// Lấy thông tin người dùng từ Social API
const userInfo = await zaloService.getSocialUserInfo(accessToken, 'id,name,picture');

// Kiểm tra tính hợp lệ của access token
const isValid = await zaloService.validateAccessToken(accessToken);

// Tạo URL xác thực cho Official Account
const authUrl = zaloService.createOaAuthUrl(appId, redirectUri);

// Tạo URL xác thực cho Social API
const socialAuthUrl = zaloService.createSocialAuthUrl(appId, redirectUri);
```

### ZaloSocialService

Service chuyên biệt cho Zalo Social API V4, cung cấp các phương thức tối ưu để làm việc với User Access Token.

```typescript
// Tạo URL xác thực với scope tùy chỉnh
const authUrl = zaloSocialService.createAuthUrl(
  appId,
  redirectUri,
  'id,name,picture,gender,birthday',
  'custom_state'
);

// Lấy access token từ authorization code
const tokens = await zaloSocialService.getAccessToken(appId, appSecret, code, redirectUri);

// Làm mới access token
const newTokens = await zaloSocialService.refreshAccessToken(appId, refreshToken);

// Lấy thông tin cơ bản của người dùng
const userInfo = await zaloSocialService.getUserInfo(accessToken);

// Lấy thông tin chi tiết của người dùng
const detailedInfo = await zaloSocialService.getDetailedUserInfo(accessToken);

// Kiểm tra và làm mới token tự động nếu cần
const validTokens = await zaloSocialService.ensureValidToken(appId, accessToken, refreshToken);

// Các phương thức an toàn (không throw exception)
const userInfo = await zaloSocialService.getUserInfoSafely(accessToken);
const newTokens = await zaloSocialService.refreshTokenSafely(appId, refreshToken);
```

### ZaloOaService

Service cung cấp các phương thức để tương tác với Zalo Official Account API.

```typescript
// Lấy thông tin cơ bản Official Account
const oaInfo = await zaloOaService.getOaInfo(accessToken);

// Lấy thông tin chi tiết Official Account (bao gồm số follower, thông tin liên hệ, ví ZCA)
const detailedOaInfo = await zaloOaService.getDetailedOaInfo(accessToken);

// Kiểm tra hạn mức tin nhắn OA
const quota = await zaloOaService.getMessageQuota(accessToken);
console.log(`Còn lại: ${quota.remaining_quota}/${quota.daily_quota} tin nhắn`);

// Gửi tin nhắn văn bản
const result = await zaloOaService.sendTextMessage(
  accessToken,
  userId,
  'Xin chào từ RedAI!'
);

// Thiết lập webhook
await zaloOaService.setWebhook(accessToken, 'https://your-domain.com/api/zalo/webhook');
```

### ZaloZnsService

Service cung cấp các phương thức để tương tác với Zalo Notification Service (ZNS).

```typescript
// Gửi tin nhắn ZNS
const result = await zaloZnsService.sendZnsMessage(accessToken, {
  phone: '**********',
  template_id: 'your_template_id',
  template_data: {
    customer_name: 'Nguyễn Văn A',
    order_id: 'ORD123456',
    amount: '100,000 VND'
  }
});

// Lấy danh sách template ZNS
const templates = await zaloZnsService.getZnsTemplates(accessToken);
```

### ZaloWebhookService

Service cung cấp các phương thức để xử lý webhook từ Zalo với type-safe event handling.

```typescript
import {
  ZaloWebhookEvent,
  isUserMessageEvent,
  isInteractionEvent,
  isFollowEvent,
  ZaloWebhookEventType
} from '@/shared/services/zalo';

// Xác thực webhook
const isValid = zaloWebhookService.verifyWebhook(timestamp, mac, body);

// Xử lý sự kiện webhook với type guards
await zaloWebhookService.processWebhookEvent(event);

// Xử lý cụ thể từng loại sự kiện
if (isUserMessageEvent(event)) {
  switch (event.event_name) {
    case ZaloWebhookEventType.USER_SEND_TEXT:
      console.log(`User sent: ${event.data.message.text}`);
      break;
    case ZaloWebhookEventType.USER_SEND_IMAGE:
      console.log(`User sent image: ${event.data.message.attachments[0].payload.url}`);
      break;
  }
} else if (isInteractionEvent(event)) {
  if (event.event_name === ZaloWebhookEventType.USER_REACTION) {
    console.log(`User reacted: ${event.data.reaction.reaction}`);
  }
} else if (isFollowEvent(event)) {
  console.log(`User ${event.data.follower.id} ${event.event_name}`);
}
```

### ZaloAgentService

Service cung cấp các phương thức để tích hợp Zalo với Agent trong hệ thống.

```typescript
// Gửi tin nhắn từ agent đến người dùng Zalo
const result = await zaloAgentService.sendAgentMessageToZalo(
  accessToken,
  userId,
  'Xin chào, tôi là trợ lý ảo của bạn!'
);

// Gửi tin nhắn phức tạp từ agent đến người dùng Zalo
const result = await zaloAgentService.sendAgentComplexMessageToZalo(
  accessToken,
  userId,
  {
    type: 'image',
    url: 'https://example.com/image.jpg',
    caption: 'Hình ảnh từ agent'
  }
);

// Xử lý tin nhắn từ người dùng Zalo và chuyển tiếp đến agent
await zaloAgentService.processZaloMessageToAgent(
  accessToken,
  userId,
  'Tôi cần hỗ trợ',
  agentId
);
```

### ZaloAnonymousService

Service cung cấp các phương thức để gửi tin nhắn đến người dùng ẩn danh (chưa quan tâm Official Account).

```typescript
// Gửi tin nhắn văn bản đến người dùng ẩn danh
const result = await zaloAnonymousService.sendAnonymousTextMessage(
  accessToken,
  userId,
  'Xin chào! Cảm ơn bạn đã quan tâm đến dịch vụ của chúng tôi.'
);

// Gửi tin nhắn ảnh đến người dùng ẩn danh
const result = await zaloAnonymousService.sendAnonymousImageMessage(
  accessToken,
  userId,
  'https://example.com/promotion.jpg',
  'Khuyến mãi đặc biệt dành cho bạn!'
);

// Gửi tin nhắn file đến người dùng ẩn danh
const result = await zaloAnonymousService.sendAnonymousFileMessage(
  accessToken,
  userId,
  'https://example.com/catalog.pdf',
  'catalog.pdf',
  'Catalog sản phẩm mới nhất'
);

// Gửi tin nhắn sticker đến người dùng ẩn danh
const result = await zaloAnonymousService.sendAnonymousStickerMessage(
  accessToken,
  userId,
  'sticker_id_123',
  'Cảm ơn bạn!'
);
```

### ZaloMessageService

Service cung cấp các phương thức để xử lý các loại tin nhắn đặc biệt như reaction và miniapp.

```typescript
// Thả biểu tượng cảm xúc vào tin nhắn
const result = await zaloMessageService.sendReaction(
  accessToken,
  messageId,
  'heart'
);

// Gửi tin nhắn miniapp
const result = await zaloMessageService.sendMiniAppMessage(
  accessToken,
  userId,
  'miniapp_id_123',
  'Mở ứng dụng đặt hàng',
  'Đặt hàng nhanh chóng và tiện lợi',
  'https://example.com/miniapp-icon.jpg',
  { product_id: '123', category: 'food' }
);

// Xóa biểu tượng cảm xúc khỏi tin nhắn
const result = await zaloMessageService.removeReaction(
  accessToken,
  messageId
);

// Lấy danh sách các biểu tượng cảm xúc có sẵn
const reactions = await zaloMessageService.getAvailableReactions(accessToken);

// Lấy thông tin miniapp
const appInfo = await zaloMessageService.getMiniAppInfo(accessToken, 'miniapp_id_123');
```

### ZaloUserManagementService

Service cung cấp các phương thức để quản lý thông tin người dùng, nhãn và dữ liệu tùy biến của Zalo Official Account.

```typescript
// Gắn nhãn cho người dùng
await zaloUserManagementService.assignLabelToUser(
  accessToken,
  'user_*********',
  'label_*********'
);

// Gỡ nhãn khỏi người dùng
await zaloUserManagementService.removeLabelFromUser(
  accessToken,
  'user_*********',
  'label_*********'
);

// Lấy danh sách nhãn
const labels = await zaloUserManagementService.getLabels(accessToken, 0, 20);

// Tạo nhãn mới
const newLabel = await zaloUserManagementService.createLabel(accessToken, {
  label_name: 'Khách hàng VIP',
  description: 'Khách hàng có giá trị cao',
  color: '#FF0000'
});

// Lấy danh sách người dùng
const users = await zaloUserManagementService.getUsers(accessToken, 0, 20, 'label_*********');

// Lấy chi tiết người dùng
const userDetail = await zaloUserManagementService.getUserDetail(accessToken, 'user_*********');

// Cập nhật thông tin người dùng
await zaloUserManagementService.updateUserDetail(accessToken, {
  user_id: 'user_*********',
  display_name: 'Nguyễn Văn B',
  note: 'Khách hàng thân thiết'
});

// Lấy thông tin tùy biến của người dùng
const customInfo = await zaloUserManagementService.getUserCustomInfo(accessToken, 'user_*********');

// Cập nhật thông tin tùy biến
await zaloUserManagementService.updateUserCustomInfo(accessToken, {
  user_id: 'user_*********',
  custom_fields: {
    company: 'XYZ Corp',
    position: 'Senior Manager',
    interests: ['technology', 'music']
  }
});

// Tìm kiếm người dùng
const searchResults = await zaloUserManagementService.searchUsers(
  accessToken,
  'Nguyễn Văn',
  0,
  20
);
```

### ZaloMessageManagementService

Service cung cấp các phương thức để quản lý tin nhắn, kiểm tra hạn mức và upload file.

```typescript
// Kiểm tra hạn mức gửi tin nhắn
const quota = await zaloMessageManagementService.checkMessageQuota(
  accessToken,
  userId
);
console.log(`Daily remaining: ${quota.daily_remaining}`);
console.log(`Monthly remaining: ${quota.monthly_remaining}`);

// Lấy tin nhắn gần nhất
const recentMessages = await zaloMessageManagementService.getRecentMessages(
  accessToken,
  userId,
  20
);

// Lấy tin nhắn trong hội thoại với phân trang
const conversationMessages = await zaloMessageManagementService.getConversationMessages(
  accessToken,
  userId,
  { count: 20, offset: 0 }
);

// Upload hình ảnh
const imageFile = {
  filename: 'product.jpg',
  data: imageBuffer,
  mimetype: 'image/jpeg',
  size: imageBuffer.length
};
const imageResult = await zaloMessageManagementService.uploadImage(accessToken, imageFile);

// Upload file PDF
const pdfFile = {
  filename: 'catalog.pdf',
  data: pdfBuffer,
  mimetype: 'application/pdf',
  size: pdfBuffer.length
};
const pdfResult = await zaloMessageManagementService.uploadFile(accessToken, pdfFile);

// Upload ảnh GIF
const gifFile = {
  filename: 'animation.gif',
  data: gifBuffer,
  mimetype: 'image/gif',
  size: gifBuffer.length
};
const gifResult = await zaloMessageManagementService.uploadGif(accessToken, gifFile);

// Đánh dấu tin nhắn đã đọc
await zaloMessageManagementService.markAsRead(accessToken, userId, messageId);

// Tìm kiếm tin nhắn
const searchResults = await zaloMessageManagementService.searchMessages(
  accessToken,
  'đơn hàng',
  userId,
  { count: 10, offset: 0 }
);

// Lấy thống kê tin nhắn
const statistics = await zaloMessageManagementService.getMessageStatistics(
  accessToken,
  '2024-01-01',
  '2024-01-31'
);
```

### ZaloCallService

Service cung cấp các phương thức để quản lý tính năng gọi thoại của Zalo Official Account.

```typescript
// Gửi yêu cầu cấp quyền gọi thoại
const permissionRequest = {
  user_id: '*********',
  reason: 'Yêu cầu cấp quyền gọi thoại để hỗ trợ tốt hơn',
  metadata: { department: 'support', priority: 'high' }
};
const result = await zaloCallService.requestCallPermission(accessToken, permissionRequest);

// Kiểm tra trạng thái cấp quyền gọi
const status = await zaloCallService.checkCallPermission(accessToken, '*********');
console.log(status.permission_status); // 'granted', 'denied', 'pending'

// Tạo link gọi thoại
const callRequest = {
  user_id: '*********',
  call_type: 'audio', // hoặc 'video'
  agent_id: 'agent_001',
  branch_id: 'branch_001',
  expires_in: 3600 // 1 giờ
};
const callLink = await zaloCallService.createCallLink(accessToken, callRequest);
console.log(callLink.call_link); // Link để thực hiện cuộc gọi

// Lấy thông tin agent
const agentInfo = await zaloCallService.getAgentInfo(accessToken, 'agent_001');
const allAgents = await zaloCallService.getAgentInfo(accessToken);

// Lấy thông tin branch
const branchInfo = await zaloCallService.getBranchInfo(accessToken, 'branch_001');
const allBranches = await zaloCallService.getBranchInfo(accessToken);

// Lấy lịch sử cuộc gọi
const callHistory = await zaloCallService.getCallHistory(accessToken, {
  userId: '*********',
  agentId: 'agent_001',
  fromTime: 1625097600000,
  toTime: 1625184000000,
  limit: 20,
  offset: 0
});

// Lấy thống kê cuộc gọi
const statistics = await zaloCallService.getCallStatistics(
  accessToken,
  '2024-01-01',
  '2024-01-31',
  'agent_001',
  'branch_001'
);

// Cập nhật trạng thái agent
await zaloCallService.updateAgentStatus(accessToken, 'agent_001', 'online');

// Kết thúc cuộc gọi
await zaloCallService.endCall(accessToken, 'call_*********', 'Kết thúc cuộc gọi');

// Lấy danh sách mã lỗi
const errorCodes = zaloCallService.getCallErrorCodes();
```

### ZaloContentService

Service cung cấp các phương thức để quản lý nội dung dạng bài viết của Zalo Official Account.

```typescript
// Tạo nội dung dạng bài viết
const contentRequest = {
  title: 'Khuyến mãi đặc biệt tháng 12',
  description: 'Chương trình khuyến mãi lớn nhất trong năm',
  content: '<p>Nội dung bài viết với <strong>định dạng HTML</strong></p>',
  cover_photo_url: 'https://example.com/cover-photo.jpg',
  status: 'draft',
  tags: ['khuyến mãi', 'sale', 'giảm giá']
};
const content = await zaloContentService.createContent(accessToken, contentRequest);

// Upload video cho nội dung
const videoUploadRequest = {
  filename: 'promotional-video.mp4',
  file_size: ********, // 50MB
  mime_type: 'video/mp4',
  description: 'Video quảng cáo sản phẩm mới'
};
const uploadInfo = await zaloContentService.uploadVideo(accessToken, videoUploadRequest);

// Upload file video thực tế
const fileInfo = {
  filename: 'promotional-video.mp4',
  data: videoBuffer,
  mimetype: 'video/mp4',
  size: ********
};
const uploadResult = await zaloContentService.uploadVideoFile(
  uploadInfo.upload_url,
  uploadInfo.upload_token,
  fileInfo
);

// Kiểm tra tiến trình tạo nội dung
const process = await zaloContentService.checkContentProcess(accessToken, 'process_*********');
console.log(`Progress: ${process.progress}%`);

// Lấy chi tiết nội dung
const contentDetail = await zaloContentService.getContentDetail(accessToken, 'content_*********');

// Lấy danh sách nội dung
const contentList = await zaloContentService.getContentList(accessToken, {
  status: 'published',
  limit: 20,
  offset: 0,
  tags: ['khuyến mãi']
});

// Cập nhật nội dung
const updateRequest = {
  title: 'Tiêu đề đã cập nhật',
  description: 'Mô tả đã cập nhật'
};
await zaloContentService.updateContent(accessToken, 'content_*********', updateRequest);

// Xuất bản nội dung
await zaloContentService.publishContent(accessToken, 'content_*********');

// Lên lịch xuất bản
const publishTime = Math.floor(Date.now() / 1000) + 3600; // 1 giờ sau
await zaloContentService.publishContent(accessToken, 'content_*********', publishTime);

// Hủy xuất bản nội dung
await zaloContentService.unpublishContent(accessToken, 'content_*********');

// Xóa nội dung
await zaloContentService.deleteContent(accessToken, 'content_*********');

// Lấy thống kê nội dung
const statistics = await zaloContentService.getContentStatistics(accessToken, 'content_*********');

// Tìm kiếm nội dung
const searchResults = await zaloContentService.searchContent(accessToken, 'khuyến mãi', {
  status: 'published',
  limit: 10,
  tags: ['sale']
});

// Lấy danh sách tags
const tags = await zaloContentService.getTags(accessToken);

// Kiểm tra trạng thái video
const videoStatus = await zaloContentService.getVideoStatus(accessToken, 'video_*********');

// ==================== VIDEO CONTENT APIs ====================

// Tạo nội dung dạng video
const videoContentRequest = {
  title: 'Video giới thiệu sản phẩm mới',
  description: 'Video chi tiết về sản phẩm mới nhất',
  video_url: 'https://video.zalo.me/v1/video_*********.mp4',
  thumbnail_url: 'https://example.com/thumbnail.jpg',
  status: 'draft',
  tags: ['sản phẩm', 'giới thiệu', 'video']
};
const videoContent = await zaloContentService.createVideoContent(accessToken, videoContentRequest);

// Lấy chi tiết nội dung video
const videoContentDetail = await zaloContentService.getVideoContentDetail(accessToken, 'video_content_*********');

// Lấy danh sách nội dung video
const videoContentList = await zaloContentService.getVideoContentList(accessToken, {
  status: 'published',
  limit: 20,
  offset: 0,
  tags: ['sản phẩm']
});

// Cập nhật nội dung video
const videoUpdateRequest = {
  title: 'Tiêu đề video đã cập nhật',
  description: 'Mô tả video đã cập nhật'
};
await zaloContentService.updateVideoContent(accessToken, 'video_content_*********', videoUpdateRequest);

// Xuất bản nội dung video
await zaloContentService.publishVideoContent(accessToken, 'video_content_*********');

// Hủy xuất bản nội dung video
await zaloContentService.unpublishVideoContent(accessToken, 'video_content_*********');

// Xóa nội dung video
await zaloContentService.deleteVideoContent(accessToken, 'video_content_*********');

// Lấy thống kê nội dung video
const videoStatistics = await zaloContentService.getVideoContentStatistics(accessToken, 'video_content_*********');

// Tìm kiếm nội dung video
const videoSearchResults = await zaloContentService.searchVideoContent(accessToken, 'sản phẩm', {
  status: 'published',
  limit: 10,
  tags: ['video'],
  duration_min: 30,
  duration_max: 300
});
```

## Các loại tin nhắn

### Tin nhắn văn bản

```typescript
const message: ZaloTextMessage = {
  type: 'text',
  text: 'Xin chào từ RedAI!'
};
await zaloOaService.sendMessage(accessToken, userId, message);
```

### Tin nhắn hình ảnh

```typescript
const message: ZaloImageMessage = {
  type: 'image',
  url: 'https://example.com/image.jpg',
  caption: 'Mô tả hình ảnh'
};
await zaloOaService.sendMessage(accessToken, userId, message);
```

### Tin nhắn tệp đính kèm

```typescript
const message: ZaloFileMessage = {
  type: 'file',
  url: 'https://example.com/document.pdf',
  name: 'Tài liệu.pdf'
};
await zaloOaService.sendMessage(accessToken, userId, message);
```

### Tin nhắn template

```typescript
const message: ZaloTemplateMessage = {
  type: 'template',
  template_id: 'your_template_id',
  template_data: {
    title: 'Tiêu đề',
    subtitle: 'Mô tả',
    image_url: 'https://example.com/image.jpg',
    buttons: [
      {
        title: 'Xem chi tiết',
        url: 'https://example.com/details'
      }
    ]
  }
};
await zaloOaService.sendMessage(accessToken, userId, message);
```

### Tin nhắn đến người dùng ẩn danh

```typescript
// Tin nhắn văn bản đến người dùng ẩn danh
const message: ZaloAnonymousTextMessage = {
  type: 'anonymous_text',
  text: 'Xin chào! Cảm ơn bạn đã quan tâm đến dịch vụ của chúng tôi.'
};
await zaloAnonymousService.sendAnonymousMessage(accessToken, userId, message);

// Tin nhắn ảnh đến người dùng ẩn danh
const message: ZaloAnonymousImageMessage = {
  type: 'anonymous_image',
  url: 'https://example.com/promotion.jpg',
  message: 'Khuyến mãi đặc biệt dành cho bạn!'
};
await zaloAnonymousService.sendAnonymousMessage(accessToken, userId, message);

// Tin nhắn file đến người dùng ẩn danh
const message: ZaloAnonymousFileMessage = {
  type: 'anonymous_file',
  url: 'https://example.com/catalog.pdf',
  filename: 'catalog.pdf',
  message: 'Catalog sản phẩm mới nhất'
};
await zaloAnonymousService.sendAnonymousMessage(accessToken, userId, message);

// Tin nhắn sticker đến người dùng ẩn danh
const message: ZaloAnonymousStickerMessage = {
  type: 'anonymous_sticker',
  sticker_id: 'sticker_id_123',
  message: 'Cảm ơn bạn!'
};
await zaloAnonymousService.sendAnonymousMessage(accessToken, userId, message);
```

### Tin nhắn đặc biệt

```typescript
// Thả biểu tượng cảm xúc vào tin nhắn
const reaction: ZaloReactionMessage = {
  type: 'reaction',
  message_id: 'message_id_123',
  reaction_type: 'heart'
};
await zaloMessageService.sendOtherMessage(accessToken, null, reaction);

// Tin nhắn miniapp
const miniapp: ZaloMiniAppMessage = {
  type: 'miniapp',
  app_id: 'miniapp_id_123',
  title: 'Mở ứng dụng đặt hàng',
  subtitle: 'Đặt hàng nhanh chóng và tiện lợi',
  image_url: 'https://example.com/miniapp-icon.jpg',
  data: { product_id: '123', category: 'food' }
};
await zaloMessageService.sendOtherMessage(accessToken, userId, miniapp);
```

## Xử lý webhook

Để xử lý webhook từ Zalo, bạn cần tạo một controller để nhận các sự kiện webhook:

```typescript
@Controller('api/zalo/webhook')
export class ZaloWebhookController {
  constructor(private readonly zaloWebhookService: ZaloWebhookService) {}

  @Post()
  async handleWebhook(
    @Headers('X-ZEvent-Signature') mac: string,
    @Headers('X-ZEvent-Timestamp') timestamp: string,
    @Body() body: any,
    @Req() req: Request
  ) {
    // Xác thực webhook
    const rawBody = req.rawBody.toString();
    const isValid = this.zaloWebhookService.verifyWebhook(timestamp, mac, rawBody);

    if (!isValid) {
      throw new UnauthorizedException('Invalid webhook signature');
    }

    // Xử lý sự kiện webhook
    return this.zaloWebhookService.processWebhookEvent(body);
  }
}
```

## Tích hợp với Agent

Để tích hợp Zalo với Agent trong hệ thống, bạn cần:

1. Tạo một service để xử lý việc chuyển tiếp tin nhắn giữa Zalo và Agent
2. Sử dụng `ZaloAgentService` để gửi tin nhắn từ Agent đến người dùng Zalo
3. Xử lý webhook từ Zalo và chuyển tiếp tin nhắn đến Agent

Ví dụ về cách tích hợp:

```typescript
// Trong controller hoặc service của bạn
@Injectable()
export class YourService {
  constructor(
    private readonly zaloAgentService: ZaloAgentService,
    // Các dependency khác
  ) {}

  async handleMessageFromZalo(oaId: string, userId: string, message: string): Promise<void> {
    // Tìm agent được kết nối với Official Account
    const agent = await this.findAgentByOaId(oaId);

    if (!agent) {
      return;
    }

    // Chuyển tiếp tin nhắn đến agent
    const agentResponse = await this.processMessageWithAgent(agent.id, message);

    // Gửi phản hồi từ agent đến người dùng Zalo
    await this.zaloAgentService.sendAgentMessageToZalo(
      agent.accessToken,
      userId,
      agentResponse
    );
  }
}
```

## Ví dụ

Thư mục `examples` chứa các ví dụ về cách sử dụng Zalo Integration Services trong ứng dụng:

- `zalo-controller.example.ts`: Ví dụ về controller để xử lý các request liên quan đến Zalo
- `zalo-entity.example.ts`: Ví dụ về các entity để lưu trữ dữ liệu Zalo
- `zalo-repository.example.ts`: Ví dụ về các repository để tương tác với database
- `zalo-service.example.ts`: Ví dụ về service để xử lý logic nghiệp vụ

## Tài liệu tham khảo

- [Zalo Official Account API](https://developers.zalo.me/docs/api/official-account-api/api/api-tong-quan-post-4219)
- [Zalo Notification Service (ZNS)](https://developers.zalo.me/docs/api/zalo-notification-service/api/api-tong-quan-post-4219)
- [Zalo Social API](https://developers.zalo.me/docs/api/social-api/api/api-tong-quan-post-4219)
- [Zalo Developers](https://developers.zalo.me/)
- [Zalo Business API](https://business.zalo.me/)
