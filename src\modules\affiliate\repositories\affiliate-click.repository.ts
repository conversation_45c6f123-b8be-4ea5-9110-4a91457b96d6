import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AffiliateClick } from '../entities/affiliate-click.entity';

/**
 * Repository xử lý truy vấn và lưu trữ dữ liệu AffiliateClick
 */
@Injectable()
export class AffiliateClickRepository {
  constructor(
    @InjectRepository(AffiliateClick)
    private readonly affiliateClickRepo: Repository<AffiliateClick>,
  ) {}

  /**
   * Lưu một danh sách affiliate click vào database
   * @param clicks Danh sách affiliate click cần lưu
   * @returns Danh sách affiliate click đã lưu
   */
  async saveBatch(clicks: AffiliateClick[]): Promise<AffiliateClick[]> {
    // Sử dụng insert để tối ưu hơn cho việc insert nhiều records
    const result = await this.affiliateClickRepo.insert(clicks);

    // <PERSON><PERSON> <PERSON> cho các đối tượng ban đầu nếu cần
    if (result.identifiers?.length) {
      for (let i = 0; i < clicks.length; i++) {
        clicks[i].id = result.identifiers[i].id;
      }
    }

    return clicks;
  }

  /**
   * Lưu một affiliate click vào database
   * @param click Đối tượng affiliate click
   * @returns Affiliate click đã lưu
   */
  async save(click: AffiliateClick): Promise<AffiliateClick> {
    return this.affiliateClickRepo.save(click);
  }
}
