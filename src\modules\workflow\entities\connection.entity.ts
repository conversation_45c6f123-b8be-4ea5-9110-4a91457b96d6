import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  Index,
} from 'typeorm';

@Entity('connections')
@Index('idx_connections_workflow_id', ['workflowId'])
export class Connection {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'workflow_id', type: 'uuid' })
  workflowId: string;

  @Column({ name: 'source_node_id', type: 'uuid' })
  sourceNodeId: string;

  @Column({ name: 'target_node_id', type: 'uuid' })
  targetNodeId: string;

  @Column({ name: 'source_handle', type: 'varchar', length: 100 })
  sourceHandle: string;

  @Column({ name: 'source_handle_index', type: 'integer', default: 0 })
  sourceHandleIndex: number;

  @Column({ name: 'target_handle', type: 'varchar', length: 100 })
  targetHandle: string;

  @Column({ name: 'target_handle_index', type: 'integer', default: 0 })
  targetHandleIndex: number;
}
