// Types and Interfaces
export * from './types';

// Base Executors and Factory
export * from './executors';

// Core Services
export * from './services';

// XState Machines
export * from './machines';

// Main XState Workflow Engine
export { WorkflowXStateService } from './services/workflow-xstate.service';

// Convenience exports for quick setup
export {
  // Core types
  type WorkflowContext,
  type NodeExecutionContext,
  type DetailedNodeExecutionResult,
  type WorkflowEvent,
  type WorkflowEventTypes,
  
  // Main services
  DependencyResolverService,
  WorkflowStateManagerService,
  WorkflowXStateService,
  
  // LangGraph integration
  LangGraphIntegrationService,
  AgentNodeDetectorService,
  LangGraphResultConverterService,
  AgentExecutionContextService,
  
  // Executors
  NodeExecutorFactory,
  ExecutorRegistry,
  BaseNodeExecutor,
  HttpNodeExecutor,
  LogicNodeExecutor,
  TransformNodeExecutor,
  AINodeExecutor,
  IntegrationNodeExecutor,
  UtilityNodeExecutor,
  ALL_NODE_EXECUTORS,
  
  // Machines
  workflowMachine,
  nodeExecutionMachine,
  MachineServicesProvider,
  MachineIntegrationService,
  createWorkflowMachineWithServices,
  createNodeExecutionMachineWithServices,
  createCompleteWorkflowMachine,
  createCompleteNodeExecutionMachine,

  // Guards and Actions
  workflowGuards,
  nodeExecutionGuards,
  allGuards,
  guardUtils,
  workflowActions,
  nodeExecutionActions,
  allActions,
  actionUtils,
} from './index';
