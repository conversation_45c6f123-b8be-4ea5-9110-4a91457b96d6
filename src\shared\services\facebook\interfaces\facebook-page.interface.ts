/**
 * Interface cho thông tin chi tiết của Facebook Page
 */
export interface PageDetailsResponse {
  id: string;
  name: string;
  fan_count?: number; // <PERSON><PERSON> thể không tồn tại tùy vào quyền
  followers_count?: number;
  link: string;
  is_published: boolean;
  website?: string;
  about?: string;
  phone?: string;
  category?: string;
  picture?: {
    data: {
      url: string;
      is_silhouette: boolean;
    };
  };
  cover?: {
    source: string;
    id: string;
  };
  verification_status?: string;
  description?: string;
  emails?: string[];
  location?: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    zip?: string;
    latitude?: number;
    longitude?: number;
  };
}

/**
 * Interface cho yêu cầu cập nhật thông tin Page
 */
export interface UpdatePageDetailsRequest {
  website?: string;
  about?: string;
  phone?: string;
  description?: string;
  access_token: string;
}

/**
 * Interface cho phản hồi cập nhật thông tin Page
 */
export interface UpdatePageDetailsResponse {
  success: boolean;
}

/**
 * Interface cho yêu cầu tạo bài viết mới
 */
export interface CreatePostRequest {
  message?: string;
  link?: string;
  published?: boolean; // Mặc định là true. false để tạo bài nháp
  scheduled_publish_time?: number; // Unix timestamp
  access_token: string;
}

/**
 * Interface cho phản hồi tạo bài viết
 */
export interface CreatePostResponse {
  id: string; // ID của bài viết mới, có dạng {page-id}_{post-id}
}

/**
 * Interface cho thông tin người bình luận
 */
export interface CommentFrom {
  id: string;
  name: string;
}

/**
 * Interface cho bình luận
 */
export interface Comment {
  id: string;
  message: string;
  created_time: string; // ISO 8601 date string
  from: CommentFrom;
  like_count?: number;
  comment_count?: number;
  parent?: {
    id: string;
  };
}

/**
 * Interface cho phản hồi danh sách bình luận
 */
export interface GetCommentsResponse {
  data: Comment[];
  paging?: FacebookPaging;
}

/**
 * Interface cho giá trị insight
 */
export interface InsightValue {
  value: number | Record<string, number>;
  end_time: string; // ISO 8601 date string
}

/**
 * Interface cho metric insight
 */
export interface InsightMetric {
  name: string;
  period: 'day' | 'week' | 'days_28' | 'lifetime';
  values: InsightValue[];
  title: string;
  description: string;
  id: string;
}

/**
 * Interface cho phản hồi insights
 */
export interface GetInsightsResponse {
  data: InsightMetric[];
  paging?: FacebookPaging;
}

/**
 * Interface cho paging Facebook
 */
export interface FacebookPaging {
  cursors?: {
    before?: string;
    after?: string;
  };
  next?: string;
  previous?: string;
}

/**
 * Interface cho webhook change value
 */
export interface WebhookChangeValue {
  from: {
    id: string;
    name: string;
  };
  item: 'comment' | 'reaction' | 'post' | 'share';
  verb: 'add' | 'edit' | 'remove';
  comment_id?: string;
  post_id: string;
  created_time: number; // Unix timestamp
  message?: string;
}

/**
 * Interface cho webhook change
 */
export interface WebhookChange {
  value: WebhookChangeValue;
  field: 'feed';
}

/**
 * Interface cho webhook entry
 */
export interface WebhookEntry {
  id: string; // Page ID
  time: number; // Unix timestamp
  changes: WebhookChange[];
}

/**
 * Interface cho webhook payload
 */
export interface WebhookPayload {
  object: 'page';
  entry: WebhookEntry[];
}

/**
 * Interface cho tham số lấy insights
 */
export interface GetInsightsParams {
  metric: string; // Danh sách metrics phân tách bằng dấu phẩy
  period?: 'day' | 'week' | 'days_28' | 'lifetime';
  since?: string; // YYYY-MM-DD format
  until?: string; // YYYY-MM-DD format
  breakdown?: string;
}

/**
 * Interface cho tham số lấy bài viết
 */
export interface GetPostsParams {
  fields?: string;
  limit?: number;
  since?: string;
  until?: string;
}

/**
 * Interface cho bài viết Facebook
 */
export interface FacebookPost {
  id: string;
  message?: string;
  story?: string;
  created_time: string;
  updated_time?: string;
  type: 'link' | 'status' | 'photo' | 'video' | 'offer';
  status_type?: string;
  permalink_url?: string;
  is_published?: boolean;
  scheduled_publish_time?: string;
  from?: {
    id: string;
    name: string;
  };
  likes?: {
    data: Array<{
      id: string;
      name: string;
    }>;
    summary: {
      total_count: number;
    };
  };
  comments?: {
    data: Comment[];
    summary: {
      total_count: number;
    };
  };
  shares?: {
    count: number;
  };
}

/**
 * Interface cho phản hồi danh sách bài viết
 */
export interface GetPostsResponse {
  data: FacebookPost[];
  paging?: FacebookPaging;
}

/**
 * Interface cho tham số lấy thông tin page
 */
export interface GetPageDetailsParams {
  fields: string; // Danh sách fields phân tách bằng dấu phẩy
}
