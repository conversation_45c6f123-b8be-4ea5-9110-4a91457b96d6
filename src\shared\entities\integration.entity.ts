import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity đại diện cho bảng integration trong cơ sở dữ liệu
 * Quản lý thông tin các tích hợp của người dùng
 */
@Entity('integration')
export class Integration {

  /**
   * UUID định danh duy nhất cho tích hợp
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên của tích hợp
   */
  @Column({
    name: 'integration_name',
    type: 'varchar',
    length: 255,
    nullable: false,
    comment: 'Tên của tích hợp'
  })
  integrationName: string;

  /**
   * ID tham chiếu đến bảng integration_providers
   */
  @Column({
    name: 'type_id',
    type: 'integer',
    nullable: false,
    comment: 'ID tham chiếu đến bảng integration_providers'
  })
  typeId: number;

  /**
   * ID của người dùng (null cho admin integrations)
   */
  @Column({
    name: 'user_id',
    type: 'integer',
    nullable: true,
    comment: 'ID của người dùng (null cho admin integrations)'
  })
  userId: number | null;

  /**
   * Loại chủ sở hữu tích hợp
   */
  @Column({
    name: 'owned_type',
    type: 'varchar',
    nullable: true,
    comment: 'Loại chủ sở hữu tích hợp'
  })
  ownedType: string | null;

  /**
   * Thời điểm tạo tích hợp
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    nullable: false,
    comment: 'Thời điểm tạo tích hợp'
  })
  createdAt: number;

  /**
   * ID của nhân viên (nếu được tạo bởi admin)
   */
  @Column({
    name: 'employee_id',
    type: 'integer',
    nullable: true,
    comment: 'ID của nhân viên'
  })
  employeeId: number | null;

  /**
   * Cấu hình được mã hóa
   */
  @Column({
    name: 'encrypted_config',
    type: 'text',
    nullable: true,
    comment: 'Cấu hình được mã hóa'
  })
  encryptedConfig: string | null;

  /**
   * Cấu hình MCP được mã hóa
   */
  @Column({
    name: 'mcp_encrypted_config',
    type: 'text',
    nullable: true,
    comment: 'Cấu hình MCP được mã hóa'
  })
  mcpEncryptedConfig: string | null;

  /**
   * Metadata dưới dạng JSON
   */
  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Metadata dưới dạng JSON'
  })
  metadata: any;

  /**
   * Khóa bí mật để giải mã
   */
  @Column({
    name: 'secret_key',
    type: 'text',
    nullable: true,
    comment: 'Khóa bí mật để giải mã'
  })
  secretKey: string | null;
}
