import * as crypto from 'crypto';
import { Injectable, Logger } from '@nestjs/common';
import { env } from '../../../config';

/**
 * Utility class for encryption and decryption operations
 */
@Injectable()
export class EncryptionUtil {
  private readonly logger = new Logger(EncryptionUtil.name);
  private readonly algorithm = 'aes-256-cbc';
  private readonly secretKey: string;

  constructor() {
    // Sử dụng secret key từ environment variables
    this.secretKey = process.env.APP_SECRET || 'default-secret-key-for-encryption';

    // Đảm bảo secret key có độ dài phù hợp
    if (this.secretKey.length < 32) {
      this.secretKey = crypto.createHash('sha256').update(this.secretKey).digest('hex').substring(0, 32);
    } else if (this.secretKey.length > 32) {
      this.secretKey = this.secretKey.substring(0, 32);
    }
  }

  /**
   * Encrypt a string
   * @param text - Text to encrypt
   * @returns Encrypted string in format: iv:encryptedData
   */
  encrypt(text: string): string {
    try {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv(this.algorithm, this.secretKey, iv);

      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      return `${iv.toString('hex')}:${encrypted}`;
    } catch (error) {
      this.logger.error('Failed to encrypt data', error);
      throw new Error('Encryption failed');
    }
  }

  /**
   * Decrypt a string
   * @param encryptedText - Encrypted text in format: iv:encryptedData
   * @returns Decrypted string
   */
  decrypt(encryptedText: string): string {
    try {
      // Nếu text không có format iv:data, có thể là plain text (backward compatibility)
      if (!encryptedText.includes(':')) {
        this.logger.warn('Encrypted text does not contain IV separator, treating as plain text');
        return encryptedText;
      }

      const [ivHex, encrypted] = encryptedText.split(':');

      if (!ivHex || !encrypted) {
        throw new Error('Invalid encrypted text format');
      }

      const iv = Buffer.from(ivHex, 'hex');
      const decipher = crypto.createDecipheriv(this.algorithm, this.secretKey, iv);

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      this.logger.error('Failed to decrypt data', error);
      // Fallback: return original text if decryption fails (might be plain text)
      this.logger.warn('Decryption failed, returning original text as fallback');
      return encryptedText;
    }
  }

  /**
   * Decrypt API key safely with error handling
   * @param encryptedApiKey - Encrypted API key from database
   * @returns Decrypted API key or null if failed
   */
  decryptApiKey(encryptedApiKey: string): string | null {
    try {
      if (!encryptedApiKey) {
        this.logger.warn('Empty API key provided for decryption');
        return null;
      }

      const decryptedKey = this.decrypt(encryptedApiKey);
      
      if (!decryptedKey) {
        this.logger.error('Decryption returned empty result');
        return null;
      }

      return decryptedKey;
    } catch (error) {
      this.logger.error('Failed to decrypt API key', error);
      return null;
    }
  }

  /**
   * Validate if a string is properly encrypted
   * @param text - Text to validate
   * @returns True if text appears to be encrypted
   */
  isEncrypted(text: string): boolean {
    try {
      // Check if text has the expected format: iv:encryptedData
      if (!text.includes(':')) {
        return false;
      }

      const [ivHex, encrypted] = text.split(':');
      
      // Check if IV is valid hex (32 characters for 16 bytes)
      if (!ivHex || ivHex.length !== 32) {
        return false;
      }

      // Check if encrypted part exists and is hex
      if (!encrypted || !/^[0-9a-fA-F]+$/.test(encrypted)) {
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Hash a string using SHA256
   * @param text - Text to hash
   * @returns SHA256 hash
   */
  hash(text: string): string {
    return crypto.createHash('sha256').update(text).digest('hex');
  }

  /**
   * Generate a random string
   * @param length - Length of random string
   * @returns Random string
   */
  generateRandomString(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Compare a plain text with its hash
   * @param plainText - Plain text
   * @param hashedText - Hashed text
   * @returns True if they match
   */
  compareHash(plainText: string, hashedText: string): boolean {
    const hash = this.hash(plainText);
    return hash === hashedText;
  }
}
