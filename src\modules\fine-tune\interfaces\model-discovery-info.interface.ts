/**
 * Interface cho thông tin model discovery
 */
export interface ModelDiscoveryInfo {
  /** Tổng số models được tìm thấy từ provider */
  totalModelsFound: number;
  /** Số models match với patterns */
  modelsMatched: number;
  /** Số models mới được tạo */
  newModelsCreated: number;
  /** Số models đã tồn tại */
  existingModelsFound: number;
  /** Số mappings được tạo */
  mappingsCreated: number;
  /** Thời gian thực hiện discovery (timestamp) */
  discoveryTime: number;
  /** Trạng thái thành công */
  success: boolean;
  /** Message mô tả */
  message: string;
  /** Danh sách lỗi nếu có */
  errors?: string[];
}

/**
 * Interface cho response khi tạo/cập nhật key với model discovery
 */
export interface KeyOperationWithDiscoveryResponse {
  /** ID của key đượ<PERSON> tạo/cập nhật */
  id: string;
  /** Lỗi connection test nếu có */
  connectionError?: string;
  /** Thông tin model discovery */
  modelDiscovery?: ModelDiscoveryInfo;
}
