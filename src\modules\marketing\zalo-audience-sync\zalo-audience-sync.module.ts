import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { ZaloAudienceSyncProcessor } from './zalo-audience-sync.processor';
import { ZaloAudienceSyncService } from './zalo-audience-sync.service';
import { ZaloOAWorkerAdapterService } from './services/zalo-oa-worker-adapter.service';
import { KeyPairEncryptionService } from '../../../shared/services/encryption/key-pair-encryption.service';
import { InfraModule } from '../../../infra/infra.module';
import { SharedModule } from '../../../shared/shared.module';
import { ZaloModule } from '../../../shared/services/zalo/zalo.module';
import {
  UserAudienceRepository,
  UserAudienceCustomFieldRepository,
  UserAudienceCustomFieldDefinitionRepository,
} from '../repositories';
import {
  UserAudience,
  UserAudienceCustomField,
  UserAudienceCustomFieldDefinition,
} from '../entities';
import { Integration } from '../../../shared/entities/integration.entity';
import { IntegrationProvider } from '../../../shared/entities/integration-provider.entity';

/**
 * Module xử lý đồng bộ Zalo audience trong worker
 */
@Module({
  imports: [
    ConfigModule,
    InfraModule,
    SharedModule,
    ZaloModule,
    TypeOrmModule.forFeature([
      UserAudience,
      UserAudienceCustomField,
      UserAudienceCustomFieldDefinition,
      Integration,
      IntegrationProvider,
    ]),
  ],
  providers: [
    ZaloAudienceSyncProcessor,
    ZaloAudienceSyncService,
    KeyPairEncryptionService,
    ZaloOAWorkerAdapterService,
    UserAudienceRepository,
    UserAudienceCustomFieldRepository,
    UserAudienceCustomFieldDefinitionRepository,
  ],
  exports: [
    ZaloAudienceSyncService,
  ],
})
export class ZaloAudienceSyncModule {}
