import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { lastValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { AppException, ErrorCode } from '@common/exceptions';
import {
  ZnsQualityAssessmentDto,
  ZnsQualityAssessmentHistoryDto,
  GetZnsQualityAssessmentDto,
  ZnsQualityViolationDto,
  ZnsSendingPrivilegeDto,
  ZnsErrorResponseDto,
  ZnsBinCodeListDto,
  ZnsBinCodeInfoDto,
  ZnsComponentType,
  ZnsTemplateComponentDto,
} from './dto/zalo-zns.dto';

/**
 * Service cho quản lý chất lượng và component ZNS
 *
 * Điều kiện sử dụng:
 * - Official Account phải được duyệt và có quyền truy cập ZNS Quality API
 * - Access token hợp lệ với quyền đọc thông tin chất lượng
 * - <PERSON><PERSON> thủ các quy định về bảo mật và quyền riêng tư
 * - Chỉ có thể truy cập dữ liệu của chính Official Account đó
 *
 * Tài liệu tham khảo:
 * - https://developers.zalo.me/docs/zalo-notification-service/phu-luc/co-che-danh-gia-chat-luong-va-quyen-loi-gui-zns
 * - https://developers.zalo.me/docs/zalo-notification-service/phu-luc/component
 * - https://developers.zalo.me/docs/zalo-notification-service/phu-luc/bang-ma-loi
 * - https://developers.zalo.me/docs/zalo-notification-service/phu-luc/danh-sach-bin-code
 */
@Injectable()
export class ZaloZnsQualityService {
  private readonly logger = new Logger(ZaloZnsQualityService.name);
  private readonly qualityApiUrl =
    'https://business.openapi.zalo.me/zns/quality';
  private readonly componentApiUrl =
    'https://business.openapi.zalo.me/zns/component';
  private readonly binCodeApiUrl =
    'https://business.openapi.zalo.me/zns/bincode';

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Lấy thông tin đánh giá chất lượng hiện tại
   * @param accessToken Access token của Official Account
   * @returns Thông tin đánh giá chất lượng
   * @throws AppException nếu có lỗi xảy ra
   */
  async getCurrentQualityAssessment(
    accessToken: string,
  ): Promise<ZnsQualityAssessmentDto> {
    try {
      const url = `${this.qualityApiUrl}/current`;
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZnsQualityAssessmentDto;
        }>(url, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy thông tin đánh giá chất lượng: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy thông tin đánh giá chất lượng: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting current quality assessment: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy thông tin đánh giá chất lượng: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy thông tin đánh giá chất lượng: ${error.message}`,
      );
    }
  }

  /**
   * Lấy lịch sử đánh giá chất lượng
   * @param accessToken Access token của Official Account
   * @param params Tham số lọc và phân trang
   * @returns Lịch sử đánh giá chất lượng
   * @throws AppException nếu có lỗi xảy ra
   */
  async getQualityAssessmentHistory(
    accessToken: string,
    params?: GetZnsQualityAssessmentDto,
  ): Promise<ZnsQualityAssessmentHistoryDto> {
    try {
      const url = `${this.qualityApiUrl}/history`;
      const queryParams: any = {};

      if (params?.start_time) queryParams.start_time = params.start_time;
      if (params?.end_time) queryParams.end_time = params.end_time;
      if (params?.offset) queryParams.offset = params.offset;
      if (params?.limit) queryParams.limit = params.limit;
      if (params?.quality_level)
        queryParams.quality_level = params.quality_level;
      if (params?.violations_only)
        queryParams.violations_only = params.violations_only;

      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZnsQualityAssessmentHistoryDto;
        }>(url, { params: queryParams, headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy lịch sử đánh giá chất lượng: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy lịch sử đánh giá chất lượng: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting quality assessment history: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy lịch sử đánh giá chất lượng: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy lịch sử đánh giá chất lượng: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách vi phạm chất lượng
   * @param accessToken Access token của Official Account
   * @param status Trạng thái vi phạm (tùy chọn)
   * @returns Danh sách vi phạm
   * @throws AppException nếu có lỗi xảy ra
   */
  async getQualityViolations(
    accessToken: string,
    status?: 'ACTIVE' | 'RESOLVED' | 'DISMISSED',
  ): Promise<ZnsQualityViolationDto[]> {
    try {
      const url = `${this.qualityApiUrl}/violations`;
      const params: any = {};
      if (status) params.status = status;

      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: { violations: ZnsQualityViolationDto[] };
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy danh sách vi phạm chất lượng: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy danh sách vi phạm chất lượng: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data.violations;
    } catch (error) {
      this.logger.error(
        `Error getting quality violations: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy danh sách vi phạm chất lượng: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy danh sách vi phạm chất lượng: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin quyền lợi gửi ZNS hiện tại
   * @param accessToken Access token của Official Account
   * @returns Thông tin quyền lợi gửi
   * @throws AppException nếu có lỗi xảy ra
   */
  async getSendingPrivileges(
    accessToken: string,
  ): Promise<ZnsSendingPrivilegeDto> {
    try {
      const url = `${this.qualityApiUrl}/privileges`;
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZnsSendingPrivilegeDto;
        }>(url, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy thông tin quyền lợi gửi: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy thông tin quyền lợi gửi: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting sending privileges: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy thông tin quyền lợi gửi: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy thông tin quyền lợi gửi: ${error.message}`,
      );
    }
  }

  /**
   * Xác nhận đã khắc phục vi phạm chất lượng
   * @param accessToken Access token của Official Account
   * @param violationId ID vi phạm cần xác nhận khắc phục
   * @param resolutionNote Ghi chú về cách khắc phục
   * @returns Kết quả xác nhận
   * @throws AppException nếu có lỗi xảy ra
   */
  async resolveQualityViolation(
    accessToken: string,
    violationId: string,
    resolutionNote?: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      const url = `${this.qualityApiUrl}/violations/${violationId}/resolve`;
      const data = {
        resolution_note: resolutionNote,
      };

      const headers = {
        'Content-Type': 'application/json',
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.post<{
          error: number;
          message: string;
          data?: { success: boolean; message: string };
        }>(url, data, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi xác nhận khắc phục vi phạm: ${response.data.message}`,
        );
      }

      return (
        response.data.data || {
          success: true,
          message: 'Đã xác nhận khắc phục vi phạm',
        }
      );
    } catch (error) {
      this.logger.error(
        `Error resolving quality violation: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi xác nhận khắc phục vi phạm: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi xác nhận khắc phục vi phạm: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách BIN code ngân hàng được hỗ trợ
   * @param accessToken Access token của Official Account
   * @returns Danh sách BIN code
   * @throws AppException nếu có lỗi xảy ra
   */
  async getSupportedBinCodes(accessToken: string): Promise<ZnsBinCodeListDto> {
    try {
      const url = `${this.binCodeApiUrl}/supported`;
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZnsBinCodeListDto;
        }>(url, { headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy danh sách BIN code: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy danh sách BIN code: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting supported BIN codes: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy danh sách BIN code: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy danh sách BIN code: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra thông tin BIN code cụ thể
   * @param accessToken Access token của Official Account
   * @param binCode Mã BIN cần kiểm tra
   * @returns Thông tin BIN code
   * @throws AppException nếu có lỗi xảy ra
   */
  async getBinCodeInfo(
    accessToken: string,
    binCode: string,
  ): Promise<ZnsBinCodeInfoDto> {
    try {
      const url = `${this.binCodeApiUrl}/info`;
      const params = { bin_code: binCode };
      const headers = {
        access_token: accessToken,
      };

      const response = await lastValueFrom(
        this.httpService.get<{
          error: number;
          message: string;
          data?: ZnsBinCodeInfoDto;
        }>(url, { params, headers }),
      );

      if (response.data.error !== 0) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy thông tin BIN code: ${response.data.message}`,
        );
      }

      if (!response.data.data) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Lỗi khi lấy thông tin BIN code: Không nhận được dữ liệu từ Zalo API',
        );
      }

      return response.data.data;
    } catch (error) {
      this.logger.error(
        `Error getting BIN code info: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      if (error instanceof AxiosError && error.response) {
        const response = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi khi lấy thông tin BIN code: ${response.message || error.message}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi lấy thông tin BIN code: ${error.message}`,
      );
    }
  }
}
