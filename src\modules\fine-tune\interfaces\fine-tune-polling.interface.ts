import { ProviderFineTuneEnum } from '../constants/provider.enum';

/**
 * Interface cho job data fine-tune polling
 */
export interface FineTunePollingJobData {
  /**
   * ID người dùng
   */
  userId: string;

  /**
   * ID của user model fine tune record
   */
  modelFineTuneId: string;

  /**
   * Nhà cung cấp AI
   */
  provider: ProviderFineTuneEnum;

  /**
   * Thời gian tạo job
   */
  timestamp: number;
}

/**
 * Interface cho kết quả polling từ provider
 */
export interface PollingResult {
  /**
   * Polling có thành công không
   */
  success: boolean;

  /**
   * Trạng thái hiện tại từ provider
   */
  status: string;

  /**
   * ID của model fine-tuned (nếu thành công)
   */
  modelId?: string;

  /**
   * Thông tin lỗi (nếu thất bại)
   */
  error?: string;

  /**
   * C<PERSON> nên tiếp tục polling không
   */
  shouldContinuePolling: boolean;

  /**
   * Metadata bổ sung từ provider
   */
  metadata?: Record<string, any>;
}

/**
 * Interface cho dữ liệu cập nhật status
 */
export interface StatusUpdateData {
  /**
   * ID của model fine tune
   */
  modelFineTuneId: string;

  /**
   * Trạng thái mới
   */
  status: string;

  /**
   * Job có thành công không
   */
  isSuccess: boolean;

  /**
   * ID của model fine-tuned (nếu thành công)
   */
  modelId?: string;

  /**
   * Thông tin lỗi (nếu thất bại)
   */
  error?: string;

  /**
   * Metadata bổ sung
   */
  metadata?: Record<string, any>;
}

/**
 * Interface cho thông tin job cần polling
 */
export interface FineTuneJobInfo {
  /**
   * ID của model fine tune
   */
  modelFineTuneId: string;

  /**
   * ID người dùng
   */
  userId: string;

  /**
   * Nhà cung cấp
   */
  provider: ProviderFineTuneEnum;

  /**
   * ID của job từ provider
   */
  jobId: string;

  /**
   * Trạng thái hiện tại
   */
  currentStatus: string;

  /**
   * ID của system model (nếu có)
   */
  systemModelId?: string;

  /**
   * ID của user model (nếu có)
   */
  userModelId?: string;

  /**
   * API key đã mã hóa
   */
  encryptedApiKey: string;

  /**
   * Public key để decrypt API key
   */
  publicKey: string;

  /**
   * API key (deprecated - use encryptedApiKey)
   */
  apiKey?: string;

  /**
   * Metadata bổ sung
   */
  metadata?: Record<string, any>;
}

/**
 * Interface cho response từ OpenAI API
 */
export interface OpenAIFineTuneJobResponse {
  /**
   * ID của fine-tune job
   */
  id: string;

  /**
   * Loại object
   */
  object: string;

  /**
   * Thời gian tạo
   */
  created_at: number;

  /**
   * Model được fine-tune
   */
  model: string;

  /**
   * ID của model fine-tuned (nếu hoàn thành)
   */
  fine_tuned_model: string | null;

  /**
   * ID tổ chức
   */
  organization_id: string;

  /**
   * Trạng thái job
   */
  status: 'validating_files' | 'queued' | 'running' | 'succeeded' | 'failed' | 'cancelled';

  /**
   * File training
   */
  training_file: string;

  /**
   * File validation (nếu có)
   */
  validation_file: string | null;

  /**
   * Files kết quả
   */
  result_files: string[];

  /**
   * Thông tin lỗi (nếu có)
   */
  error?: {
    message: string;
    code?: string;
    param?: string;
  };

  /**
   * Hyperparameters
   */
  hyperparameters: {
    n_epochs: number;
    batch_size: number | 'auto';
    learning_rate_multiplier: number | 'auto';
  };

  /**
   * Số tokens đã train
   */
  trained_tokens: number | null;
}

/**
 * Interface cho response từ Google AI API
 */
export interface GoogleFineTuneJobResponse {
  /**
   * Tên của tuning job
   */
  name: string;

  /**
   * Thời gian tạo
   */
  createTime: string;

  /**
   * Thời gian cập nhật
   */
  updateTime: string;

  /**
   * Trạng thái job
   */
  state: 'JOB_STATE_UNSPECIFIED' | 'JOB_STATE_QUEUED' | 'JOB_STATE_RUNNING' | 'JOB_STATE_SUCCEEDED' | 'JOB_STATE_FAILED' | 'JOB_STATE_CANCELLED';

  /**
   * ID model cơ sở
   */
  baseModelId: string;

  /**
   * Tên hiển thị
   */
  displayName: string;

  /**
   * Mô tả
   */
  description?: string;

  /**
   * Thông tin lỗi (nếu có)
   */
  error?: {
    code: number;
    message: string;
    details?: any[];
  };

  /**
   * Metadata bổ sung
   */
  metadata?: Record<string, any>;
}

/**
 * Interface cho thống kê polling
 */
export interface PollingStats {
  /**
   * Tổng số jobs đang polling
   */
  totalPollingJobs: number;

  /**
   * Số jobs theo provider
   */
  jobsByProvider: Record<ProviderFineTuneEnum, number>;

  /**
   * Số jobs theo status
   */
  jobsByStatus: Record<string, number>;

  /**
   * Thời gian cập nhật cuối
   */
  lastUpdated: number;
}
