import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { AppException, ErrorCode } from '../../../common/exceptions';
import { env } from '../../../config';
import { ZaloOATokenAdapterService } from '../../../shared/services/zalo/zalo-oa-token-adapter.service';

interface ZaloRefreshTokenResponse {
  access_token: string;
  refresh_token: string;
  expires_in: string;
}

@Injectable()
export class ZaloTokenRefresherService {
  private readonly logger = new Logger(ZaloTokenRefresherService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly zaloOATokenAdapterService: ZaloOATokenAdapterService,
  ) {}

  /**
   * T<PERSON><PERSON> vụ định kỳ chạy mỗi 24 giờ để làm mới các access token sắp hết hạn
   * Tìm những tài khoản có refreshToken và expiresAt còn dưới 1 ngày
   * để gọi API làm mới access token
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async refreshExpiredTokens() {
    this.logger.log('Bắt đầu tác vụ làm mới access token Zalo');

    try {
      // Lấy thời gian hiện tại
      const now = Date.now();
      // Thời gian 24 giờ tính bằng milliseconds
      const oneDayInMs = 24 * 60 * 60 * 1000;
      // Lấy thời gian sau 24 giờ
      const expiryThreshold = now + oneDayInMs;

      // Tìm tất cả các tài khoản Zalo OA cần làm mới token
      const accountsToRefresh = await this.zaloOATokenAdapterService.findAccountsNeedingRefresh(expiryThreshold);

      this.logger.log(
        `Tìm thấy ${accountsToRefresh.length} tài khoản Zalo cần làm mới token`,
      );

      // Lấy app_id và secret_key từ env.ts
      const appId = env.zalo.ZALO_APP_ID;
      const secretKey = env.zalo.ZALO_APP_SECRET;

      if (!appId || !secretKey) {
        throw new Error('Thiếu cấu hình ZALO_APP_ID hoặc ZALO_APP_SECRET');
      }

      // Duyệt qua từng tài khoản và làm mới token
      for (const account of accountsToRefresh) {
        try {
          await this.refreshAccountToken(account, appId, secretKey);
        } catch (error) {
          this.logger.error(
            `Lỗi khi làm mới token cho tài khoản ${account.oaId}: ${error.message}`,
            error.stack,
          );
        }
      }

      this.logger.log('Hoàn thành tác vụ làm mới access token Zalo');
    } catch (error) {
      this.logger.error(
        `Lỗi khi thực hiện tác vụ làm mới token: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Làm mới token cho một Official Account
   * @param account Thông tin tài khoản Zalo Official Account
   * @param appId ID của ứng dụng
   * @param secretKey Khóa bí mật của ứng dụng
   */
  private async refreshAccountToken(
    account: any,
    appId: string,
    secretKey: string,
  ): Promise<void> {
    this.logger.log(
      `Đang làm mới token cho tài khoản ${account.oaId} (${account.name})`,
    );

    try {
      // Lấy token đã giải mã từ Integration entity
      const decryptedTokens = await this.zaloOATokenAdapterService.getDecryptedTokens(account.id);

      // Chuẩn bị form data
      const formData = new URLSearchParams();
      formData.append('refresh_token', decryptedTokens.refreshToken);
      formData.append('app_id', appId);
      formData.append('grant_type', 'refresh_token');

      // Cấu hình request
      const config = {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          secret_key: secretKey,
        },
      };

      // Gọi API để lấy token mới
      const response = await lastValueFrom(
        this.httpService.post<ZaloRefreshTokenResponse>(
          'https://oauth.zaloapp.com/v4/oa/access_token',
          formData.toString(),
          config,
        ),
      );

      // Kiểm tra phản hồi
      if (!response.data || !response.data.access_token) {
        throw new Error('Không nhận được access token mới từ Zalo API');
      }

      const { access_token, refresh_token, expires_in } = response.data;
      const expiresInMs = parseInt(expires_in) * 1000; // Chuyển giây thành mili giây
      const newExpiresAt = Date.now() + expiresInMs;

      // Cập nhật dữ liệu vào database thông qua adapter service
      await this.zaloOATokenAdapterService.save({
        id: account.id,
        accessToken: access_token,
        refreshToken: refresh_token,
        expiresAt: newExpiresAt,
        status: 'active',
      });

      this.logger.log(
        `Đã làm mới token thành công cho tài khoản ${account.oaId}. ` +
          `Token mới hết hạn sau ${Math.round(expiresInMs / (1000 * 60 * 60))} giờ.`,
      );
    } catch (error) {
      // Xử lý lỗi
      if (error.response) {
        this.logger.error(
          `Lỗi API khi làm mới token: ${JSON.stringify(error.response.data)}`,
        );
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi làm mới token cho tài khoản ${account.oaId}: ${error.message}`,
      );
    }
  }
}
