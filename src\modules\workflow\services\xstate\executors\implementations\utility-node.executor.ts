import { Injectable, Logger } from '@nestjs/common';
import { BaseNodeExecutor } from '../base/base-node.executor';
import { 
  ExecutorContext, 
  ValidationResult,
  DetailedNodeExecutionResult 
} from '../base/node-executor.interface';
import { NodeExecutionConfig } from '../../types';
import { NodeGroupEnum } from '../../../../enums/node-group.enum';
import * as crypto from 'crypto';
import * as fs from 'fs/promises';
import * as path from 'path';

/**
 * Utility operation types
 */
type UtilityOperationType = 
  | 'delay'
  | 'random'
  | 'uuid'
  | 'hash'
  | 'encrypt'
  | 'decrypt'
  | 'base64-encode'
  | 'base64-decode'
  | 'url-encode'
  | 'url-decode'
  | 'json-parse'
  | 'json-stringify'
  | 'date-format'
  | 'date-parse'
  | 'string-format'
  | 'regex-match'
  | 'regex-replace'
  | 'file-read'
  | 'file-write'
  | 'file-delete'
  | 'directory-list'
  | 'environment-variable'
  | 'system-info'
  | 'counter'
  | 'cache-set'
  | 'cache-get'
  | 'cache-delete'
  | 'log'
  | 'debug'
  | 'error'
  | 'custom-function';

/**
 * Utility node configuration
 */
interface UtilityNodeConfig {
  /** Type of utility operation */
  operation: UtilityOperationType;
  
  /** Operation parameters */
  parameters: {
    /** Delay parameters */
    delay?: {
      duration: number; // milliseconds
      unit?: 'ms' | 's' | 'm' | 'h';
    };
    
    /** Random parameters */
    random?: {
      type: 'number' | 'string' | 'boolean' | 'uuid';
      min?: number;
      max?: number;
      length?: number;
      charset?: string;
    };
    
    /** Hash parameters */
    hash?: {
      algorithm: 'md5' | 'sha1' | 'sha256' | 'sha512';
      encoding?: 'hex' | 'base64';
      input: string;
    };
    
    /** Encryption parameters */
    encryption?: {
      algorithm: 'aes-256-cbc' | 'aes-256-gcm';
      key: string;
      iv?: string;
      input: string;
    };
    
    /** Encoding parameters */
    encoding?: {
      input: string;
      charset?: string;
    };
    
    /** Date parameters */
    date?: {
      input?: string | number;
      format?: string;
      timezone?: string;
      locale?: string;
    };
    
    /** String parameters */
    string?: {
      template: string;
      variables?: Record<string, any>;
    };
    
    /** Regex parameters */
    regex?: {
      pattern: string;
      input: string;
      flags?: string;
      replacement?: string;
    };
    
    /** File parameters */
    file?: {
      path: string;
      content?: string;
      encoding?: 'utf8' | 'base64' | 'binary';
      mode?: number;
    };
    
    /** Environment parameters */
    env?: {
      variable: string;
      defaultValue?: any;
    };
    
    /** Counter parameters */
    counter?: {
      key: string;
      increment?: number;
      initialValue?: number;
    };
    
    /** Cache parameters */
    cache?: {
      key: string;
      value?: any;
      ttl?: number; // seconds
    };
    
    /** Log parameters */
    log?: {
      level: 'debug' | 'info' | 'warn' | 'error';
      message: string;
      data?: any;
    };
    
    /** Custom function parameters */
    customFunction?: {
      code: string;
      timeout?: number;
      context?: Record<string, any>;
    };
  };
  
  /** Output configuration */
  output?: {
    /** Output field name */
    field?: string;
    
    /** Include metadata */
    includeMetadata?: boolean;
    
    /** Format output */
    format?: 'raw' | 'json' | 'string';
  };
  
  /** Options */
  options?: {
    /** Silent mode (no logging) */
    silent?: boolean;
    
    /** Timeout for operation */
    timeout?: number;
    
    /** Retry on failure */
    retryOnFailure?: boolean;
    
    /** Max retries */
    maxRetries?: number;
  };
}

/**
 * Utility execution result
 */
interface UtilityExecutionResult {
  /** Result value */
  result: any;
  
  /** Operation metadata */
  metadata: {
    operationType: UtilityOperationType;
    executionTime: number;
    success: boolean;
    cached?: boolean;
    retryCount?: number;
  };
}

/**
 * Executor cho Utility nodes - handle utility functions, helpers, system operations
 */
@Injectable()
export class UtilityNodeExecutor extends BaseNodeExecutor {
  readonly nodeGroup = NodeGroupEnum.UTILITY;
  readonly supportedNodeTypes = [
    'delay',
    'random',
    'uuid',
    'hash',
    'encrypt',
    'decrypt',
    'base64-encode',
    'base64-decode',
    'url-encode',
    'url-decode',
    'json-parse',
    'json-stringify',
    'date-format',
    'date-parse',
    'string-format',
    'regex-match',
    'regex-replace',
    'file-read',
    'file-write',
    'file-delete',
    'directory-list',
    'environment-variable',
    'system-info',
    'counter',
    'cache-set',
    'cache-get',
    'cache-delete',
    'log',
    'debug',
    'error',
    'custom-function',
    'utility',
    'helper',
    'system',
  ];
  readonly executorName = 'UtilityNodeExecutor';
  readonly version = '1.0.0';
  
  // In-memory cache and counters (in production, use Redis)
  private readonly cache = new Map<string, { value: any; expiry: number }>();
  private readonly counters = new Map<string, number>();
  
  /**
   * Execute utility node
   */
  protected async executeNode(
    context: ExecutorContext,
    config: NodeExecutionConfig
  ): Promise<DetailedNodeExecutionResult> {
    const startTime = Date.now();
    
    try {
      // Parse utility configuration
      const utilityConfig = this.parseUtilityConfig(context);
      
      // Execute utility operation
      const result = await this.executeUtilityOperation(utilityConfig, context);
      
      // Format output
      const outputData = this.formatOutput(result, utilityConfig);
      
      const executionTime = Date.now() - startTime;
      
      return {
        success: true,
        outputData,
        metadata: {
          executionTime,
          customMetrics: {
            operationType: result.metadata.operationType,
            cached: result.metadata.cached || false,
            retryCount: result.metadata.retryCount || 0,
          },
          logs: utilityConfig.options?.silent ? [] : [
            `Utility operation: ${result.metadata.operationType}`,
            `Execution time: ${executionTime}ms`,
            `Result type: ${typeof result.result}`,
          ],
        },
      };
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        success: false,
        error,
        metadata: {
          executionTime,
          logs: [
            `Utility operation failed: ${error.message}`,
            `Execution time: ${executionTime}ms`,
          ],
        },
      };
    }
  }
  
  /**
   * Validate utility node input
   */
  protected async validateNodeSpecificInput(
    context: ExecutorContext,
    result: ValidationResult
  ): Promise<void> {
    const params = context.node.parameters as any;
    
    // Validate operation type
    if (!params.operation) {
      result.errors.push({
        code: 'MISSING_OPERATION',
        message: 'Utility node requires an operation type',
        field: 'operation',
        severity: 'error',
      });
    }
    
    // Validate operation-specific parameters
    this.validateOperationParameters(params, result);
  }
  
  // Private helper methods
  
  private parseUtilityConfig(context: ExecutorContext): UtilityNodeConfig {
    const params = context.node.parameters as any;
    
    return {
      operation: params.operation,
      parameters: params.parameters || {},
      output: params.output,
      options: {
        silent: params.options?.silent || false,
        timeout: params.options?.timeout || 30000,
        retryOnFailure: params.options?.retryOnFailure || false,
        maxRetries: params.options?.maxRetries || 3,
      },
    };
  }
  
  private async executeUtilityOperation(
    config: UtilityNodeConfig,
    context: ExecutorContext
  ): Promise<UtilityExecutionResult> {
    const startTime = Date.now();
    
    let result: any;
    
    switch (config.operation) {
      case 'delay':
        result = await this.executeDelay(config.parameters.delay!);
        break;
        
      case 'random':
        result = this.executeRandom(config.parameters.random!);
        break;
        
      case 'uuid':
        result = this.executeUUID();
        break;
        
      case 'hash':
        result = this.executeHash(config.parameters.hash!);
        break;
        
      case 'encrypt':
        result = this.executeEncrypt(config.parameters.encryption!);
        break;
        
      case 'decrypt':
        result = this.executeDecrypt(config.parameters.encryption!);
        break;
        
      case 'base64-encode':
        result = this.executeBase64Encode(config.parameters.encoding!);
        break;
        
      case 'base64-decode':
        result = this.executeBase64Decode(config.parameters.encoding!);
        break;
        
      case 'url-encode':
        result = this.executeURLEncode(config.parameters.encoding!);
        break;
        
      case 'url-decode':
        result = this.executeURLDecode(config.parameters.encoding!);
        break;
        
      case 'json-parse':
        result = this.executeJSONParse(config.parameters.encoding!);
        break;
        
      case 'json-stringify':
        result = this.executeJSONStringify(config.parameters.encoding!);
        break;
        
      case 'date-format':
        result = this.executeDateFormat(config.parameters.date!);
        break;
        
      case 'date-parse':
        result = this.executeDateParse(config.parameters.date!);
        break;
        
      case 'string-format':
        result = this.executeStringFormat(config.parameters.string!, context);
        break;
        
      case 'regex-match':
        result = this.executeRegexMatch(config.parameters.regex!);
        break;
        
      case 'regex-replace':
        result = this.executeRegexReplace(config.parameters.regex!);
        break;
        
      case 'file-read':
        result = await this.executeFileRead(config.parameters.file!);
        break;
        
      case 'file-write':
        result = await this.executeFileWrite(config.parameters.file!);
        break;
        
      case 'file-delete':
        result = await this.executeFileDelete(config.parameters.file!);
        break;
        
      case 'directory-list':
        result = await this.executeDirectoryList(config.parameters.file!);
        break;
        
      case 'environment-variable':
        result = this.executeEnvironmentVariable(config.parameters.env!);
        break;
        
      case 'system-info':
        result = this.executeSystemInfo();
        break;
        
      case 'counter':
        result = this.executeCounter(config.parameters.counter!);
        break;
        
      case 'cache-set':
        result = this.executeCacheSet(config.parameters.cache!);
        break;
        
      case 'cache-get':
        result = this.executeCacheGet(config.parameters.cache!);
        break;
        
      case 'cache-delete':
        result = this.executeCacheDelete(config.parameters.cache!);
        break;
        
      case 'log':
        result = this.executeLog(config.parameters.log!);
        break;
        
      case 'debug':
        result = this.executeDebug(config.parameters.log!, context);
        break;
        
      case 'error':
        result = this.executeError(config.parameters.log!);
        break;
        
      case 'custom-function':
        result = await this.executeCustomFunction(config.parameters.customFunction!, context);
        break;
        
      default:
        throw new Error(`Unsupported utility operation: ${config.operation}`);
    }
    
    return {
      result,
      metadata: {
        operationType: config.operation,
        executionTime: Date.now() - startTime,
        success: true,
      },
    };
  }
  
  // Operation implementations
  
  private async executeDelay(params: any): Promise<string> {
    let duration = params.duration;
    
    // Convert to milliseconds
    switch (params.unit) {
      case 's':
        duration *= 1000;
        break;
      case 'm':
        duration *= 60 * 1000;
        break;
      case 'h':
        duration *= 60 * 60 * 1000;
        break;
    }
    
    await new Promise(resolve => setTimeout(resolve, duration));
    return `Delayed for ${duration}ms`;
  }
  
  private executeRandom(params: any): any {
    switch (params.type) {
      case 'number':
        const min = params.min || 0;
        const max = params.max || 100;
        return Math.floor(Math.random() * (max - min + 1)) + min;
        
      case 'string':
        const length = params.length || 10;
        const charset = params.charset || 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
          result += charset.charAt(Math.floor(Math.random() * charset.length));
        }
        return result;
        
      case 'boolean':
        return Math.random() < 0.5;
        
      case 'uuid':
        return crypto.randomUUID();
        
      default:
        return Math.random();
    }
  }
  
  private executeUUID(): string {
    return crypto.randomUUID();
  }
  
  private executeHash(params: any): string {
    const hash = crypto.createHash(params.algorithm);
    hash.update(params.input);
    return hash.digest(params.encoding || 'hex');
  }
  
  private executeEncrypt(params: any): string {
    const cipher = crypto.createCipher(params.algorithm, params.key);
    let encrypted = cipher.update(params.input, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  }
  
  private executeDecrypt(params: any): string {
    const decipher = crypto.createDecipher(params.algorithm, params.key);
    let decrypted = decipher.update(params.input, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }
  
  private executeBase64Encode(params: any): string {
    return Buffer.from(params.input, params.charset || 'utf8').toString('base64');
  }
  
  private executeBase64Decode(params: any): string {
    return Buffer.from(params.input, 'base64').toString(params.charset || 'utf8');
  }
  
  private executeURLEncode(params: any): string {
    return encodeURIComponent(params.input);
  }
  
  private executeURLDecode(params: any): string {
    return decodeURIComponent(params.input);
  }
  
  private executeJSONParse(params: any): any {
    return JSON.parse(params.input);
  }
  
  private executeJSONStringify(params: any): string {
    return JSON.stringify(params.input);
  }
  
  private executeDateFormat(params: any): string {
    const date = params.input ? new Date(params.input) : new Date();
    
    // Simple date formatting - in production, use a library like date-fns
    if (params.format) {
      return params.format
        .replace('YYYY', date.getFullYear().toString())
        .replace('MM', (date.getMonth() + 1).toString().padStart(2, '0'))
        .replace('DD', date.getDate().toString().padStart(2, '0'))
        .replace('HH', date.getHours().toString().padStart(2, '0'))
        .replace('mm', date.getMinutes().toString().padStart(2, '0'))
        .replace('ss', date.getSeconds().toString().padStart(2, '0'));
    }
    
    return date.toISOString();
  }
  
  private executeDateParse(params: any): number {
    return new Date(params.input).getTime();
  }
  
  private executeStringFormat(params: any, context: ExecutorContext): string {
    let result = params.template;
    
    // Replace variables from context
    result = result.replace(/\{\{(\w+(?:\.\w+)*)\}\}/g, (match, path) => {
      const value = this.getNestedValue(context.inputData, path);
      return value != null ? String(value) : match;
    });
    
    // Replace variables from parameters
    if (params.variables) {
      for (const [key, value] of Object.entries(params.variables)) {
        result = result.replace(new RegExp(`\\{\\{${key}\\}\\}`, 'g'), String(value));
      }
    }
    
    return result;
  }
  
  private executeRegexMatch(params: any): any {
    const regex = new RegExp(params.pattern, params.flags || 'g');
    const matches = params.input.match(regex);
    return matches || [];
  }
  
  private executeRegexReplace(params: any): string {
    const regex = new RegExp(params.pattern, params.flags || 'g');
    return params.input.replace(regex, params.replacement || '');
  }
  
  private async executeFileRead(params: any): Promise<string> {
    const content = await fs.readFile(params.path, params.encoding || 'utf8');
    return content;
  }
  
  private async executeFileWrite(params: any): Promise<string> {
    await fs.writeFile(params.path, params.content, {
      encoding: params.encoding || 'utf8',
      mode: params.mode,
    });
    return `File written: ${params.path}`;
  }
  
  private async executeFileDelete(params: any): Promise<string> {
    await fs.unlink(params.path);
    return `File deleted: ${params.path}`;
  }
  
  private async executeDirectoryList(params: any): Promise<string[]> {
    const files = await fs.readdir(params.path);
    return files;
  }
  
  private executeEnvironmentVariable(params: any): any {
    const value = process.env[params.variable];
    return value !== undefined ? value : params.defaultValue;
  }
  
  private executeSystemInfo(): any {
    return {
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      pid: process.pid,
      cwd: process.cwd(),
    };
  }
  
  private executeCounter(params: any): number {
    const current = this.counters.get(params.key) || params.initialValue || 0;
    const increment = params.increment || 1;
    const newValue = current + increment;
    
    this.counters.set(params.key, newValue);
    return newValue;
  }
  
  private executeCacheSet(params: any): string {
    const expiry = params.ttl ? Date.now() + (params.ttl * 1000) : Number.MAX_SAFE_INTEGER;
    this.cache.set(params.key, { value: params.value, expiry });
    return `Cached: ${params.key}`;
  }
  
  private executeCacheGet(params: any): any {
    const cached = this.cache.get(params.key);
    
    if (!cached) {
      return null;
    }
    
    if (Date.now() > cached.expiry) {
      this.cache.delete(params.key);
      return null;
    }
    
    return cached.value;
  }
  
  private executeCacheDelete(params: any): string {
    const deleted = this.cache.delete(params.key);
    return deleted ? `Deleted: ${params.key}` : `Not found: ${params.key}`;
  }
  
  private executeLog(params: any): string {
    const message = `[${params.level.toUpperCase()}] ${params.message}`;
    
    switch (params.level) {
      case 'debug':
        this.logger.debug(message, params.data);
        break;
      case 'info':
        this.logger.log(message, params.data);
        break;
      case 'warn':
        this.logger.warn(message, params.data);
        break;
      case 'error':
        this.logger.error(message, params.data);
        break;
    }
    
    return message;
  }
  
  private executeDebug(params: any, context: ExecutorContext): any {
    const debugInfo = {
      message: params.message,
      data: params.data,
      context: {
        nodeId: context.node.id,
        nodeName: context.node.name,
        executionId: context.executionId,
        workflowId: context.workflowId,
        inputData: context.inputData,
      },
      timestamp: new Date().toISOString(),
    };
    
    this.logger.debug('Debug info:', debugInfo);
    return debugInfo;
  }
  
  private executeError(params: any): never {
    throw new Error(params.message || 'Utility error node triggered');
  }
  
  private async executeCustomFunction(params: any, context: ExecutorContext): Promise<any> {
    try {
      // WARNING: eval is dangerous - use a safe sandbox in production
      const func = new Function('context', 'require', 'console', params.code);
      
      // Create limited context
      const limitedContext = {
        inputData: context.inputData,
        nodeId: context.node.id,
        executionId: context.executionId,
        ...params.context,
      };
      
      // Execute with timeout
      const result = await Promise.race([
        Promise.resolve(func(limitedContext, require, console)),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Custom function timeout')), params.timeout || 5000)
        ),
      ]);
      
      return result;
      
    } catch (error) {
      throw new Error(`Custom function failed: ${error.message}`);
    }
  }
  
  private formatOutput(result: UtilityExecutionResult, config: UtilityNodeConfig): any {
    let output = result.result;
    
    // Format based on configuration
    if (config.output?.format) {
      switch (config.output.format) {
        case 'json':
          output = typeof output === 'string' ? output : JSON.stringify(output);
          break;
        case 'string':
          output = String(output);
          break;
        case 'raw':
        default:
          // Keep as is
          break;
      }
    }
    
    // Wrap in field if specified
    if (config.output?.field) {
      const wrapped: any = {};
      wrapped[config.output.field] = output;
      output = wrapped;
    }
    
    // Include metadata if requested
    if (config.output?.includeMetadata) {
      return {
        result: output,
        metadata: result.metadata,
      };
    }
    
    return output;
  }
  
  private validateOperationParameters(params: any, result: ValidationResult): void {
    switch (params.operation) {
      case 'delay':
        if (!params.parameters?.delay?.duration) {
          result.errors.push({
            code: 'MISSING_DELAY_DURATION',
            message: 'Delay operation requires duration',
            field: 'parameters.delay.duration',
            severity: 'error',
          });
        }
        break;
        
      case 'hash':
        if (!params.parameters?.hash?.input) {
          result.errors.push({
            code: 'MISSING_HASH_INPUT',
            message: 'Hash operation requires input',
            field: 'parameters.hash.input',
            severity: 'error',
          });
        }
        break;
        
      case 'file-read':
      case 'file-write':
      case 'file-delete':
        if (!params.parameters?.file?.path) {
          result.errors.push({
            code: 'MISSING_FILE_PATH',
            message: 'File operation requires path',
            field: 'parameters.file.path',
            severity: 'error',
          });
        }
        break;
        
      case 'custom-function':
        if (!params.parameters?.customFunction?.code) {
          result.errors.push({
            code: 'MISSING_CUSTOM_CODE',
            message: 'Custom function requires code',
            field: 'parameters.customFunction.code',
            severity: 'error',
          });
        }
        break;
    }
  }
  
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }
}
