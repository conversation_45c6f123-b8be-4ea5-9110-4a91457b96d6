import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloService } from './zalo.service';
import {
  ZaloCallPermissionRequest,
  ZaloCallPermissionResponse,
  ZaloCallPermissionStatus,
  ZaloCallLinkRequest,
  ZaloCallLinkResponse,
  ZaloAgentInfo,
  ZaloBranchInfo,
  ZaloCallInfo,
  ZaloCallErrorCode,
} from './zalo.interface';

/**
 * Service xử lý các API gọi thoại của Zalo Official Account
 *
 * Điều kiện sử dụng từ tài liệu Zalo:
 * - Cấp quyền gọi: OA cần được phê duyệt tính năng gọi thoại từ Zalo
 * - Kiểm tra quyền: Chỉ kiểm tra được quyền của user đã tương tác với OA
 * - Tạo link gọi: Chỉ tạo được link cho user đã cấp quyền gọi thoại
 * - Agent/Branch: Cần cấu hình agent và branch trước khi sử dụng
 * - Thời gian hết hạn: Link gọi thoại có thời gian hết hạn tối đa 24 giờ
 * - Giới hạn cuộc gọi: Mỗi OA có giới hạn số cuộc gọi đồng thời
 */
@Injectable()
export class ZaloCallService {
  private readonly logger = new Logger(ZaloCallService.name);
  private readonly baseApiUrl = 'https://openapi.zalo.me/v2.0/oa';

  constructor(
    private readonly zaloService: ZaloService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Gửi yêu cầu cấp quyền gọi thoại đến người dùng
   * @param accessToken Access token của Official Account
   * @param request Thông tin yêu cầu cấp quyền
   * @returns Kết quả gửi yêu cầu
   */
  async requestCallPermission(
    accessToken: string,
    request: ZaloCallPermissionRequest,
  ): Promise<ZaloCallPermissionResponse> {
    try {
      this.logger.debug(
        `Requesting call permission for user ${request.user_id}`,
      );

      const data = {
        user_id: request.user_id,
        reason:
          request.reason || 'Yêu cầu cấp quyền gọi thoại để hỗ trợ tốt hơn',
        metadata: request.metadata,
      };

      return await this.zaloService.post<ZaloCallPermissionResponse>(
        `${this.baseApiUrl}/call/permission/request`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(
        `Error requesting call permission: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi yêu cầu cấp quyền gọi thoại',
      );
    }
  }

  /**
   * Kiểm tra trạng thái cấp quyền gọi thoại của người dùng
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @returns Trạng thái cấp quyền gọi thoại
   */
  async checkCallPermission(
    accessToken: string,
    userId: string,
  ): Promise<ZaloCallPermissionStatus> {
    try {
      this.logger.debug(`Checking call permission for user ${userId}`);

      return await this.zaloService.get<ZaloCallPermissionStatus>(
        `${this.baseApiUrl}/call/permission/check?user_id=${userId}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(
        `Error checking call permission: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi kiểm tra trạng thái cấp quyền gọi thoại',
      );
    }
  }

  /**
   * Tạo link gọi thoại outbound từ OA đến User
   * @param accessToken Access token của Official Account
   * @param request Thông tin yêu cầu tạo link gọi
   * @returns Link gọi thoại và thông tin liên quan
   */
  async createCallLink(
    accessToken: string,
    request: ZaloCallLinkRequest,
  ): Promise<ZaloCallLinkResponse> {
    try {
      this.logger.debug(`Creating call link for user ${request.user_id}`);

      const data = {
        user_id: request.user_id,
        call_type: request.call_type,
        agent_id: request.agent_id,
        branch_id: request.branch_id,
        expires_in: request.expires_in || 3600, // Mặc định 1 giờ
        metadata: request.metadata,
      };

      return await this.zaloService.post<ZaloCallLinkResponse>(
        `${this.baseApiUrl}/call/link/create`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(
        `Error creating call link: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tạo link gọi thoại',
      );
    }
  }

  /**
   * Lấy thông tin agent
   * @param accessToken Access token của Official Account
   * @param agentId ID của agent (tùy chọn, nếu không có sẽ lấy tất cả)
   * @returns Thông tin agent
   */
  async getAgentInfo(
    accessToken: string,
    agentId?: string,
  ): Promise<ZaloAgentInfo | ZaloAgentInfo[]> {
    try {
      const url = agentId
        ? `${this.baseApiUrl}/call/agent/${agentId}`
        : `${this.baseApiUrl}/call/agent`;

      this.logger.debug(`Getting agent info: ${agentId || 'all agents'}`);

      return await this.zaloService.get<ZaloAgentInfo | ZaloAgentInfo[]>(
        url,
        accessToken,
      );
    } catch (error) {
      this.logger.error(
        `Error getting agent info: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin agent',
      );
    }
  }

  /**
   * Lấy thông tin branch
   * @param accessToken Access token của Official Account
   * @param branchId ID của branch (tùy chọn, nếu không có sẽ lấy tất cả)
   * @returns Thông tin branch
   */
  async getBranchInfo(
    accessToken: string,
    branchId?: string,
  ): Promise<ZaloBranchInfo | ZaloBranchInfo[]> {
    try {
      const url = branchId
        ? `${this.baseApiUrl}/call/branch/${branchId}`
        : `${this.baseApiUrl}/call/branch`;

      this.logger.debug(`Getting branch info: ${branchId || 'all branches'}`);

      return await this.zaloService.get<ZaloBranchInfo | ZaloBranchInfo[]>(
        url,
        accessToken,
      );
    } catch (error) {
      this.logger.error(
        `Error getting branch info: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin branch',
      );
    }
  }

  /**
   * Lấy thông tin cuộc gọi
   * @param accessToken Access token của Official Account
   * @param callId ID của cuộc gọi
   * @returns Thông tin cuộc gọi
   */
  async getCallInfo(
    accessToken: string,
    callId: string,
  ): Promise<ZaloCallInfo> {
    try {
      this.logger.debug(`Getting call info for call ${callId}`);

      return await this.zaloService.get<ZaloCallInfo>(
        `${this.baseApiUrl}/call/info/${callId}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(
        `Error getting call info: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin cuộc gọi',
      );
    }
  }

  /**
   * Lấy danh sách cuộc gọi
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng (tùy chọn)
   * @param agentId ID của agent (tùy chọn)
   * @param fromTime Thời gian bắt đầu (Unix timestamp)
   * @param toTime Thời gian kết thúc (Unix timestamp)
   * @param limit Số lượng kết quả tối đa
   * @param offset Offset cho phân trang
   * @returns Danh sách cuộc gọi
   */
  async getCallHistory(
    accessToken: string,
    options: {
      userId?: string;
      agentId?: string;
      fromTime?: number;
      toTime?: number;
      limit?: number;
      offset?: number;
    } = {},
  ): Promise<{ calls: ZaloCallInfo[]; total: number; has_more: boolean }> {
    try {
      const params = new URLSearchParams();

      if (options.userId) params.append('user_id', options.userId);
      if (options.agentId) params.append('agent_id', options.agentId);
      if (options.fromTime)
        params.append('from_time', options.fromTime.toString());
      if (options.toTime) params.append('to_time', options.toTime.toString());
      if (options.limit)
        params.append('limit', Math.min(options.limit, 100).toString());
      if (options.offset) params.append('offset', options.offset.toString());

      this.logger.debug(
        `Getting call history with params: ${params.toString()}`,
      );

      return await this.zaloService.get<{
        calls: ZaloCallInfo[];
        total: number;
        has_more: boolean;
      }>(`${this.baseApiUrl}/call/history?${params.toString()}`, accessToken);
    } catch (error) {
      this.logger.error(
        `Error getting call history: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy lịch sử cuộc gọi',
      );
    }
  }

  /**
   * Kết thúc cuộc gọi
   * @param accessToken Access token của Official Account
   * @param callId ID của cuộc gọi
   * @param reason Lý do kết thúc cuộc gọi
   * @returns Kết quả kết thúc cuộc gọi
   */
  async endCall(
    accessToken: string,
    callId: string,
    reason?: string,
  ): Promise<{ success: boolean; message?: string }> {
    try {
      this.logger.debug(`Ending call ${callId}`);

      const data = {
        call_id: callId,
        reason: reason || 'Kết thúc cuộc gọi',
      };

      return await this.zaloService.post<{
        success: boolean;
        message?: string;
      }>(`${this.baseApiUrl}/call/end`, accessToken, data);
    } catch (error) {
      this.logger.error(`Error ending call: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi kết thúc cuộc gọi',
      );
    }
  }

  /**
   * Lấy thống kê cuộc gọi
   * @param accessToken Access token của Official Account
   * @param fromDate Ngày bắt đầu (YYYY-MM-DD)
   * @param toDate Ngày kết thúc (YYYY-MM-DD)
   * @param agentId ID của agent (tùy chọn)
   * @param branchId ID của branch (tùy chọn)
   * @returns Thống kê cuộc gọi
   */
  async getCallStatistics(
    accessToken: string,
    fromDate: string,
    toDate: string,
    agentId?: string,
    branchId?: string,
  ): Promise<{
    total_calls: number;
    successful_calls: number;
    failed_calls: number;
    average_duration: number;
    total_duration: number;
    call_types: {
      audio: number;
      video: number;
    };
    daily_stats: Array<{
      date: string;
      total_calls: number;
      successful_calls: number;
      average_duration: number;
    }>;
  }> {
    try {
      const params = new URLSearchParams();
      params.append('from_date', fromDate);
      params.append('to_date', toDate);
      if (agentId) params.append('agent_id', agentId);
      if (branchId) params.append('branch_id', branchId);

      this.logger.debug(
        `Getting call statistics from ${fromDate} to ${toDate}`,
      );

      return await this.zaloService.get<{
        total_calls: number;
        successful_calls: number;
        failed_calls: number;
        average_duration: number;
        total_duration: number;
        call_types: {
          audio: number;
          video: number;
        };
        daily_stats: Array<{
          date: string;
          total_calls: number;
          successful_calls: number;
          average_duration: number;
        }>;
      }>(
        `${this.baseApiUrl}/call/statistics?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(
        `Error getting call statistics: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thống kê cuộc gọi',
      );
    }
  }

  /**
   * Cập nhật trạng thái agent
   * @param accessToken Access token của Official Account
   * @param agentId ID của agent
   * @param status Trạng thái mới (online, offline, busy)
   * @returns Kết quả cập nhật
   */
  async updateAgentStatus(
    accessToken: string,
    agentId: string,
    status: 'online' | 'offline' | 'busy',
  ): Promise<{ success: boolean; message?: string }> {
    try {
      this.logger.debug(`Updating agent ${agentId} status to ${status}`);

      const data = {
        agent_id: agentId,
        status: status,
      };

      return await this.zaloService.post<{
        success: boolean;
        message?: string;
      }>(`${this.baseApiUrl}/call/agent/status`, accessToken, data);
    } catch (error) {
      this.logger.error(
        `Error updating agent status: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi cập nhật trạng thái agent',
      );
    }
  }

  /**
   * Lấy danh sách mã lỗi Call API
   * @returns Danh sách mã lỗi và mô tả
   */
  getCallErrorCodes(): ZaloCallErrorCode[] {
    return [
      {
        error_code: 1001,
        error_name: 'PERMISSION_DENIED',
        error_description: 'Người dùng chưa cấp quyền gọi thoại',
      },
      {
        error_code: 1002,
        error_name: 'PERMISSION_EXPIRED',
        error_description: 'Quyền gọi thoại đã hết hạn',
      },
      {
        error_code: 1003,
        error_name: 'USER_NOT_FOUND',
        error_description: 'Không tìm thấy người dùng',
      },
      {
        error_code: 1004,
        error_name: 'AGENT_NOT_AVAILABLE',
        error_description: 'Agent không có sẵn',
      },
      {
        error_code: 1005,
        error_name: 'CALL_LIMIT_EXCEEDED',
        error_description: 'Vượt quá giới hạn số cuộc gọi đồng thời',
      },
      {
        error_code: 1006,
        error_name: 'INVALID_CALL_TYPE',
        error_description: 'Loại cuộc gọi không hợp lệ',
      },
      {
        error_code: 1007,
        error_name: 'CALL_LINK_EXPIRED',
        error_description: 'Link gọi thoại đã hết hạn',
      },
      {
        error_code: 1008,
        error_name: 'BRANCH_NOT_FOUND',
        error_description: 'Không tìm thấy branch',
      },
      {
        error_code: 1009,
        error_name: 'AGENT_NOT_FOUND',
        error_description: 'Không tìm thấy agent',
      },
      {
        error_code: 1010,
        error_name: 'CALL_FEATURE_NOT_ENABLED',
        error_description: 'Tính năng gọi thoại chưa được kích hoạt cho OA',
      },
    ];
  }
}
