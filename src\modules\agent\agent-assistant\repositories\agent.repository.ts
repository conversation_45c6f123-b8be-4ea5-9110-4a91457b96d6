import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { Agent } from '../entities/agent.entity';

@Injectable()
export class AgentRepository {
  constructor(
    @InjectRepository(Agent)
    private readonly repository: Repository<Agent>,
  ) {}

  async findById(id: string): Promise<Agent | null> {
    return this.repository.findOne({
      where: {
        id,
        deletedAt: IsNull(),
      }
    });
  }
}
