import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { CampaignAudience } from '../types/campaign.types';

/**
 * Entity đại diện cho bảng user_campaign_history trong cơ sở dữ liệu
 * Bảng lịch sử chăm sóc khách hàng
 */
@Entity('user_campaign_history')
export class UserCampaignHistory {
  /**
   * ID của lịch sử
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của campaign
   */
  @Column({
    name: 'campaign_id',
    type: 'bigint',
    nullable: true,
    comment: 'Mã chiến dịch',
  })
  campaignId: number;

  // Không sử dụng quan hệ với bảng UserCampaign, chỉ lưu ID

  /**
   * Thông tin audience
   */
  @Column({
    name: 'audience',
    type: 'jsonb',
    nullable: true,
    comment: 'Thông tin audience với tên và email',
  })
  audience: CampaignAudience | null;

  // Không sử dụng quan hệ với bảng UserAudience, lưu trực tiếp thông tin audience

  /**
   * Trạng thái gửi
   */
  @Column({ name: 'status', length: 20, nullable: true, comment: 'Trạng thái' })
  status: string;

  /**
   * Thời gian gửi (Unix timestamp)
   */
  @Column({
    name: 'sent_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian gửi',
  })
  sentAt: number;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian tạo',
  })
  createdAt: number;
}
