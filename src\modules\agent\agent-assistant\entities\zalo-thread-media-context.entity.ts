import { Entity, PrimaryColumn, Column } from 'typeorm';
import { ZaloThreadMediaContextTypeEnum } from '../enums/zalo-thread-media-context.enum';

/**
 * Zalo thread media context entity - associates Zalo media with conversation threads
 */
@Entity('zalo_thread_media_context')
export class ZaloThreadMediaContext {
  @PrimaryColumn({ name: 'thread_id', type: 'uuid' })
  threadId: string;

  @PrimaryColumn({ name: 'zalo_media_id', type: 'uuid' })
  zaloMediaId: string;

  @Column({
    name: 'context_type',
    type: 'enum',
    enum: ZaloThreadMediaContextTypeEnum,
    default: ZaloThreadMediaContextTypeEnum.IMAGE,
  })
  contextType: ZaloThreadMediaContextTypeEnum;

  @Column({ name: 'human_notes', type: 'text', nullable: true })
  humanNotes: string | null;

  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;
}
