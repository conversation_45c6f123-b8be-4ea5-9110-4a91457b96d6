import { Injectable, Logger } from '@nestjs/common';

/**
 * Service xử lý template SMS marketing
 */
@Injectable()
export class SmsTemplateService {
  private readonly logger = new Logger(SmsTemplateService.name);

  /**
   * Thay thế các placeholder trong nội dung SMS
   * @param content Nội dung SMS gốc
   * @param variables Dữ liệu để thay thế (templateVariables + customFields)
   * @returns Nội dung SMS đã được thay thế
   */
  replaceTemplateVariables(
    content: string,
    variables: Record<string, any>,
  ): string {
    this.logger.debug('Thay thế biến trong template SMS marketing');

    let processedContent = content;

    // Thay thế các biến có cú pháp {{BIEN}}
    Object.keys(variables).forEach((key) => {
      const placeholder = `{{${key}}}`;
      const value = variables[key]?.toString() || '';
      processedContent = processedContent.replace(
        new RegExp(placeholder, 'g'),
        value,
      );

      this.logger.debug(`Thay thế ${placeholder} -> ${value}`);
    });

    this.logger.debug(`Nội dung SMS sau khi thay thế: ${processedContent}`);
    return processedContent;
  }

  /**
   * Kết hợp template variables với custom fields
   * Custom fields sẽ override template variables nếu trùng key
   * @param templateVariables Variables global cho tất cả recipients
   * @param customFields Custom fields của từng audience
   * @returns Object variables đã được kết hợp
   */
  combineVariables(
    templateVariables: Record<string, any>,
    customFields: Record<string, any>,
  ): Record<string, any> {
    const combinedVariables = {
      ...templateVariables,
      ...customFields,
    };

    this.logger.debug(
      `Combined variables: templateVariables=${JSON.stringify(templateVariables)}, customFields=${JSON.stringify(customFields)}, result=${JSON.stringify(combinedVariables)}`,
    );

    return combinedVariables;
  }

  /**
   * Xử lý template SMS hoàn chỉnh
   * @param content Nội dung SMS template
   * @param templateVariables Variables global
   * @param customFields Custom fields của audience
   * @returns Nội dung SMS đã được xử lý
   */
  processTemplate(
    content: string,
    templateVariables: Record<string, any>,
    customFields: Record<string, any>,
  ): string {
    this.logger.debug('Xử lý template SMS marketing');

    // Kết hợp variables
    const combinedVariables = this.combineVariables(
      templateVariables,
      customFields,
    );

    // Thay thế variables trong content
    const processedContent = this.replaceTemplateVariables(
      content,
      combinedVariables,
    );

    this.logger.debug('Đã xử lý template SMS marketing thành công');
    return processedContent;
  }

  /**
   * Validate nội dung SMS
   * @param content Nội dung SMS
   * @returns True nếu hợp lệ
   */
  validateSmsContent(content: string): boolean {
    if (!content || content.trim().length === 0) {
      this.logger.error('SMS content is empty');
      return false;
    }

    // Kiểm tra độ dài SMS (thông thường tối đa 160 ký tự cho SMS đơn)
    if (content.length > 1600) {
      // Cho phép SMS dài (concatenated SMS)
      this.logger.warn(
        `SMS content is very long: ${content.length} characters`,
      );
    }

    return true;
  }

  /**
   * Tính số lượng SMS cần thiết dựa trên độ dài nội dung
   * @param content Nội dung SMS
   * @returns Số lượng SMS
   */
  calculateSmsCount(content: string): number {
    if (!content) return 0;

    // SMS đơn: 160 ký tự
    // SMS nối: 153 ký tự mỗi phần (do header)
    if (content.length <= 160) {
      return 1;
    }

    return Math.ceil((content.length - 160) / 153) + 1;
  }
}
