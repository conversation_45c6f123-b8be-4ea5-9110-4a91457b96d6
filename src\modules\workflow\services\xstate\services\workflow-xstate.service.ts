import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { createMachine, interpret, InterpreterFrom, StateFrom } from 'xstate';
import { 
  WorkflowContext, 
  WorkflowEvent, 
  NodeExecutionContext,
  DetailedNodeExecutionResult,
  WorkflowEventTypes
} from '../types';
import { DependencyResolverService } from './dependency-resolver.service';
import { WorkflowStateManagerService } from './workflow-state-manager.service';
import { NodeExecutorFactory } from '../executors/node-executor.factory';
import { WorkflowRepository } from '../../../repositories/workflow.repository';
import { NodeRepository } from '../../../repositories/node.repository';
import { ConnectionRepository } from '../../../repositories/connection.repository';
import { ExecutionRepository } from '../../../repositories/execution.repository';

/**
 * Workflow execution request
 */
export interface WorkflowExecutionRequest {
  workflowId: string;
  executionId: string;
  userId: number;
  triggerData: any;
  triggerType: 'manual' | 'webhook' | 'schedule';
  options?: {
    enableSSE?: boolean;
    timeout?: number;
    retryOnFailure?: boolean;
    skipValidation?: boolean;
    maxConcurrency?: number;
  };
}

/**
 * Workflow execution result
 */
export interface WorkflowExecutionResult {
  success: boolean;
  executionId: string;
  workflowId: string;
  startTime: number;
  endTime?: number;
  executionTime?: number;
  completedNodes: number;
  totalNodes: number;
  finalOutputData?: any;
  error?: Error;
  metadata?: {
    nodeResults: Record<string, DetailedNodeExecutionResult>;
    checkpoints: string[];
    retryCount: number;
    [key: string]: any;
  };
}

/**
 * Main orchestrator service để manage XState workflow execution
 */
@Injectable()
export class WorkflowXStateService {
  private readonly logger = new Logger(WorkflowXStateService.name);
  private readonly activeExecutions = new Map<string, InterpreterFrom<any>>();
  
  constructor(
    private readonly dependencyResolver: DependencyResolverService,
    private readonly stateManager: WorkflowStateManagerService,
    private readonly executorFactory: NodeExecutorFactory,
    private readonly eventEmitter: EventEmitter2,
    private readonly workflowRepository: WorkflowRepository,
    private readonly nodeRepository: NodeRepository,
    private readonly connectionRepository: ConnectionRepository,
    private readonly executionRepository: ExecutionRepository,
  ) {}
  
  /**
   * Execute workflow với XState machine
   */
  async executeWorkflow(request: WorkflowExecutionRequest): Promise<WorkflowExecutionResult> {
    const { workflowId, executionId, userId, triggerData, triggerType, options } = request;
    
    this.logger.log(`Starting workflow execution: ${executionId} for workflow: ${workflowId}`);
    
    try {
      // Load workflow definition
      const context = await this.loadWorkflowContext(
        workflowId, 
        executionId, 
        userId, 
        triggerData, 
        triggerType, 
        options
      );
      
      // Create và start XState machine
      const machine = this.createWorkflowMachine(context);
      const service = interpret(machine);
      
      // Store active execution
      this.activeExecutions.set(executionId, service);
      
      // Setup event listeners
      this.setupServiceListeners(service, context);
      
      // Start execution
      service.start();
      service.send({ type: WorkflowEventTypes.START_EXECUTION, triggerData });
      
      // Wait for completion hoặc timeout
      const result = await this.waitForCompletion(service, context, options?.timeout);
      
      // Cleanup
      this.activeExecutions.delete(executionId);
      service.stop();
      
      this.logger.log(`Workflow execution completed: ${executionId}`);
      
      return result;
      
    } catch (error) {
      this.logger.error(`Workflow execution failed: ${executionId}`, error);
      
      // Cleanup on error
      this.activeExecutions.delete(executionId);
      
      return {
        success: false,
        executionId,
        workflowId,
        startTime: Date.now(),
        completedNodes: 0,
        totalNodes: 0,
        error,
      };
    }
  }
  
  /**
   * Pause workflow execution
   */
  async pauseWorkflow(executionId: string, reason?: string): Promise<boolean> {
    const service = this.activeExecutions.get(executionId);
    
    if (!service) {
      this.logger.warn(`Cannot pause workflow: execution ${executionId} not found`);
      return false;
    }
    
    try {
      service.send({ 
        type: WorkflowEventTypes.PAUSE_EXECUTION, 
        reason,
        pausedBy: 'user'
      });
      
      this.logger.log(`Paused workflow execution: ${executionId}`);
      return true;
      
    } catch (error) {
      this.logger.error(`Failed to pause workflow ${executionId}:`, error);
      return false;
    }
  }
  
  /**
   * Resume workflow execution
   */
  async resumeWorkflow(executionId: string): Promise<boolean> {
    const service = this.activeExecutions.get(executionId);
    
    if (!service) {
      this.logger.warn(`Cannot resume workflow: execution ${executionId} not found`);
      return false;
    }
    
    try {
      service.send({ 
        type: WorkflowEventTypes.RESUME_EXECUTION,
        resumedBy: 'user'
      });
      
      this.logger.log(`Resumed workflow execution: ${executionId}`);
      return true;
      
    } catch (error) {
      this.logger.error(`Failed to resume workflow ${executionId}:`, error);
      return false;
    }
  }
  
  /**
   * Cancel workflow execution
   */
  async cancelWorkflow(executionId: string, reason?: string): Promise<boolean> {
    const service = this.activeExecutions.get(executionId);
    
    if (!service) {
      this.logger.warn(`Cannot cancel workflow: execution ${executionId} not found`);
      return false;
    }
    
    try {
      service.send({ 
        type: WorkflowEventTypes.CANCEL_EXECUTION,
        reason,
        cancelledBy: 'user'
      });
      
      // Stop service
      service.stop();
      this.activeExecutions.delete(executionId);
      
      this.logger.log(`Cancelled workflow execution: ${executionId}`);
      return true;
      
    } catch (error) {
      this.logger.error(`Failed to cancel workflow ${executionId}:`, error);
      return false;
    }
  }
  
  /**
   * Get workflow execution status
   */
  getExecutionStatus(executionId: string): {
    isActive: boolean;
    currentState?: string;
    context?: Partial<WorkflowContext>;
  } {
    const service = this.activeExecutions.get(executionId);
    
    if (!service) {
      return { isActive: false };
    }
    
    const snapshot = service.getSnapshot();
    
    return {
      isActive: true,
      currentState: snapshot.value as string,
      context: {
        workflowId: snapshot.context.workflowId,
        executionId: snapshot.context.executionId,
        currentNode: snapshot.context.currentNode,
        runningNodes: snapshot.context.runningNodes,
        readyNodes: snapshot.context.readyNodes,
        waitingNodes: snapshot.context.waitingNodes,
        metadata: snapshot.context.metadata,
      },
    };
  }
  
  /**
   * List tất cả active executions
   */
  getActiveExecutions(): Array<{
    executionId: string;
    workflowId: string;
    currentState: string;
    startTime: number;
  }> {
    const executions: Array<{
      executionId: string;
      workflowId: string;
      currentState: string;
      startTime: number;
    }> = [];
    
    for (const [executionId, service] of this.activeExecutions.entries()) {
      const snapshot = service.getSnapshot();
      executions.push({
        executionId,
        workflowId: snapshot.context.workflowId,
        currentState: snapshot.value as string,
        startTime: snapshot.context.metadata.startTime,
      });
    }
    
    return executions;
  }
  
  /**
   * Retry failed workflow từ checkpoint
   */
  async retryWorkflow(
    executionId: string,
    fromCheckpoint?: string,
    resetFailedNodes: boolean = true
  ): Promise<WorkflowExecutionResult> {
    try {
      // Load snapshot
      const snapshot = await this.stateManager.loadStateSnapshot(executionId, fromCheckpoint);
      
      if (!snapshot) {
        throw new Error(`No snapshot found for execution ${executionId}`);
      }
      
      // Restore context
      const context = await this.stateManager.restoreWorkflowContext(snapshot, {
        resetRunningNodes: true,
        clearErrors: resetFailedNodes,
        validateAfterRestore: true,
      });
      
      // Create new execution request
      const retryRequest: WorkflowExecutionRequest = {
        workflowId: context.workflowId,
        executionId: `${executionId}_retry_${Date.now()}`,
        userId: context.metadata.userId,
        triggerData: context.triggerData,
        triggerType: context.metadata.triggerType,
        options: context.options,
      };
      
      this.logger.log(`Retrying workflow from checkpoint: ${executionId} -> ${retryRequest.executionId}`);
      
      return await this.executeWorkflow(retryRequest);
      
    } catch (error) {
      this.logger.error(`Failed to retry workflow ${executionId}:`, error);
      throw error;
    }
  }
  
  // Private helper methods
  
  private async loadWorkflowContext(
    workflowId: string,
    executionId: string,
    userId: number,
    triggerData: any,
    triggerType: 'manual' | 'webhook' | 'schedule',
    options?: any
  ): Promise<WorkflowContext> {
    // Load workflow definition
    const workflow = await this.workflowRepository.findById(workflowId);
    if (!workflow) {
      throw new Error(`Workflow not found: ${workflowId}`);
    }
    
    // Load nodes
    const nodes = await this.nodeRepository.findByWorkflowId(workflowId);
    if (nodes.length === 0) {
      throw new Error(`No nodes found for workflow: ${workflowId}`);
    }
    
    // Load connections
    const connections = await this.connectionRepository.findByWorkflowId(workflowId);
    
    // Analyze dependencies
    const dependencyAnalysis = await this.dependencyResolver.analyzeDependencies(nodes, connections);
    
    if (dependencyAnalysis.hasCircularDependencies) {
      throw new Error(`Circular dependencies detected in workflow: ${workflowId}`);
    }
    
    // Create node execution states
    const nodeStates = new Map();
    for (const node of nodes) {
      nodeStates.set(node.id, {
        id: node.id,
        status: 'pending',
        inputData: null,
        outputData: null,
        retryCount: 0,
        node,
      });
    }
    
    // Create workflow context
    const context: WorkflowContext = {
      workflowId,
      executionId,
      nodes: nodeStates,
      connections,
      runningNodes: [],
      executionData: new Map(),
      errors: new Map(),
      triggerData,
      metadata: {
        startTime: Date.now(),
        userId,
        retryCount: 0,
        triggerType,
        totalNodes: nodes.length,
        completedNodes: 0,
        failedNodes: 0,
      },
      dependencyGraph: dependencyAnalysis.graph,
      readyNodes: dependencyAnalysis.graph.rootNodes,
      waitingNodes: [],
      options,
      workflowSettings: workflow.settings,
    };
    
    return context;
  }
  
  private createWorkflowMachine(initialContext: WorkflowContext) {
    // This is a simplified machine - full implementation would be in machines/workflow.machine.ts
    return createMachine({
      id: 'workflow',
      initial: 'loading',
      context: initialContext,
      states: {
        loading: {
          on: {
            [WorkflowEventTypes.START_EXECUTION]: 'executing'
          }
        },
        executing: {
          // Parallel execution states would be defined here
          on: {
            [WorkflowEventTypes.WORKFLOW_COMPLETED]: 'completed',
            [WorkflowEventTypes.WORKFLOW_FAILED]: 'failed',
            [WorkflowEventTypes.PAUSE_EXECUTION]: 'paused',
            [WorkflowEventTypes.CANCEL_EXECUTION]: 'cancelled'
          }
        },
        completed: {
          type: 'final'
        },
        failed: {
          on: {
            [WorkflowEventTypes.RETRY_WORKFLOW]: 'executing'
          }
        },
        paused: {
          on: {
            [WorkflowEventTypes.RESUME_EXECUTION]: 'executing',
            [WorkflowEventTypes.CANCEL_EXECUTION]: 'cancelled'
          }
        },
        cancelled: {
          type: 'final'
        }
      }
    });
  }
  
  private setupServiceListeners(service: InterpreterFrom<any>, context: WorkflowContext): void {
    service.onTransition((state) => {
      this.logger.debug(`Workflow ${context.executionId} transitioned to: ${state.value}`);
      
      // Emit SSE events if enabled
      if (context.options?.enableSSE) {
        this.eventEmitter.emit('workflow.state.changed', {
          executionId: context.executionId,
          workflowId: context.workflowId,
          state: state.value,
          context: state.context,
        });
      }
      
      // Auto-save checkpoints on important transitions
      if (['paused', 'failed'].includes(state.value as string)) {
        this.stateManager.createAutoCheckpoint(
          state.context,
          state,
          `state_${state.value}`
        ).catch(error => {
          this.logger.warn(`Failed to create auto checkpoint:`, error);
        });
      }
    });
    
    service.onDone(() => {
      this.logger.log(`Workflow ${context.executionId} completed`);
    });
    
    service.onStop(() => {
      this.logger.log(`Workflow ${context.executionId} stopped`);
    });
  }
  
  private async waitForCompletion(
    service: InterpreterFrom<any>,
    context: WorkflowContext,
    timeout?: number
  ): Promise<WorkflowExecutionResult> {
    return new Promise((resolve, reject) => {
      const timeoutId = timeout ? setTimeout(() => {
        reject(new Error(`Workflow execution timeout after ${timeout}ms`));
      }, timeout) : null;
      
      service.onDone(() => {
        if (timeoutId) clearTimeout(timeoutId);
        
        const snapshot = service.getSnapshot();
        const finalContext = snapshot.context as WorkflowContext;
        
        resolve({
          success: true,
          executionId: context.executionId,
          workflowId: context.workflowId,
          startTime: context.metadata.startTime,
          endTime: Date.now(),
          executionTime: Date.now() - context.metadata.startTime,
          completedNodes: finalContext.metadata.completedNodes,
          totalNodes: finalContext.metadata.totalNodes,
          finalOutputData: Object.fromEntries(finalContext.executionData.entries()),
        });
      });
      
      service.onStop(() => {
        if (timeoutId) clearTimeout(timeoutId);
        
        const snapshot = service.getSnapshot();
        const finalContext = snapshot.context as WorkflowContext;
        
        // Check if stopped due to error
        const hasErrors = finalContext.errors.size > 0;
        
        resolve({
          success: !hasErrors,
          executionId: context.executionId,
          workflowId: context.workflowId,
          startTime: context.metadata.startTime,
          endTime: Date.now(),
          executionTime: Date.now() - context.metadata.startTime,
          completedNodes: finalContext.metadata.completedNodes,
          totalNodes: finalContext.metadata.totalNodes,
          error: hasErrors ? Array.from(finalContext.errors.values())[0] : undefined,
        });
      });
    });
  }
}
