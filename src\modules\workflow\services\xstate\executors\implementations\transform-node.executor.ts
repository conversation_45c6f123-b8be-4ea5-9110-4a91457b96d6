import { Injectable, Logger } from '@nestjs/common';
import { BaseNodeExecutor } from '../base/base-node.executor';
import { 
  ExecutorContext, 
  ValidationResult,
  DetailedNodeExecutionResult 
} from '../base/node-executor.interface';
import { NodeExecutionConfig } from '../../types';
import { NodeGroupEnum } from '../../../../enums/node-group.enum';

/**
 * Transform operation types
 */
type TransformOperationType = 
  | 'map'
  | 'filter'
  | 'reduce'
  | 'sort'
  | 'group'
  | 'aggregate'
  | 'flatten'
  | 'merge'
  | 'split'
  | 'extract'
  | 'format'
  | 'convert'
  | 'validate'
  | 'sanitize';

/**
 * Field mapping configuration
 */
interface FieldMapping {
  /** Source field path (supports dot notation) */
  source: string;
  
  /** Target field path */
  target: string;
  
  /** Transform function to apply */
  transform?: 'uppercase' | 'lowercase' | 'trim' | 'number' | 'boolean' | 'date' | 'json' | 'string' | 'custom';
  
  /** Custom transform function (JavaScript code) */
  customTransform?: string;
  
  /** Default value if source is null/undefined */
  defaultValue?: any;
  
  /** Required field */
  required?: boolean;
  
  /** Validation rules */
  validation?: {
    type?: 'string' | 'number' | 'boolean' | 'date' | 'email' | 'url' | 'regex';
    pattern?: string;
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
  };
}

/**
 * Transform node configuration
 */
interface TransformNodeConfig {
  /** Type of transform operation */
  operation: TransformOperationType;
  
  /** Field mappings */
  mappings?: FieldMapping[];
  
  /** Filter conditions */
  filter?: {
    conditions: Array<{
      field: string;
      operator: '==' | '!=' | '>' | '<' | '>=' | '<=' | 'contains' | 'regex';
      value: any;
    }>;
    logic?: 'AND' | 'OR';
  };
  
  /** Sort configuration */
  sort?: {
    field: string;
    direction: 'asc' | 'desc';
    type?: 'string' | 'number' | 'date';
  }[];
  
  /** Group configuration */
  group?: {
    by: string | string[];
    aggregations?: Array<{
      field: string;
      operation: 'sum' | 'avg' | 'count' | 'min' | 'max' | 'first' | 'last';
      alias?: string;
    }>;
  };
  
  /** Merge configuration */
  merge?: {
    sources: string[];
    strategy: 'deep' | 'shallow' | 'overwrite' | 'append';
    conflictResolution?: 'first' | 'last' | 'merge' | 'error';
  };
  
  /** Format configuration */
  format?: {
    template?: string;
    dateFormat?: string;
    numberFormat?: {
      decimals?: number;
      thousands?: string;
      decimal?: string;
    };
  };
  
  /** Validation configuration */
  validation?: {
    schema?: any; // JSON Schema
    strict?: boolean;
    removeInvalid?: boolean;
  };
  
  /** Options */
  options?: {
    preserveTypes?: boolean;
    allowPartialTransform?: boolean;
    skipErrors?: boolean;
    maxDepth?: number;
    timeout?: number;
  };
}

/**
 * Transform execution result
 */
interface TransformExecutionResult {
  /** Transformed data */
  result: any;
  
  /** Transform statistics */
  statistics: {
    inputCount: number;
    outputCount: number;
    transformedFields: number;
    skippedFields: number;
    errors: number;
    warnings: number;
  };
  
  /** Validation results */
  validation?: {
    valid: number;
    invalid: number;
    errors: Array<{
      field: string;
      value: any;
      error: string;
    }>;
  };
  
  /** Execution metadata */
  metadata: {
    operationType: TransformOperationType;
    executionTime: number;
    memoryUsage?: number;
  };
}

/**
 * Executor cho Transform nodes - handle data transformation, mapping, filtering, aggregation
 */
@Injectable()
export class TransformNodeExecutor extends BaseNodeExecutor {
  readonly nodeGroup = NodeGroupEnum.TRANSFORM;
  readonly supportedNodeTypes = [
    'transform',
    'map-data',
    'filter-data',
    'sort-data',
    'group-data',
    'aggregate',
    'flatten',
    'merge',
    'split',
    'extract',
    'format',
    'convert',
    'validate',
    'sanitize',
    'data-mapper',
    'field-mapper',
  ];
  readonly executorName = 'TransformNodeExecutor';
  readonly version = '1.0.0';
  
  /**
   * Execute transform node
   */
  protected async executeNode(
    context: ExecutorContext,
    config: NodeExecutionConfig
  ): Promise<DetailedNodeExecutionResult> {
    const startTime = Date.now();
    
    try {
      // Parse transform configuration
      const transformConfig = this.parseTransformConfig(context);
      
      // Execute transform operation
      const result = await this.executeTransformOperation(transformConfig, context);
      
      const executionTime = Date.now() - startTime;
      
      return {
        success: true,
        outputData: {
          result: result.result,
          statistics: result.statistics,
          validation: result.validation,
          metadata: result.metadata,
        },
        metadata: {
          executionTime,
          customMetrics: {
            operationType: result.metadata.operationType,
            inputCount: result.statistics.inputCount,
            outputCount: result.statistics.outputCount,
            transformedFields: result.statistics.transformedFields,
            errorCount: result.statistics.errors,
          },
          logs: [
            `Transform operation: ${result.metadata.operationType}`,
            `Input count: ${result.statistics.inputCount}`,
            `Output count: ${result.statistics.outputCount}`,
            `Transformed fields: ${result.statistics.transformedFields}`,
            `Execution time: ${executionTime}ms`,
          ],
        },
      };
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        success: false,
        error,
        metadata: {
          executionTime,
          logs: [
            `Transform operation failed: ${error.message}`,
            `Execution time: ${executionTime}ms`,
          ],
        },
      };
    }
  }
  
  /**
   * Validate transform node input
   */
  protected async validateNodeSpecificInput(
    context: ExecutorContext,
    result: ValidationResult
  ): Promise<void> {
    const params = context.node.parameters as any;
    
    // Validate operation type
    if (!params.operation) {
      result.errors.push({
        code: 'MISSING_OPERATION',
        message: 'Transform node requires an operation type',
        field: 'operation',
        severity: 'error',
      });
    } else if (!this.supportedNodeTypes.includes(params.operation)) {
      result.errors.push({
        code: 'INVALID_OPERATION',
        message: 'Invalid transform operation type',
        field: 'operation',
        currentValue: params.operation,
        expectedValue: this.supportedNodeTypes,
        severity: 'error',
      });
    }
    
    // Validate mappings
    if (params.mappings && Array.isArray(params.mappings)) {
      for (let i = 0; i < params.mappings.length; i++) {
        const mapping = params.mappings[i];
        
        if (!mapping.source) {
          result.errors.push({
            code: 'MISSING_SOURCE',
            message: `Mapping ${i} missing source field`,
            field: `mappings[${i}].source`,
            severity: 'error',
          });
        }
        
        if (!mapping.target) {
          result.errors.push({
            code: 'MISSING_TARGET',
            message: `Mapping ${i} missing target field`,
            field: `mappings[${i}].target`,
            severity: 'error',
          });
        }
        
        // Validate transform function
        if (mapping.transform && !['uppercase', 'lowercase', 'trim', 'number', 'boolean', 'date', 'json', 'string', 'custom'].includes(mapping.transform)) {
          result.errors.push({
            code: 'INVALID_TRANSFORM',
            message: `Invalid transform function in mapping ${i}`,
            field: `mappings[${i}].transform`,
            currentValue: mapping.transform,
            severity: 'error',
          });
        }
        
        // Validate custom transform
        if (mapping.transform === 'custom' && !mapping.customTransform) {
          result.errors.push({
            code: 'MISSING_CUSTOM_TRANSFORM',
            message: `Custom transform requires customTransform code in mapping ${i}`,
            field: `mappings[${i}].customTransform`,
            severity: 'error',
          });
        }
      }
    }
    
    // Validate group configuration
    if (params.group) {
      if (!params.group.by) {
        result.errors.push({
          code: 'MISSING_GROUP_BY',
          message: 'Group operation requires groupBy field',
          field: 'group.by',
          severity: 'error',
        });
      }
      
      if (params.group.aggregations) {
        for (let i = 0; i < params.group.aggregations.length; i++) {
          const agg = params.group.aggregations[i];
          
          if (!agg.field) {
            result.errors.push({
              code: 'MISSING_AGG_FIELD',
              message: `Aggregation ${i} missing field`,
              field: `group.aggregations[${i}].field`,
              severity: 'error',
            });
          }
          
          if (!['sum', 'avg', 'count', 'min', 'max', 'first', 'last'].includes(agg.operation)) {
            result.errors.push({
              code: 'INVALID_AGG_OPERATION',
              message: `Invalid aggregation operation in ${i}`,
              field: `group.aggregations[${i}].operation`,
              currentValue: agg.operation,
              severity: 'error',
            });
          }
        }
      }
    }
    
    // Validate sort configuration
    if (params.sort && Array.isArray(params.sort)) {
      for (let i = 0; i < params.sort.length; i++) {
        const sortConfig = params.sort[i];
        
        if (!sortConfig.field) {
          result.errors.push({
            code: 'MISSING_SORT_FIELD',
            message: `Sort configuration ${i} missing field`,
            field: `sort[${i}].field`,
            severity: 'error',
          });
        }
        
        if (!['asc', 'desc'].includes(sortConfig.direction)) {
          result.errors.push({
            code: 'INVALID_SORT_DIRECTION',
            message: `Invalid sort direction in ${i}`,
            field: `sort[${i}].direction`,
            currentValue: sortConfig.direction,
            expectedValue: ['asc', 'desc'],
            severity: 'error',
          });
        }
      }
    }
  }
  
  // Private helper methods
  
  private parseTransformConfig(context: ExecutorContext): TransformNodeConfig {
    const params = context.node.parameters as any;
    
    return {
      operation: params.operation,
      mappings: params.mappings,
      filter: params.filter,
      sort: params.sort,
      group: params.group,
      merge: params.merge,
      format: params.format,
      validation: params.validation,
      options: {
        preserveTypes: params.options?.preserveTypes !== false,
        allowPartialTransform: params.options?.allowPartialTransform !== false,
        skipErrors: params.options?.skipErrors || false,
        maxDepth: params.options?.maxDepth || 10,
        timeout: params.options?.timeout || 30000,
      },
    };
  }
  
  private async executeTransformOperation(
    config: TransformNodeConfig,
    context: ExecutorContext
  ): Promise<TransformExecutionResult> {
    const startTime = Date.now();
    const inputData = context.inputData;
    
    let result: any;
    const statistics = {
      inputCount: 0,
      outputCount: 0,
      transformedFields: 0,
      skippedFields: 0,
      errors: 0,
      warnings: 0,
    };
    
    // Count input
    if (Array.isArray(inputData)) {
      statistics.inputCount = inputData.length;
    } else if (inputData && typeof inputData === 'object') {
      statistics.inputCount = 1;
    }
    
    switch (config.operation) {
      case 'map':
        result = await this.executeMap(config, inputData, statistics);
        break;
        
      case 'filter':
        result = await this.executeFilter(config, inputData, statistics);
        break;
        
      case 'sort':
        result = await this.executeSort(config, inputData, statistics);
        break;
        
      case 'group':
        result = await this.executeGroup(config, inputData, statistics);
        break;
        
      case 'aggregate':
        result = await this.executeAggregate(config, inputData, statistics);
        break;
        
      case 'flatten':
        result = await this.executeFlatten(config, inputData, statistics);
        break;
        
      case 'merge':
        result = await this.executeMerge(config, inputData, statistics);
        break;
        
      case 'format':
        result = await this.executeFormat(config, inputData, statistics);
        break;
        
      case 'validate':
        result = await this.executeValidate(config, inputData, statistics);
        break;
        
      default:
        throw new Error(`Unsupported transform operation: ${config.operation}`);
    }
    
    // Count output
    if (Array.isArray(result)) {
      statistics.outputCount = result.length;
    } else if (result && typeof result === 'object') {
      statistics.outputCount = 1;
    }
    
    return {
      result,
      statistics,
      metadata: {
        operationType: config.operation,
        executionTime: Date.now() - startTime,
      },
    };
  }
  
  private async executeMap(
    config: TransformNodeConfig,
    inputData: any,
    statistics: any
  ): Promise<any> {
    if (!config.mappings || config.mappings.length === 0) {
      throw new Error('Map operation requires field mappings');
    }
    
    if (Array.isArray(inputData)) {
      return inputData.map(item => this.mapObject(item, config.mappings!, statistics));
    } else {
      return this.mapObject(inputData, config.mappings!, statistics);
    }
  }
  
  private mapObject(obj: any, mappings: FieldMapping[], statistics: any): any {
    const result: any = {};
    
    for (const mapping of mappings) {
      try {
        const sourceValue = this.getNestedValue(obj, mapping.source);
        
        if (sourceValue === undefined || sourceValue === null) {
          if (mapping.required) {
            statistics.errors++;
            if (!config.options?.skipErrors) {
              throw new Error(`Required field ${mapping.source} is missing`);
            }
            continue;
          }
          
          if (mapping.defaultValue !== undefined) {
            this.setNestedValue(result, mapping.target, mapping.defaultValue);
            statistics.transformedFields++;
            continue;
          }
        }
        
        let transformedValue = sourceValue;
        
        // Apply transformation
        if (mapping.transform) {
          transformedValue = this.applyTransform(sourceValue, mapping.transform, mapping.customTransform);
        }
        
        // Apply validation
        if (mapping.validation) {
          const isValid = this.validateValue(transformedValue, mapping.validation);
          if (!isValid) {
            statistics.errors++;
            if (!config.options?.skipErrors) {
              throw new Error(`Validation failed for field ${mapping.source}`);
            }
            continue;
          }
        }
        
        this.setNestedValue(result, mapping.target, transformedValue);
        statistics.transformedFields++;
        
      } catch (error) {
        statistics.errors++;
        if (!config.options?.skipErrors) {
          throw error;
        }
      }
    }
    
    return result;
  }
  
  private async executeFilter(
    config: TransformNodeConfig,
    inputData: any,
    statistics: any
  ): Promise<any> {
    if (!config.filter || !config.filter.conditions) {
      throw new Error('Filter operation requires filter conditions');
    }
    
    if (!Array.isArray(inputData)) {
      throw new Error('Filter operation requires array input');
    }
    
    return inputData.filter(item => {
      const logic = config.filter!.logic || 'AND';
      
      if (logic === 'AND') {
        return config.filter!.conditions.every(condition => 
          this.evaluateFilterCondition(item, condition)
        );
      } else {
        return config.filter!.conditions.some(condition => 
          this.evaluateFilterCondition(item, condition)
        );
      }
    });
  }
  
  private async executeSort(
    config: TransformNodeConfig,
    inputData: any,
    statistics: any
  ): Promise<any> {
    if (!config.sort || config.sort.length === 0) {
      throw new Error('Sort operation requires sort configuration');
    }
    
    if (!Array.isArray(inputData)) {
      throw new Error('Sort operation requires array input');
    }
    
    return [...inputData].sort((a, b) => {
      for (const sortConfig of config.sort!) {
        const aValue = this.getNestedValue(a, sortConfig.field);
        const bValue = this.getNestedValue(b, sortConfig.field);
        
        let comparison = 0;
        
        if (sortConfig.type === 'number') {
          comparison = Number(aValue) - Number(bValue);
        } else if (sortConfig.type === 'date') {
          comparison = new Date(aValue).getTime() - new Date(bValue).getTime();
        } else {
          comparison = String(aValue).localeCompare(String(bValue));
        }
        
        if (comparison !== 0) {
          return sortConfig.direction === 'desc' ? -comparison : comparison;
        }
      }
      
      return 0;
    });
  }
  
  private async executeGroup(
    config: TransformNodeConfig,
    inputData: any,
    statistics: any
  ): Promise<any> {
    if (!config.group || !config.group.by) {
      throw new Error('Group operation requires groupBy configuration');
    }
    
    if (!Array.isArray(inputData)) {
      throw new Error('Group operation requires array input');
    }
    
    const groups = new Map();
    
    for (const item of inputData) {
      const groupKey = Array.isArray(config.group.by)
        ? config.group.by.map(field => this.getNestedValue(item, field)).join('|')
        : this.getNestedValue(item, config.group.by);
      
      if (!groups.has(groupKey)) {
        groups.set(groupKey, []);
      }
      
      groups.get(groupKey).push(item);
    }
    
    const result: any[] = [];
    
    for (const [groupKey, items] of groups.entries()) {
      const groupResult: any = {
        groupKey,
        items,
        count: items.length,
      };
      
      // Apply aggregations
      if (config.group.aggregations) {
        for (const agg of config.group.aggregations) {
          const alias = agg.alias || `${agg.operation}_${agg.field}`;
          groupResult[alias] = this.calculateAggregation(items, agg.field, agg.operation);
        }
      }
      
      result.push(groupResult);
    }
    
    return result;
  }
  
  private async executeAggregate(
    config: TransformNodeConfig,
    inputData: any,
    statistics: any
  ): Promise<any> {
    if (!config.group?.aggregations) {
      throw new Error('Aggregate operation requires aggregation configuration');
    }
    
    if (!Array.isArray(inputData)) {
      throw new Error('Aggregate operation requires array input');
    }
    
    const result: any = {
      count: inputData.length,
    };
    
    for (const agg of config.group.aggregations) {
      const alias = agg.alias || `${agg.operation}_${agg.field}`;
      result[alias] = this.calculateAggregation(inputData, agg.field, agg.operation);
    }
    
    return result;
  }
  
  private async executeFlatten(
    config: TransformNodeConfig,
    inputData: any,
    statistics: any
  ): Promise<any> {
    if (!Array.isArray(inputData)) {
      throw new Error('Flatten operation requires array input');
    }
    
    const maxDepth = config.options?.maxDepth || 10;
    return this.flattenArray(inputData, maxDepth);
  }
  
  private async executeMerge(
    config: TransformNodeConfig,
    inputData: any,
    statistics: any
  ): Promise<any> {
    if (!config.merge || !config.merge.sources) {
      throw new Error('Merge operation requires merge configuration');
    }
    
    const sources = config.merge.sources.map(source => 
      this.getNestedValue(inputData, source)
    );
    
    return this.mergeObjects(sources, config.merge.strategy || 'deep');
  }
  
  private async executeFormat(
    config: TransformNodeConfig,
    inputData: any,
    statistics: any
  ): Promise<any> {
    if (!config.format) {
      throw new Error('Format operation requires format configuration');
    }
    
    if (config.format.template) {
      return this.applyTemplate(inputData, config.format.template);
    }
    
    return inputData;
  }
  
  private async executeValidate(
    config: TransformNodeConfig,
    inputData: any,
    statistics: any
  ): Promise<any> {
    if (!config.validation) {
      throw new Error('Validate operation requires validation configuration');
    }
    
    // Simplified validation - in real implementation, use a JSON schema validator
    const errors: any[] = [];
    
    if (Array.isArray(inputData)) {
      inputData.forEach((item, index) => {
        // Validate each item
        // Add validation logic here
      });
    }
    
    return {
      data: inputData,
      valid: errors.length === 0,
      errors,
    };
  }
  
  // Utility methods
  
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }
  
  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, key) => {
      if (!(key in current)) {
        current[key] = {};
      }
      return current[key];
    }, obj);
    
    target[lastKey] = value;
  }
  
  private applyTransform(value: any, transform: string, customTransform?: string): any {
    switch (transform) {
      case 'uppercase':
        return String(value).toUpperCase();
      case 'lowercase':
        return String(value).toLowerCase();
      case 'trim':
        return String(value).trim();
      case 'number':
        return Number(value);
      case 'boolean':
        return Boolean(value);
      case 'date':
        return new Date(value);
      case 'json':
        return typeof value === 'string' ? JSON.parse(value) : value;
      case 'string':
        return String(value);
      case 'custom':
        if (customTransform) {
          // WARNING: eval is dangerous - use a safe expression evaluator in production
          try {
            const func = new Function('value', customTransform);
            return func(value);
          } catch (error) {
            this.logger.error('Custom transform failed:', error);
            return value;
          }
        }
        return value;
      default:
        return value;
    }
  }
  
  private validateValue(value: any, validation: FieldMapping['validation']): boolean {
    if (!validation) return true;
    
    // Simplified validation - implement full validation logic
    if (validation.type) {
      switch (validation.type) {
        case 'string':
          if (typeof value !== 'string') return false;
          break;
        case 'number':
          if (typeof value !== 'number' || isNaN(value)) return false;
          break;
        case 'boolean':
          if (typeof value !== 'boolean') return false;
          break;
        case 'email':
          if (typeof value !== 'string' || !/\S+@\S+\.\S+/.test(value)) return false;
          break;
      }
    }
    
    if (validation.min !== undefined && value < validation.min) return false;
    if (validation.max !== undefined && value > validation.max) return false;
    if (validation.minLength !== undefined && String(value).length < validation.minLength) return false;
    if (validation.maxLength !== undefined && String(value).length > validation.maxLength) return false;
    
    return true;
  }
  
  private evaluateFilterCondition(item: any, condition: any): boolean {
    const fieldValue = this.getNestedValue(item, condition.field);
    
    switch (condition.operator) {
      case '==':
        return fieldValue == condition.value;
      case '!=':
        return fieldValue != condition.value;
      case '>':
        return fieldValue > condition.value;
      case '<':
        return fieldValue < condition.value;
      case '>=':
        return fieldValue >= condition.value;
      case '<=':
        return fieldValue <= condition.value;
      case 'contains':
        return String(fieldValue).includes(String(condition.value));
      case 'regex':
        return new RegExp(condition.value).test(String(fieldValue));
      default:
        return false;
    }
  }
  
  private calculateAggregation(items: any[], field: string, operation: string): any {
    const values = items.map(item => this.getNestedValue(item, field)).filter(v => v != null);
    
    switch (operation) {
      case 'sum':
        return values.reduce((sum, val) => sum + Number(val), 0);
      case 'avg':
        return values.length > 0 ? values.reduce((sum, val) => sum + Number(val), 0) / values.length : 0;
      case 'count':
        return values.length;
      case 'min':
        return Math.min(...values.map(Number));
      case 'max':
        return Math.max(...values.map(Number));
      case 'first':
        return values[0];
      case 'last':
        return values[values.length - 1];
      default:
        return null;
    }
  }
  
  private flattenArray(arr: any[], maxDepth: number, currentDepth: number = 0): any[] {
    if (currentDepth >= maxDepth) return arr;
    
    const result: any[] = [];
    
    for (const item of arr) {
      if (Array.isArray(item)) {
        result.push(...this.flattenArray(item, maxDepth, currentDepth + 1));
      } else {
        result.push(item);
      }
    }
    
    return result;
  }
  
  private mergeObjects(objects: any[], strategy: string): any {
    if (objects.length === 0) return {};
    if (objects.length === 1) return objects[0];
    
    switch (strategy) {
      case 'shallow':
        return Object.assign({}, ...objects);
      case 'deep':
        return this.deepMerge(...objects);
      case 'overwrite':
        return objects[objects.length - 1];
      case 'append':
        if (objects.every(obj => Array.isArray(obj))) {
          return objects.flat();
        }
        return Object.assign({}, ...objects);
      default:
        return this.deepMerge(...objects);
    }
  }
  
  private deepMerge(...objects: any[]): any {
    const result: any = {};
    
    for (const obj of objects) {
      if (obj && typeof obj === 'object' && !Array.isArray(obj)) {
        for (const [key, value] of Object.entries(obj)) {
          if (value && typeof value === 'object' && !Array.isArray(value)) {
            result[key] = this.deepMerge(result[key] || {}, value);
          } else {
            result[key] = value;
          }
        }
      }
    }
    
    return result;
  }
  
  private applyTemplate(data: any, template: string): string {
    return template.replace(/\{\{(\w+(?:\.\w+)*)\}\}/g, (match, path) => {
      const value = this.getNestedValue(data, path);
      return value != null ? String(value) : '';
    });
  }
}
