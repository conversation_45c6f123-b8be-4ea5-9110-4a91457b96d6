import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../../../infra/redis/redis.service';
import { ZaloUserManagementService } from '../../../shared/services/zalo/zalo-user-management.service';
import { UserAudienceRepository } from '../repositories/user-audience.repository';
import { UserAudienceCustomFieldRepository } from '../repositories/user-audience-custom-field.repository';
import { UserAudienceCustomFieldDefinitionRepository } from '../repositories/user-audience-custom-field-definition.repository';
import { ZaloOAWorkerAdapterService } from './services/zalo-oa-worker-adapter.service';
import {
  SyncZaloUsersToAudienceDto,
  SyncZaloUsersToAudienceResponseDto,
  SyncErrorDto,
  CreatedCustomFieldDto
} from '../dto/zalo/sync-zalo-users-to-audience.dto';
import { ImportResourceEnum } from '../enums/import-resource.enum';
import { CustomFieldDataType } from '../common/enums/custom-field-data-type.enum';
import { UserAudience } from '../entities/user-audience.entity';
import { UserAudienceCustomFieldDefinition } from '../entities/user-audience-custom-field-definition.entity';
import {
  ZaloUserGetListRequest,
  ZaloUserDetail
} from '../../../shared/services/zalo/zalo.interface';

/**
 * Interface cho sync progress event
 */
export interface SyncProgressEvent {
  status: 'started' | 'progress' | 'completed' | 'failed';
  message: string;
  progress: number;
  result?: SyncZaloUsersToAudienceResponseDto;
  error?: string;
  timestamp: number;
}

/**
 * Service xử lý đồng bộ người dùng Zalo vào hệ thống audience trong worker
 * TODO: Implement full functionality when shared modules are available
 */
@Injectable()
export class ZaloAudienceSyncService {
  private readonly logger = new Logger(ZaloAudienceSyncService.name);

  constructor(
    private readonly redisService: RedisService,
    private readonly zaloUserManagementService: ZaloUserManagementService,
    private readonly audienceRepository: UserAudienceRepository,
    private readonly customFieldRepository: UserAudienceCustomFieldRepository,
    private readonly customFieldDefinitionRepository: UserAudienceCustomFieldDefinitionRepository,
    private readonly zaloOAAdapter: ZaloOAWorkerAdapterService,
  ) {}

  /**
   * Đồng bộ người dùng Zalo vào audience với progress tracking
   * TODO: Implement full functionality
   */
  async syncZaloUsersToAudience(
    userId: number,
    oaId: string,
    syncDto: SyncZaloUsersToAudienceDto,
    syncId: string,
  ): Promise<SyncZaloUsersToAudienceResponseDto> {
    const startedAt = Date.now();

    this.logger.log(
      `Starting sync Zalo users to audience for user ${userId}, OA ${oaId}, syncId: ${syncId}`,
    );

    try {
      const errors: SyncErrorDto[] = [];
      const createdCustomFields: CreatedCustomFieldDto[] = [];

      // Validate Official Account
      this.logger.debug(`Validating Official Account for userId: ${userId}, oaId: ${oaId}`);
      const officialAccount = await this.validateOfficialAccount(userId, oaId);
      this.logger.debug(`Official Account validated:`, {
        id: officialAccount.id,
        status: officialAccount.status,
        hasAccessToken: !!officialAccount.accessToken,
        accessTokenLength: officialAccount.accessToken?.length || 0
      });

      // Get user list from Zalo
      await this.emitSyncProgress(syncId, {
        status: 'progress',
        message: 'Đang lấy danh sách người dùng từ Zalo...',
        progress: 10,
        timestamp: Date.now(),
      });

      this.logger.debug(`Calling getUserListFromZalo with params:`, {
        accessToken: officialAccount.accessToken?.substring(0, 20) + '...',
        syncDto
      });

      const userList = await this.getUserListFromZalo(officialAccount.accessToken, syncDto);
      this.logger.debug('Raw userList response:', JSON.stringify(userList, null, 2));

      if (!userList) {
        this.logger.error('userList is null or undefined');
        throw new Error('Không thể lấy danh sách người dùng từ Zalo - Response null');
      }

      // Kiểm tra cấu trúc response từ Zalo API
      if (userList.error && userList.error !== 0) {
        this.logger.error('Zalo API returned error:', userList.error, userList.message);
        throw new Error(`Lỗi từ Zalo API: ${userList.message} (Code: ${userList.error})`);
      }

      if (!userList.data) {
        this.logger.error('userList.data is missing. UserList structure:', Object.keys(userList));
        throw new Error('Không thể lấy danh sách người dùng từ Zalo - Missing data field');
      }

      // Lấy users từ data wrapper
      const users = userList.data.users || [];
      this.logger.debug(`Found ${users.length} users in userList.data.users`);
      this.logger.debug(`Users array:`, users.map(u => u.user_id));
      this.logger.debug(`Found ${users.length} users in response`);
      const totalUsers = users.length;

      this.logger.log(`Retrieved ${totalUsers} users from Zalo`);

      if (totalUsers === 0) {
        this.logger.warn('No users found from Zalo API');

        const completedResult: SyncZaloUsersToAudienceResponseDto = {
          syncId,
          totalUsersFromZalo: 0,
          processedCount: 0,
          newAudienceCreated: 0,
          existingAudienceUpdated: 0,
          skippedCount: 0,
          errorCount: 0,
          errors: [],
          newCustomFieldsCreated: 0,
          createdCustomFields: [],
          startedAt,
          completedAt: Date.now(),
          processingTime: Date.now() - startedAt,
          successRate: 100,
        };

        await this.emitSyncProgress(syncId, {
          status: 'completed',
          message: 'Hoàn thành đồng bộ - Không có người dùng nào được tìm thấy',
          progress: 100,
          timestamp: Date.now(),
          result: completedResult,
        });

        return completedResult;
      }

      await this.emitSyncProgress(syncId, {
        status: 'progress',
        message: `Đã lấy ${totalUsers} người dùng từ Zalo. Bắt đầu đồng bộ...`,
        progress: 20,
        timestamp: Date.now(),
      });

      // Process users in batches
      let processedCount = 0;
      let newAudienceCreated = 0;
      let existingAudienceUpdated = 0;
      let skippedCount = 0;
      let newCustomFieldsCreated = 0;

      for (let i = 0; i < users.length; i++) {
        const user = users[i];

        try {
          // Lấy thông tin chi tiết từ Zalo API với retry logic
          let userDetailResponse: any;
          let retryCount = 0;
          const maxRetries = 3;

          while (retryCount < maxRetries) {
            try {
              userDetailResponse = await this.zaloUserManagementService.getUserDetail(
                officialAccount.accessToken,
                user.user_id,
              ) as any;
              break; // Thành công, thoát khỏi retry loop
            } catch (apiError) {
              retryCount++;
              if (retryCount >= maxRetries) {
                throw apiError;
              }
              // Đợi 1 giây trước khi retry để tránh rate limit
              await new Promise(resolve => setTimeout(resolve, 1000));
              this.logger.warn(`Retry ${retryCount}/${maxRetries} for user ${user.user_id}: ${apiError.message}`);
            }
          }

          // Kiểm tra response từ Zalo API
          if (!userDetailResponse || !userDetailResponse.data) {
            throw new Error('Invalid response from Zalo API - missing data field');
          }

          if (userDetailResponse.error !== 0) {
            throw new Error(`Zalo API error: ${userDetailResponse.message} (Code: ${userDetailResponse.error})`);
          }

          const userDetail = userDetailResponse.data;
          const result = await this.syncUserToAudience(userId, userDetail, syncDto, oaId);

          if (result.created) {
            newAudienceCreated++;
          }
          if (result.updated) {
            existingAudienceUpdated++;
          }
          if (result.skipped) {
            skippedCount++;
          }

          processedCount++;

          // Rate limiting: đợi 100ms giữa các request để tránh quá tải API
          if (i < users.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 100));
          }

          // Update progress every 10 users
          if (i % 10 === 0 || i === users.length - 1) {
            const progress = 20 + Math.floor((i / users.length) * 70);
            await this.emitSyncProgress(syncId, {
              status: 'progress',
              message: `Đã xử lý ${i + 1}/${totalUsers} người dùng`,
              progress,
              timestamp: Date.now(),
            });
          }

        } catch (error) {
          this.logger.error(`Error syncing user ${user.user_id}: ${error.message}`);
          errors.push({
            userId: user.user_id,
            message: error.message,
            details: error,
          });
        }
      }

      const completedAt = Date.now();
      const processingTime = completedAt - startedAt;
      const successRate = totalUsers > 0 ? Math.round((processedCount / totalUsers) * 100) : 100;

      const result: SyncZaloUsersToAudienceResponseDto = {
        syncId,
        totalUsersFromZalo: totalUsers,
        processedCount,
        newAudienceCreated,
        existingAudienceUpdated,
        skippedCount,
        errorCount: errors.length,
        errors,
        newCustomFieldsCreated,
        createdCustomFields,
        startedAt,
        completedAt,
        processingTime,
        successRate,
      };

      this.logger.log(
        `Sync completed: ${processedCount}/${totalUsers} processed, ` +
        `${newAudienceCreated} created, ${existingAudienceUpdated} updated, ` +
        `${errors.length} errors`,
      );

      return result;
    } catch (error) {
      this.logger.error(`Error syncing Zalo users: ${error.message}`, error.stack);
      
      await this.emitSyncProgress(syncId, {
        status: 'failed',
        message: `Lỗi đồng bộ: ${error.message}`,
        progress: 0,
        error: error.message,
        timestamp: Date.now(),
      });

      throw error;
    }
  }

  /**
   * Validate Official Account
   */
  private async validateOfficialAccount(userId: number, oaId: string) {
    this.logger.debug(`Looking for Official Account with oaId: ${oaId}, userId: ${userId}`);

    const officialAccount = await this.zaloOAAdapter.findByOaIdAndUserId(oaId, userId);
    this.logger.debug(`Found Official Account:`, {
      found: !!officialAccount,
      id: officialAccount?.id,
      status: officialAccount?.status,
      accessToken: officialAccount?.accessToken?.substring(0, 20) + '...' || 'N/A'
    });

    if (!officialAccount) {
      this.logger.error(`Official Account not found for oaId: ${oaId}, userId: ${userId}`);
      throw new Error(`Không tìm thấy Official Account với ID: ${oaId}`);
    }

    // Note: accessToken sẽ là '[ENCRYPTED]' từ adapter, cần kiểm tra khác
    // Chỉ cần kiểm tra OA tồn tại và có status active
    if (officialAccount.status !== 'active') {
      this.logger.error(`Official Account status is not active: ${officialAccount.status}`);
      throw new Error('Official Account không hoạt động');
    }

    // Kiểm tra access token
    if (!officialAccount.accessToken || officialAccount.accessToken === '[ENCRYPTED]') {
      this.logger.error(`Invalid access token: ${officialAccount.accessToken}`);
      throw new Error('Access token không hợp lệ hoặc chưa được giải mã');
    }

    this.logger.debug(`Official Account validation successful`);
    return officialAccount;
  }

  /**
   * Get user list from Zalo API
   */
  private async getUserListFromZalo(accessToken: string, syncDto: SyncZaloUsersToAudienceDto) {
    const requestData: ZaloUserGetListRequest = {
      count: syncDto.count || 50,
      offset: syncDto.offset || 0,
    };

    if (syncDto.tagName) {
      requestData.tag_name = syncDto.tagName;
    }

    if (syncDto.lastInteractionPeriod) {
      requestData.last_interaction_period = String(syncDto.lastInteractionPeriod);
    }

    if (syncDto.isFollower !== undefined) {
      requestData.is_follower = syncDto.isFollower;
    }

    this.logger.debug(`Calling zaloUserManagementService.getUserList with:`, {
      accessTokenLength: accessToken?.length || 0,
      requestData
    });

    try {
      const result = await this.zaloUserManagementService.getUserList(accessToken, requestData);
      this.logger.debug(`getUserList result:`, {
        hasResult: !!result,
        resultKeys: result ? Object.keys(result) : [],
        resultType: typeof result
      });
      return result;
    } catch (error) {
      this.logger.error(`Error in getUserListFromZalo:`, {
        error: error.message,
        stack: error.stack,
        accessTokenLength: accessToken?.length || 0,
        requestData
      });
      throw error;
    }
  }

  /**
   * Sync single user to audience
   */
  private async syncUserToAudience(
    userId: number,
    userDetail: ZaloUserDetail,
    syncDto: SyncZaloUsersToAudienceDto,
    oaId?: string,
  ): Promise<{ created: boolean; updated: boolean; skipped: boolean }> {
    // Tìm audience đã tồn tại theo zaloSocialId trước
    let existingAudience: UserAudience | null = null;

    if (userDetail.user_id) {
      existingAudience = await this.audienceRepository.findByZaloSocialId(
        userDetail.user_id,
        userId,
      );
    }

    let created = false;
    let updated = false;
    let skipped = false;
    let audience: UserAudience;

    if (existingAudience) {
      if (syncDto.updateExisting) {
        // Cập nhật audience đã tồn tại
        existingAudience.name = userDetail.display_name || existingAudience.name;
        existingAudience.phoneNumber = userDetail.shared_info?.phone || existingAudience.phoneNumber;
        existingAudience.userLastInteractionDate = userDetail.user_last_interaction_date || existingAudience.userLastInteractionDate;
        existingAudience.zaloUserIsFollower = userDetail.user_is_follower ?? existingAudience.zaloUserIsFollower;

        // Cập nhật avatars_external từ Zalo
        existingAudience.avatarsExternal = this.extractZaloAvatars(userDetail);

        existingAudience.updatedAt = Date.now();

        audience = await this.audienceRepository.save(existingAudience);
        updated = true;
      } else {
        skipped = true;
        audience = existingAudience;
      }
    } else {
      // Tạo audience mới
      const mappedAvatars = this.extractZaloAvatars(userDetail);

      audience = await this.audienceRepository.createAudience({
        userId,
        name: userDetail.display_name || 'Zalo User',
        phoneNumber: userDetail.shared_info?.phone || null,
        zaloSocialId: userDetail.user_id,
        avatarsExternal: mappedAvatars,
        importResource: ImportResourceEnum.ZALO,
        zaloUserIsFollower: userDetail.user_is_follower ?? null,
        userLastInteractionDate: userDetail.user_last_interaction_date,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
      created = true;
    }

    // Tạo/cập nhật custom fields
    if (!skipped) {
      await this.syncCustomFields(audience.id, userDetail, userId);
    }

    return { created, updated, skipped };
  }

  /**
   * Trích xuất danh sách avatar URLs từ Zalo user detail
   */
  private extractZaloAvatars(userDetail: ZaloUserDetail): string[] | null {
    const avatars: string[] = [];

    // Thêm avatar chính
    if (userDetail.avatar) {
      avatars.push(userDetail.avatar);
    }

    // Thêm các avatar từ avatars object
    if (userDetail.avatars) {
      if (
        userDetail.avatars['120'] &&
        !avatars.includes(userDetail.avatars['120'])
      ) {
        avatars.push(userDetail.avatars['120']);
      }
      if (
        userDetail.avatars['240'] &&
        !avatars.includes(userDetail.avatars['240'])
      ) {
        avatars.push(userDetail.avatars['240']);
      }
    }

    return avatars.length > 0 ? avatars : null;
  }

  /**
   * Sync custom fields for audience
   */
  private async syncCustomFields(
    audienceId: number,
    userDetail: ZaloUserDetail,
    userId: number,
  ) {
    const fieldMappings = [
      { key: 'zalo_user_id', value: userDetail.user_id },
      { key: 'zalo_user_id_by_app', value: userDetail.user_id_by_app },
      { key: 'zalo_user_external_id', value: userDetail.user_external_id },
      { key: 'zalo_display_name', value: userDetail.display_name },
      { key: 'zalo_user_alias', value: userDetail.user_alias },
      { key: 'zalo_is_sensitive', value: userDetail.is_sensitive },
      { key: 'zalo_last_interaction_date', value: userDetail.user_last_interaction_date },
      { key: 'zalo_user_is_follower', value: userDetail.user_is_follower },

      // Avatar fields
      { key: 'zalo_avatar', value: userDetail.avatar },
      { key: 'zalo_avatars', value: userDetail.avatars },

      // Additional fields
      { key: 'zalo_dynamic_param', value: userDetail.dynamic_param },
      { key: 'zalo_tags_and_notes', value: userDetail.tags_and_notes_info },
      { key: 'zalo_shared_info', value: userDetail.shared_info },

      // Map các trường từ shared_info để dễ truy cập
      { key: 'zalo_shared_address', value: userDetail.shared_info?.address },
      { key: 'zalo_shared_city', value: userDetail.shared_info?.city },
      { key: 'zalo_shared_district', value: userDetail.shared_info?.district },
      { key: 'zalo_shared_phone', value: userDetail.shared_info?.phone },
      { key: 'zalo_shared_name', value: userDetail.shared_info?.name },
      { key: 'zalo_shared_dob', value: userDetail.shared_info?.user_dob },
    ];

    for (const mapping of fieldMappings) {
      if (mapping.value !== undefined && mapping.value !== null) {
        try {
          // Ensure field definition exists
          await this.ensureCustomFieldDefinition(mapping.key, userId);

          // Create or update custom field
          await this.customFieldRepository.upsertCustomField({
            audienceId,
            fieldName: mapping.key,
            fieldValue: mapping.value,
          });
        } catch (error) {
          this.logger.error(`Error syncing custom field ${mapping.key}: ${error.message}`);
        }
      }
    }
  }

  /**
   * Ensure custom field definition exists
   */
  private async ensureCustomFieldDefinition(fieldKey: string, userId: number) {
    const existing = await this.customFieldDefinitionRepository.findByFieldKeyAndUserId(
      fieldKey,
      userId,
    );

    if (!existing) {
      await this.customFieldDefinitionRepository.createDefinition({
        fieldKey,
        displayName: this.getDisplayNameForField(fieldKey),
        dataType: this.getDataTypeForField(fieldKey),
        userId,
      });
    }
  }

  /**
   * Get display name for field
   */
  private getDisplayNameForField(fieldName: string): string {
    const displayNames: Record<string, string> = {
      zalo_user_id: 'Zalo User ID',
      zalo_user_id_by_app: 'Zalo User ID by App',
      zalo_user_external_id: 'Zalo External ID',
      zalo_display_name: 'Zalo Display Name',
      zalo_user_alias: 'Zalo User Alias',
      zalo_is_sensitive: 'Zalo Is Sensitive',
      zalo_last_interaction_date: 'Zalo Last Interaction Date',
      zalo_user_is_follower: 'Zalo Is Follower',

      // Avatar fields
      zalo_avatar: 'Zalo Avatar',
      zalo_avatars: 'Zalo Avatars',

      // Additional fields
      zalo_dynamic_param: 'Zalo Dynamic Param',
      zalo_tags_and_notes: 'Zalo Tags and Notes',
      zalo_shared_info: 'Zalo Shared Info',

      // Shared info fields
      zalo_shared_address: 'Zalo Shared Address',
      zalo_shared_city: 'Zalo Shared City',
      zalo_shared_district: 'Zalo Shared District',
      zalo_shared_phone: 'Zalo Shared Phone',
      zalo_shared_name: 'Zalo Shared Name',
      zalo_shared_dob: 'Zalo Shared Date of Birth',
    };

    return displayNames[fieldName] || fieldName;
  }

  /**
   * Get data type for field
   */
  private getDataTypeForField(fieldName: string): CustomFieldDataType {
    const dataTypes: Record<string, CustomFieldDataType> = {
      zalo_user_id: CustomFieldDataType.TEXT,
      zalo_user_id_by_app: CustomFieldDataType.TEXT,
      zalo_user_external_id: CustomFieldDataType.TEXT,
      zalo_display_name: CustomFieldDataType.TEXT,
      zalo_user_alias: CustomFieldDataType.TEXT,
      zalo_is_sensitive: CustomFieldDataType.BOOLEAN,
      zalo_last_interaction_date: CustomFieldDataType.DATE,
      zalo_user_is_follower: CustomFieldDataType.BOOLEAN,

      // Avatar fields
      zalo_avatar: CustomFieldDataType.TEXT,
      zalo_avatars: CustomFieldDataType.OBJECT,

      // Additional fields
      zalo_dynamic_param: CustomFieldDataType.TEXT,
      zalo_tags_and_notes: CustomFieldDataType.OBJECT,
      zalo_shared_info: CustomFieldDataType.OBJECT,

      // Shared info fields
      zalo_shared_address: CustomFieldDataType.TEXT,
      zalo_shared_city: CustomFieldDataType.TEXT,
      zalo_shared_district: CustomFieldDataType.TEXT,
      zalo_shared_phone: CustomFieldDataType.TEXT,
      zalo_shared_name: CustomFieldDataType.TEXT,
      zalo_shared_dob: CustomFieldDataType.DATE,
    };

    return dataTypes[fieldName] || CustomFieldDataType.TEXT;
  }

  /**
   * Gửi progress event qua Redis
   */
  async emitSyncProgress(syncId: string, event: SyncProgressEvent): Promise<void> {
    try {
      const streamKey = `zalo_audience_sync:${syncId}`;
      await this.redisService.xadd(streamKey, {
        event: JSON.stringify(event),
      });

      // Set TTL for the stream (24 hours)
      await this.redisService.getRawClient().expire(streamKey, 24 * 60 * 60);

      this.logger.debug(`Emitted sync progress for ${syncId}: ${event.status} - ${event.progress}%`);
    } catch (error) {
      this.logger.error(`Failed to emit sync progress: ${error.message}`, error.stack);
    }
  }
}
