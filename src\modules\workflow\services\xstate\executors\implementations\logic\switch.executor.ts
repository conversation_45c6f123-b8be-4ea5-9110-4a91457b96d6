import { Injectable, Logger } from '@nestjs/common';
import { BaseNodeExecutor } from '../../base/base-node.executor';
import { 
  ExecutorContext, 
  ValidationResult,
} from '../../base/node-executor.interface';
import { DetailedNodeExecutionResult, NodeExecutionConfig } from '../../../types';
import { NodeGroupEnum } from '../../../../../enums/node-group.enum';
import { ENodeType } from '../../../../../interfaces/node-manager.interface';
import {
  ISwitchParameters,
  ISwitchInput,
  ISwitchOutput,
  validateSwitchParameters
} from '../../../../../interfaces';
import { ConditionEvaluatorService } from '../../shared/condition-evaluator.service';
import { ValidationUtils } from '../../shared/validation.utils';

/**
 * Executor for SWITCH node type
 * Handles multi-branch conditional logic
 */
@Injectable()
export class SwitchExecutor extends BaseNodeExecutor {
  readonly nodeGroup = NodeGroupEnum.LOGIC;
  readonly supportedNodeTypes = [ENodeType.SWITCH];
  readonly executorName = 'SwitchExecutor';
  readonly version = '1.0.0';

  constructor(
    private readonly conditionEvaluatorService: ConditionEvaluatorService
  ) {
    super();
  }

  /**
   * Execute SWITCH node
   */
  protected async executeNode(
    context: ExecutorContext,
    config: NodeExecutionConfig
  ): Promise<DetailedNodeExecutionResult> {
    const startTime = Date.now();

    // Parse and validate parameters
    const params = context.node.parameters as ISwitchParameters;
    const input = context.inputData as ISwitchInput;

    try {
      
      this.logger.debug(`Executing SWITCH with ${params.cases.length} cases`);
      
      // Evaluate each case until one matches
      let matchedCase: any = null;
      let caseIndex = -1;
      const caseEvaluations: any[] = [];

      for (let i = 0; i < params.cases.length; i++) {
        const switchCase = params.cases[i];

        // Skip disabled cases
        if (switchCase.enabled === false) {
          continue;
        }

        try {
          let evaluationResult: any;

          // Evaluate based on switch mode
          if (params.mode === 'condition' && switchCase.condition) {
            // Use single condition evaluation
            evaluationResult = await this.conditionEvaluatorService.evaluateCondition(
              switchCase.condition,
              input.data
            );
          } else {
            // For other modes, create a simple result
            evaluationResult = {
              finalResult: false,
              evaluationTime: 0,
              details: null
            };
          }

          caseEvaluations.push({
            case_id: switchCase.id,
            case_name: switchCase.name,
            result: evaluationResult.finalResult,
            evaluation_time: evaluationResult.evaluationTime || 0
          });

          if (evaluationResult.finalResult && matchedCase === null) {
            matchedCase = switchCase;
            caseIndex = i;
          }

        } catch (error) {
          this.logger.warn(`Failed to evaluate case ${i}: ${error.message}`);
          caseEvaluations.push({
            case_id: switchCase.id,
            case_name: switchCase.name,
            result: false,
            evaluation_time: 0
          });
        }
      }
      
      const executionTime = Date.now() - startTime;
      
      // Note: Switch node sử dụng output_handle để routing, không có nextNodes
      
      const totalEvaluationTime = caseEvaluations.reduce((sum, evaluation) => sum + (evaluation.evaluation_time || 0), 0);

      const output: ISwitchOutput = {
        matched_case: matchedCase ? {
          id: matchedCase.id,
          name: matchedCase.name,
          output_handle: matchedCase.output_handle,
          matched_value: input.data[params.field_name]
        } : undefined,
        has_match: matchedCase !== null,
        data: input.data,
        evaluation_details: {
          field_name: params.field_name,
          field_value: input.data[params.field_name],
          cases_evaluated: caseEvaluations,
          total_evaluation_time: totalEvaluationTime
        },
        metadata: {
          switch_mode: params.mode,
          timestamp: Date.now()
        }
      };
      
      return {
        nodeType: 'SWITCH',
        success: true,
        outputData: output,
        metadata: {
          executionTime,
          totalCases: params.cases.length,
          matchedCaseIndex: caseIndex,
          defaultCaseUsed: !output.has_match && params.default_case.enabled,
          evaluationTime: totalEvaluationTime,
          customMetrics: {
            totalCases: params.cases.length,
            matchedCaseIndex: caseIndex,
            defaultCaseUsed: !output.has_match && params.default_case.enabled,
            evaluationTime: totalEvaluationTime,
          },
          logs: [
            `SWITCH evaluation completed`,
            `Total cases: ${params.cases.length}`,
            `Matched case: ${caseIndex >= 0 ? `${caseIndex} (${matchedCase?.name || 'Unlabeled'})` : 'None'}`,
            `Default case used: ${!output.has_match && params.default_case.enabled}`,
            `Execution time: ${executionTime}ms`,
          ],
        },
      };
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        nodeType: 'SWITCH',
        success: false,
        error,
        metadata: {
          executionTime,
          totalCases: params.cases?.length || 0,
          matchedCaseIndex: -1,
          defaultCaseUsed: false,
          evaluationTime: 0,
          logs: [
            `SWITCH evaluation failed: ${error.message}`,
            `Execution time: ${executionTime}ms`,
          ],
        },
      };
    }
  }

  /**
   * Validate SWITCH node input
   */
  protected async validateNodeSpecificInput(
    context: ExecutorContext,
    result: ValidationResult
  ): Promise<void> {
    const params = context.node.parameters as ISwitchParameters;
    
    // Use existing validation function from interface
    const interfaceValidation = validateSwitchParameters(params);
    if (!interfaceValidation.isValid) {
      for (const error of interfaceValidation.errors) {
        ValidationUtils.addError(
          result,
          'INTERFACE_VALIDATION_ERROR',
          error,
          'parameters'
        );
      }
    }

    // Additional custom validations
    this.validateSwitchSpecific(params, result);
  }

  /**
   * SWITCH specific validations
   */
  private validateSwitchSpecific(
    params: ISwitchParameters,
    result: ValidationResult
  ): void {
    // Validate cases array
    ValidationUtils.validateArray(
      result,
      params.cases,
      'Switch cases',
      1,
      20,
      'cases'
    );

    // Validate each case
    for (let i = 0; i < params.cases.length; i++) {
      const switchCase = params.cases[i];

      // Validate case ID
      ValidationUtils.validateRequired(
        result,
        switchCase.id,
        `Case ${i + 1} ID`,
        `cases[${i}].id`
      );

      // Validate case name
      ValidationUtils.validateRequired(
        result,
        switchCase.name,
        `Case ${i + 1} name`,
        `cases[${i}].name`
      );

      // Validate output handle
      ValidationUtils.validateRequired(
        result,
        switchCase.output_handle,
        `Case ${i + 1} output handle`,
        `cases[${i}].output_handle`
      );

      // Validate condition based on mode
      if (params.mode === 'condition' && !switchCase.condition) {
        ValidationUtils.addError(
          result,
          'MISSING_CONDITION',
          `Case ${i + 1} requires a condition for condition mode`,
          `cases[${i}].condition`
        );
      }

      if (params.mode === 'expression' && !switchCase.expression) {
        ValidationUtils.addError(
          result,
          'MISSING_EXPRESSION',
          `Case ${i + 1} requires an expression for expression mode`,
          `cases[${i}].expression`
        );
      }

      if (params.mode === 'data_type' && !switchCase.data_type) {
        ValidationUtils.addError(
          result,
          'MISSING_DATA_TYPE',
          `Case ${i + 1} requires a data type for data_type mode`,
          `cases[${i}].data_type`
        );
      }
    }

    // Validate default case if provided
    if (params.default_case) {
      if (params.default_case.enabled && !params.default_case.output_handle) {
        ValidationUtils.addError(
          result,
          'MISSING_DEFAULT_OUTPUT_HANDLE',
          'Default case output handle is required when enabled',
          'default_case.output_handle'
        );
      }
    }

    // Performance warnings
    if (params.cases.length > 10) {
      ValidationUtils.addWarning(
        result,
        'HIGH_CASE_COUNT',
        `High number of switch cases (${params.cases.length}) may impact performance`,
        'cases',
        'Consider using nested switches or different logic'
      );
    }

    // Check for duplicate case names
    const names = params.cases
      .map(c => c.name)
      .filter(name => name && name.trim() !== '');

    const uniqueNames = new Set(names);
    if (names.length !== uniqueNames.size) {
      ValidationUtils.addWarning(
        result,
        'DUPLICATE_CASE_NAMES',
        'Some switch cases have duplicate names',
        'cases',
        'Consider using unique names for better debugging'
      );
    }

    // Check for duplicate case IDs
    const ids = params.cases.map(c => c.id);
    const uniqueIds = new Set(ids);
    if (ids.length !== uniqueIds.size) {
      ValidationUtils.addError(
        result,
        'DUPLICATE_CASE_IDS',
        'Switch cases must have unique IDs',
        'cases'
      );
    }

    // Recommend default case if not provided
    if (!params.default_case) {
      ValidationUtils.addWarning(
        result,
        'NO_DEFAULT_CASE',
        'No default case provided',
        'default_case',
        'Consider adding a default case to handle unmatched conditions'
      );
    }
  }


}
