import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { RunStatusEnum, PlatformEnum } from '../enums/agent-run.enum';

/**
 * User Agent Run entity
 * Tracks AI agent processing runs across all platforms
 */
@Entity('user_agent_runs')
export class UserAgentRun {
  /**
   * Primary key - UUID
   */
  @PrimaryGeneratedColumn('uuid')
  id: string; /**
   * Agent payload/configuration
   */

  @Column({ type: 'jsonb', nullable: false })
  payload: any; /**
   * Run status
   */

  @Column({
    name: 'status',
    type: 'enum',
    enum: RunStatusEnum,
    default: RunStatusEnum.CREATED
  })
  status: RunStatusEnum; /**
   * Creation timestamp
   */

  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number; /**
   * User who created this run
   */

  @Column({ name: 'created_by', type: 'int', nullable: false })
  createdBy: number; /**
   * Thread ID
   */

  @Column({ name: 'thread_id', type: 'varchar', length: 255, nullable: false })
  threadId?: string | null; /**
   * Platform (chat, zalo, messenger, etc.)
   */

  @Column({
    name: 'platform',
    type: 'enum',
    enum: PlatformEnum,
    default: PlatformEnum.CHAT
  })
  platform: PlatformEnum; /**
   * Platform-specific thread ID
   */

  @Column({ name: 'platform_thread_id', type: 'uuid', nullable: true })
  platformThreadId?: string; /**
   * Platform-specific message ID
   */

  @Column({ name: 'platform_message_id', type: 'uuid', nullable: true })
  platformMessageId?: string; /**
   * BullMQ job ID
   */

  @Column({ name: 'job_id', type: 'varchar', length: 255, nullable: true })
  jobId?: string; /**
   * LangGraph run ID
   */

  @Column({ name: 'langgraph_run_id', type: 'varchar', length: 255, nullable: true })
  langgraphRunId?: string; /**
   * Processing start timestamp
   */

  @Column({ name: 'started_at', type: 'bigint', nullable: true })
  startedAt?: number; /**
   * Processing completion timestamp
   */

  @Column({ name: 'completed_at', type: 'bigint', nullable: true })
  completedAt?: number; /**
   * Processing time in milliseconds
   */

  @Column({ name: 'processing_time_ms', type: 'int', nullable: true })
  processingTimeMs?: number; /**
   * Token usage data
   */

  @Column({ name: 'token_usage', type: 'jsonb', nullable: true })
  tokenUsage?: any; /**
   * Error details if failed
   */

  @Column({ name: 'error_details', type: 'jsonb', nullable: true })
  errorDetails?: any; /**
   * Additional metadata
   */

  @Column({ name: 'metadata', type: 'jsonb', default: '{}' })
  metadata: any;
}
