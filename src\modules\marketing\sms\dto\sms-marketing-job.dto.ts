import { SmsCampaignType } from '../constants';
import { SenderTypeEnum } from '../../enums/sender-type.enum';

/**
 * DTO cho recipient trong SMS marketing job
 */
export interface SmsRecipientDto {
  /**
   * Số điện thoại người nhận
   */
  phone: string;
}

/**
 * DTO cho job SMS marketing trong queue
 */
export interface SmsMarketingJobDto {
  /**
   * ID của campaign
   */
  campaignId: number;

  /**
   * Nội dung SMS đã được xử lý sẵn từ app
   */
  content: string;

  /**
   * Danh sách người nhận
   */
  recipients: SmsRecipientDto[];

  /**
   * ID của SMS server configuration (deprecated - use smsServerConfig)
   */
  smsServerId?: number;

  /**
   * SMS server configuration (pre-calculated from app)
   */
  smsServerConfig?: Record<string, any>;



  /**
   * Loại chiến dịch SMS
   */
  campaignType: SmsCampaignType;

  /**
   * Tên campaign cho FPT SMS (dùng cho brandname SMS)
   */
  campaignName?: string;

  /**
   * Timestamp tạo job
   */
  createdAt: number;

  /**
   * Thời gian lên lịch gửi (Unix timestamp)
   * Bắt buộc đối với ADS campaign
   */
  scheduledAt?: number;

  /**
   * Flag để phân biệt campaign admin và user
   */
  isAdminCampaign?: boolean;

  /**
   * Provider-specific options (Twilio, generic providers)
   */
  providerOptions?: {
    provider?: string;
    [key: string]: any;
  };

  /**
   * Loại người gửi SMS (USER hoặc EMPLOYEE)
   */
  senderType?: SenderTypeEnum;

  /**
   * ID của người gửi SMS (user_id hoặc employee_id)
   */
  senderId?: number;
}
