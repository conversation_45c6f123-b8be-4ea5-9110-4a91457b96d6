# Service Migration Plan: BE App → BE Worker

## Overview

This document outlines the migration plan for moving specific services from BE App to BE Worker to support workflow node execution. The migration focuses on creating dedicated node executors while maintaining clean separation of concerns.

## Migration Strategy

### 1. Service Categories

#### A. Services to Migrate (Create Node Executors)
- **Google Services** → Google Node Executors
- **Facebook Services** → Facebook Node Executors  
- **Zalo Services** → Zalo Node Executors
- **Email Services** → Email Node Executors
- **SMS Services** → SMS Node Executors

#### B. Services to Keep in BE App
- **Authentication Services** (Auth, User management)
- **Business Logic Services** (Subscription, Marketing, etc.)
- **CRUD Operations** (Repositories, Controllers)
- **Admin Services** (Dashboard, System Configuration)

#### C. Shared Services
- **Database Entities** (Shared via npm package or direct import)
- **Common Utilities** (Validation, Formatting, etc.)
- **Configuration Services** (Environment, Constants)

### 2. Migration Approach

#### Phase 1: Analysis & Planning ✅
- [x] Analyze existing BE App services
- [x] Identify services for migration
- [x] Define migration boundaries
- [x] Create migration plan document

#### Phase 2: Shared Infrastructure Setup
- [ ] Create shared package for entities and interfaces
- [ ] Setup common utilities in BE Worker
- [ ] Configure shared database connections
- [ ] Setup shared configuration management

#### Phase 3: Google Services Migration
- [ ] Create Google node executors
- [ ] Migrate Google API services
- [ ] Setup Google authentication in Worker
- [ ] Create Google-specific interfaces

#### Phase 4: Facebook Services Migration
- [ ] Create Facebook node executors
- [ ] Migrate Facebook API services
- [ ] Setup Facebook authentication in Worker
- [ ] Create Facebook-specific interfaces

#### Phase 5: Zalo Services Migration
- [ ] Create Zalo node executors
- [ ] Migrate Zalo API services
- [ ] Setup Zalo authentication in Worker
- [ ] Create Zalo-specific interfaces

## Detailed Migration Plan

### Google Services Migration

#### Current BE App Structure:
```
redai-v201-be-app/src/shared/services/google/
├── ads/                    # Google Ads services
├── analytics/              # Google Analytics services
├── auth/                   # Google OAuth services
├── calendar/               # Google Calendar services
├── docs/                   # Google Docs services
├── drive/                  # Google Drive services
├── gmail/                  # Gmail services
├── sheets/                 # Google Sheets services
├── google-storage.service.ts
├── google-translate.service.ts
└── google-vision.service.ts
```

#### Target BE Worker Structure:
```
redai-v201-be-worker/src/modules/workflow/executors/google/
├── google-ads.executor.ts
├── google-analytics.executor.ts
├── google-calendar.executor.ts
├── google-docs.executor.ts
├── google-drive.executor.ts
├── google-gmail.executor.ts
├── google-sheets.executor.ts
├── google-storage.executor.ts
├── google-translate.executor.ts
├── google-vision.executor.ts
└── index.ts
```

#### Migration Steps:
1. **Create Base Google Executor**
   - Extend BaseNodeExecutor
   - Setup Google authentication
   - Common error handling
   - Rate limiting support

2. **Migrate Individual Services**
   - Convert each service to node executor
   - Maintain API compatibility
   - Add workflow-specific features
   - Setup proper input/output schemas

3. **Setup Authentication**
   - OAuth token management
   - Service account support
   - Token refresh mechanisms
   - Secure credential storage

### Facebook Services Migration

#### Target Structure:
```
redai-v201-be-worker/src/modules/workflow/executors/facebook/
├── facebook-ads.executor.ts
├── facebook-pages.executor.ts
├── facebook-posts.executor.ts
├── facebook-insights.executor.ts
└── index.ts
```

### Zalo Services Migration

#### Target Structure:
```
redai-v201-be-worker/src/modules/workflow/executors/zalo/
├── zalo-oa.executor.ts
├── zalo-ads.executor.ts
├── zalo-mini-app.executor.ts
└── index.ts
```

## Service Dependencies

### 1. Database Dependencies
- **Entities**: Shared via TypeORM entities
- **Repositories**: Keep in BE App, expose via API
- **Migrations**: Managed in BE App

### 2. Authentication Dependencies
- **User Authentication**: Keep in BE App
- **Service Authentication**: Migrate to BE Worker
- **Token Management**: Shared mechanism

### 3. Configuration Dependencies
- **Environment Variables**: Shared configuration
- **API Keys**: Secure storage in BE Worker
- **Rate Limits**: Service-specific configuration

## Communication Patterns

### 1. BE App → BE Worker
- **Job Queue**: Redis-based job processing
- **API Calls**: Direct HTTP calls for immediate responses
- **Event Publishing**: Redis pub/sub for notifications

### 2. BE Worker → BE App
- **Database Updates**: Via shared database
- **Status Updates**: Via Redis events
- **Error Reporting**: Via logging service

### 3. Shared Resources
- **Database**: Direct connection from both services
- **Redis**: Shared instance for queues and cache
- **File Storage**: Shared storage service

## Migration Checklist

### Pre-Migration
- [ ] Backup existing services
- [ ] Setup testing environment
- [ ] Create migration scripts
- [ ] Document API changes

### During Migration
- [ ] Maintain backward compatibility
- [ ] Implement gradual rollout
- [ ] Monitor performance impact
- [ ] Test integration points

### Post-Migration
- [ ] Update documentation
- [ ] Remove deprecated code
- [ ] Optimize performance
- [ ] Monitor production usage

## Risk Mitigation

### 1. Service Availability
- **Gradual Migration**: Migrate services one by one
- **Fallback Mechanisms**: Keep original services during transition
- **Health Checks**: Monitor service availability

### 2. Data Consistency
- **Transaction Management**: Ensure data consistency
- **Error Recovery**: Implement rollback mechanisms
- **Monitoring**: Track data integrity

### 3. Performance Impact
- **Load Testing**: Test under production load
- **Resource Monitoring**: Monitor CPU, memory usage
- **Optimization**: Optimize critical paths

## Success Metrics

### 1. Technical Metrics
- **Migration Completion**: 100% of planned services migrated
- **Test Coverage**: >90% test coverage for migrated services
- **Performance**: <10% performance degradation
- **Error Rate**: <1% error rate increase

### 2. Business Metrics
- **Workflow Execution**: Successful workflow node execution
- **User Experience**: No impact on user workflows
- **System Reliability**: 99.9% uptime maintained

## Timeline

### Week 1-2: Infrastructure Setup
- Setup shared packages
- Configure database connections
- Setup authentication mechanisms

### Week 3-4: Google Services Migration
- Migrate Google API services
- Create Google node executors
- Test Google integrations

### Week 5-6: Facebook Services Migration
- Migrate Facebook API services
- Create Facebook node executors
- Test Facebook integrations

### Week 7-8: Zalo Services Migration
- Migrate Zalo API services
- Create Zalo node executors
- Test Zalo integrations

### Week 9-10: Testing & Optimization
- Integration testing
- Performance optimization
- Documentation updates
- Production deployment

## Conclusion

This migration plan ensures a systematic approach to moving services from BE App to BE Worker while maintaining system reliability and performance. The phased approach allows for gradual migration with proper testing and validation at each step.
