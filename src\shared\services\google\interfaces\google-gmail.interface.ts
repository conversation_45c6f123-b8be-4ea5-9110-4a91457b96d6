/**
 * Interface cho cấu hình Gmail API
 */
export interface GoogleGmailConfig {
  /**
   * Client ID từ Google Cloud Console
   */
  clientId: string;

  /**
   * Client Secret từ Google Cloud Console
   */
  clientSecret: string;

  /**
   * Redirect URI cho OAuth
   */
  redirectUri: string;
}

/**
 * Interface cho Gmail OAuth tokens
 */
export interface GmailTokens {
  /**
   * Access token
   */
  access_token: string;

  /**
   * Refresh token
   */
  refresh_token?: string;

  /**
   * Thời gian hết hạn (Unix timestamp)
   */
  expiry_date?: number;

  /**
   * Loại token
   */
  token_type?: string;

  /**
   * Scope của token
   */
  scope?: string;
}

/**
 * Interface cho Gmail user info
 */
export interface GmailUserInfo {
  /**
   * Email address
   */
  email: string;

  /**
   * Tên hiển thị
   */
  name?: string;

  /**
   * URL ảnh đại diện
   */
  picture?: string;

  /**
   * Email đã được verify chưa
   */
  verified_email?: boolean;

  /**
   * ID của user
   */
  id?: string;

  /**
   * Locale
   */
  locale?: string;
}

/**
 * Interface cho Gmail message
 */
export interface GmailMessage {
  /**
   * Email người nhận
   */
  to: string;

  /**
   * Tiêu đề email
   */
  subject: string;

  /**
   * Nội dung email
   */
  body: string;

  /**
   * Có phải HTML không
   */
  isHtml?: boolean;

  /**
   * Danh sách CC
   */
  cc?: string[];

  /**
   * Danh sách BCC
   */
  bcc?: string[];

  /**
   * Danh sách attachments
   */
  attachments?: GmailAttachment[];

  /**
   * Reply-To address
   */
  replyTo?: string;

  /**
   * Priority
   */
  priority?: 'high' | 'normal' | 'low';

  /**
   * Custom headers
   */
  headers?: Record<string, string>;
}

/**
 * Interface cho Gmail attachment
 */
export interface GmailAttachment {
  /**
   * Tên file
   */
  filename: string;

  /**
   * Nội dung file (Buffer)
   */
  content: Buffer;

  /**
   * Content type
   */
  contentType: string;

  /**
   * Content ID (cho inline attachments)
   */
  contentId?: string;

  /**
   * Có phải inline attachment không
   */
  inline?: boolean;
}

/**
 * Interface cho kết quả gửi email
 */
export interface GmailSendResult {
  /**
   * ID của message
   */
  messageId: string;

  /**
   * ID của thread
   */
  threadId: string;

  /**
   * Có thành công không
   */
  success: boolean;

  /**
   * Thông tin bổ sung
   */
  metadata?: {
    sentAt: Date;
    size: number;
    labelIds?: string[];
  };
}

/**
 * Interface cho email template
 */
export interface GmailEmailTemplate {
  /**
   * ID của template
   */
  id?: string;

  /**
   * Tên template
   */
  name: string;

  /**
   * Tiêu đề email
   */
  subject: string;

  /**
   * Nội dung email
   */
  body: string;

  /**
   * Có phải HTML không
   */
  isHtml?: boolean;

  /**
   * Variables mặc định
   */
  variables?: Record<string, string>;

  /**
   * Mô tả template
   */
  description?: string;

  /**
   * Thời gian tạo
   */
  createdAt?: Date;

  /**
   * Thời gian cập nhật cuối
   */
  updatedAt?: Date;
}

/**
 * Interface cho bulk email
 */
export interface GmailBulkEmail {
  /**
   * Template để sử dụng
   */
  template: GmailEmailTemplate;

  /**
   * Danh sách người nhận
   */
  recipients: Array<{
    email: string;
    variables?: Record<string, string>;
    metadata?: Record<string, unknown>;
  }>;

  /**
   * Kích thước batch
   */
  batchSize?: number;

  /**
   * Delay giữa các batch (ms)
   */
  delayMs?: number;

  /**
   * Có retry khi fail không
   */
  retryOnFailure?: boolean;

  /**
   * Số lần retry tối đa
   */
  maxRetries?: number;
}

/**
 * Interface cho email stats
 */
export interface GmailEmailStats {
  /**
   * Số email đã gửi thành công
   */
  sent: number;

  /**
   * Số email gửi thất bại
   */
  failed: number;

  /**
   * Tổng số email
   */
  total: number;

  /**
   * Danh sách lỗi
   */
  errors: Array<{
    email: string;
    error: string;
    retryCount?: number;
  }>;

  /**
   * Thời gian bắt đầu
   */
  startTime: Date;

  /**
   * Thời gian kết thúc
   */
  endTime?: Date;

  /**
   * Thời gian xử lý (ms)
   */
  processingTime?: number;
}

/**
 * Interface cho OAuth state
 */
export interface GmailOAuthState {
  /**
   * ID của user
   */
  userId: number;

  /**
   * Timestamp tạo state
   */
  timestamp: number;

  /**
   * Action để phân biệt flow
   */
  action: string;

  /**
   * Metadata bổ sung
   */
  metadata?: Record<string, unknown>;
}

/**
 * Interface cho OAuth result
 */
export interface GmailOAuthResult {
  /**
   * Tokens OAuth
   */
  tokens: GmailTokens;

  /**
   * Thông tin user
   */
  userInfo: GmailUserInfo;

  /**
   * State đã parse
   */
  state: GmailOAuthState;
}

/**
 * Interface cho Gmail quota info
 */
export interface GmailQuotaInfo {
  /**
   * Có thể gửi email không
   */
  canSend: boolean;

  /**
   * Giới hạn hàng ngày
   */
  dailyLimit: number;

  /**
   * Giới hạn mỗi giây
   */
  perSecondLimit: number;

  /**
   * Số email đã gửi hôm nay
   */
  usedToday?: number;

  /**
   * Thời gian reset quota
   */
  resetTime?: Date;
}

/**
 * Interface cho Gmail search options
 */
export interface GmailSearchOptions {
  /**
   * Query string
   */
  query?: string;

  /**
   * Số lượng kết quả tối đa
   */
  maxResults?: number;

  /**
   * Page token cho pagination
   */
  pageToken?: string;

  /**
   * Label IDs để filter
   */
  labelIds?: string[];

  /**
   * Include spam và trash
   */
  includeSpamTrash?: boolean;
}

/**
 * Interface cho Gmail search result
 */
export interface GmailSearchResult {
  /**
   * Danh sách messages
   */
  messages: GmailMessageInfo[];

  /**
   * Next page token
   */
  nextPageToken?: string;

  /**
   * Tổng số kết quả ước tính
   */
  resultSizeEstimate: number;
}

/**
 * Interface cho Gmail message info
 */
export interface GmailMessageInfo {
  /**
   * ID của message
   */
  id: string;

  /**
   * Thread ID
   */
  threadId: string;

  /**
   * Label IDs
   */
  labelIds?: string[];

  /**
   * Snippet
   */
  snippet?: string;

  /**
   * History ID
   */
  historyId?: string;

  /**
   * Internal date
   */
  internalDate?: string;

  /**
   * Payload
   */
  payload?: GmailMessagePayload;

  /**
   * Size estimate
   */
  sizeEstimate?: number;

  /**
   * Raw message
   */
  raw?: string;
}

/**
 * Interface cho Gmail message payload
 */
export interface GmailMessagePayload {
  /**
   * Part ID
   */
  partId?: string;

  /**
   * MIME type
   */
  mimeType?: string;

  /**
   * Filename
   */
  filename?: string;

  /**
   * Headers
   */
  headers?: Array<{
    name: string;
    value: string;
  }>;

  /**
   * Body
   */
  body?: {
    attachmentId?: string;
    size?: number;
    data?: string;
  };

  /**
   * Parts
   */
  parts?: GmailMessagePayload[];
}

/**
 * Gmail scopes constants
 */
export const GMAIL_SCOPES = {
  SEND: 'https://www.googleapis.com/auth/gmail.send',
  READONLY: 'https://www.googleapis.com/auth/gmail.readonly',
  MODIFY: 'https://www.googleapis.com/auth/gmail.modify',
  COMPOSE: 'https://www.googleapis.com/auth/gmail.compose',
  LABELS: 'https://www.googleapis.com/auth/gmail.labels',
  METADATA: 'https://www.googleapis.com/auth/gmail.metadata',
  PROFILE: 'https://www.googleapis.com/auth/userinfo.profile',
  EMAIL: 'https://www.googleapis.com/auth/userinfo.email',
} as const;
