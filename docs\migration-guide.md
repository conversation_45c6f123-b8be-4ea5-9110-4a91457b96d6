# Service Migration Guide

## Overview

This guide provides step-by-step instructions for migrating services from BE App to BE Worker using the automated migration tools.

## Prerequisites

1. **BE App Source Code**: Access to redai-v201-be-app source code
2. **BE Worker Setup**: redai-v201-be-worker properly configured
3. **Node.js & npm**: Required for running migration commands
4. **TypeScript**: For code generation and compilation

## Migration Tools

### 1. Migration CLI Commands

The migration system provides several CLI commands:

```bash
# Analyze existing services
npm run migrate analyze

# Generate migration plan for specific category
npm run migrate plan --category google

# Generate executor templates
npm run migrate generate --category google

# Check migration status
npm run migrate status
```

### 2. Available Options

- `--be-app-path`: Path to BE App source directory (default: ../redai-v201-be-app)
- `--category`: Service category (google|facebook|zalo)
- `--service`: Specific service name (optional)
- `--output`: Output file or directory path

## Step-by-Step Migration Process

### Phase 1: Analysis

1. **Analyze Existing Services**
   ```bash
   npm run migrate analyze --be-app-path ../redai-v201-be-app
   ```

   This command will:
   - Scan BE App for existing services
   - Calculate complexity scores
   - Identify migration candidates
   - Generate analysis report

2. **Review Analysis Report**
   - Check `docs/migration-analysis.json`
   - Review service complexity scores
   - Identify high-priority services
   - Note dependencies and recommendations

### Phase 2: Planning

1. **Generate Migration Plan**
   ```bash
   npm run migrate plan --category google --output docs/google-migration-plan.json
   ```

   This creates a detailed plan including:
   - Migration phases
   - Time estimates
   - Dependencies
   - Risk assessment
   - Success criteria

2. **Review Migration Plan**
   - Validate estimated hours
   - Check phase dependencies
   - Review identified risks
   - Confirm success criteria

### Phase 3: Code Generation

1. **Generate All Executors for Category**
   ```bash
   npm run migrate generate --category google
   ```

2. **Generate Specific Service Executor**
   ```bash
   npm run migrate generate --category google --service ads
   ```

   Generated files:
   - `src/modules/workflow/executors/google/ads.executor.ts`
   - `src/modules/workflow/executors/google/index.ts`

### Phase 4: Implementation

1. **Implement Executor Logic**
   - Replace TODO comments with actual implementation
   - Add proper input/output schemas
   - Implement authentication
   - Add error handling

2. **Example Implementation**
   ```typescript
   protected async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
     const { campaignId, operation } = inputs;
     
     try {
       // Initialize Google Ads client
       const adsClient = await this.getGoogleAdsClient(context);
       
       // Perform operation
       const result = await adsClient.performOperation(campaignId, operation);
       
       return {
         success: true,
         data: result,
         timestamp: new Date().toISOString(),
       };
     } catch (error) {
       this.logger.error(`Google Ads operation failed: ${error.message}`);
       throw new Error(`Google Ads operation failed: ${error.message}`);
     }
   }
   ```

### Phase 5: Testing

1. **Create Unit Tests**
   ```typescript
   describe('GoogleAdsExecutor', () => {
     it('should execute ads operation successfully', async () => {
       // Test implementation
     });
   });
   ```

2. **Create Integration Tests**
   - Test with real API credentials (in test environment)
   - Validate input/output schemas
   - Test error handling scenarios

3. **Run Tests**
   ```bash
   npm run test
   npm run test:e2e
   ```

### Phase 6: Registration

1. **Register Executors in Module**
   ```typescript
   // In workflow.module.ts
   import { GoogleAdsExecutor } from './executors/google';
   
   @Module({
     providers: [
       // ... other providers
       GoogleAdsExecutor,
     ],
   })
   ```

2. **Register with Executor Registry**
   ```typescript
   // In google-executor-registry.service.ts
   async onModuleInit() {
     this.nodeExecutorRegistry.registerExecutor(GoogleAdsExecutor);
   }
   ```

## Service Categories

### Google Services

**High Priority:**
- Google Ads (`google.ads`)
- Google Analytics (`google.analytics`)
- Gmail (`google.gmail`)
- Google Calendar (`google.calendar`)

**Medium Priority:**
- Google Drive (`google.drive`)
- Google Sheets (`google.sheets`)
- Google Docs (`google.docs`)

**Low Priority:**
- Google Storage (`google.storage`)
- Google Translate (`google.translate`)
- Google Vision (`google.vision`)

### Facebook Services

**Available Services:**
- Facebook Ads (`facebook.ads`)
- Facebook Pages (`facebook.pages`)
- Facebook Posts (`facebook.posts`)
- Facebook Insights (`facebook.insights`)

### Zalo Services

**Available Services:**
- Zalo OA (`zalo.oa`)
- Zalo Ads (`zalo.ads`)
- Zalo Mini App (`zalo.miniapp`)

## Best Practices

### 1. Code Organization

```
src/modules/workflow/executors/
├── google/
│   ├── ads.executor.ts
│   ├── analytics.executor.ts
│   ├── base-google.executor.ts
│   └── index.ts
├── facebook/
│   ├── ads.executor.ts
│   └── index.ts
└── zalo/
    ├── oa.executor.ts
    └── index.ts
```

### 2. Authentication Handling

```typescript
// Base executor for category
export abstract class BaseGoogleExecutor extends BaseNodeExecutor {
  protected async getGoogleClient(context: ExecutionContext) {
    // Implement Google authentication
    // Handle token refresh
    // Return authenticated client
  }
}
```

### 3. Error Handling

```typescript
protected async executeNode(inputs: any, context: ExecutionContext): Promise<any> {
  try {
    // Implementation
  } catch (error) {
    if (error.code === 'QUOTA_EXCEEDED') {
      throw new Error('API quota exceeded - please try again later');
    }
    
    if (error.code === 'UNAUTHORIZED') {
      throw new Error('Authentication failed - please check credentials');
    }
    
    throw new Error(`Operation failed: ${error.message}`);
  }
}
```

### 4. Input/Output Schemas

```typescript
getInputSchema(): Record<string, any> {
  return {
    type: 'object',
    required: ['campaignId'],
    properties: {
      campaignId: {
        type: 'string',
        description: 'Google Ads campaign ID',
      },
      operation: {
        type: 'string',
        enum: ['pause', 'resume', 'update'],
        description: 'Operation to perform',
      },
    },
  };
}
```

## Troubleshooting

### Common Issues

1. **Build Errors**
   - Check TypeScript compilation errors
   - Verify import paths
   - Ensure all dependencies are installed

2. **Authentication Issues**
   - Verify API credentials
   - Check token expiration
   - Validate scopes and permissions

3. **Test Failures**
   - Mock external API calls
   - Use test credentials
   - Validate test data

### Getting Help

1. **Check Migration Status**
   ```bash
   npm run migrate status
   ```

2. **Review Generated Code**
   - Check executor templates
   - Validate schemas
   - Review error handling

3. **Run Analysis Again**
   ```bash
   npm run migrate analyze
   ```

## Migration Checklist

### Pre-Migration
- [ ] Backup existing services
- [ ] Setup test environment
- [ ] Review analysis report
- [ ] Validate migration plan

### During Migration
- [ ] Generate executor templates
- [ ] Implement service logic
- [ ] Add authentication
- [ ] Create unit tests
- [ ] Register executors

### Post-Migration
- [ ] Run integration tests
- [ ] Update documentation
- [ ] Monitor performance
- [ ] Clean up old code

## Conclusion

The migration tools provide a systematic approach to moving services from BE App to BE Worker. Follow this guide step-by-step to ensure a successful migration with minimal disruption to existing functionality.
