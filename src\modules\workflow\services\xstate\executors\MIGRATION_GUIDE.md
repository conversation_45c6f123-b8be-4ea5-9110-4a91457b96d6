# 🚀 ExecutorContext Migration Guide

## 📋 **Tổng <PERSON>uan**

Chúng ta đã refactor `ExecutorContext` từ **static interface** sang **generic type system** để giải quyết vấn đề context evolution và type safety trong workflow execution.

## ❌ **Vấn <PERSON>**

```typescript
// ❌ OLD: Static interface
interface ExecutorContext extends NodeExecutionContext {
  executionId: string;
  startTime: number;
  // ... fixed properties
}

// ❌ Không thể type-safe access node outputs
const httpResult = context.previousOutputs.get('http-node'); // any
const statusCode = httpResult?.status_code; // No IntelliSense
```

## ✅ **Gi<PERSON>i Pháp Mới**

```typescript
// ✅ NEW: Generic type system
type ExecutorContext<TOutput = any> = BaseExecutorContext & {
  outputData?: TOutput;
  nodeOutputs: Map<string, any>;
  getOutput<T>(nodeId: string): T | undefined;
  setOutput(nodeId: string, output: any): void;
  // ... helper methods
};

// ✅ Node-specific typed contexts
type HttpExecutorContext = ExecutorContext<IHttpRequestOutput>;
type LogicExecutorContext = ExecutorContext<IIfConditionOutput>;
```

## 🔄 **Migration Steps**

### **Step 1: Update Executor Signatures**

```typescript
// ❌ BEFORE
protected async executeNode(
  context: ExecutorContext,
  config: NodeExecutionConfig
): Promise<DetailedNodeExecutionResult>

// ✅ AFTER  
protected async executeNode(
  context: HttpExecutorContext, // Typed context
  config: NodeExecutionConfig
): Promise<DetailedNodeExecutionResult>
```

### **Step 2: Update Output Access**

```typescript
// ❌ BEFORE
const previousOutput = context.previousOutputs.get('node-id');
const statusCode = previousOutput?.status_code; // No type safety

// ✅ AFTER
const httpOutput = context.getOutput<IHttpRequestOutput>('node-id');
const statusCode = httpOutput?.status_code; // Type-safe with IntelliSense
```

### **Step 3: Update Output Storage**

```typescript
// ❌ BEFORE
context.previousOutputs.set(nodeId, result);

// ✅ AFTER
context.setOutput(nodeId, result);
context.outputData = result; // Typed assignment
```

### **Step 4: Use Context Factory**

```typescript
// ✅ NEW: Create typed contexts
const httpContext = ExecutorContextFactory.create<IHttpRequestOutput>(
  baseContext,
  {
    logger: this.logger,
    isDebugMode: true,
  }
);
```

## 🎯 **Benefits**

### **1. Type Safety**
```typescript
// ✅ Compile-time error detection
const httpOutput = context.getOutput<IHttpRequestOutput>('node-id');
console.log(httpOutput.status_code); // ✅ TypeScript knows this exists
console.log(httpOutput.invalidProp); // ❌ Compile error
```

### **2. IntelliSense Support**
```typescript
// ✅ Auto-completion for node-specific properties
context.outputData.| // Shows IHttpRequestOutput properties
```

### **3. Context Evolution**
```typescript
// ✅ Track outputs through workflow execution
const allOutputs = context.getAllOutputs();
const hasHttpOutput = context.hasOutput('http-node');
```

### **4. Type Guards**
```typescript
// ✅ Runtime type checking
if (ExecutorContextGuards.isHttpContext(context)) {
  // TypeScript knows this is HttpExecutorContext
  const statusCode = context.outputData?.status_code;
}
```

## 📝 **Node-Specific Context Types**

```typescript
// HTTP Nodes
type HttpExecutorContext = ExecutorContext<IHttpRequestOutput>;

// AI Nodes  
type AIExecutorContext = ExecutorContext<IAINodeOutput>;

// Logic Nodes
type LogicExecutorContext = ExecutorContext<IIfConditionOutput>;

// Transform Nodes
type TransformExecutorContext = ExecutorContext<ITransformNodeOutput>;

// Integration Nodes
type IntegrationExecutorContext = ExecutorContext<IIntegrationNodeOutput>;

// Utility Nodes
type UtilityExecutorContext = ExecutorContext<IWaitOutput>;
```

## 🔧 **Helper Methods**

```typescript
// ✅ Context helper methods
context.getOutput<T>(nodeId: string): T | undefined;
context.setOutput(nodeId: string, output: any): void;
context.hasOutput(nodeId: string): boolean;
context.getAllOutputs(): Record<string, any>;
context.clearOutput(nodeId: string): void;
```

## 🚨 **Breaking Changes**

### **1. Executor Method Signatures**
- All `executeNode` methods need typed context parameter
- Update validation methods to use typed contexts

### **2. Output Access Pattern**
- Replace `context.previousOutputs.get()` with `context.getOutput<T>()`
- Replace `context.previousOutputs.set()` with `context.setOutput()`

### **3. Context Creation**
- Use `ExecutorContextFactory.create()` instead of manual object creation

## 📚 **Examples**

### **HTTP Executor**
```typescript
@Injectable()
export class HttpRequestExecutor extends BaseNodeExecutor {
  protected async executeNode(
    context: HttpExecutorContext,
    config: NodeExecutionConfig
  ): Promise<DetailedNodeExecutionResult> {
    
    // ✅ Type-safe access to previous outputs
    const previousHttp = context.getOutput<IHttpRequestOutput>('prev-http');
    
    // ✅ Execute HTTP request
    const result: IHttpRequestOutput = await this.executeHttpRequest();
    
    // ✅ Store typed output
    context.setOutput(context.node.id, result);
    context.outputData = result;
    
    return { success: true, outputData: result };
  }
}
```

### **Logic Executor**
```typescript
@Injectable()
export class IfConditionExecutor extends BaseNodeExecutor {
  protected async executeNode(
    context: LogicExecutorContext,
    config: NodeExecutionConfig
  ): Promise<DetailedNodeExecutionResult> {
    
    // ✅ Access outputs from different node types
    const httpOutput = context.getOutput<IHttpRequestOutput>('http-node');
    const aiOutput = context.getOutput<IAINodeOutput>('ai-node');
    
    // ✅ Type-safe condition evaluation
    const conditionResult = httpOutput?.success && aiOutput?.confidence > 0.8;
    
    const result: IIfConditionOutput = {
      result: conditionResult,
      data: context.inputData,
      evaluation_details: { /* ... */ },
      metadata: { /* ... */ }
    };
    
    context.setOutput(context.node.id, result);
    context.outputData = result;
    
    return { success: true, outputData: result };
  }
}
```

## ⚡ **Performance Impact**

- **Minimal Runtime Overhead**: Generic types are compile-time only
- **Better Memory Management**: Structured output storage
- **Improved Developer Experience**: Faster development with IntelliSense

## 🔮 **Future Enhancements**

1. **Auto-generated Context Types**: Generate contexts from node definitions
2. **Context Validation**: Runtime validation of context structure  
3. **Context Serialization**: Better state persistence support
4. **Context Debugging**: Enhanced debugging tools for context evolution

## 📞 **Support**

For questions about migration:
1. Check the examples in `typed-context-usage.example.ts`
2. Review existing executor implementations
3. Use TypeScript compiler to catch migration issues
4. Test thoroughly with your specific node types

---

**Happy Coding! 🚀**
