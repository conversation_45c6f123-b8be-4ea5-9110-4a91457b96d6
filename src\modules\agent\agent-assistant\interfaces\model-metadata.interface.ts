import {
  ProviderEnum,
  ModelFeatureEnum,
  InputModalityEnum,
  OutputModalityEnum,
  SamplingParameterEnum,
} from '../enums/model-capabilities.enum';
import { ModelPricingInterface } from './model-pricing.interface';

/**
 * Model metadata interface
 * Defines comprehensive metadata about AI model capabilities and specifications
 */
export interface ModelMetadata {
  /**
   * AI model provider (OPENAI, ANTHROPIC, etc.)
   */
  provider: ProviderEnum;

  /**
   * Model capabilities (tool_call, parallel_tool_call, etc.)
   */
  capabilities: ModelFeatureEnum[];

  /**
   * Maximum context window size in tokens
   */
  contextWindow: number;

  /**
   * Maximum output tokens per request
   */
  maxTokens: number;

  /**
   * Supported input modalities (text, image, audio, video)
   */
  inputModalities: InputModalityEnum[];

  /**
   * Supported output modalities (text, image, audio, video)
   */
  outputModalities: OutputModalityEnum[];

  /**
   * Available sampling parameters (temperature, top_p, etc.)
   */
  samplingParameters: SamplingParameterEnum[];

  /**
   * Base pricing configuration for the model
   */
  basePricing: ModelPricingInterface;

  /**
   * Fine-tune pricing configuration for the model
   */
  fineTunePricing: ModelPricingInterface;
}
