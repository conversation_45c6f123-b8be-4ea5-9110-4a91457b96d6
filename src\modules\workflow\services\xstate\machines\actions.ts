import { assign, sendParent, spawn } from 'xstate';
import { WorkflowContext, NodeExecutionContext, DetailedNodeExecutionResult } from '../types';
import { nodeExecutionMachine } from './node-execution.machine';

/**
 * Workflow machine actions
 */
export const workflowActions = {
  /**
   * Initialize workflow context
   */
  initializeWorkflow: assign({
    workflowId: ({ event }: { event: any }) => event.workflowId,
    executionId: ({ event }: { event: any }) => event.executionId,
    triggerData: ({ event }: { event: any }) => event.triggerData,
    metadata: ({ context, event }: { context: WorkflowContext; event: any }) => ({
      ...context.metadata,
      startTime: Date.now(),
      triggerType: event.triggerType || 'manual',
      totalNodes: 0,
      completedNodes: 0,
      failedNodes: 0,
      cancelledNodes: 0,
      retryCount: 0,
    }),
    errors: () => new Map(),
    executionData: () => new Map(),
    runningNodes: () => [],
    readyNodes: () => [],
    waitingNodes: () => [],
  }),

  /**
   * Load workflow data
   */
  loadWorkflowData: assign({
    nodes: ({ event }: { event: any }) => event.output.nodes,
    connections: ({ event }: { event: any }) => event.output.connections,
    dependencyGraph: ({ event }: { event: any }) => event.output.dependencyGraph,
    readyNodes: ({ event }: { event: any }) => event.output.readyNodes,
    waitingNodes: ({ event }: { event: any }) => event.output.waitingNodes,
    workflowSettings: ({ event }: { event: any }) => event.output.workflowSettings,
    metadata: ({ context, event }: { context: WorkflowContext; event: any }) => ({
      ...context.metadata,
      totalNodes: event.output.nodes?.size || 0,
    }),
  }),

  /**
   * Mark workflow as validated
   */
  markWorkflowValidated: assign({
    metadata: ({ context }: { context: WorkflowContext }) => ({
      ...context.metadata,
      validatedAt: Date.now(),
    }),
  }),

  /**
   * Start workflow execution
   */
  startExecution: assign({
    metadata: ({ context }: { context: WorkflowContext }) => ({
      ...context.metadata,
      executionStartTime: Date.now(),
    }),
  }),

  /**
   * Spawn ready nodes for execution
   */
  spawnReadyNodes: assign({
    runningNodes: ({ context, spawn }: { context: WorkflowContext; spawn: any }) => {
      const newRunningNodes = [...(context.runningNodes || [])];
      
      for (const nodeId of context.readyNodes || []) {
        const nodeState = context.nodes?.get(nodeId);
        if (nodeState && nodeState.status === 'pending') {
          // Create node execution context
          const nodeContext: NodeExecutionContext = {
            node: nodeState.node,
            inputData: nodeState.inputData,
            previousOutputs: context.executionData,
            executionId: context.executionId,
            workflowId: context.workflowId,
            userId: context.userId,
            headers: context.headers || {},
            options: context.options,
            nodes: context.nodes,
            triggerData: context.triggerData,
          };

          // Spawn node execution machine
          const nodeActor = spawn(nodeExecutionMachine, {
            id: `node-${nodeId}`,
            input: {
              nodeContext,
              config: {},
            },
          });
          
          // Update node state
          nodeState.status = 'running';
          nodeState.startTime = Date.now();
          nodeState.actor = nodeActor;
          
          newRunningNodes.push(nodeId);
        }
      }
      
      return newRunningNodes;
    },
    readyNodes: () => [], // Clear ready nodes after spawning
  }),

  /**
   * Handle node completion
   */
  handleNodeCompleted: assign({
    runningNodes: ({ context, event }: { context: WorkflowContext; event: any }) => {
      return (context.runningNodes || []).filter(id => id !== event.nodeId);
    },
    executionData: ({ context, event }: { context: WorkflowContext; event: any }) => {
      const newExecutionData = new Map(context.executionData);
      newExecutionData.set(event.nodeId, event.result.outputData);
      return newExecutionData;
    },
    metadata: ({ context }: { context: WorkflowContext }) => ({
      ...context.metadata,
      completedNodes: (context.metadata?.completedNodes || 0) + 1,
    }),
  }),

  /**
   * Handle node failure
   */
  handleNodeFailed: assign({
    runningNodes: ({ context, event }: { context: WorkflowContext; event: any }) => {
      return (context.runningNodes || []).filter(id => id !== event.nodeId);
    },
    errors: ({ context, event }: { context: WorkflowContext; event: any }) => {
      const newErrors = new Map(context.errors);
      newErrors.set(event.nodeId, event.error);
      return newErrors;
    },
    metadata: ({ context }: { context: WorkflowContext }) => ({
      ...context.metadata,
      failedNodes: (context.metadata?.failedNodes || 0) + 1,
    }),
  }),

  /**
   * Update dependencies and ready nodes
   */
  updateDependencies: assign({
    readyNodes: ({ context }: { context: WorkflowContext }) => {
      // Recalculate ready nodes based on completed nodes
      const completedNodes = new Set<string>();
      
      for (const [nodeId, nodeState] of context.nodes?.entries() || []) {
        if (nodeState.status === 'completed') {
          completedNodes.add(nodeId);
        }
      }
      
      const readyNodes: string[] = [];
      
      for (const [nodeId, nodeState] of context.nodes?.entries() || []) {
        if (nodeState.status === 'pending') {
          const dependencies = context.dependencyGraph?.dependencies.get(nodeId) || [];
          const allDependenciesCompleted = dependencies.every(depId => 
            completedNodes.has(depId)
          );
          
          if (allDependenciesCompleted) {
            readyNodes.push(nodeId);
          }
        }
      }
      
      return readyNodes;
    },
  }),

  /**
   * Prepare workflow result
   */
  prepareWorkflowResult: assign({
    metadata: ({ context }: { context: WorkflowContext }) => ({
      ...context.metadata,
      finalOutputData: Object.fromEntries(context.executionData?.entries() || []),
      completedAt: Date.now(),
      executionTime: Date.now() - (context.metadata?.startTime || 0),
    }),
  }),

  /**
   * Pause running nodes
   */
  pauseRunningNodes: ({ context }: { context: WorkflowContext }) => {
    for (const nodeId of context.runningNodes || []) {
      const nodeState = context.nodes?.get(nodeId);
      if (nodeState?.actor) {
        nodeState.actor.send({ type: 'PAUSE_EXECUTION' });
      }
    }
  },

  /**
   * Resume running nodes
   */
  resumeRunningNodes: ({ context }: { context: WorkflowContext }) => {
    for (const nodeId of context.runningNodes || []) {
      const nodeState = context.nodes?.get(nodeId);
      if (nodeState?.actor) {
        nodeState.actor.send({ type: 'RESUME_EXECUTION' });
      }
    }
  },

  /**
   * Cancel running nodes
   */
  cancelRunningNodes: ({ context }: { context: WorkflowContext }) => {
    for (const nodeId of context.runningNodes || []) {
      const nodeState = context.nodes?.get(nodeId);
      if (nodeState?.actor) {
        nodeState.actor.send({ type: 'CANCEL_EXECUTION' });
      }
    }
  },

  /**
   * Cancel all nodes
   */
  cancelAllNodes: ({ context }: { context: WorkflowContext }) => {
    for (const [nodeId, nodeState] of context.nodes?.entries() || []) {
      if (nodeState.status === 'running' || nodeState.status === 'pending') {
        nodeState.status = 'cancelled';
        if (nodeState.actor) {
          nodeState.actor.send({ type: 'CANCEL_EXECUTION' });
        }
      }
    }
  },

  /**
   * Reset workflow state for retry
   */
  resetWorkflowState: assign({
    errors: () => new Map(),
    runningNodes: () => [],
    executionData: () => new Map(),
    nodes: ({ context }: { context: WorkflowContext }) => {
      // Reset all node states to pending
      const resetNodes = new Map(context.nodes);
      for (const [nodeId, nodeState] of resetNodes.entries()) {
        if (nodeState.status !== 'completed') {
          nodeState.status = 'pending';
          nodeState.startTime = undefined;
          nodeState.endTime = undefined;
          nodeState.error = undefined;
          nodeState.retryCount = 0;
          nodeState.actor = undefined;
        }
      }
      return resetNodes;
    },
    metadata: ({ context }: { context: WorkflowContext }) => ({
      ...context.metadata,
      retryCount: (context.metadata?.retryCount || 0) + 1,
      startTime: Date.now(),
      completedNodes: 0,
      failedNodes: 0,
      cancelledNodes: 0,
    }),
  }),

  /**
   * Create checkpoint
   */
  createCheckpoint: ({ context }: { context: WorkflowContext }) => {
    // Implementation would save checkpoint
    console.log(`Creating checkpoint for workflow ${context.workflowId}`);
  },

  /**
   * Notify workflow completed
   */
  notifyWorkflowCompleted: ({ context }: { context: WorkflowContext }) => {
    console.log(`Workflow ${context.workflowId} completed successfully`);
    // Emit event to external systems
  },

  /**
   * Notify workflow failed
   */
  notifyWorkflowFailed: ({ context }: { context: WorkflowContext }) => {
    console.log(`Workflow ${context.workflowId} failed`);
    // Emit event to external systems
  },

  /**
   * Notify workflow cancelled
   */
  notifyWorkflowCancelled: ({ context }: { context: WorkflowContext }) => {
    console.log(`Workflow ${context.workflowId} cancelled`);
    // Emit event to external systems
  },
};

/**
 * Node execution machine actions
 */
export const nodeExecutionActions = {
  /**
   * Initialize node execution
   */
  initializeNodeExecution: assign({
    nodeContext: ({ event }: { event: any }) => event.context,
    config: ({ event }: { event: any }) => event.config,
    startTime: () => Date.now(),
    metadata: ({ event }: { event: any }) => ({
      nodeId: event.context.node.id,
      nodeName: event.context.node.name,
      nodeType: event.context.nodeDefinition?.typeName || 'unknown',
      executionId: event.context.executionId,
      workflowId: event.context.workflowId,
      attempts: 1,
      totalExecutionTime: 0,
      pausedDuration: 0,
    }),
  }),

  /**
   * Update node state to completed
   */
  updateNodeState: ({ context }: { context: any }) => {
    if (context.nodeContext) {
      const nodeState = context.nodeContext.nodes?.get(context.metadata.nodeId);
      if (nodeState) {
        nodeState.status = 'completed';
        nodeState.endTime = Date.now();
        nodeState.outputData = context.result?.outputData;
      }
    }
  },

  /**
   * Update node state to failed
   */
  updateNodeStateToFailed: ({ context }: { context: any }) => {
    if (context.nodeContext) {
      const nodeState = context.nodeContext.nodes?.get(context.metadata.nodeId);
      if (nodeState) {
        nodeState.status = 'failed';
        nodeState.endTime = Date.now();
        nodeState.error = context.error;
        nodeState.retryCount = context.retryCount;
      }
    }
  },

  /**
   * Update node state to cancelled
   */
  updateNodeStateToCancelled: ({ context }: { context: any }) => {
    if (context.nodeContext) {
      const nodeState = context.nodeContext.nodes?.get(context.metadata.nodeId);
      if (nodeState) {
        nodeState.status = 'cancelled';
        nodeState.endTime = Date.now();
      }
    }
  },

  /**
   * Save execution result
   */
  saveExecutionResult: ({ context }: { context: any }) => {
    if (context.nodeContext && context.result) {
      context.nodeContext.executionData.set(
        context.metadata.nodeId, 
        context.result.outputData
      );
    }
  },

  /**
   * Save execution error
   */
  saveExecutionError: ({ context }: { context: any }) => {
    if (context.nodeContext && context.error) {
      context.nodeContext.errors.set(
        context.metadata.nodeId, 
        context.error
      );
    }
  },

  /**
   * Notify execution completed
   */
  notifyExecutionCompleted: sendParent(({ context }: { context: any }) => ({
    type: 'NODE_COMPLETED',
    nodeId: context.metadata.nodeId,
    result: context.result,
  })),

  /**
   * Notify execution failed
   */
  notifyExecutionFailed: sendParent(({ context }: { context: any }) => ({
    type: 'NODE_FAILED',
    nodeId: context.metadata.nodeId,
    error: context.error,
  })),

  /**
   * Notify execution cancelled
   */
  notifyExecutionCancelled: sendParent(({ context }: { context: any }) => ({
    type: 'NODE_CANCELLED',
    nodeId: context.metadata.nodeId,
  })),

  /**
   * Increment retry count
   */
  incrementRetryCount: assign({
    retryCount: ({ context }: { context: any }) => context.retryCount + 1,
    metadata: ({ context }: { context: any }) => ({
      ...context.metadata,
      attempts: context.metadata.attempts + 1,
    }),
  }),

  /**
   * Record pause time
   */
  recordPauseTime: assign({
    pauseTime: () => Date.now(),
  }),

  /**
   * Record resume time and calculate paused duration
   */
  recordResumeTime: assign({
    resumeTime: () => Date.now(),
    metadata: ({ context }: { context: any }) => ({
      ...context.metadata,
      pausedDuration: context.metadata.pausedDuration + 
        (Date.now() - (context.pauseTime || 0)),
    }),
  }),
};

/**
 * Combined actions for easy import
 */
export const allActions = {
  ...workflowActions,
  ...nodeExecutionActions,
};

/**
 * Action utilities
 */
export const actionUtils = {
  /**
   * Create a logged action
   */
  createLoggedAction: (name: string, actionFn: Function) => {
    return (params: any) => {
      console.log(`Action [${name}] executing`);
      const result = actionFn(params);
      console.log(`Action [${name}] completed`);
      return result;
    };
  },

  /**
   * Combine multiple actions
   */
  combineActions: (...actions: Function[]) => {
    return (params: any) => {
      return actions.map(action => action(params));
    };
  },

  /**
   * Create conditional action
   */
  conditionalAction: (condition: Function, actionFn: Function) => {
    return (params: any) => {
      if (condition(params)) {
        return actionFn(params);
      }
    };
  },
};
