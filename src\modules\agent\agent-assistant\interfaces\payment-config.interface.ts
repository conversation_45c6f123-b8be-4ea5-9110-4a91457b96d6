import { PaymentMethodEnum } from '../enums/payment-method.enum';
import { PaymentGateway, UserProviderShipment } from '../entities';

/**
 * Payment configuration interface
 * Defines payment and shipping configuration for agent transactions
 */
export interface PaymentConfig {
  /**
   * Payment gateway configuration (bank account, etc.)
   */
  paymentGateway: PaymentGateway | null;

  /**
   * Shipment provider configuration (GHTK, GHN, etc.)
   */
  shipmentProvider: UserProviderShipment | null;

  /**
   * Whether the receiver pays the shipping fee
   */
  receiverPayShippingFee: boolean;

  /**
   * Supported payment methods (COD, BANKING, etc.)
   */
  paymentMethods: PaymentMethodEnum[];
}
