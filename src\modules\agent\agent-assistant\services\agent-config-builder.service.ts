import { Injectable, Logger } from '@nestjs/common';
import { MessageIngestResult } from '../interfaces/message-ingest-result.interface';
import { PlatformContextInput } from '../interfaces/platform-context.interface';
import { AgentAssistantCustomConfigurableType, ConvertCustomerContext } from '../schemas/agent-assistant.schema';
import { AgentMemoryRepository } from '../repositories/agent-memory.repository';
import { ZaloContextLoadingService } from './zalo-context-loading.service';
import {
  AgentCoreConfigService,
  AgentModelConfigService,
  AgentPaymentConfigService,
  AgentMediaContextService,
  AgentPlatformContextService,
  AgentMcpConfigService,
  AgentStrategistConfigBuilderService,
} from './agent-config-builder';
import { ZaloCustomer, ZaloOfficialAccount } from '../entities';
import { ModelTypeEnum } from '../enums';
import { ModelPricing } from '../core/configurable-interfaces';

/**
 * Agent Config Builder Service
 * Orchestrates all individual config builder services to create complete agent configuration
 */
@Injectable()
export class AgentConfigBuilderService {
  private readonly logger = new Logger(AgentConfigBuilderService.name);

  constructor(
    private readonly coreConfigService: AgentCoreConfigService,
    private readonly modelConfigService: AgentModelConfigService,
    private readonly paymentConfigService: AgentPaymentConfigService,
    private readonly mediaContextService: AgentMediaContextService,
    private readonly platformContextService: AgentPlatformContextService,
    private readonly mcpConfigService: AgentMcpConfigService,
    private readonly strategistConfigBuilderService: AgentStrategistConfigBuilderService,
    private readonly agentMemoryRepository: AgentMemoryRepository,
    private readonly zaloContextLoadingService: ZaloContextLoadingService,
  ) {}

  /**
   * Build complete agent configuration for LangGraph processing
   * @param ingestResult Result from message ingestion (Phase 1)
   * @param platformInput Platform-specific input data
   * @param checkpointId Optional checkpoint ID for LangGraph
   * @returns Complete AgentAssistantCustomConfigurableType object
   */
  async buildAgentConfig(
    ingestResult: MessageIngestResult,
    platformInput: PlatformContextInput,
    checkpointId?: string,
  ): Promise<AgentAssistantCustomConfigurableType> {
    const { agentId, userId } =
      platformInput.zaloOfficialAccount as ZaloOfficialAccount;
    this.logger.debug(`Building agent config for agent ${agentId}`);

    try {
      // Extract UUID from LangGraph thread ID for database operations
      const threadUuid = this.extractUuidFromThreadId(ingestResult.threadId);

      // 1. Single validation: Get and validate core agent data first
      const coreConfig = await this.coreConfigService.getCoreAgentData(
        agentId as string,
        userId,
      );
      if (!coreConfig) {
        throw new Error(
          `Agent ${agentId} not found or not accessible by user ${userId}`,
        );
      }

      // 2. Now build remaining configurations in parallel (no validation needed)
      // Pass the validated agentUser object to avoid redundant database calls
      const [
        modelConfig,
        paymentConfig,
        platformContext,
        mcpConfig,
        mediaContext,
        strategistConfig,
        replyContext,
        agentMemories,
      ] = await Promise.all([
        this.modelConfigService.getModelConfigFromValidatedAgent({
          userModelId: coreConfig.userModelId,
          modelFineTuneId: coreConfig.modelFineTuneId,
          systemModelId: coreConfig.systemModelId,
          keyLlmId: coreConfig.keyLlmId,
        }),
        this.paymentConfigService.getPaymentConfigFromValidatedAgent({
          paymentGatewayId: coreConfig.paymentGatewayId,
          userProviderShipmentId: coreConfig.userProviderShipmentId,
          receiverPayShippingFee: coreConfig.receiverPayShippingFee,
          paymentMethods: coreConfig.paymentMethods,
        }),
        this.platformContextService.getPlatformContext(platformInput),
        this.mcpConfigService.getMcpConfigFromValidatedAgent(agentId as string),
        this.mediaContextService.getMediaContext(threadUuid),
        this.strategistConfigBuilderService.buildStrategistConfig(
          coreConfig.strategyId as string,
          userId as number,
        ),
        this.platformContextService.getReplyContext(
          threadUuid,
          platformInput.replyToMessageId,
        ),
        this.agentMemoryRepository.findByAgentId(agentId as string),
      ]);

      // 🔥 NEW: Load comprehensive customer context data (use customer from ingestResult)
      const convertCustomerContext = await this.loadCustomerContextData(
        ingestResult.customer,
        agentId as string,
      );

      // Validate API keys after model config is loaded
      const validApiKeys = modelConfig.apiKeys.filter(
        (key: any) => key.encryptedKey != null,
      );
      if (validApiKeys.length === 0) {
        throw new Error(`No valid API keys found for agent ${agentId}`);
      }

      let modelPricing: {
        inputRate: number;
        outputRate: number;
      } = {
        inputRate: 0,
        outputRate: 0,
      } // detault user type

      if (modelConfig.modelType === ModelTypeEnum.FINE_TUNE) {
        modelPricing = modelConfig.modelMetadata.fineTunePricing;
      } else if (modelConfig.modelType === ModelTypeEnum.SYSTEM) {
        modelPricing = modelConfig.modelMetadata.basePricing;
      } else {
        throw new Error(`Unsupported model type: ${modelConfig.modelType}`);
      }

      // Build complete agent configuration matching the schema
      const agentConfig: AgentAssistantCustomConfigurableType = {
        // Core LangGraph fields
        thread_id: ingestResult.threadId,
        checkpoint_id: checkpointId,
        userId: userId,

        // Main agent configuration
        mainAgent: {
          ...coreConfig,
          description: coreConfig.description || 'Default Description',
          instruction:
            coreConfig.instruction || 'You are a helpful AI assistant.',
          model: {
            type: modelConfig.modelType,
            name: modelConfig.modelId,
            provider: modelConfig.modelMetadata.provider,
            inputModalities: modelConfig.modelMetadata.inputModalities,
            outputModalities: modelConfig.modelMetadata.outputModalities,
            samplingParameters: modelConfig.modelMetadata.samplingParameters,
            features: modelConfig.modelMetadata.capabilities,
            parameters: {
              temperature: coreConfig.modelConfig?.temperature,
              topP: coreConfig.modelConfig?.top_p,
              topK: coreConfig.modelConfig?.top_k,
              maxTokens:
                coreConfig.modelConfig?.max_tokens ||
                modelConfig.modelMetadata.maxTokens,
              maxOutputTokens:
                coreConfig.modelConfig?.max_tokens ||
                modelConfig.modelMetadata.maxTokens,
            },
            pricing: modelPricing,
            apiKeys: modelConfig.apiKeys
              .filter((key: any) => key.encryptedKey != null)
              .map((key: any) => key.encryptedKey),
          },
          mcpConfig: mcpConfig,
          paymentGatewayId: paymentConfig.paymentGateway?.id ? Number(paymentConfig.paymentGateway.id) : null,
          userProviderShipmentId: paymentConfig.shipmentProvider?.id || null,
          receiverPayShippingFee: paymentConfig.receiverPayShippingFee,
          paymentMethods: paymentConfig.paymentMethods,
          agentMemories: agentMemories?.map(memory => ({
            id: memory.id,
            title: memory.structuredContent?.title || 'Untitled Memory',
            content: memory.structuredContent?.content || 'No content',
            reason: memory.structuredContent?.reason || 'No reason',
            createdAt: memory.createdAt || Date.now(),
          })) || [],
        },

        // Strategist agent configuration (if available)
        strategistAgent: strategistConfig,

        // Media context for image processing
        attachmentImageMap: mediaContext.attachmentImageMap,
        attachmentContext: mediaContext.attachmentContext,

        // Reply context (fetched separately, not from platformContext)
        replyToContext: replyContext,

        // Platform context
        zaloOfficialAccount: platformContext.zaloOfficialAccount,
        platformContext: platformContext.platformContext,

        // 🔥 NEW: Customer context data
        convertCustomerContext,

        // Contextual tools (can be added later)
        contextualTools: [],
      };

      this.logger.log(`Agent config built successfully for agent ${agentId}`);
      return agentConfig;
    } catch (error) {
      this.logger.error(
        `Failed to build agent config for agent ${agentId}:`,
        error,
      );
      throw new Error(`Agent config building failed: ${error.message}`);
    }
  }

  /**
   * Load comprehensive customer context data
   * @param zaloCustomer Zalo customer entity
   * @param agentId Agent ID for loading agent-specific data
   * @returns Customer context data
   */
  private async loadCustomerContextData(
    zaloCustomer: ZaloCustomer,
    agentId: string,
  ): Promise<ConvertCustomerContext> {
    try {
      // Check if we have the necessary data for customer context loading
      if (!zaloCustomer) {
        throw new Error('Missing required customer data for context loading');
      }

      // Load comprehensive customer data using the context loading service
      return await this.zaloContextLoadingService.loadComprehensiveCustomerData(
        zaloCustomer,
        agentId,
      );
    } catch (error) {
      this.logger.error('Failed to load customer context:', error);
      throw error;
    }
  }

  /**
   * Extract UUID from LangGraph thread ID format
   * @param threadId Thread ID in format "zalo:userId:uuid"
   * @returns UUID string
   */
  private extractUuidFromThreadId(threadId: string): string {
    const parts = threadId.split(':');
    if (parts.length !== 3 || parts[0] !== 'zalo') {
      throw new Error(
        `Invalid thread ID format: ${threadId}. Expected format: zalo:userId:uuid`,
      );
    }
    return parts[2]; // Extract UUID from thread ID
  }
}
