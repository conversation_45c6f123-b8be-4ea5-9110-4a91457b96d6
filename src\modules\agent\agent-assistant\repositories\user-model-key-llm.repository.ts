import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserModelKeyLlm } from '../entities/user-model-key-llm.entity';
import { UserKeyLlm } from '../entities/user-key-llm.entity';
import { UserModelWithKeys } from '../interfaces/user-model-key.interface';



@Injectable()
export class UserModelKeyLlmRepository {
  constructor(
    @InjectRepository(UserModelKeyLlm)
    private readonly repository: Repository<UserModelKeyLlm>,
  ) {}

  async findKeysByModelId(modelId: string): Promise<UserModelWithKeys[]> {
    return this.repository
      .createQueryBuilder('umkl')
      .innerJoin('user_key_llm', 'ukl', 'umkl.llm_key_id = ukl.id')
      .select([
        'umkl.model_id as "modelId"',
        'umkl.llm_key_id as "llmKeyId"',
        'ukl.name as "keyName"',
        'ukl.provider as "provider"',
        'ukl.api_key as "apiKey"',
        'ukl.user_id as "userId"',
        'ukl.created_at as "keyCreatedAt"',
        'ukl.deleted_at as "keyDeletedAt"'
      ])
      .where('umkl.model_id = :modelId', { modelId })
      .andWhere('ukl.deleted_at IS NULL')
      .getRawMany();
  }
}
