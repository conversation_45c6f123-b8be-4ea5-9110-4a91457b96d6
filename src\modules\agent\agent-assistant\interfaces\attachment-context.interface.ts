
/**
 * Attachment context interface for system prompt
 */
export interface AttachmentContext {
  id: string;           // Media ID
  name: string;         // File name
  description: string;  // AI/human description
  tag: string;          // context_type (attachment, product_reference, etc.)
  type: string;         // attachment type derived from mime_type (image, video, audio, file)
  metadata: {
    mimeType?: string;
    width?: number;
    height?: number;
    tags: string[];
  };
}