import { IsOptional, IsString, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { SegmentCriteriaDto } from './segment-criteria.dto';

/**
 * DTO cho việc cập nhật segment
 */
export class UpdateSegmentDto {
  /**
   * Tên segment
   * @example "Khách hàng tiềm năng"
   */
  @ApiProperty({
    description: 'Tên segment',
    example: 'Khách hàng tiềm năng',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên segment phải là chuỗi' })
  name?: string;

  /**
   * Mô tả segment
   * @example "Khách hàng có khả năng mua hàng cao"
   */
  @ApiProperty({
    description: 'Mô tả segment',
    example: 'Khách hàng có khả năng mua hàng cao',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '<PERSON><PERSON> tả phải là chuỗi' })
  description?: string;

  /**
   * <PERSON>i<PERSON><PERSON> kiện lọc khách hàng
   */
  @ApiProperty({
    description: 'Điều kiện lọc khách hàng',
    type: SegmentCriteriaDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SegmentCriteriaDto)
  criteria?: SegmentCriteriaDto;
}
