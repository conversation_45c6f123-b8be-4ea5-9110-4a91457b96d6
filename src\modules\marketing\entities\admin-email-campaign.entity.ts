import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { CampaignAudience, CampaignSegment, EmailCampaignContent, AdminEmailCampaignStatus } from '../types/admin-email-campaign.types';

/**
 * Entity đại diện cho bảng admin_email_campaigns trong cơ sở dữ liệu
 * Bảng chiến dịch email của admin
 */
@Entity('admin_email_campaigns')
export class AdminEmailCampaign {
  /**
   * ID của campaign
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Tên chiến dịch
   */
  @Column({ name: 'name', type: 'varchar', length: 255, nullable: false, comment: 'Tên chiến dịch' })
  name: string;

  /**
   * Tiêu đề email
   */
  @Column({ name: 'subject', type: 'varchar', length: 255, nullable: false, comment: 'Tiêu đề email' })
  subject: string;

  /**
   * Thông tin segment (lưu trữ trực tiếp dưới dạng JSON)
   */
  @Column({ name: 'segment', type: 'jsonb', nullable: true, comment: 'Thông tin segment' })
  segment: CampaignSegment | null;

  /**
   * Tên người gửi
   */
  @Column({ name: 'sender_name', type: 'varchar', length: 100, nullable: true, comment: 'Tên người gửi' })
  senderName: string | null;

  /**
   * Email người gửi
   */
  @Column({ name: 'sender_email', type: 'varchar', length: 255, nullable: true, comment: 'Email người gửi' })
  senderEmail: string | null;

  /**
   * Email reply-to
   */
  @Column({ name: 'reply_to', type: 'varchar', length: 255, nullable: true, comment: 'Email reply-to' })
  replyTo: string | null;

  /**
   * Nội dung email (HTML và text)
   */
  @Column({ name: 'content', type: 'jsonb', nullable: true, comment: 'Nội dung email (HTML và text)' })
  content: EmailCampaignContent | null;

  /**
   * Danh sách audience (lưu trữ trực tiếp dưới dạng JSON)
   */
  @Column({ name: 'audiences', type: 'jsonb', nullable: true, comment: 'Danh sách audience với name và email' })
  audiences: CampaignAudience[] | null;

  /**
   * Danh sách email cụ thể (nếu gửi theo email list)
   */
  @Column({ name: 'email_list', type: 'jsonb', nullable: true, comment: 'Danh sách email cụ thể' })
  emailList: string[] | null;

  /**
   * Biến template (nếu sử dụng template)
   */
  @Column({ name: 'template_variables', type: 'jsonb', nullable: true, comment: 'Biến template' })
  templateVariables: Record<string, any> | null;

  /**
   * Thời gian lên lịch gửi (Unix timestamp)
   */
  @Column({ name: 'scheduled_at', type: 'bigint', nullable: true, comment: 'Thời gian lên lịch gửi' })
  scheduledAt: number | null;

  /**
   * Thời gian bắt đầu gửi (Unix timestamp)
   */
  @Column({ name: 'started_at', type: 'bigint', nullable: true, comment: 'Thời gian bắt đầu gửi' })
  startedAt: number | null;

  /**
   * Thời gian hoàn thành (Unix timestamp)
   */
  @Column({ name: 'completed_at', type: 'bigint', nullable: true, comment: 'Thời gian hoàn thành' })
  completedAt: number | null;

  /**
   * Trạng thái chiến dịch
   * DRAFT: Bản nháp
   * SCHEDULED: Đã lên lịch
   * SENDING: Đang gửi
   * COMPLETED: Đã hoàn thành
   * FAILED: Thất bại
   * CANCELLED: Đã hủy
   */
  @Column({
    name: 'status',
    type: 'varchar',
    length: 20,
    nullable: false,
    default: AdminEmailCampaignStatus.DRAFT,
    comment: 'Trạng thái chiến dịch (DRAFT, SCHEDULED, SENDING, COMPLETED, FAILED, CANCELLED)'
  })
  status: AdminEmailCampaignStatus;

  /**
   * Tổng số email dự kiến gửi
   */
  @Column({ name: 'total_recipients', type: 'int', nullable: false, default: 0, comment: 'Tổng số email dự kiến gửi' })
  totalRecipients: number;

  /**
   * Danh sách ID của job trong queue (để có thể hủy job khi cần)
   */
  @Column({ name: 'job_ids', type: 'jsonb', nullable: true, comment: 'Danh sách ID của job trong queue' })
  jobIds: string[] | null;

  /**
   * Cấu hình email server
   */
  @Column({ name: 'email_server_config', type: 'jsonb', nullable: true, comment: 'Cấu hình email server' })
  emailServerConfig: Record<string, any> | null;

  /**
   * Ghi chú hoặc mô tả thêm
   */
  @Column({ name: 'notes', type: 'text', nullable: true, comment: 'Ghi chú hoặc mô tả thêm' })
  notes: string | null;

  /**
   * ID của nhân viên tạo chiến dịch
   */
  @Column({ name: 'created_by', type: 'bigint', nullable: false, comment: 'ID của nhân viên tạo chiến dịch' })
  createdBy: number;

  /**
   * ID của nhân viên cập nhật cuối cùng
   */
  @Column({ name: 'updated_by', type: 'bigint', nullable: true, comment: 'ID của nhân viên cập nhật cuối cùng' })
  updatedBy: number | null;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'Thời gian tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Thời gian cập nhật' })
  updatedAt: number | null;
}
