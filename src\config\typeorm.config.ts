import { DataSource, DataSourceOptions } from 'typeorm';
import { env } from './env';

// <PERSON><PERSON><PERSON> hình DataSource cho TypeORM CLI
export const dataSourceOptions: DataSourceOptions = {
  type: 'postgres',
  host: env.database.DB_HOST,
  port: Number(env.database.DB_PORT),
  username: env.database.DB_USERNAME,
  password: env.database.DB_PASSWORD,
  database: env.database.DB_DATABASE,
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  migrations: [__dirname + '/../modules/database/migrations/*{.ts,.js}'],
  synchronize: false, // Không bật synchronize khi chạy migrations
  logging: true,
};

const dataSource = new DataSource(dataSourceOptions);
export default dataSource;
