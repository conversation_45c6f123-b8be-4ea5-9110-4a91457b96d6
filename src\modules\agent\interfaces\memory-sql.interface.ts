/**
 * @file Memory SQL Interfaces
 *
 * Defines interfaces specifically for SQL operations that include embedding vectors.
 * These interfaces are used for database queries and should not be exposed to the business logic layer.
 */

/**
 * Interface for inserting user memory into the database (no embeddings)
 *
 * @interface InsertUserMemorySQL
 * @description Used for SQL INSERT operations into the user_memories table.
 * Matches the actual database schema (without embedding column).
 */
export interface InsertUserMemorySQL {
  /**
   * User identifier (integer foreign key)
   */
  userId: number;

  /**
   * Structured content (JSONB)
   */
  structuredContent: Record<string, any>;

  /**
   * Additional metadata (optional)
   */
  metadata?: Record<string, any> | null;
}

/**
 * Interface for inserting agent memory with embedding into the database
 *
 * @interface InsertAgentMemorySQL
 * @description Used for SQL INSERT operations into the agent_memories table.
 * Matches the actual database schema.
 */
export interface InsertAgentMemorySQL {
  /**
   * Agent identifier (UUID foreign key)
   */
  agentId: string;

  /**
   * Structured content (JSONB)
   */
  structuredContent: Record<string, any>;

  /**
   * Additional metadata (optional)
   */
  metadata?: Record<string, any> | null;
}

// Only keeping the essential InsertUserMemorySQL and InsertAgentMemorySQL interfaces for LangChain tools
