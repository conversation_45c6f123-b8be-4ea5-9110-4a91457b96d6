import { Injectable, Logger } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import { EmailTrackingService } from './email-tracking.service';

/**
 * Service xử lý delivery status tracking cho email
 * Hỗ trợ tracking delivery, bounce, và các events khác từ SMTP
 */
@Injectable()
export class EmailDeliveryService {
  private readonly logger = new Logger(EmailDeliveryService.name);

  constructor(private readonly emailTrackingService: EmailTrackingService) {}

  /**
   * Tạo enhanced transporter với delivery tracking
   * @param config SMTP configuration
   * @param trackingId Tracking ID cho email
   * @returns Enhanced nodemailer transporter
   */
  createTrackingTransporter(
    config: any,
    trackingId: string,
  ): nodemailer.Transporter {
    const transporter = nodemailer.createTransport({
      ...config,
      // Thêm custom headers để tracking
      headers: {
        'X-Tracking-ID': trackingId,
        'X-Mailer': 'RedAI-EmailMarketing',
      },
    });

    // Listen cho delivery events
    this.setupTransporterEvents(transporter, trackingId);

    return transporter;
  }

  /**
   * Setup event listeners cho transporter
   * @param transporter Nodemailer transporter
   * @param trackingId Tracking ID
   */
  private setupTransporterEvents(
    transporter: nodemailer.Transporter,
    trackingId: string,
  ): void {
    // Event khi email được gửi thành công từ server
    transporter.on('idle', () => {
      this.logger.debug(`Transporter idle for tracking: ${trackingId}`);
    });

    // Event khi có lỗi
    transporter.on('error', (error) => {
      this.logger.error(
        `Transporter error for tracking ${trackingId}: ${error.message}`,
      );
      this.emailTrackingService.trackEmailBounced(trackingId, {
        error: error.message,
        source: 'transporter',
        timestamp: Date.now(),
      });
    });
  }

  /**
   * Gửi email với enhanced tracking
   * @param transporter Nodemailer transporter
   * @param mailOptions Mail options
   * @param trackingId Tracking ID
   * @returns Send result với tracking info
   */
  async sendEmailWithTracking(
    transporter: nodemailer.Transporter,
    mailOptions: any,
    trackingId: string,
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      // Thêm tracking headers vào email
      const enhancedOptions = {
        ...mailOptions,
        headers: {
          ...mailOptions.headers,
          'X-Tracking-ID': trackingId,
          'Message-ID': `<${trackingId}@redai.com>`,
        },
      };

      const info = await transporter.sendMail(enhancedOptions);

      this.logger.debug(
        `Email sent with tracking: ${trackingId}, messageId: ${info.messageId}`,
      );

      // Simulate delivery tracking (trong thực tế sẽ nhận từ webhook)
      this.scheduleDeliveryCheck(trackingId, info.messageId);

      return {
        success: true,
        messageId: info.messageId,
      };
    } catch (error) {
      this.logger.error(
        `Error sending email with tracking ${trackingId}: ${error.message}`,
      );

      await this.emailTrackingService.trackEmailBounced(trackingId, {
        error: error.message,
        source: 'send',
        timestamp: Date.now(),
      });

      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Schedule delivery check (simulate delivery tracking)
   * Trong thực tế, delivery status sẽ được nhận từ webhook
   * @param trackingId Tracking ID
   * @param messageId Message ID từ SMTP server
   */
  private scheduleDeliveryCheck(trackingId: string, messageId: string): void {
    // Simulate delivery sau 5-30 giây (random)
    const deliveryDelay = Math.random() * 25000 + 5000; // 5-30 seconds

    setTimeout(async () => {
      try {
        // Simulate 90% delivery success rate
        const isDelivered = Math.random() > 0.1;

        if (isDelivered) {
          await this.emailTrackingService.trackEmailDelivered(trackingId, {
            messageId,
            deliveryTime: Date.now(),
            source: 'simulation',
            status: 'delivered',
          });
          this.logger.debug(`Simulated delivery for tracking: ${trackingId}`);
        } else {
          await this.emailTrackingService.trackEmailBounced(trackingId, {
            messageId,
            bounceTime: Date.now(),
            source: 'simulation',
            reason: 'Simulated bounce',
            bounceType: 'soft',
          });
          this.logger.debug(`Simulated bounce for tracking: ${trackingId}`);
        }
      } catch (error) {
        this.logger.error(
          `Error in delivery simulation for ${trackingId}: ${error.message}`,
        );
      }
    }, deliveryDelay);
  }

  /**
   * Parse bounce message để extract tracking info
   * @param bounceMessage Raw bounce message
   * @returns Parsed bounce info
   */
  parseBounceMessage(bounceMessage: string): {
    trackingId?: string;
    bounceType?: 'hard' | 'soft';
    reason?: string;
    originalRecipient?: string;
  } {
    try {
      const result: any = {};

      // Extract tracking ID từ headers
      const trackingMatch = bounceMessage.match(/X-Tracking-ID:\s*([^\r\n]+)/i);
      if (trackingMatch) {
        result.trackingId = trackingMatch[1].trim();
      }

      // Extract original recipient
      const recipientMatch = bounceMessage.match(
        /Original-Recipient:\s*([^\r\n]+)/i,
      );
      if (recipientMatch) {
        result.originalRecipient = recipientMatch[1].trim();
      }

      // Determine bounce type based on SMTP codes
      if (
        bounceMessage.includes('550') ||
        bounceMessage.includes('551') ||
        bounceMessage.includes('553')
      ) {
        result.bounceType = 'hard';
      } else if (
        bounceMessage.includes('421') ||
        bounceMessage.includes('450') ||
        bounceMessage.includes('451')
      ) {
        result.bounceType = 'soft';
      }

      // Extract reason
      const reasonMatch = bounceMessage.match(/Action:\s*([^\r\n]+)/i);
      if (reasonMatch) {
        result.reason = reasonMatch[1].trim();
      }

      return result;
    } catch (error) {
      this.logger.error(`Error parsing bounce message: ${error.message}`);
      return {};
    }
  }

  /**
   * Process delivery status notification (DSN)
   * @param dsnMessage DSN message content
   */
  async processDSN(dsnMessage: string): Promise<void> {
    try {
      const bounceInfo = this.parseBounceMessage(dsnMessage);

      if (!bounceInfo.trackingId) {
        this.logger.warn('No tracking ID found in DSN message');
        return;
      }

      const metadata = {
        source: 'dsn',
        bounceType: bounceInfo.bounceType,
        reason: bounceInfo.reason,
        originalRecipient: bounceInfo.originalRecipient,
        timestamp: Date.now(),
        rawMessage: dsnMessage,
      };

      if (
        bounceInfo.bounceType === 'hard' ||
        bounceInfo.bounceType === 'soft'
      ) {
        await this.emailTrackingService.trackEmailBounced(
          bounceInfo.trackingId,
          metadata,
        );
      } else {
        // Assume delivered if not a bounce
        await this.emailTrackingService.trackEmailDelivered(
          bounceInfo.trackingId,
          metadata,
        );
      }

      this.logger.debug(`Processed DSN for tracking: ${bounceInfo.trackingId}`);
    } catch (error) {
      this.logger.error(`Error processing DSN: ${error.message}`, error.stack);
    }
  }

  /**
   * Setup SMTP event monitoring (cho advanced SMTP servers)
   * @param smtpConfig SMTP configuration
   * @returns Event monitoring setup result
   */
  async setupSMTPMonitoring(smtpConfig: any): Promise<boolean> {
    try {
      // Placeholder for advanced SMTP monitoring setup
      // Có thể integrate với các SMTP servers hỗ trợ real-time events

      this.logger.log('SMTP monitoring setup completed');
      return true;
    } catch (error) {
      this.logger.error(`Error setting up SMTP monitoring: ${error.message}`);
      return false;
    }
  }

  /**
   * Validate email deliverability trước khi gửi
   * @param email Email address to validate
   * @returns Validation result
   */
  async validateEmailDeliverability(email: string): Promise<{
    isValid: boolean;
    reason?: string;
    riskLevel: 'low' | 'medium' | 'high';
  }> {
    try {
      // Basic email format validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return {
          isValid: false,
          reason: 'Invalid email format',
          riskLevel: 'high',
        };
      }

      // Check for common disposable email domains
      const disposableDomains = [
        '10minutemail.com',
        'tempmail.org',
        'guerrillamail.com',
      ];
      const domain = email.split('@')[1].toLowerCase();

      if (disposableDomains.includes(domain)) {
        return {
          isValid: false,
          reason: 'Disposable email domain',
          riskLevel: 'high',
        };
      }

      // TODO: Implement more sophisticated validation
      // - DNS MX record check
      // - SMTP verification
      // - Reputation check

      return {
        isValid: true,
        riskLevel: 'low',
      };
    } catch (error) {
      this.logger.error(
        `Error validating email deliverability: ${error.message}`,
      );
      return {
        isValid: false,
        reason: 'Validation error',
        riskLevel: 'medium',
      };
    }
  }
}
