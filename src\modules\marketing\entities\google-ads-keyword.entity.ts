import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng google_ads_keywords trong cơ sở dữ liệu
 * Lưu trữ thông tin từ khóa Google Ads
 */
@Entity('google_ads_keywords')
export class GoogleAdsKeyword {
  /**
   * ID của từ khóa trong hệ thống
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', nullable: false, comment: 'ID của người dùng' })
  userId: number;

  /**
   * ID của nhóm quảng cáo trong hệ thống
   */
  @Column({
    name: 'ad_group_id',
    nullable: false,
    comment: 'ID của nhóm quảng cáo trong hệ thống',
  })
  adGroupId: number;

  /**
   * ID của từ khóa trên Google Ads
   */
  @Column({
    name: 'keyword_id',
    nullable: false,
    comment: 'ID của từ khóa trên Google Ads',
  })
  keywordId: string;

  /**
   * Văn bản từ khóa
   */
  @Column({
    name: 'text',
    length: 255,
    nullable: false,
    comment: 'Văn bản từ khóa',
  })
  text: string;

  /**
   * Loại đối sánh (EXACT, PHRASE, BROAD)
   */
  @Column({
    name: 'match_type',
    length: 20,
    nullable: false,
    comment: 'Loại đối sánh (EXACT, PHRASE, BROAD)',
  })
  matchType: string;

  /**
   * Trạng thái từ khóa
   */
  @Column({
    name: 'status',
    length: 20,
    nullable: false,
    comment: 'Trạng thái từ khóa',
  })
  status: string;

  /**
   * CPC tối đa (micro amount)
   */
  @Column({
    name: 'cpc_bid_micros',
    type: 'bigint',
    nullable: true,
    comment: 'CPC tối đa (micro amount)',
  })
  cpcBidMicros: number;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: false,
    comment: 'Thời gian tạo',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian cập nhật',
  })
  updatedAt: number;
}
