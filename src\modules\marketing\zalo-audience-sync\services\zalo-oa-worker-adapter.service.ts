import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Integration } from '../../../../shared/entities/integration.entity';
import { IntegrationProvider } from '../../../../shared/entities/integration-provider.entity';
import { KeyPairEncryptionService, ZaloOAPayload } from '../../../../shared/services/encryption/key-pair-encryption.service';

/**
 * Interface cho Zalo OA metadata trong Integration
 */
interface ZaloOAMetadata {
  oaId: string;
  name: string;
  description?: string;
  avatarUrl?: string;
  expiresAt: number;
  status: string;
  createdAt: number;
  updatedAt: number;
}

/**
 * Adapter service để tương thích với ZaloOfficialAccount entity cũ trong worker
 * Truy vấn dữ liệu từ Integration table thay vì zalo_official_account table
 */
@Injectable()
export class ZaloOAWorkerAdapterService {
  private readonly logger = new Logger(ZaloOAWorkerAdapterService.name);

  constructor(
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
    @InjectRepository(IntegrationProvider)
    private readonly integrationProviderRepository: Repository<IntegrationProvider>,
    private readonly keyPairEncryptionService: KeyPairEncryptionService,
  ) {}

  /**
   * Tìm Zalo OA theo oaId và userId với token đã giải mã (tương thích với findByOaIdAndUserId)
   */
  async findByOaIdAndUserId(oaId: string, userId: number): Promise<any | null> {
    try {
      this.logger.debug(`Finding Zalo OA by oaId: ${oaId}, userId: ${userId}`);

      // Tìm provider ZALO_OA
      const provider = await this.integrationProviderRepository.findOne({
        where: { type: 'ZALO_OA' as any }
      });

      if (!provider) {
        this.logger.error('ZALO_OA provider not found');
        return null;
      }

      this.logger.debug(`Found ZALO_OA provider with ID: ${provider.id}`);

      // Tìm integration theo oaId trong metadata và userId
      const integration = await this.integrationRepository
        .createQueryBuilder('integration')
        .where('integration.typeId = :typeId', { typeId: provider.id })
        .andWhere('integration.userId = :userId', { userId })
        .andWhere("integration.metadata->>'oaId' = :oaId", { oaId })
        .getOne();

      if (!integration) {
        this.logger.warn(`No integration found for oaId: ${oaId}, userId: ${userId}`);
        return null;
      }

      this.logger.debug(`Found integration with ID: ${integration.id}`);
      return this.mapIntegrationToZaloOAWithTokens(integration);
    } catch (error) {
      this.logger.error(`Error finding Zalo OA by oaId ${oaId} and userId ${userId}:`, error);
      return null;
    }
  }

  /**
   * Tìm Zalo OA theo userId
   */
  async findByUserId(userId: number): Promise<any[]> {
    try {
      // Tìm provider ZALO_OA
      const provider = await this.integrationProviderRepository.findOne({
        where: { type: 'ZALO_OA' as any }
      });

      if (!provider) {
        this.logger.error('ZALO_OA provider not found');
        return [];
      }

      // Tìm tất cả integration của user với type ZALO_OA
      const integrations = await this.integrationRepository.find({
        where: {
          typeId: provider.id,
          userId: userId
        }
      });

      return integrations.map(integration => this.mapIntegrationToZaloOA(integration));
    } catch (error) {
      this.logger.error(`Error finding Zalo OA by userId ${userId}:`, error);
      return [];
    }
  }

  /**
   * Map Integration entity sang format ZaloOfficialAccount cũ với token đã giải mã
   */
  private async mapIntegrationToZaloOAWithTokens(integration: Integration): Promise<any> {
    const metadata = integration.metadata as any as ZaloOAMetadata;
    this.logger.debug(`Mapping integration ${integration.id} to ZaloOA with tokens`);

    try {
      // Giải mã tokens
      this.logger.debug(`Attempting to decrypt tokens for integration ${integration.id}`);
      const tokens = await this.getDecryptedTokens(integration.id);
      this.logger.debug(`Successfully decrypted tokens for integration ${integration.id}`);

      const result = {
        id: integration.id,
        userId: integration.userId,
        employeeId: integration.employeeId,
        oaId: metadata.oaId,
        name: metadata.name,
        description: metadata.description,
        avatarUrl: metadata.avatarUrl,
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresAt: metadata.expiresAt,
        status: metadata.status,
        createdAt: new Date(metadata.createdAt),
        updatedAt: new Date(metadata.updatedAt),
      };

      this.logger.debug(`Mapped result:`, {
        id: result.id,
        oaId: result.oaId,
        status: result.status,
        hasAccessToken: !!result.accessToken,
        accessTokenLength: result.accessToken?.length || 0
      });

      return result;
    } catch (error) {
      this.logger.error(`Error decrypting tokens for integration ${integration.id}: ${error.message}`);

      // Fallback to encrypted format
      const fallbackResult = this.mapIntegrationToZaloOA(integration);
      this.logger.warn(`Using fallback result for integration ${integration.id}:`, {
        id: fallbackResult.id,
        status: fallbackResult.status,
        accessToken: fallbackResult.accessToken
      });
      return fallbackResult;
    }
  }

  /**
   * Lấy token đã giải mã
   */
  private async getDecryptedTokens(id: string): Promise<ZaloOAPayload> {
    this.logger.debug(`Getting decrypted tokens for integration ${id}`);

    const integration = await this.integrationRepository.findOne({
      where: { id },
    });

    if (!integration) {
      this.logger.error(`Integration ${id} not found`);
      throw new Error('Integration không tồn tại');
    }

    if (!integration.encryptedConfig) {
      this.logger.error(`Integration ${id} missing encryptedConfig`);
      throw new Error('Integration thiếu encryptedConfig');
    }

    if (!integration.secretKey) {
      this.logger.error(`Integration ${id} missing secretKey`);
      throw new Error('Integration thiếu secretKey');
    }

    this.logger.debug(`Integration ${id} has required fields for decryption`);

    try {
      const result = this.keyPairEncryptionService.decryptObject<ZaloOAPayload>(
        integration.encryptedConfig,
        integration.secretKey
      );
      this.logger.debug(`Successfully decrypted tokens for integration ${id}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to decrypt tokens for integration ${id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Map Integration entity sang format ZaloOfficialAccount cũ
   */
  private mapIntegrationToZaloOA(integration: Integration): any {
    const metadata = integration.metadata as any as ZaloOAMetadata;

    return {
      id: integration.id,
      userId: integration.userId,
      employeeId: integration.employeeId,
      oaId: metadata.oaId,
      name: metadata.name,
      description: metadata.description,
      avatarUrl: metadata.avatarUrl,
      // accessToken và refreshToken không trả về trực tiếp vì đã mã hóa
      accessToken: '[ENCRYPTED]',
      refreshToken: '[ENCRYPTED]',
      expiresAt: metadata.expiresAt,
      status: metadata.status,
      createdAt: new Date(metadata.createdAt),
      updatedAt: new Date(metadata.updatedAt),
    };
  }
}
