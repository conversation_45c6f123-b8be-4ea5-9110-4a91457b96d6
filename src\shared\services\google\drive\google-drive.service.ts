import { AppException } from '@common/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { drive_v3, google, Auth } from 'googleapis';
import { GOOGLE_ERROR_CODES, handleGoogleApiError } from '../exceptions/google.exception';
import {
  CopyFileRequest,
  CreateFolderRequest,
  DriveFileInfo,
  GoogleDriveConfig,
  GoogleDriveCredentials,
  MoveFileRequest,
  SearchFilesRequest,
  SearchFilesResult,
  ShareFileRequest,
  UpdateFileMetadataRequest,
  UploadFileRequest,
  UploadFileResult
} from '../interfaces/google-drive.interface';

/**
 * Service để tương tác với Google Drive API
 */
@Injectable()
export class GoogleDriveService {
  private readonly logger = new Logger(GoogleDriveService.name);
  private oauth2Client: Auth.OAuth2Client;
  private driveConfig: GoogleDriveConfig;

  constructor(private readonly configService: ConfigService) {
    this.initializeConfig();
    this.initializeOAuth2Client();
  }

  /**
   * Khởi tạo cấu hình từ environment variables
   */
  private initializeConfig(): void {
    this.driveConfig = {
      clientId: this.configService.get<string>('GOOGLE_CLIENT_ID') || '',
      clientSecret: this.configService.get<string>('GOOGLE_CLIENT_SECRET') || '',
      redirectUri: this.configService.get<string>('GOOGLE_REDIRECT_URI') || '',
      defaultFolderId: this.configService.get<string>('GOOGLE_DRIVE_FOLDER_ID'),
    };

    if (!this.driveConfig.clientId || !this.driveConfig.clientSecret) {
      this.logger.warn('Google Drive configuration is incomplete');
    }
  }

  /**
   * Khởi tạo OAuth2 client
   */
  private initializeOAuth2Client(): void {
    this.oauth2Client = new google.auth.OAuth2(
      this.driveConfig.clientId,
      this.driveConfig.clientSecret,
      this.driveConfig.redirectUri,
    );
  }


  /**
   * Thiết lập credentials cho OAuth2 client
   * @param credentials Thông tin xác thực
   */
  private setCredentials(credentials: GoogleDriveCredentials): void {
    this.oauth2Client.setCredentials({
      access_token: credentials.accessToken,
      refresh_token: credentials.refreshToken,
      expiry_date: credentials.expiresAt,
    });
  }

  /**
   * Lấy instance của Drive API
   * @param accessToken Access token
   * @returns Drive API instance
   */
  private getDriveInstance(accessToken: string): drive_v3.Drive {
    // Use access token directly to avoid OAuth2Client version conflicts
    return google.drive({ version: 'v3', auth: accessToken });
  }

  /**
   * Upload file lên Google Drive
   * @param accessToken Access token
   * @param request Thông tin upload file
   * @returns Kết quả upload
   */
  async uploadFile(
    accessToken: string,
    request: UploadFileRequest,
  ): Promise<UploadFileResult> {
    try {
      const drive = this.getDriveInstance(accessToken);

      const fileMetadata: drive_v3.Schema$File = {
        name: request.name,
        parents: request.parentId ? [request.parentId] :
          this.driveConfig.defaultFolderId ? [this.driveConfig.defaultFolderId] : undefined,
        description: request.description,
      };

      const media = {
        mimeType: request.mimeType,
        body: request.content,
      };

      const response = await drive.files.create({
        requestBody: fileMetadata,
        media: media,
        fields: 'id,name,webViewLink,webContentLink,size',
      });

      const file = response.data;

      this.logger.log(`File uploaded: ${file.name} (ID: ${file.id})`);

      return {
        fileId: file.id || '',
        name: file.name || '',
        webViewLink: file.webViewLink || '',
        webContentLink: file.webContentLink ?? undefined,
        size: file.size || '0',
      };
    } catch (error) {
      this.logger.error(`Error uploading file: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      const errorCode = handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_DRIVE_UPLOAD_FAILED);
      throw new AppException(
        errorCode,
        `Không thể upload file: ${error.message}`,
      );
    }
  }

  /**
   * Download file từ Google Drive
   * @param accessToken Access token
   * @param fileId ID của file
   * @returns Buffer chứa nội dung file
   */
  async downloadFile(accessToken: string, fileId: string): Promise<Buffer> {
    try {
      const drive = this.getDriveInstance(accessToken);

      const response = await drive.files.get({
        fileId,
        alt: 'media',
      }, { responseType: 'arraybuffer' });

      this.logger.log(`File downloaded: ${fileId}`);

      return Buffer.from(response.data as ArrayBuffer);
    } catch (error) {
      this.logger.error(`Error downloading file: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      const errorCode = handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_DRIVE_DOWNLOAD_FAILED);
      throw new AppException(
        errorCode,
        `Không thể download file: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin file
   * @param accessToken Access token
   * @param fileId ID của file
   * @returns Thông tin file
   */
  async getFileInfo(accessToken: string, fileId: string): Promise<DriveFileInfo> {
    try {
      const drive = this.getDriveInstance(accessToken);

      const response = await drive.files.get({
        fileId,
        fields: 'id,name,mimeType,size,createdTime,modifiedTime,webViewLink,webContentLink,parents,owners,trashed',
      });

      return this.mapToDriveFileInfo(response.data);
    } catch (error) {
      this.logger.error(`Error getting file info: ${error.message}`, error.stack);
      throw new Error(`Không thể lấy thông tin file: ${error.message}`);
    }
  }

  /**
   * Tạo folder mới
   * @param accessToken Access token
   * @param request Thông tin tạo folder
   * @returns Thông tin folder đã tạo
   */
  async createFolder(
    accessToken: string,
    request: CreateFolderRequest,
  ): Promise<DriveFileInfo> {
    try {
      const drive = this.getDriveInstance(accessToken);

      const fileMetadata: drive_v3.Schema$File = {
        name: request.name,
        mimeType: 'application/vnd.google-apps.folder',
        parents: request.parentId ? [request.parentId] :
          this.driveConfig.defaultFolderId ? [this.driveConfig.defaultFolderId] : undefined,
        description: request.description,
      };

      const response = await drive.files.create({
        requestBody: fileMetadata,
        fields: 'id,name,mimeType,createdTime,modifiedTime,webViewLink,parents',
      });

      this.logger.log(`Folder created: ${response.data.name} (ID: ${response.data.id})`);

      return this.mapToDriveFileInfo(response.data);
    } catch (error) {
      this.logger.error(`Error creating folder: ${error.message}`, error.stack);
      throw new Error(`Không thể tạo folder: ${error.message}`);
    }
  }

  /**
   * Tìm kiếm files
   * @param accessToken Access token
   * @param request Thông tin tìm kiếm
   * @returns Kết quả tìm kiếm
   */
  async searchFiles(
    accessToken: string,
    request: SearchFilesRequest,
  ): Promise<SearchFilesResult> {
    try {
      const drive = this.getDriveInstance(accessToken);

      let query = '';
      const queryParts: string[] = [];

      if (request.query) {
        queryParts.push(`name contains '${request.query}'`);
      }

      if (request.folderId) {
        queryParts.push(`'${request.folderId}' in parents`);
      }

      if (request.mimeType) {
        queryParts.push(`mimeType='${request.mimeType}'`);
      }

      if (!request.includeTrashed) {
        queryParts.push('trashed=false');
      }

      query = queryParts.join(' and ');

      const response = await drive.files.list({
        q: query || undefined,
        pageSize: request.pageSize || 10,
        pageToken: request.pageToken,
        orderBy: request.orderBy,
        fields: 'nextPageToken,incompleteSearch,files(id,name,mimeType,size,createdTime,modifiedTime,webViewLink,webContentLink,parents,owners,trashed)',
      });

      const files = response.data.files?.map(file => this.mapToDriveFileInfo(file)) || [];

      this.logger.log(`Found ${files.length} files`);

      return {
        files,
        nextPageToken: response.data.nextPageToken ?? undefined,
        incompleteSearch: response.data.incompleteSearch || false,
      };
    } catch (error) {
      this.logger.error(`Error searching files: ${error.message}`, error.stack);
      throw new Error(`Không thể tìm kiếm files: ${error.message}`);
    }
  }

  /**
   * Lấy danh sách files trong folder
   * @param accessToken Access token
   * @param request Thông tin list files
   * @returns Kết quả list files
   */
  async listFiles(
    accessToken: string,
    request: any
  ): Promise<SearchFilesResult> {
    try {
      const drive = this.getDriveInstance(accessToken);

      // Build query for listing files
      let query = '';
      const queryParts: string[] = [];

      if (request.parentId) {
        queryParts.push(`'${request.parentId}' in parents`);
      }

      if (request.fileType === 'file') {
        queryParts.push(`mimeType != 'application/vnd.google-apps.folder'`);
      } else if (request.fileType === 'folder') {
        queryParts.push(`mimeType = 'application/vnd.google-apps.folder'`);
      }

      if (!request.includeTrashed) {
        queryParts.push('trashed = false');
      }

      query = queryParts.join(' and ');

      // Build orderBy
      let orderBy = 'name';
      if (request.orderBy) {
        orderBy = request.orderBy;
        if (request.sortDirection === 'desc') {
          orderBy += ' desc';
        }
      }

      const response = await drive.files.list({
        q: query || undefined,
        orderBy,
        pageSize: request.limit || 100,
        pageToken: request.pageToken,
        fields: 'nextPageToken, files(id, name, mimeType, size, webViewLink, webContentLink, createdTime, modifiedTime, owners, parents, thumbnailLink, shared, starred, trashed)',
      });

      const files = (response.data.files || []).map(file => ({
        id: file.id || '',
        name: file.name || '',
        mimeType: file.mimeType || '',
        size: file.size || '0',
        webViewLink: file.webViewLink || '',
        webContentLink: file.webContentLink || undefined,
        createdTime: file.createdTime || '',
        modifiedTime: file.modifiedTime || '',
        owners: file.owners?.map(owner => ({
          displayName: owner.displayName || owner.emailAddress || '',
          emailAddress: owner.emailAddress || '',
          photoLink: owner.photoLink || undefined,
        })) || [],
        parents: file.parents || [],
        thumbnailLink: file.thumbnailLink,
        shared: file.shared || false,
        starred: file.starred || false,
        trashed: file.trashed || false,
        isFolder: (file.mimeType || '') === 'application/vnd.google-apps.folder',
      }));

      return {
        files,
        nextPageToken: response.data.nextPageToken || undefined,
        incompleteSearch: false, // Add missing property
      };

    } catch (error) {
      this.logger.error(`Failed to list files: ${error.message}`);
      throw handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_API_UNKNOWN_ERROR);
    }
  }

  /**
   * Chia sẻ file (legacy method)
   * @param accessToken Access token
   * @param request Thông tin chia sẻ
   * @returns True nếu thành công
   */
  async shareFile(accessToken: string, request: ShareFileRequest): Promise<boolean> {
    try {
      const drive = this.getDriveInstance(accessToken);

      for (const permission of request.permissions) {
        await drive.permissions.create({
          fileId: request.fileId,
          requestBody: permission,
          sendNotificationEmail: request.sendNotificationEmail,
          emailMessage: request.emailMessage,
        });
      }

      this.logger.log(`File shared: ${request.fileId} with ${request.permissions.length} permissions`);

      return true;
    } catch (error) {
      this.logger.error(`Error sharing file: ${error.message}`, error.stack);
      throw new Error(`Không thể chia sẻ file: ${error.message}`);
    }
  }

  /**
   * Share file với format mới cho executor
   * @param accessToken Access token
   * @param request Share request
   * @returns Share result với permissions
   */
  async shareFileAdvanced(accessToken: string, request: any): Promise<any> {
    try {
      const drive = this.getDriveInstance(accessToken);
      const permissions: any[] = [];

      if (request.shareType === 'anyone') {
        // Public sharing
        const response = await drive.permissions.create({
          fileId: request.fileId,
          requestBody: {
            type: 'anyone',
            role: request.role,
          },
          fields: 'id, type, role',
        });
        permissions.push(response.data);
      } else if (request.shareType === 'domain') {
        // Domain sharing
        const response = await drive.permissions.create({
          fileId: request.fileId,
          requestBody: {
            type: 'domain',
            role: request.role,
            domain: request.domain,
          },
          sendNotificationEmail: request.sendNotificationEmail,
          emailMessage: request.message,
          fields: 'id, type, role, domain',
        });
        permissions.push(response.data);
      } else if (request.shareType === 'specific' && request.emails) {
        // Specific people sharing
        for (const email of request.emails) {
          const response = await drive.permissions.create({
            fileId: request.fileId,
            requestBody: {
              type: 'user',
              role: request.role,
              emailAddress: email,
            },
            sendNotificationEmail: request.sendNotificationEmail,
            emailMessage: request.message,
            fields: 'id, type, role, emailAddress, displayName',
          });
          permissions.push(response.data);
        }
      }

      // Get share link if public
      let shareLink: string | undefined;
      if (request.shareType === 'anyone') {
        const fileInfo = await this.getFileInfo(accessToken, request.fileId);
        shareLink = fileInfo.webViewLink;
      }

      return {
        permissions,
        shareLink,
      };

    } catch (error) {
      this.logger.error(`Failed to share file: ${error.message}`);
      throw handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_API_UNKNOWN_ERROR);
    }
  }

  /**
   * Copy file
   * @param accessToken Access token
   * @param request Thông tin copy
   * @returns Thông tin file đã copy
   */
  async copyFile(
    accessToken: string,
    request: CopyFileRequest,
  ): Promise<DriveFileInfo> {
    try {
      const drive = this.getDriveInstance(accessToken);

      const fileMetadata: drive_v3.Schema$File = {
        name: request.name,
        parents: request.parentId ? [request.parentId] : undefined,
        description: request.description,
      };

      const response = await drive.files.copy({
        fileId: request.sourceFileId,
        requestBody: fileMetadata,
        fields: 'id,name,mimeType,size,createdTime,modifiedTime,webViewLink,webContentLink,parents',
      });

      this.logger.log(`File copied: ${request.sourceFileId} -> ${response.data.id}`);

      return this.mapToDriveFileInfo(response.data);
    } catch (error) {
      this.logger.error(`Error copying file: ${error.message}`, error.stack);
      throw new Error(`Không thể copy file: ${error.message}`);
    }
  }

  /**
   * Di chuyển file
   * @param accessToken Access token
   * @param request Thông tin di chuyển
   * @returns Thông tin file đã di chuyển
   */
  async moveFile(
    accessToken: string,
    request: MoveFileRequest,
  ): Promise<DriveFileInfo> {
    try {
      const drive = this.getDriveInstance(accessToken);

      // Lấy thông tin file hiện tại để biết parent cũ
      const currentFile = await drive.files.get({
        fileId: request.fileId,
        fields: 'parents',
      });

      const previousParents = currentFile.data.parents?.join(',') || '';

      const response = await drive.files.update({
        fileId: request.fileId,
        addParents: request.newParentId,
        removeParents: request.oldParentId || previousParents,
        fields: 'id,name,mimeType,size,createdTime,modifiedTime,webViewLink,webContentLink,parents',
      });

      this.logger.log(`File moved: ${request.fileId} to ${request.newParentId}`);

      return this.mapToDriveFileInfo(response.data);
    } catch (error) {
      this.logger.error(`Error moving file: ${error.message}`, error.stack);
      throw new Error(`Không thể di chuyển file: ${error.message}`);
    }
  }

  /**
   * Move file to trash
   * @param accessToken Access token
   * @param fileId ID của file
   * @returns True nếu thành công
   */
  async trashFile(accessToken: string, fileId: string): Promise<boolean> {
    try {
      const drive = this.getDriveInstance(accessToken);

      await drive.files.update({
        fileId,
        requestBody: {
          trashed: true,
        },
      });

      this.logger.log(`File ${fileId} moved to trash successfully`);
      return true;

    } catch (error) {
      this.logger.error(`Failed to trash file: ${error.message}`);
      throw handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_API_UNKNOWN_ERROR);
    }
  }

  /**
   * Xóa file
   * @param accessToken Access token
   * @param fileId ID của file
   * @returns True nếu thành công
   */
  async deleteFile(accessToken: string, fileId: string): Promise<boolean> {
    try {
      const drive = this.getDriveInstance(accessToken);

      await drive.files.delete({
        fileId,
      });

      this.logger.log(`File deleted: ${fileId}`);

      return true;
    } catch (error) {
      this.logger.error(`Error deleting file: ${error.message}`, error.stack);
      throw new Error(`Không thể xóa file: ${error.message}`);
    }
  }

  /**
   * Cập nhật metadata của file
   * @param accessToken Access token
   * @param request Thông tin cập nhật
   * @returns Thông tin file đã cập nhật
   */
  async updateFileMetadata(
    accessToken: string,
    request: UpdateFileMetadataRequest,
  ): Promise<DriveFileInfo> {
    try {
      const drive = this.getDriveInstance(accessToken);

      const fileMetadata: drive_v3.Schema$File = {
        name: request.name,
        description: request.description,
        starred: request.starred,
        trashed: request.trashed,
      };

      const response = await drive.files.update({
        fileId: request.fileId,
        requestBody: fileMetadata,
        fields: 'id,name,mimeType,size,createdTime,modifiedTime,webViewLink,webContentLink,parents,trashed,starred',
      });

      this.logger.log(`File metadata updated: ${request.fileId}`);

      return this.mapToDriveFileInfo(response.data);
    } catch (error) {
      this.logger.error(`Error updating file metadata: ${error.message}`, error.stack);
      throw new Error(`Không thể cập nhật metadata file: ${error.message}`);
    }
  }

  /**
   * Map từ Google Drive API response sang DriveFileInfo
   * @param file File từ API
   * @returns DriveFileInfo
   */
  private mapToDriveFileInfo(file: drive_v3.Schema$File): DriveFileInfo {
    return {
      id: file.id || '',
      name: file.name || '',
      mimeType: file.mimeType || '',
      size: file.size || undefined,
      createdTime: file.createdTime || '',
      modifiedTime: file.modifiedTime || '',
      webViewLink: file.webViewLink || undefined,
      webContentLink: file.webContentLink || undefined,
      parents: file.parents || undefined,
      owners: file.owners?.map(owner => ({
        displayName: owner.displayName || '',
        emailAddress: owner.emailAddress || '',
        photoLink: owner.photoLink || undefined,
        me: owner.me || undefined,
      })),
      isFolder: file.mimeType === 'application/vnd.google-apps.folder',
      trashed: file.trashed || false,
    };
  }

  /**
   * List permissions của file
   * @param accessToken Access token
   * @param fileId ID của file
   * @returns Danh sách permissions
   */
  async listPermissions(accessToken: string, fileId: string): Promise<any[]> {
    try {
      const drive = this.getDriveInstance(accessToken);

      const response = await drive.permissions.list({
        fileId,
        fields: 'permissions(id, type, role, emailAddress, domain, displayName, photoLink, deleted)',
      });

      return response.data.permissions || [];

    } catch (error) {
      this.logger.error(`Failed to list permissions: ${error.message}`);
      throw handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_API_UNKNOWN_ERROR);
    }
  }

  /**
   * Add permission cho file
   * @param accessToken Access token
   * @param request Permission request
   * @returns Permission đã tạo
   */
  async addPermission(accessToken: string, request: any): Promise<any> {
    try {
      const drive = this.getDriveInstance(accessToken);

      const response = await drive.permissions.create({
        fileId: request.fileId,
        requestBody: {
          type: request.type,
          role: request.role,
          emailAddress: request.email,
          domain: request.domain,
        },
        sendNotificationEmail: request.sendNotificationEmail,
        emailMessage: request.message,
        fields: 'id, type, role, emailAddress, displayName',
      });

      return response.data;

    } catch (error) {
      this.logger.error(`Failed to add permission: ${error.message}`);
      throw handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_API_UNKNOWN_ERROR);
    }
  }

  /**
   * Update permission
   * @param accessToken Access token
   * @param request Update request
   * @returns Permission đã update
   */
  async updatePermission(accessToken: string, request: any): Promise<any> {
    try {
      const drive = this.getDriveInstance(accessToken);

      const response = await drive.permissions.update({
        fileId: request.fileId,
        permissionId: request.permissionId,
        requestBody: {
          role: request.role,
        },
        transferOwnership: request.role === 'owner',
        fields: 'id, type, role, emailAddress, displayName',
      });

      return response.data;

    } catch (error) {
      this.logger.error(`Failed to update permission: ${error.message}`);
      throw handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_API_UNKNOWN_ERROR);
    }
  }

  /**
   * Remove permission
   * @param accessToken Access token
   * @param fileId ID của file
   * @param permissionId ID của permission
   * @returns True nếu thành công
   */
  async removePermission(accessToken: string, fileId: string, permissionId: string): Promise<boolean> {
    try {
      const drive = this.getDriveInstance(accessToken);

      await drive.permissions.delete({
        fileId,
        permissionId,
      });

      return true;

    } catch (error) {
      this.logger.error(`Failed to remove permission: ${error.message}`);
      throw handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_API_UNKNOWN_ERROR);
    }
  }

  /**
   * Kiểm tra kết nối với Google Drive API
   * @param accessToken Access token
   * @returns True nếu kết nối thành công
   */
  async testConnection(accessToken: string): Promise<boolean> {
    try {
      const drive = this.getDriveInstance(accessToken);

      // Thử lấy thông tin về Drive của user
      const response = await drive.about.get({
        fields: 'user',
      });

      if (response.data.user) {
        this.logger.log('Google Drive connection test successful');
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Google Drive connection test failed: ${error.message}`);
      return false;
    }
  }
}
