import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { CampaignAudience, CampaignSegment } from '../types/campaign.types';

/**
 * Entity đại diện cho bảng user_campaigns trong cơ sở dữ liệu
 * Bảng chiến dịch của người dùng
 */
@Entity('user_campaigns')
export class UserCampaign {
  /**
   * ID của campaign
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', nullable: true, comment: 'Mã người dùng' })
  userId: number;

  // Không sử dụng quan hệ với bảng User, chỉ lưu ID

  /**
   * Tiêu đề chiến dịch
   */
  @Column({ name: 'title', length: 255, nullable: true, comment: 'Tiêu đề' })
  title: string;

  /**
   * <PERSON><PERSON> tả chiến dịch
   */
  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
    comment: '<PERSON><PERSON> tả',
  })
  description: string;

  /**
   * <PERSON><PERSON><PERSON> tảng gửi (email, sms, ...)
   */
  @Column({
    name: 'platform',
    length: 255,
    nullable: true,
    comment: 'Nền tảng',
  })
  platform: string;

  /**
   * Nội dung chiến dịch
   */
  @Column({
    name: 'content',
    type: 'text',
    nullable: true,
    comment: 'Nội dung',
  })
  content: string;

  /**
   * Thông tin máy chủ gửi
   */
  @Column({
    name: 'server',
    type: 'jsonb',
    nullable: true,
    comment: 'Thông tin máy chủ gửi',
  })
  server: any;

  /**
   * Thời gian dự kiến gửi chiến dịch (Unix timestamp)
   */
  @Column({
    name: 'scheduled_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian dự kiến gửi chiến dịch',
  })
  scheduledAt: number;

  /**
   * Tiêu đề email (chỉ áp dụng cho chiến dịch email)
   */
  @Column({
    name: 'subject',
    length: 255,
    nullable: true,
    comment: 'Nội dung tiêu đề với chiến dịch là email',
  })
  subject: string;

  /**
   * Trạng thái chiến dịch
   */
  @Column({ name: 'status', length: 20, nullable: true, comment: 'Trạng thái' })
  status: string;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: true,
    comment: 'Ngày tạo',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: true,
    comment: 'Ngày cập nhật',
  })
  updatedAt: number;

  // Không sử dụng quan hệ với bảng UserCampaignHistory, chỉ lưu ID

  /**
   * Thông tin segment (nếu có)
   */
  @Column({
    name: 'segment',
    type: 'jsonb',
    nullable: true,
    comment: 'Thông tin segment',
  })
  segment: CampaignSegment | null;

  /**
   * Danh sách audience với thông tin chi tiết
   */
  @Column({
    name: 'audiences',
    type: 'jsonb',
    nullable: true,
    comment: 'Danh sách audience với tên và email',
  })
  audiences: CampaignAudience[] | null;

  /**
   * Thời gian bắt đầu gửi (Unix timestamp)
   */
  @Column({
    name: 'started_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian bắt đầu gửi',
  })
  startedAt: number | null;

  /**
   * Thời gian hoàn thành (Unix timestamp)
   */
  @Column({
    name: 'completed_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian hoàn thành',
  })
  completedAt: number | null;

  /**
   * Tổng số người nhận
   */
  @Column({
    name: 'total_recipients',
    type: 'integer',
    default: 0,
    comment: 'Tổng số người nhận',
  })
  totalRecipients: number;

  /**
   * Số email/SMS đã gửi thành công
   */
  @Column({
    name: 'sent_count',
    type: 'integer',
    default: 0,
    comment: 'Số email/SMS đã gửi thành công',
  })
  sentCount: number;

  /**
   * Số email/SMS gửi thất bại
   */
  @Column({
    name: 'failed_count',
    type: 'integer',
    default: 0,
    comment: 'Số email/SMS gửi thất bại',
  })
  failedCount: number;
}
