import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi thông tin log tự động hóa Zalo
 */
export class ZaloAutomationLogResponseDto {
  @ApiProperty({
    description: 'ID của log',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của tự động hóa',
    example: 1,
  })
  automationId: number;

  @ApiProperty({
    description: 'ID của người dùng',
    example: 123,
  })
  userId: number;

  @ApiProperty({
    description: 'ID của Official Account',
    example: '*********',
  })
  oaId: string;

  @ApiProperty({
    description: 'ID của người theo dõi',
    example: 1,
  })
  followerId: number;

  @ApiProperty({
    description: 'ID người dùng của người theo dõi trên Zalo',
    example: '*********',
  })
  followerUserId: string;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> sự kiện kích ho<PERSON>t',
    example: 'follow',
  })
  triggerType: string;

  @ApiProperty({
    description: 'Loại hành động',
    example: 'send_message',
  })
  actionType: string;

  @ApiProperty({
    description: 'Trạng thái của log',
    example: 'success',
    enum: ['pending', 'success', 'failed'],
  })
  status: string;

  @ApiProperty({
    description: 'Thông báo lỗi (nếu có)',
    example: 'Không thể gửi tin nhắn',
    nullable: true,
  })
  error?: string;

  @ApiProperty({
    description: 'Thời điểm tạo (Unix timestamp)',
    example: *************,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thông tin người theo dõi',
    example: {
      displayName: 'Nguyễn Văn A',
      avatar: 'https://example.com/avatar.jpg',
    },
    nullable: true,
  })
  follower?: {
    displayName?: string;
    avatar?: string;
  };

  @ApiProperty({
    description: 'Thông tin tự động hóa',
    example: {
      name: 'Chào mừng người theo dõi mới',
    },
    nullable: true,
  })
  automation?: {
    name?: string;
  };
}
