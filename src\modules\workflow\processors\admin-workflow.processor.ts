import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName, WorkflowExecutionJobName } from '../../../queue/queue-name.enum';
import { WorkflowExecutionJobData, WorkflowNodeExecutionJobData } from '../../../queue/queue.types';
import { AdminWorkflowService } from '../service/admin-workflow.service';

/**
 * Processor xử lý workflow jobs cho admin
 * Xử lý các job từ queue WORKFLOW_EXECUTION cho admin users
 */
@Injectable()
@Processor(QueueName.WORKFLOW_EXECUTION, {
  concurrency: 3, // Xử lý tối đa 3 job đồng thời cho admin
  stalledInterval: 60 * 1000, // 60 giây
  maxStalledCount: 2,
})
export class AdminWorkflowProcessor extends WorkerHost {
  private readonly logger = new Logger(AdminWorkflowProcessor.name);

  constructor(
    private readonly adminWorkflowService: AdminWorkflowService,
  ) {
    super();
  }

  /**
   * Xử lý job từ queue workflow execution
   * @param job Job chứa dữ liệu workflow execution
   */
  async process(job: Job<WorkflowExecutionJobData | WorkflowNodeExecutionJobData, any, string>): Promise<void> {
    this.logger.log(
      `[ADMIN] Bắt đầu xử lý workflow job: ${job.id} - Type: ${job.name}`,
    );

    try {
      switch (job.name) {
        case WorkflowExecutionJobName.EXECUTE_WORKFLOW:
          await this.handleExecuteWorkflow(job as Job<WorkflowExecutionJobData>);
          break;

        case WorkflowExecutionJobName.EXECUTE_NODE:
          await this.handleExecuteNode(job as Job<WorkflowNodeExecutionJobData>);
          break;

        case WorkflowExecutionJobName.RETRY_WORKFLOW:
          await this.handleRetryWorkflow(job as Job<WorkflowExecutionJobData>);
          break;

        case WorkflowExecutionJobName.CLEANUP_EXECUTION:
          await this.handleCleanupExecution(job);
          break;

        default:
          this.logger.warn(`[ADMIN] Không hỗ trợ job type: ${job.name}`);
          throw new Error(`Unsupported job type: ${job.name}`);
      }

      this.logger.log(
        `[ADMIN] Hoàn thành xử lý workflow job: ${job.id} - Type: ${job.name}`,
      );
    } catch (error) {
      this.logger.error(
        `[ADMIN] Lỗi khi xử lý workflow job: ${job.id} - Type: ${job.name}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý job thực thi workflow hoàn chỉnh
   * @param job Job chứa dữ liệu workflow execution
   */
  private async handleExecuteWorkflow(job: Job<WorkflowExecutionJobData>): Promise<void> {
    const { executionId, workflowId, userId, triggerData, triggerType, metadata, options } = job.data;

    this.logger.log(
      `[ADMIN] Thực thi workflow: ${workflowId} cho user: ${userId} - Execution: ${executionId}`,
    );

    // TODO: Implement workflow execution logic
    // 1. Load workflow definition
    // 2. Validate workflow structure
    // 3. Create execution record
    // 4. Execute workflow nodes in sequence
    // 5. Handle node dependencies and conditions
    // 6. Update execution status
    // 7. Send SSE events if enabled
    // 8. Handle errors and retries

    this.logger.log(
      `[ADMIN] Workflow execution completed: ${executionId}`,
    );
  }

  /**
   * Xử lý job thực thi node đơn lẻ
   * @param job Job chứa dữ liệu node execution
   */
  private async handleExecuteNode(job: Job<WorkflowNodeExecutionJobData>): Promise<void> {
    const { executionId, nodeId, nodeType, nodeConfig, inputData, executionContext, options } = job.data;

    this.logger.log(
      `[ADMIN] Thực thi node: ${nodeId} (${nodeType}) - Execution: ${executionId}`,
    );

    // TODO: Implement node execution logic
    // 1. Load node definition
    // 2. Validate node configuration
    // 3. Prepare execution context
    // 4. Execute node with input data
    // 5. Validate output data
    // 6. Update execution node data
    // 7. Send SSE events if enabled
    // 8. Handle node-specific errors

    this.logger.log(
      `[ADMIN] Node execution completed: ${nodeId}`,
    );
  }

  /**
   * Xử lý job retry workflow execution
   * @param job Job chứa dữ liệu retry workflow
   */
  private async handleRetryWorkflow(job: Job<WorkflowExecutionJobData>): Promise<void> {
    const { executionId, workflowId, userId } = job.data;

    this.logger.log(
      `[ADMIN] Retry workflow execution: ${executionId} - Workflow: ${workflowId}`,
    );

    // TODO: Implement retry logic
    // 1. Load failed execution
    // 2. Identify failed nodes
    // 3. Reset execution state
    // 4. Resume from failed point
    // 5. Update retry count
    // 6. Handle max retry limits

    this.logger.log(
      `[ADMIN] Workflow retry completed: ${executionId}`,
    );
  }

  /**
   * Xử lý job cleanup execution data
   * @param job Job chứa dữ liệu cleanup
   */
  private async handleCleanupExecution(job: Job<any>): Promise<void> {
    const { executionId } = job.data;

    this.logger.log(
      `[ADMIN] Cleanup execution data: ${executionId}`,
    );

    // TODO: Implement cleanup logic
    // 1. Remove old execution logs
    // 2. Clean temporary files
    // 3. Archive execution data
    // 4. Update cleanup metrics

    this.logger.log(
      `[ADMIN] Execution cleanup completed: ${executionId}`,
    );
  }

  /**
   * Event handler khi job hoàn thành
   */
  async onCompleted(job: Job, result: any): Promise<void> {
    this.logger.log(
      `[ADMIN] Job completed successfully: ${job.id} - Type: ${job.name}`,
    );
  }

  /**
   * Event handler khi job thất bại
   */
  async onFailed(job: Job, error: Error): Promise<void> {
    this.logger.error(
      `[ADMIN] Job failed: ${job.id} - Type: ${job.name} - Error: ${error.message}`,
      error.stack,
    );
  }

  /**
   * Event handler khi job bị stalled
   */
  async onStalled(job: Job): Promise<void> {
    this.logger.warn(
      `[ADMIN] Job stalled: ${job.id} - Type: ${job.name}`,
    );
  }
}
