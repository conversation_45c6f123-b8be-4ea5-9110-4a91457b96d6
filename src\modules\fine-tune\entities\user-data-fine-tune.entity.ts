import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ProviderFineTuneEnum } from '../constants/provider.enum';

/**
 * Entity đại diện cho bảng user_data_fine_tune trong cơ sở dữ liệu
 * Lưu dữ liệu fine-tune mà người dùng tự cung cấp, bao gồm tập train và validation
 */
@Entity('user_data_fine_tune')
export class UserDataFineTune {
  /**
   * ID duy nhất của bộ dữ liệu fine-tune người dùng
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên của bộ dữ liệu fine-tune
   */
  @Column({ name: 'name', type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * Mô tả nội dung bộ dữ liệu
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;



  /**
   * Key S3 của tập dữ liệu huấn luyện
   */
  @Column({ name: 'train_dataset', type: 'varchar', length: 255, nullable: false })
  trainDataset: string;

  /**
   * Key S3 của tập dữ liệu validation (nếu có)
   */
  @Column({ name: 'valid_dataset', type: 'varchar', length: 255, nullable: true })
  validDataset: string | null;

  /**
   * ID người dùng sở hữu bộ dữ liệu
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  /**
   * Thời gian tạo bản ghi
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật cuối cùng
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;

  /**
   * Thời gian bị xóa mềm (nếu có)
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;

  /**
   * Nhà cung cấp AI
   */
  @Column({
    name: 'provider',
    type: 'enum',
    enum: ProviderFineTuneEnum,
    default: ProviderFineTuneEnum.OPENAI,
  })
  provider: ProviderFineTuneEnum;

  /**
   * Trạng thái sở hữu: TRUE - người dùng là chủ sở hữu, FALSE - người dùng chỉ được cấp quyền truy cập
   */
  @Column({ name: 'is_owner', type: 'boolean', default: true })
  isOwner: boolean;

  /**
   * Có thể bán hay không
   */
  @Column({ name: 'is_for_sale', type: 'boolean', default: false })
  isForSale: boolean;

  /**
   * Dữ liệu có hợp lệ hay không
   */
  @Column({ name: 'is_valid', type: 'boolean', default: false })
  isValid: boolean;

  /**
   * Ước lượng số token trong tập dữ liệu
   */
  @Column({ name: 'estimated_token', type: 'integer', default: 0 })
  estimatedToken: number;

  /**
   * dung lượng tập dữ liệu (bytes) gồm file train + validation
   */
  @Column({ name: 'size', type: 'bigint', default: 0 })
  size: number;


}
