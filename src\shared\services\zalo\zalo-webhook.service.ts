import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@common/exceptions';
import {
  ZaloWebhookEvent,
  ZaloUserMessageEvent,
  ZaloInteractionEvent,
  ZaloOaMessageEvent,
  ZaloFollowEvent,
  ZaloWebhookEventType,
  isUserMessageEvent,
  isInteractionEvent,
  isOaMessageEvent,
  isFollowEvent,
} from './dto/zalo-webhook.dto';
import * as crypto from 'crypto';

@Injectable()
export class ZaloWebhookService {
  private readonly logger = new Logger(ZaloWebhookService.name);
  private readonly webhookSecret: string;

  constructor(private readonly configService: ConfigService) {
    const secret = this.configService.get<string>('ZALO_WEBHOOK_SECRET');
    if (!secret) {
      throw new Error('ZALO_WEBHOOK_SECRET is not defined in configuration');
    }
    this.webhookSecret = secret;
  }

  /**
   * <PERSON><PERSON><PERSON> thực webhook từ Zalo
   * @param timestamp Thời gian gửi webhook
   * @param mac MAC (Message Authentication Code)
   * @param body Nội dung webhook
   * @returns Kết quả xác thực
   */
  verifyWebhook(timestamp: string, mac: string, body: string): boolean {
    try {
      if (!this.webhookSecret) {
        this.logger.warn('ZALO_WEBHOOK_SECRET is not configured');
        return false;
      }

      // Tạo chuỗi dữ liệu để tính toán MAC
      const data = `${timestamp}.${body}`;

      // Tính toán MAC bằng HMAC-SHA256
      const expectedMac = crypto
        .createHmac('sha256', this.webhookSecret)
        .update(data)
        .digest('hex');

      // So sánh MAC tính toán với MAC nhận được
      return mac === expectedMac;
    } catch (error) {
      this.logger.error(
        `Error verifying webhook: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Xử lý sự kiện webhook từ Zalo
   * @param event Sự kiện webhook
   * @returns Kết quả xử lý
   */
  async processWebhookEvent(
    event: ZaloWebhookEvent,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log(
        `Processing webhook event: ${event.event_name} (ID: ${event.event_id})`,
      );

      // Xử lý các loại sự kiện theo category
      if (isUserMessageEvent(event)) {
        await this.processUserMessageEvent(event);
      } else if (isInteractionEvent(event)) {
        await this.processInteractionEvent(event);
      } else if (isOaMessageEvent(event)) {
        await this.processOaMessageEvent(event);
      } else if (isFollowEvent(event)) {
        await this.processFollowEvent(event);
      } else {
        this.logger.warn(`Unhandled event type: ${event.event_name}`);
      }

      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error processing webhook event: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xử lý sự kiện webhook',
      );
    }
  }

  /**
   * Xử lý các sự kiện tin nhắn từ người dùng
   * @param event Sự kiện tin nhắn từ người dùng
   */
  private async processUserMessageEvent(
    event: ZaloUserMessageEvent,
  ): Promise<void> {
    try {
      const { sender, recipient, message } = event.data;

      switch (event.event_name) {
        case ZaloWebhookEventType.USER_SEND_TEXT:
          const textMessage = message as {
            msg_id: string;
            text: string;
            timestamp: number;
          };
          this.logger.log(`User ${sender.id} sent text: ${textMessage.text}`);
          await this.handleUserTextMessage(
            event,
            sender,
            recipient,
            textMessage,
          );
          break;

        case ZaloWebhookEventType.USER_SEND_IMAGE:
          const imageMessage = message as {
            msg_id: string;
            attachments: any[];
            timestamp: number;
          };
          this.logger.log(
            `User ${sender.id} sent image: ${imageMessage.attachments?.[0]?.payload?.url}`,
          );
          await this.handleUserImageMessage(
            event,
            sender,
            recipient,
            imageMessage,
          );
          break;

        case ZaloWebhookEventType.USER_SEND_FILE:
          const fileMessage = message as {
            msg_id: string;
            attachments: any[];
            timestamp: number;
          };
          this.logger.log(
            `User ${sender.id} sent file: ${fileMessage.attachments?.[0]?.payload?.name}`,
          );
          await this.handleUserFileMessage(
            event,
            sender,
            recipient,
            fileMessage,
          );
          break;

        case ZaloWebhookEventType.USER_SEND_STICKER:
          const stickerMessage = message as {
            msg_id: string;
            attachments: any[];
            timestamp: number;
          };
          this.logger.log(
            `User ${sender.id} sent sticker: ${stickerMessage.attachments?.[0]?.payload?.sticker_id}`,
          );
          await this.handleUserStickerMessage(
            event,
            sender,
            recipient,
            stickerMessage,
          );
          break;

        case ZaloWebhookEventType.USER_SEND_GIF:
          const gifMessage = message as {
            msg_id: string;
            attachments: any[];
            timestamp: number;
          };
          this.logger.log(
            `User ${sender.id} sent GIF: ${gifMessage.attachments?.[0]?.payload?.url}`,
          );
          await this.handleUserGifMessage(event, sender, recipient, gifMessage);
          break;

        case ZaloWebhookEventType.USER_SEND_AUDIO:
          const audioMessage = message as {
            msg_id: string;
            attachments: any[];
            timestamp: number;
          };
          this.logger.log(
            `User ${sender.id} sent audio: ${audioMessage.attachments?.[0]?.payload?.url}`,
          );
          await this.handleUserAudioMessage(
            event,
            sender,
            recipient,
            audioMessage,
          );
          break;

        case ZaloWebhookEventType.USER_SEND_VIDEO:
          const videoMessage = message as {
            msg_id: string;
            attachments: any[];
            timestamp: number;
          };
          this.logger.log(
            `User ${sender.id} sent video: ${videoMessage.attachments?.[0]?.payload?.url}`,
          );
          await this.handleUserVideoMessage(
            event,
            sender,
            recipient,
            videoMessage,
          );
          break;

        case ZaloWebhookEventType.USER_SEND_LOCATION:
          const locationMessage = message as {
            msg_id: string;
            attachments: any[];
            timestamp: number;
          };
          const coords = locationMessage.attachments?.[0]?.payload?.coordinates;
          this.logger.log(
            `User ${sender.id} sent location: ${coords?.latitude}, ${coords?.longitude}`,
          );
          await this.handleUserLocationMessage(
            event,
            sender,
            recipient,
            locationMessage,
          );
          break;

        default:
          this.logger.warn(`Unhandled user message event: ${event.event_name}`);
      }
    } catch (error) {
      this.logger.error(
        `Error processing user message event: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Xử lý các sự kiện tương tác
   * @param event Sự kiện tương tác
   */
  private async processInteractionEvent(
    event: ZaloInteractionEvent,
  ): Promise<void> {
    try {
      switch (event.event_name) {
        case ZaloWebhookEventType.USER_CLICK_CHATNOW:
          this.logger.log(
            `User ${event.data.sender.id} clicked chat now button`,
          );
          await this.handleUserClickChatNow(event);
          break;

        case ZaloWebhookEventType.USER_REACTION:
          this.logger.log(
            `User ${event.data.sender.id} reacted ${event.data.reaction.reaction} to message ${event.data.reaction.msg_id}`,
          );
          await this.handleUserReaction(event);
          break;

        case ZaloWebhookEventType.OA_REACTION:
          this.logger.log(
            `OA reacted ${event.data.reaction.reaction} to message ${event.data.reaction.msg_id}`,
          );
          await this.handleOaReaction(event);
          break;

        case ZaloWebhookEventType.USER_SEEN_MESSAGE:
          this.logger.log(
            `User ${event.data.sender.id} seen message ${event.data.message.msg_id}`,
          );
          await this.handleUserSeenMessage(event);
          break;

        case ZaloWebhookEventType.USER_RECEIVED_MESSAGE:
          this.logger.log(
            `User ${event.data.recipient.id} received message ${event.data.message.msg_id}`,
          );
          await this.handleUserReceivedMessage(event);
          break;

        default:
          this.logger.warn(`Unhandled interaction event: ${event.event_name}`);
      }
    } catch (error) {
      this.logger.error(
        `Error processing interaction event: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Xử lý các sự kiện tin nhắn từ OA
   * @param event Sự kiện tin nhắn từ OA
   */
  private async processOaMessageEvent(
    event: ZaloOaMessageEvent,
  ): Promise<void> {
    try {
      switch (event.event_name) {
        case ZaloWebhookEventType.OA_SEND_TEXT:
          this.logger.log(`OA sent message to user ${event.data.recipient.id}`);
          await this.handleOaSendMessage(event);
          break;

        case ZaloWebhookEventType.OA_SEND_ANONYMOUS:
          this.logger.log(
            `OA sent message to anonymous user ${event.data.recipient.id}`,
          );
          await this.handleOaSendAnonymousMessage(event);
          break;

        default:
          this.logger.warn(`Unhandled OA message event: ${event.event_name}`);
      }
    } catch (error) {
      this.logger.error(
        `Error processing OA message event: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Xử lý các sự kiện theo dõi
   * @param event Sự kiện theo dõi
   */
  private async processFollowEvent(event: ZaloFollowEvent): Promise<void> {
    try {
      switch (event.event_name) {
        case ZaloWebhookEventType.USER_FOLLOW:
          this.logger.log(`User ${event.data.follower.id} followed the OA`);
          await this.handleUserFollow(event);
          break;

        case ZaloWebhookEventType.USER_UNFOLLOW:
          this.logger.log(`User ${event.data.follower.id} unfollowed the OA`);
          await this.handleUserUnfollow(event);
          break;

        default:
          this.logger.warn(`Unhandled follow event: ${event.event_name}`);
      }
    } catch (error) {
      this.logger.error(
        `Error processing follow event: ${error.message}`,
        error.stack,
      );
    }
  }

  // ===== SPECIFIC MESSAGE HANDLERS =====

  /**
   * Xử lý tin nhắn văn bản từ người dùng
   */
  private async handleUserTextMessage(
    _event: any,
    _sender: any,
    _recipient: any,
    _message: any,
  ): Promise<void> {
    // Implement business logic for text messages
    // Ví dụ: Lưu tin nhắn vào database, trigger chatbot response, etc.
  }

  /**
   * Xử lý tin nhắn hình ảnh từ người dùng
   */
  private async handleUserImageMessage(
    _event: any,
    _sender: any,
    _recipient: any,
    _message: any,
  ): Promise<void> {
    // Implement business logic for image messages
    // Ví dụ: Download và lưu hình ảnh, phân tích nội dung, etc.
  }

  /**
   * Xử lý tin nhắn file từ người dùng
   */
  private async handleUserFileMessage(
    _event: any,
    _sender: any,
    _recipient: any,
    _message: any,
  ): Promise<void> {
    // Implement business logic for file messages
    // Ví dụ: Download và lưu file, kiểm tra virus, etc.
  }

  /**
   * Xử lý tin nhắn sticker từ người dùng
   */
  private async handleUserStickerMessage(
    _event: any,
    _sender: any,
    _recipient: any,
    _message: any,
  ): Promise<void> {
    // Implement business logic for sticker messages
  }

  /**
   * Xử lý tin nhắn GIF từ người dùng
   */
  private async handleUserGifMessage(
    _event: any,
    _sender: any,
    _recipient: any,
    _message: any,
  ): Promise<void> {
    // Implement business logic for GIF messages
  }

  /**
   * Xử lý tin nhắn audio từ người dùng
   */
  private async handleUserAudioMessage(
    _event: any,
    _sender: any,
    _recipient: any,
    _message: any,
  ): Promise<void> {
    // Implement business logic for audio messages
    // Ví dụ: Speech-to-text processing, etc.
  }

  /**
   * Xử lý tin nhắn video từ người dùng
   */
  private async handleUserVideoMessage(
    _event: any,
    _sender: any,
    _recipient: any,
    _message: any,
  ): Promise<void> {
    // Implement business logic for video messages
  }

  /**
   * Xử lý tin nhắn vị trí từ người dùng
   */
  private async handleUserLocationMessage(
    _event: any,
    _sender: any,
    _recipient: any,
    _message: any,
  ): Promise<void> {
    // Implement business logic for location messages
    // Ví dụ: Tìm kiếm dịch vụ gần đó, etc.
  }

  // ===== INTERACTION HANDLERS =====

  /**
   * Xử lý sự kiện người dùng click nút "Nhắn tin"
   */
  private async handleUserClickChatNow(_event: any): Promise<void> {
    // Implement business logic for chat now clicks
    // Ví dụ: Gửi tin nhắn chào mừng, etc.
  }

  /**
   * Xử lý sự kiện người dùng thả reaction
   */
  private async handleUserReaction(_event: any): Promise<void> {
    // Implement business logic for user reactions
  }

  /**
   * Xử lý sự kiện OA thả reaction
   */
  private async handleOaReaction(_event: any): Promise<void> {
    // Implement business logic for OA reactions
  }

  /**
   * Xử lý sự kiện người dùng đã xem tin nhắn
   */
  private async handleUserSeenMessage(_event: any): Promise<void> {
    // Implement business logic for message seen events
    // Ví dụ: Cập nhật trạng thái tin nhắn, analytics, etc.
  }

  /**
   * Xử lý sự kiện người dùng nhận tin nhắn
   */
  private async handleUserReceivedMessage(_event: any): Promise<void> {
    // Implement business logic for message received events
  }

  // ===== OA MESSAGE HANDLERS =====

  /**
   * Xử lý sự kiện OA gửi tin nhắn
   */
  private async handleOaSendMessage(_event: any): Promise<void> {
    // Implement business logic for OA sent messages
  }

  /**
   * Xử lý sự kiện OA gửi tin nhắn cho người dùng ẩn danh
   */
  private async handleOaSendAnonymousMessage(_event: any): Promise<void> {
    // Implement business logic for OA sent anonymous messages
  }

  // ===== FOLLOW HANDLERS =====

  /**
   * Xử lý sự kiện người dùng theo dõi OA
   */
  private async handleUserFollow(_event: any): Promise<void> {
    // Implement business logic for user follow events
    // Ví dụ: Gửi tin nhắn chào mừng, cập nhật database, etc.
  }

  /**
   * Xử lý sự kiện người dùng hủy theo dõi OA
   */
  private async handleUserUnfollow(_event: any): Promise<void> {
    // Implement business logic for user unfollow events
    // Ví dụ: Cập nhật database, analytics, etc.
  }
}
