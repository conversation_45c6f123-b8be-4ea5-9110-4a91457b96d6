/**
 * DTO cho request đồng bộ người dùng Zalo vào audience
 */
export interface SyncZaloUsersToAudienceDto {
  /**
   * Số lượng người dùng cần lấy (mặc định: 50, tối đa: 50)
   */
  count?: number;

  /**
   * Vị trí bắt đầu (mặc định: 0)
   */
  offset?: number;

  /**
   * Tên tag để lọc người dùng
   */
  tagName?: string;

  /**
   * Khoảng thời gian tương tác cuối (tính bằng ngày)
   */
  lastInteractionPeriod?: number;

  /**
   * Chỉ lấy người theo dõi
   */
  isFollower?: boolean;

  /**
   * C<PERSON> cập nhật audience đã tồn tại không
   */
  updateExisting?: boolean;

  /**
   * Đồng bộ tất cả người dùng
   */
  syncAll?: boolean;
}

/**
 * DTO cho response đồng bộ người dùng Zalo vào audience
 */
export interface SyncZaloUsersToAudienceResponseDto {
  /**
   * ID của sync job
   */
  syncId: string;

  /**
   * Tổng số người dùng từ Zalo
   */
  totalUsersFromZalo: number;

  /**
   * Số lượng đã xử lý
   */
  processedCount: number;

  /**
   * Số audience mới được tạo
   */
  newAudienceCreated: number;

  /**
   * Số audience đã tồn tại được cập nhật
   */
  existingAudienceUpdated: number;

  /**
   * Số lượng bị bỏ qua
   */
  skippedCount: number;

  /**
   * Số lượng lỗi
   */
  errorCount: number;

  /**
   * Danh sách lỗi
   */
  errors: SyncErrorDto[];

  /**
   * Số custom field mới được tạo
   */
  newCustomFieldsCreated: number;

  /**
   * Danh sách custom field đã tạo
   */
  createdCustomFields: CreatedCustomFieldDto[];

  /**
   * Thời gian bắt đầu
   */
  startedAt: number;

  /**
   * Thời gian hoàn thành
   */
  completedAt: number;

  /**
   * Thời gian xử lý (ms)
   */
  processingTime: number;

  /**
   * Tỷ lệ thành công (%)
   */
  successRate: number;
}

/**
 * DTO cho lỗi trong quá trình đồng bộ
 */
export interface SyncErrorDto {
  /**
   * ID người dùng Zalo
   */
  userId?: string;

  /**
   * Thông báo lỗi
   */
  message: string;

  /**
   * Chi tiết lỗi
   */
  details?: any;
}

/**
 * DTO cho custom field đã tạo
 */
export interface CreatedCustomFieldDto {
  /**
   * Tên field
   */
  fieldName: string;

  /**
   * Loại dữ liệu
   */
  dataType: string;

  /**
   * Mô tả
   */
  description?: string;
}
