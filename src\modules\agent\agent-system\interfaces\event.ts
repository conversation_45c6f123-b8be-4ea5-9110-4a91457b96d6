export type Role = 'supervisor' | 'worker';

export interface SupervisorStreamTextTokenEvent {
  type: 'stream_text_token';
  /** actual token chunk */
  data: any;
}

// Removed: SupervisorStreamToolTokenEvent - tool token events not needed

/** Supervisor bắt đầu gọi tool */
export interface SupervisorToolCallStartEvent {
  type: 'tool_call_start';
  data: any;
}

/** Supervisor kết thúc gọi tool */
export interface SupervisorToolCallEndEvent {
  type: 'tool_call_end';
  data: any;
}

export interface SupervisorToolCallInterruptEvent {
  type: 'tool_call_interrupt';
  data: any;
}

/** Worker bắt đầu làm việc (chỉ emit một lần khi chạy) */
export interface WorkerWorkingEvent {
  type: 'stream_text_token';
  data: any;
}

// Removed: WorkerStreamToolTokenEvent - tool token events not needed

/** Worker bắt đầu gọi tool */
export interface WorkerToolCallStartEvent {
  type: 'tool_call_start';
  data: any;
}

/** Worker kết thúc gọi tool */
export interface WorkerToolCallEndEvent {
  type: 'tool_call_end';
  data: any;
}

export interface WorkerToolCallInterruptEvent {
  type: 'tool_call_interrupt';
  data: any;
}

/** Event emitted when user's point balance is updated after token usage */
export interface UpdateRpointEvent {
  type: 'update_rpoint';
  data: {
    /** Cost in points for the LLM operation */
    rPointCost: number;
    /** Updated user balance after deduction */
    updatedBalance: number;
    /** Timestamp when the update occurred */
    timestamp?: number;
  };
}

/** Event emitted when each individual LLM completion finishes (not after entire streaming session) */
export interface LlmStreamEndEvent {
  type: 'llm_stream_end';
  data: {
    /** Role of the agent that completed the LLM call */
    role?: Role;
  };
}

/** Event emitted when the entire streaming session ends (final event to close SSE connection) */
export interface StreamSessionEndEvent {
  type: 'stream_session_end';
  data: {
    /** Reason for session ending */
    reason: string;
    /** Session duration in milliseconds */
    duration?: number;
    /** Timestamp when session ended */
    endTime?: number;
  };
}

/** Event emitted when an error occurs during streaming */
export interface StreamErrorEvent {
  type: 'stream_error';
  data: {
    /** Error message */
    error: string;
    /** Error name/type */
    errorName: string;
    /** Stack trace for debugging */
    stack?: string;
    /** Thread ID where error occurred */
    threadId: string;
    /** Run ID where error occurred */
    runId: string;
    /** Timestamp when error occurred */
    timestamp: number;
  };
}

/** Event emitted when a message is saved to the database */
export interface MessageCreatedEvent {
  type: 'message_created';
  data: {
    /** ID of the created message */
    message_id: string;
    /** Thread ID where message was created */
    thread_id: string;
    /** Role of the message sender */
    role: 'user' | 'assistant';
    /** Preview of the message content */
    content_preview: string;
  };
}

/** Event emitted when user's point balance is updated after token usage */
export interface UpdateRpointEvent {
  type: 'update_rpoint';
  data: {
    /** Cost in points for the LLM operation */
    rPointCost: number;
    /** Updated user balance after deduction */
    updatedBalance: number;
    /** Timestamp when the update occurred */
    timestamp?: number;
  };
}

/** Union type cho tất cả các Transformed Events */
export type TransformedEvent =
  | SupervisorStreamTextTokenEvent
  | SupervisorToolCallStartEvent
  | SupervisorToolCallEndEvent
  | SupervisorToolCallInterruptEvent
  | WorkerWorkingEvent
  | WorkerToolCallStartEvent
  | WorkerToolCallEndEvent
  | WorkerToolCallInterruptEvent
  | UpdateRpointEvent
  | LlmStreamEndEvent
  | StreamSessionEndEvent
  | StreamErrorEvent
  | MessageCreatedEvent;

/**
 * Type-safe callback function for emitting events to Redis Streams
 * Ensures all emitted events conform to the TransformedEvent union type
 *
 * @param event - The event to emit, must be one of the TransformedEvent variants
 * @returns Promise that resolves when the event is successfully emitted
 *
 * @example
 * ```typescript
 * const emitEvent: EmitEventCallback = async (event) => {
 *   await redisPublisher.emit(event);
 * };
 *
 * await emitEvent({
 *   type: 'update_rpoint',
 *   data: { rPointCost: 10, updatedBalance: 100 }
 * });
 * ```
 */
export type EmitEventCallback = (event: TransformedEvent) => Promise<void>;
