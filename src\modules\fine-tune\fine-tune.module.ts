import { HttpModule } from '@nestjs/axios';
import { BullModule } from '@nestjs/bullmq';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InfraModule } from '../../infra';
import { QueueName } from '../../queue';

// Entities
import {
  AdminDataFineTune,
  ModelRegistry,
  UserDataFineTune,
  Models,
  ModelDetail,
  ModelIntegration,
} from './entities';

// Processors
// import { FineTunePollingProcessor } from './processors/fine-tune-polling.processor'; // Commented out temporarily

// Services
import { OpenAIFineTuneService } from './services/openai-fine-tune.service';
import { GoogleFineTuneService } from './services/google-fine-tune.service';
import { FineTuneLoggingService } from './services/fine-tune-logging.service';
import { FineTuneMonitoringService } from './services/fine-tune-monitoring.service';
import { FineTuneQueueService } from './services/fine-tune-queue.service';
// import { FineTunePollingService } from './services/fine-tune-polling.service'; // Commented out temporarily

// Repositories
import { FineTunePollingRepository } from './repositories/fine-tune-polling.repository';
import { ModelsMonitoringRepository } from './repositories/models-monitoring.repository';

// Processors
import { FineTuneMonitoringProcessor } from './processors/fine-tune-monitoring.processor';
import { FineTunePollingProcessor } from './processors/fine-tune-polling.processor';

// Utils
import { EncryptionUtil } from './utils/encryption.util';
import { KeyPairEncryptionService } from '../../shared/services/encryption/key-pair-encryption.service';

/**
 * Module xử lý hệ thống Fine-Tuning
 */
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([
      // Fine-tune related entities
      ModelRegistry,
      UserDataFineTune,
      AdminDataFineTune,
      Models,
      ModelDetail,
      ModelIntegration,
    ]),
    BullModule.registerQueue({
      name: QueueName.FINE_TUNE,
    }),
    HttpModule.register({
      timeout: 60000, // 60 seconds timeout for fine-tuning operations
      maxRedirects: 5,
    }),
    InfraModule, // For OpenAI and Google AI services
  ],
  providers: [
    // Processors
    FineTuneMonitoringProcessor,
    FineTunePollingProcessor,

    // Services
    OpenAIFineTuneService,
    GoogleFineTuneService,
    FineTuneLoggingService,
    FineTuneMonitoringService,
    FineTuneQueueService,
    // FineTunePollingService, // Commented out temporarily

    // Repositories
    FineTunePollingRepository,
    ModelsMonitoringRepository,

    // Utils
    EncryptionUtil,
    KeyPairEncryptionService,
  ],
  exports: [
    // Services
    OpenAIFineTuneService,
    GoogleFineTuneService,
    FineTuneLoggingService,
    FineTuneMonitoringService,
    FineTuneQueueService,
    // FineTunePollingService, // Commented out temporarily

    // Repositories
    FineTunePollingRepository,
    ModelsMonitoringRepository,

    // Utils
    EncryptionUtil,
  ],
})
export class FineTuneModule { }
