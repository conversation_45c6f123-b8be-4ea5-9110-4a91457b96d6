import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { UserAudience } from '../entities/user-audience.entity';
import {
  EmailStatus,
  EmailErrorType,
  classifyEmailError,
  getEmailStatusFromError,
} from '../enums/email-status.enum';

/**
 * Service để quản lý email status của audience
 */
@Injectable()
export class AudienceEmailStatusService {
  private readonly logger = new Logger(AudienceEmailStatusService.name);

  constructor(
    @InjectRepository(UserAudience)
    private readonly audienceRepository: Repository<UserAudience>,
  ) {}

  /**
   * Cập nhật email status khi gửi email thành công
   * @param audienceId ID của audience
   */
  async markEmailSent(audienceId: number): Promise<void> {
    try {
      await this.audienceRepository.update(audienceId, {
        emailStatus: EmailStatus.VALID,
        lastEmailSentAt: Date.now(),
        emailFailureCount: 0, // Reset failure count khi gửi thành công
        updatedAt: Date.now(),
      });

      this.logger.debug(`Marked email sent for audience ${audienceId}`);
    } catch (error) {
      this.logger.error(
        `Error marking email sent for audience ${audienceId}: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Cập nhật email status khi gửi email thất bại
   * @param audienceId ID của audience
   * @param errorMessage Thông báo lỗi
   */
  async markEmailFailed(
    audienceId: number,
    errorMessage: string,
  ): Promise<void> {
    try {
      // Phân loại loại lỗi
      const errorType = classifyEmailError(errorMessage);
      const emailStatus = getEmailStatusFromError(errorType);

      // Lấy thông tin audience hiện tại để tăng failure count
      const audience = await this.audienceRepository.findOne({
        where: { id: audienceId },
      });

      if (!audience) {
        this.logger.warn(`Audience ${audienceId} not found`);
        return;
      }

      const newFailureCount = (audience.emailFailureCount || 0) + 1;

      await this.audienceRepository.update(audienceId, {
        emailStatus,
        emailErrorType: errorType,
        emailErrorDetails: errorMessage,
        lastEmailFailedAt: Date.now(),
        emailFailureCount: newFailureCount,
        updatedAt: Date.now(),
      });

      this.logger.debug(
        `Marked email failed for audience ${audienceId}: ${errorType} (${newFailureCount} failures)`,
      );

      // Log warning nếu failure count cao
      if (newFailureCount >= 3) {
        this.logger.warn(
          `Audience ${audienceId} has ${newFailureCount} consecutive email failures. Status: ${emailStatus}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error marking email failed for audience ${audienceId}: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Lấy danh sách audience có email không hợp lệ
   * @param userId ID của user
   * @returns Danh sách audience có email invalid
   */
  async getInvalidEmailAudiences(userId: number): Promise<UserAudience[]> {
    try {
      return await this.audienceRepository.find({
        where: {
          userId,
          emailStatus: EmailStatus.INVALID,
        },
        order: {
          lastEmailFailedAt: 'DESC',
        },
      });
    } catch (error) {
      this.logger.error(
        `Error getting invalid email audiences for user ${userId}: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }

  /**
   * Lấy thống kê email status của user
   * @param userId ID của user
   * @returns Thống kê email status
   */
  async getEmailStatusStats(userId: number): Promise<{
    total: number;
    valid: number;
    invalid: number;
    bounced: number;
    softBounced: number;
    blocked: number;
    unsubscribed: number;
    spamComplaint: number;
  }> {
    try {
      const [
        total,
        valid,
        invalid,
        bounced,
        softBounced,
        blocked,
        unsubscribed,
        spamComplaint,
      ] = await Promise.all([
        this.audienceRepository.count({ where: { userId } }),
        this.audienceRepository.count({
          where: { userId, emailStatus: EmailStatus.VALID },
        }),
        this.audienceRepository.count({
          where: { userId, emailStatus: EmailStatus.INVALID },
        }),
        this.audienceRepository.count({
          where: { userId, emailStatus: EmailStatus.BOUNCED },
        }),
        this.audienceRepository.count({
          where: { userId, emailStatus: EmailStatus.SOFT_BOUNCED },
        }),
        this.audienceRepository.count({
          where: { userId, emailStatus: EmailStatus.BLOCKED },
        }),
        this.audienceRepository.count({
          where: { userId, emailStatus: EmailStatus.UNSUBSCRIBED },
        }),
        this.audienceRepository.count({
          where: { userId, emailStatus: EmailStatus.SPAM_COMPLAINT },
        }),
      ]);

      return {
        total,
        valid,
        invalid,
        bounced,
        softBounced,
        blocked,
        unsubscribed,
        spamComplaint,
      };
    } catch (error) {
      this.logger.error(
        `Error getting email status stats for user ${userId}: ${error.message}`,
        error.stack,
      );
      return {
        total: 0,
        valid: 0,
        invalid: 0,
        bounced: 0,
        softBounced: 0,
        blocked: 0,
        unsubscribed: 0,
        spamComplaint: 0,
      };
    }
  }

  /**
   * Reset email status về VALID (dùng khi user muốn thử lại)
   * @param audienceId ID của audience
   */
  async resetEmailStatus(audienceId: number): Promise<void> {
    try {
      await this.audienceRepository.update(audienceId, {
        emailStatus: EmailStatus.VALID,
        emailFailureCount: 0,
        updatedAt: Date.now(),
      });

      this.logger.debug(`Reset email status for audience ${audienceId}`);
    } catch (error) {
      this.logger.error(
        `Error resetting email status for audience ${audienceId}: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Lấy danh sách audience có thể gửi email (loại bỏ invalid, blocked, etc.)
   * @param audienceIds Danh sách audience IDs
   * @returns Danh sách audience có thể gửi email
   */
  async getValidEmailAudiences(audienceIds: number[]): Promise<UserAudience[]> {
    try {
      return await this.audienceRepository.find({
        where: {
          id: In(audienceIds),
          emailStatus: EmailStatus.VALID,
        },
      });
    } catch (error) {
      this.logger.error(
        `Error getting valid email audiences: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }
}
