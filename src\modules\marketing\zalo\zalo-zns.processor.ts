import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName, ZaloZnsJobName } from '../../../queue';
import {
  ZaloZnsSingleJobData,
  ZaloZnsCampaignJobData,
  ZaloZnsBatchJobData,
} from './interfaces';
import { ZnsMessageType } from './dto';
import { ZaloPromotionService } from '../../../shared/services/zalo/zalo-promotion.service';
import { ZaloZnsService } from '../../../shared/services/zalo/zalo-zns.service';
import { ZaloService } from '../../../shared/services/zalo/zalo.service';
import { ConfigService } from '@nestjs/config';

/**
 * Processor xử lý queue Zalo ZNS
 * Xử lý các loại job:
 * - SEND_ZNS: G<PERSON><PERSON> ZNS đơn lẻ
 * - SEND_ZNS_CAMPAIGN: G<PERSON><PERSON> ZNS theo chiến dịch
 * - SEND_BATCH_ZNS: Gửi batch ZNS
 */
@Injectable()
@Processor(QueueName.ZALO_ZNS, { concurrency: 5 })
export class ZaloZnsProcessor extends WorkerHost {
  private readonly logger = new Logger(ZaloZnsProcessor.name);

  constructor(
    private readonly zaloPromotionService: ZaloPromotionService,
    private readonly zaloZnsService: ZaloZnsService,
    private readonly zaloService: ZaloService,
    private readonly configService: ConfigService,
  ) {
    super();
  }

  /**
   * Xử lý job từ queue
   */
  async process(
    job: Job<
      ZaloZnsSingleJobData | ZaloZnsCampaignJobData | ZaloZnsBatchJobData
    >,
  ): Promise<void> {
    this.logger.log(`Processing job ${job.name} with ID ${job.id}`);

    try {
      switch (job.name) {
        case ZaloZnsJobName.SEND_ZNS:
          await this.processSingleZns(job as Job<ZaloZnsSingleJobData>);
          break;
        case ZaloZnsJobName.SEND_ZNS_CAMPAIGN:
          await this.processCampaignZns(job as Job<ZaloZnsCampaignJobData>);
          break;
        case ZaloZnsJobName.SEND_BATCH_ZNS:
          await this.processBatchZns(job as Job<ZaloZnsBatchJobData>);
          break;
        default:
          throw new Error(`Unknown job name: ${job.name}`);
      }

      this.logger.log(`Job ${job.id} completed successfully`);
    } catch (error) {
      this.logger.error(`Job ${job.id} failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xử lý job gửi ZNS đơn lẻ
   */
  private async processSingleZns(
    job: Job<ZaloZnsSingleJobData>,
  ): Promise<void> {
    const { oaId, phone, templateId, templateData, messageType, trackingId } =
      job.data;

    this.logger.log(
      `Processing single ZNS for phone ${phone} with template ${templateId}`,
    );

    // Lấy access token cho OA
    const accessToken = await this.getAccessToken(oaId);

    // Xử lý theo loại tin nhắn
    switch (messageType) {
      case ZnsMessageType.PROMOTION:
        await this.handlePromotionMessage(
          accessToken,
          phone,
          templateId,
          templateData,
          trackingId,
        );
        break;
      case ZnsMessageType.TRANSACTION:
        await this.handleTransactionMessage(
          accessToken,
          phone,
          templateId,
          templateData,
          trackingId,
        );
        break;
      case ZnsMessageType.CUSTOMER_CARE:
        await this.handleCustomerCareMessage(
          accessToken,
          phone,
          templateId,
          templateData,
          trackingId,
        );
        break;
      default:
        // Mặc định sử dụng ZNS service thông thường
        await this.handleDefaultMessage(
          accessToken,
          phone,
          templateId,
          templateData,
          trackingId,
        );
        break;
    }
  }

  /**
   * Xử lý job gửi ZNS theo chiến dịch
   */
  private async processCampaignZns(
    job: Job<ZaloZnsCampaignJobData>,
  ): Promise<void> {
    const {
      campaignId,
      oaId,
      templateId,
      templateData,
      phoneList,
      messageType,
      batchSize = 10,
      batchDelay = 1000,
    } = job.data;

    this.logger.log(
      `Processing campaign ZNS ${campaignId} for ${phoneList.length} phones`,
    );

    // Lấy access token cho OA
    const accessToken = await this.getAccessToken(oaId);

    // Chia thành các batch để gửi
    const batches = this.chunkArray(phoneList, batchSize);

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      this.logger.log(
        `Processing batch ${i + 1}/${batches.length} with ${batch.length} phones`,
      );

      // Gửi từng tin nhắn trong batch
      for (const phone of batch) {
        try {
          const trackingId = `campaign_${campaignId}_${phone}_${Date.now()}`;

          // Xử lý theo loại tin nhắn
          switch (messageType) {
            case ZnsMessageType.PROMOTION:
              await this.handlePromotionMessage(
                accessToken,
                phone,
                templateId,
                templateData,
                trackingId,
              );
              break;
            case ZnsMessageType.TRANSACTION:
              await this.handleTransactionMessage(
                accessToken,
                phone,
                templateId,
                templateData,
                trackingId,
              );
              break;
            case ZnsMessageType.CUSTOMER_CARE:
              await this.handleCustomerCareMessage(
                accessToken,
                phone,
                templateId,
                templateData,
                trackingId,
              );
              break;
            default:
              await this.handleDefaultMessage(
                accessToken,
                phone,
                templateId,
                templateData,
                trackingId,
              );
              break;
          }
        } catch (error) {
          this.logger.error(`Failed to send ZNS to ${phone}: ${error.message}`);
          // Tiếp tục gửi cho số điện thoại khác
        }
      }

      // Delay giữa các batch (trừ batch cuối)
      if (i < batches.length - 1) {
        await this.delay(batchDelay);
      }
    }
  }

  /**
   * Xử lý job gửi batch ZNS
   */
  private async processBatchZns(job: Job<ZaloZnsBatchJobData>): Promise<void> {
    const { oaId, messages, campaignId, batchIndex, totalBatches } = job.data;

    this.logger.log(
      `Processing batch ZNS ${(batchIndex ?? 0) + 1}/${totalBatches ?? 1} with ${messages.length} messages`,
    );

    // Lấy access token cho OA
    const accessToken = await this.getAccessToken(oaId);

    // Gửi từng tin nhắn trong batch
    for (const message of messages) {
      try {
        const { phone, templateId, templateData, messageType, trackingId } =
          message;

        // Xử lý theo loại tin nhắn
        switch (messageType) {
          case ZnsMessageType.PROMOTION:
            await this.handlePromotionMessage(
              accessToken,
              phone,
              templateId,
              templateData,
              trackingId,
            );
            break;
          case ZnsMessageType.TRANSACTION:
            await this.handleTransactionMessage(
              accessToken,
              phone,
              templateId,
              templateData,
              trackingId,
            );
            break;
          case ZnsMessageType.CUSTOMER_CARE:
            await this.handleCustomerCareMessage(
              accessToken,
              phone,
              templateId,
              templateData,
              trackingId,
            );
            break;
          default:
            await this.handleDefaultMessage(
              accessToken,
              phone,
              templateId,
              templateData,
              trackingId,
            );
            break;
        }
      } catch (error) {
        this.logger.error(
          `Failed to send ZNS to ${message.phone}: ${error.message}`,
        );
        // Tiếp tục gửi cho tin nhắn khác
      }
    }
  }

  /**
   * Xử lý tin nhắn truyền thông (PROMOTION)
   */
  private async handlePromotionMessage(
    accessToken: string,
    phone: string,
    templateId: string,
    templateData: Record<string, any>,
    trackingId?: string,
  ): Promise<void> {
    this.logger.log(
      `Sending promotion message to ${phone} with template ${templateId}`,
    );

    // Chuyển đổi phone thành userId nếu cần
    const userId = await this.getZaloUserIdFromPhone(accessToken, phone);

    // Sử dụng ZaloPromotionService để gửi tin truyền thông
    const result = await this.zaloPromotionService.sendPromotionMessage(
      accessToken,
      userId,
      templateId,
      templateData,
    );

    this.logger.log(
      `Promotion message sent successfully. Message ID: ${result.message_id}, Tracking ID: ${trackingId}`,
    );
  }

  /**
   * Xử lý tin nhắn giao dịch (TRANSACTION)
   */
  private async handleTransactionMessage(
    accessToken: string,
    phone: string,
    templateId: string,
    templateData: Record<string, any>,
    trackingId?: string,
  ): Promise<void> {
    this.logger.log(
      `Sending transaction message to ${phone} with template ${templateId}`,
    );

    // Sử dụng ZaloZnsService để gửi tin giao dịch
    const result = await this.zaloZnsService.sendZnsMessage(accessToken, {
      phone,
      template_id: templateId,
      template_data: templateData,
      tracking_id: trackingId,
    });

    this.logger.log(
      `Transaction message sent successfully. Message ID: ${result.message_id}, Tracking ID: ${trackingId}`,
    );
  }

  /**
   * Xử lý tin nhắn chăm sóc khách hàng (CUSTOMER_CARE)
   */
  private async handleCustomerCareMessage(
    accessToken: string,
    phone: string,
    templateId: string,
    templateData: Record<string, any>,
    trackingId?: string,
  ): Promise<void> {
    this.logger.log(
      `Sending customer care message to ${phone} with template ${templateId}`,
    );

    // Sử dụng ZaloZnsService để gửi tin chăm sóc khách hàng
    const result = await this.zaloZnsService.sendZnsMessage(accessToken, {
      phone,
      template_id: templateId,
      template_data: templateData,
      tracking_id: trackingId,
    });

    this.logger.log(
      `Customer care message sent successfully. Message ID: ${result.message_id}, Tracking ID: ${trackingId}`,
    );
  }

  /**
   * Xử lý tin nhắn mặc định
   */
  private async handleDefaultMessage(
    accessToken: string,
    phone: string,
    templateId: string,
    templateData: Record<string, any>,
    trackingId?: string,
  ): Promise<void> {
    this.logger.log(
      `Sending default message to ${phone} with template ${templateId}`,
    );

    // Sử dụng ZaloZnsService để gửi tin nhắn mặc định
    const result = await this.zaloZnsService.sendZnsMessage(accessToken, {
      phone,
      template_id: templateId,
      template_data: templateData,
      tracking_id: trackingId,
    });

    this.logger.log(
      `Default message sent successfully. Message ID: ${result.message_id}, Tracking ID: ${trackingId}`,
    );
  }

  /**
   * Lấy access token cho Official Account
   */
  private async getAccessToken(oaId: string): Promise<string> {
    // TODO: Implement logic để lấy access token từ database hoặc cache
    // Hiện tại trả về từ config hoặc throw error
    const accessToken = this.configService.get<string>(
      `ZALO_OA_${oaId}_ACCESS_TOKEN`,
    );

    if (!accessToken) {
      throw new Error(`Access token not found for OA ${oaId}`);
    }

    return accessToken;
  }

  /**
   * Chuyển đổi số điện thoại thành Zalo User ID
   * Cần thiết cho tin nhắn truyền thông
   */
  private async getZaloUserIdFromPhone(
    accessToken: string,
    phone: string,
  ): Promise<string> {
    try {
      // TODO: Implement API call để lấy user ID từ phone number
      // Hiện tại sử dụng phone number làm user ID tạm thời
      this.logger.warn(
        `Using phone ${phone} as user ID. Should implement proper phone-to-userId conversion.`,
      );
      return phone;
    } catch (error) {
      this.logger.error(
        `Failed to get user ID for phone ${phone}: ${error.message}`,
      );
      throw new Error(`Cannot get Zalo user ID for phone ${phone}`);
    }
  }

  /**
   * Chia mảng thành các chunk nhỏ hơn
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
