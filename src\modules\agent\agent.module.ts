import { Module } from '@nestjs/common';
import { AgentSystemModule } from './agent-system/agent-system.module';
import { AgentUserModule } from './agent-user/agent-user.module';
import { AgentAssistantModule } from './agent-assistant/agent-assistant.module';

/**
 * Module for worker-related functionality
 */
@Module({
  imports: [AgentSystemModule, AgentUserModule, AgentAssistantModule],
})
export class AgentModule {}
