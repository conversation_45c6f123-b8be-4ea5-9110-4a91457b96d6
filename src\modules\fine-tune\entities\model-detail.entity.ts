import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng model_detail trong cơ sở dữ liệu
 * Lưu trữ metadata chi tiết của model
 */
@Entity('model_detail')
export class ModelDetail {
  /**
   * UUID định danh duy nhất cho model detail
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Metadata của model dưới dạng JSONB
   */
  @Column({
    type: 'jsonb',
    nullable: true,
    comment: 'Metadata của model dưới dạng JSONB'
  })
  metadata: Record<string, any> | null;
}
