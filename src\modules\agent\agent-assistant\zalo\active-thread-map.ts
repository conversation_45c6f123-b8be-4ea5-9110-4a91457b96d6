const activeThread = new Map<string, AbortController>();

function getActiveThread(threadId: string): AbortController | undefined {
  return activeThread.get(threadId);
}

function setActiveThread(threadId: string, abortController: AbortController): void {
  activeThread.set(threadId, abortController);
}

function deleteActiveThread(threadId: string): boolean {
  return activeThread.delete(threadId);
}

export {
    getActiveThread,
    setActiveThread,
    deleteActiveThread,
}