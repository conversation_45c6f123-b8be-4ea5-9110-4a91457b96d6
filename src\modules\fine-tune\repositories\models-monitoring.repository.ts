import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Models } from '../entities/models.entity';
import { ModelDetail } from '../entities/model-detail.entity';
import { ProviderFineTuneEnum } from '../constants/provider.enum';

/**
 * Interface cho thông tin model cần thiết để monitoring
 */
export interface ModelMonitoringInfo {
  /**
   * ID của model
   */
  modelId: string;

  /**
   * ID của model detail
   */
  detailId: string;

  /**
   * ID người dùng (nullable cho system models)
   */
  userId: number | null;

  /**
   * Metadata từ model detail
   */
  metadata: {
    jobId: string;
    provider: string;
    status: string;
    baseModelId: string;
    [key: string]: any;
  };

  /**
   * Trạng thái active của model
   */
  active: boolean;
}

/**
 * Repository để xử lý monitoring fine-tune models
 */
@Injectable()
export class ModelsMonitoringRepository {
  private readonly logger = new Logger(ModelsMonitoringRepository.name);

  constructor(
    @InjectRepository(Models)
    private readonly modelsRepository: Repository<Models>,
    @InjectRepository(ModelDetail)
    private readonly modelDetailRepository: Repository<ModelDetail>,
  ) {}

  /**
   * Lấy thông tin model để monitoring
   */
  async getModelMonitoringInfo(historyId: string): Promise<ModelMonitoringInfo | null> {
    try {
      // Tìm model theo ID
      const model = await this.modelsRepository.findOne({
        where: { id: historyId },
      });

      if (!model) {
        this.logger.warn(`Model not found with ID: ${historyId}`);
        return null;
      }

      if (!model.detailId) {
        this.logger.warn(`Model ${historyId} does not have detailId`);
        return null;
      }

      // Lấy model detail
      const modelDetail = await this.modelDetailRepository.findOne({
        where: { id: model.detailId },
      });

      if (!modelDetail || !modelDetail.metadata) {
        this.logger.warn(`Model detail not found or no metadata for model ${historyId}`);
        return null;
      }

      return {
        modelId: model.id,
        detailId: model.detailId,
        userId: model.userId,
        metadata: modelDetail.metadata as any, // Cast to any vì metadata có thể có structure khác nhau
        active: model.active,
      };
    } catch (error) {
      this.logger.error(`Error getting model monitoring info for ${historyId}`, {
        error: error.message,
        stack: error.stack,
      });
      return null;
    }
  }

  /**
   * Cập nhật model_id khi fine-tune thành công
   */
  async updateModelIdOnSuccess(
    historyId: string,
    fineTunedModelId: string,
    metadata?: Record<string, any>,
  ): Promise<boolean> {
    try {
      // Cập nhật model_id và active = true
      await this.modelsRepository.update(historyId, {
        modelId: fineTunedModelId,
        active: true,
      });

      // Cập nhật metadata trong model detail nếu có
      if (metadata) {
        const model = await this.modelsRepository.findOne({
          where: { id: historyId },
        });

        if (model?.detailId) {
          const currentDetail = await this.modelDetailRepository.findOne({
            where: { id: model.detailId },
          });

          if (currentDetail) {
            const updatedMetadata = {
              ...currentDetail.metadata,
              ...metadata,
              status: 'succeeded',
              fineTunedModelId,
              completedAt: Date.now(),
            };

            await this.modelDetailRepository.update(model.detailId, {
              metadata: updatedMetadata as any,
            });
          }
        }
      }

      this.logger.log(`Successfully updated model ${historyId} with fine-tuned model ID: ${fineTunedModelId}`);
      return true;
    } catch (error) {
      this.logger.error(`Error updating model ${historyId} on success`, {
        error: error.message,
        stack: error.stack,
        fineTunedModelId,
      });
      return false;
    }
  }

  /**
   * Cập nhật status khi fine-tune thất bại
   */
  async updateModelOnFailure(
    historyId: string,
    errorMessage: string,
    metadata?: Record<string, any>,
  ): Promise<boolean> {
    try {
      const model = await this.modelsRepository.findOne({
        where: { id: historyId },
      });

      if (!model?.detailId) {
        this.logger.warn(`Model ${historyId} not found or no detailId`);
        return false;
      }

      // Cập nhật metadata trong model detail
      const currentDetail = await this.modelDetailRepository.findOne({
        where: { id: model.detailId },
      });

      if (currentDetail) {
        const updatedMetadata = {
          ...currentDetail.metadata,
          ...metadata,
          status: 'failed',
          error: errorMessage,
          failedAt: Date.now(),
        };

        await this.modelDetailRepository.update(model.detailId, {
          metadata: updatedMetadata as any,
        });
      }

      this.logger.log(`Updated model ${historyId} status to failed: ${errorMessage}`);
      return true;
    } catch (error) {
      this.logger.error(`Error updating model ${historyId} on failure`, {
        error: error.message,
        stack: error.stack,
        errorMessage,
      });
      return false;
    }
  }

  /**
   * Cập nhật status trong quá trình polling
   */
  async updatePollingStatus(
    historyId: string,
    status: string,
    metadata?: Record<string, any>,
  ): Promise<boolean> {
    try {
      const model = await this.modelsRepository.findOne({
        where: { id: historyId },
      });

      if (!model?.detailId) {
        this.logger.warn(`Model ${historyId} not found or no detailId`);
        return false;
      }

      // Cập nhật metadata trong model detail
      const currentDetail = await this.modelDetailRepository.findOne({
        where: { id: model.detailId },
      });

      if (currentDetail) {
        const updatedMetadata = {
          ...currentDetail.metadata,
          ...metadata,
          status,
          lastPolledAt: Date.now(),
        };

        await this.modelDetailRepository.update(model.detailId, {
          metadata: updatedMetadata as any,
        });
      }

      this.logger.debug(`Updated polling status for model ${historyId}: ${status}`);
      return true;
    } catch (error) {
      this.logger.error(`Error updating polling status for model ${historyId}`, {
        error: error.message,
        stack: error.stack,
        status,
      });
      return false;
    }
  }

  /**
   * Kiểm tra xem model có đang được monitoring không
   */
  async isModelBeingMonitored(historyId: string): Promise<boolean> {
    try {
      const modelInfo = await this.getModelMonitoringInfo(historyId);
      
      if (!modelInfo) {
        return false;
      }

      // Kiểm tra status trong metadata
      const status = modelInfo.metadata.status;

      // Đảm bảo status là string trước khi check
      if (typeof status !== 'string' || !status) {
        return false;
      }

      // Kiểm tra xem status có phải là trạng thái cần tiếp tục monitoring không
      return !['succeeded', 'failed', 'cancelled'].includes(status);
    } catch (error) {
      this.logger.error(`Error checking if model ${historyId} is being monitored`, {
        error: error.message,
        stack: error.stack,
      });
      return false;
    }
  }
}
