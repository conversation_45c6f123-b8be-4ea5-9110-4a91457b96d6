import * as crypto from 'crypto';
import { env } from '../../../config';

/**
 * @file Encryption helper for web search API keys
 * This module provides stateless functions for encrypting and decrypting API keys
 * specifically used for OpenAI web search operations. Uses the same algorithm as
 * other encryption helpers but with a different encryption key for security isolation.
 */

// --- Configuration and Constants ---

const ALGORITHM: string = 'aes-256-cbc';
const ENCODING: BufferEncoding = 'hex';
const TEXT_ENCODING: crypto.Encoding = 'utf8';

// Web search-specific encryption key (different from LLM and embedding API keys)
const WEBSEARCH_SECRET: string =
  env.webSearchSystemEncryptionKey.WEBSEARCH_SECRET_KEY;

// --- Private Helper Functions ---

/**
 * Decrypt an encrypted string using AES-256-CBC
 * @param {string} encryptedText - The encrypted string with IV prefix
 * @param {string} secret - The secret used to derive the decryption key
 * @returns {string} The original plain text
 */
const decrypt = (encryptedText: string, secret: string): string => {
  // Create key from secretKey by hashing with SHA-256
  const key = crypto
    .createHash('sha256')
    .update(secret)
    .digest('base64')
    .substring(0, 32);

  // Split IV and encrypted data
  const [ivHex, encryptedHex] = encryptedText.split(':');
  const iv = Buffer.from(ivHex, ENCODING);
  const encrypted = Buffer.from(encryptedHex, ENCODING);

  // Create decipher
  const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);

  // Decrypt
  let decrypted = decipher.update(encrypted, undefined, TEXT_ENCODING);
  decrypted += decipher.final(TEXT_ENCODING);

  return decrypted;
};

/**
 * Encrypt a plain text string using AES-256-CBC
 * @param {string} plainText - The plain text to encrypt
 * @param {string} secret - The secret used to derive the encryption key
 * @returns {string} The encrypted string with IV prefix
 */
const encrypt = (plainText: string, secret: string): string => {
  // Create key from secretKey by hashing with SHA-256
  const key = crypto
    .createHash('sha256')
    .update(secret)
    .digest('base64')
    .substring(0, 32);

  // Generate random IV
  const iv = crypto.randomBytes(16);

  // Create cipher
  const cipher = crypto.createCipheriv(ALGORITHM, key, iv);

  // Encrypt
  let encrypted = cipher.update(plainText, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  // Return IV:encrypted format
  return `${iv.toString('hex')}:${encrypted}`;
};

// --- Public API Functions ---

/**
 * Decrypts a web search API key
 * @param {string} encryptedApiKey The encrypted API key
 * @returns {string} The original API key
 * @throws {Error} If the web search secret is not configured
 */
const decryptWebSearchApiKey = (encryptedApiKey: string): string => {
  if (!WEBSEARCH_SECRET) {
    throw new Error('WEBSEARCH_SECRET_KEY is not configured.');
  }
  return decrypt(encryptedApiKey, WEBSEARCH_SECRET);
};

/**
 * Encrypts a web search API key
 * @param {string} plainApiKey The plain text API key
 * @returns {string} The encrypted API key
 * @throws {Error} If the web search secret is not configured
 */
const encryptWebSearchApiKey = (plainApiKey: string): string => {
  if (!WEBSEARCH_SECRET) {
    throw new Error('WEBSEARCH_SECRET_KEY is not configured.');
  }
  return encrypt(plainApiKey, WEBSEARCH_SECRET);
};

export const webSearchKeyEncryption = {
  decryptWebSearchApiKey,
  encryptWebSearchApiKey,
};
