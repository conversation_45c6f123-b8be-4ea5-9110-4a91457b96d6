import { ZnsMessageType } from '../dto';

/**
 * Interface cho job gửi ZNS đơn lẻ
 */
export interface ZaloZnsSingleJobData {
  /**
   * ID Official Account
   */
  oaId: string;

  /**
   * Số điện thoại nhận
   */
  phone: string;

  /**
   * ID template ZNS
   */
  templateId: string;

  /**
   * Dữ liệu cho template
   */
  templateData: Record<string, any>;

  /**
   * ID chiến dịch (nếu có)
   */
  campaignId?: number;

  /**
   * Tracking ID (tùy chọn)
   */
  trackingId?: string;

  /**
   * Loại tin nhắn
   */
  messageType?: ZnsMessageType;

  /**
   * Số lần retry
   */
  retryCount?: number;
}

/**
 * Interface cho job gửi ZNS theo chiến dịch
 */
export interface ZaloZnsCampaignJobData {
  /**
   * ID chiến dịch
   */
  campaignId: number;

  /**
   * ID Official Account
   */
  oaId: string;

  /**
   * ID template ZNS
   */
  templateId: string;

  /**
   * Dữ liệu cho template
   */
  templateData: Record<string, any>;

  /**
   * Danh sách số điện thoại
   */
  phoneList: string[];

  /**
   * Loại tin nhắn
   */
  messageType?: ZnsMessageType;

  /**
   * Batch size (số tin nhắn gửi cùng lúc)
   */
  batchSize?: number;

  /**
   * Delay giữa các batch (milliseconds)
   */
  batchDelay?: number;
}

/**
 * Interface cho job gửi batch ZNS
 */
export interface ZaloZnsBatchJobData {
  /**
   * ID Official Account
   */
  oaId: string;

  /**
   * Danh sách tin nhắn ZNS
   */
  messages: {
    phone: string;
    templateId: string;
    templateData: Record<string, any>;
    trackingId?: string;
    messageType?: ZnsMessageType;
  }[];

  /**
   * ID chiến dịch (nếu có)
   */
  campaignId?: number;

  /**
   * Batch index (thứ tự batch)
   */
  batchIndex?: number;

  /**
   * Tổng số batch
   */
  totalBatches?: number;
}

/**
 * Union type cho tất cả các loại job data
 */
export type ZaloZnsJobData =
  | ZaloZnsSingleJobData
  | ZaloZnsCampaignJobData
  | ZaloZnsBatchJobData;
