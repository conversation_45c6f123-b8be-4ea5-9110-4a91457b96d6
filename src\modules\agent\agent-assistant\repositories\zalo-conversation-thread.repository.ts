import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ZaloConversationThread } from '../entities/zalo-conversation-thread.entity';
import { ThreadStatusEnum } from '../enums/thread-status.enum';

@Injectable()
export class ZaloConversationThreadRepository {
  constructor(
    @InjectRepository(ZaloConversationThread)
    private readonly repository: Repository<ZaloConversationThread>,
  ) {}

  /**
   * Get existing active thread by sender and recipient IDs
   * @param userId Internal user ID
   * @param customerId Customer ID
   * @param zaloUserId Zalo sender ID
   * @param oaId Zalo recipient ID (OA ID)
   * @returns Existing thread or null
   */
  async getActiveThread(
    userId: number,
    zaloCustomerId: string,
    zaloUserId: string,
    oaId: string
  ): Promise<ZaloConversationThread | null> {
    return this.repository.findOne({
      where: {
        userId,
        zaloCustomerId,
        status: ThreadStatusEnum.ACTIVE,
      },
    });
  }

  /**
   * Create new conversation thread
   * @param threadData Thread data to create
   * @returns Created thread record
   */
  async createThread(threadData: Partial<ZaloConversationThread>): Promise<ZaloConversationThread> {
    const thread = this.repository.create(threadData);
    return this.repository.save(thread);
  }

  /**
   * Update thread's last message timestamp and increment count
   * @param senderId Zalo sender ID
   * @param recipientId Zalo recipient ID (OA ID)
   */
  async updateLastMessage(threadId: string): Promise<void> {
    await this.repository.update(
      { id: threadId },
      {
        lastMessageAt: Date.now(),
        messageCount: () => 'message_count + 1',
      }
    );
  }

  /**
   * Find thread by UUID
   * @param threadId Thread UUID
   * @returns Thread entity or null if not found
   */
  async findById(threadId: string): Promise<ZaloConversationThread | null> {
    return await this.repository.findOne({
      where: { id: threadId },
    });
  }
}
