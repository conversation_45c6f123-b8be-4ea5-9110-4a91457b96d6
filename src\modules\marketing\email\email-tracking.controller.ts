import { <PERSON>, <PERSON>, Param, Req, <PERSON><PERSON>, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { EmailTrackingService } from './services';
import { EmailClickTrackingService } from './services/email-click-tracking.service';

/**
 * Controller xử lý tracking email
 */
@Controller('api/email-tracking')
export class EmailTrackingController {
  private readonly logger = new Logger(EmailTrackingController.name);

  constructor(
    private readonly emailTrackingService: EmailTrackingService,
    private readonly emailClickTrackingService: EmailClickTrackingService,
  ) {}

  /**
   * Endpoint cho tracking pixel
   * @param trackingId ID tracking từ URL
   * @param req Request object
   * @param res Response object
   */
  @Get('pixel/:trackingId')
  async trackPixel(
    @Param('trackingId') trackingId: string,
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    try {
      // Lấy thông tin metadata từ request
      const metadata = {
        ip: req.ip || req.socket?.remoteAddress,
        userAgent: req.get('User-Agent'),
        referer: req.get('Referer'),
        timestamp: Date.now(),
      };

      // Track email opened
      await this.emailTrackingService.trackEmailOpened(trackingId, metadata);

      this.logger.debug(`Email opened tracked: ${trackingId}`);

      // Trả về ảnh pixel 1x1 transparent
      const pixelBuffer = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'base64',
      );

      res.set({
        'Content-Type': 'image/png',
        'Content-Length': pixelBuffer.length.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      });

      res.send(pixelBuffer);
    } catch (error) {
      this.logger.error(`Error tracking pixel: ${error.message}`, error.stack);

      // Vẫn trả về pixel để không làm hỏng email
      const pixelBuffer = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'base64',
      );

      res.set({
        'Content-Type': 'image/png',
        'Content-Length': pixelBuffer.length.toString(),
      });

      res.send(pixelBuffer);
    }
  }

  /**
   * Endpoint để track click links
   * @param trackingId ID tracking
   * @param url URL gốc cần redirect
   * @param req Request object
   * @param res Response object
   */
  @Get('click/:trackingId')
  async trackClick(
    @Param('trackingId') trackingId: string,
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const url = req.query.url as string;

      if (!url) {
        res.status(400).send('Missing URL parameter');
        return;
      }

      // Validate tracking ID format
      if (!this.isValidTrackingId(trackingId)) {
        this.logger.warn(`Invalid tracking ID format: ${trackingId}`);
        res.status(400).send('Invalid tracking ID');
        return;
      }

      // Decode URL nếu cần
      let decodedUrl: string;
      try {
        decodedUrl = decodeURIComponent(url);
      } catch {
        this.logger.warn(`Invalid URL encoding: ${url}`);
        res.status(400).send('Invalid URL encoding');
        return;
      }

      // Validate URL format
      if (!this.isValidUrl(decodedUrl)) {
        this.logger.warn(`Invalid URL format: ${decodedUrl}`);
        res.status(400).send('Invalid URL');
        return;
      }

      // Lấy thông tin metadata từ request
      const metadata = {
        ip: req.ip || req.socket?.remoteAddress,
        userAgent: req.get('User-Agent'),
        referer: req.get('Referer'),
        acceptLanguage: req.get('Accept-Language'),
        timestamp: Date.now(),
        method: req.method,
        protocol: req.protocol,
        host: req.get('Host'),
      };

      // Process click với advanced tracking
      const clickResult = await this.emailClickTrackingService.processClick(
        trackingId,
        decodedUrl,
        metadata,
      );

      // Log result
      if (clickResult.success) {
        this.logger.debug(
          `Email click tracked: ${trackingId} -> ${decodedUrl} (first: ${clickResult.isFirstClick})`,
        );
      } else if (clickResult.isRateLimited) {
        this.logger.warn(
          `Click rate limited: ${trackingId} from ${metadata.ip}`,
        );
      } else {
        this.logger.warn(
          `Click tracking failed: ${trackingId} - ${clickResult.error}`,
        );
      }

      // Set security headers trước khi redirect
      res.set({
        'X-Robots-Tag': 'noindex, nofollow',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      });

      // Redirect nếu được phép
      if (clickResult.shouldRedirect) {
        res.redirect(302, decodedUrl);
      } else {
        res.status(400).send('Click tracking failed');
      }
    } catch (error) {
      this.logger.error(`Error tracking click: ${error.message}`, error.stack);

      // Redirect về URL fallback nếu có lỗi
      const fallbackUrl = (req.query.url as string) || 'https://redai.com';
      try {
        const decodedFallback = decodeURIComponent(fallbackUrl);
        if (this.isValidUrl(decodedFallback)) {
          res.redirect(302, decodedFallback);
        } else {
          res.redirect(302, 'https://redai.com');
        }
      } catch {
        res.redirect(302, 'https://redai.com');
      }
    }
  }

  /**
   * Validate tracking ID format
   * @param trackingId Tracking ID to validate
   * @returns True if valid
   */
  private isValidTrackingId(trackingId: string): boolean {
    // Format: {campaignId}_{audienceId}_{timestamp}_{random}
    const trackingIdRegex = /^\d+_\d+_\d+_[a-z0-9]+$/;
    return trackingIdRegex.test(trackingId);
  }

  /**
   * Validate URL format
   * @param url URL to validate
   * @returns True if valid
   */
  private isValidUrl(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      return ['http:', 'https:'].includes(parsedUrl.protocol);
    } catch {
      return false;
    }
  }

  /**
   * Endpoint để lấy click analytics cho campaign
   * @param campaignId Campaign ID
   * @returns Click analytics data
   */
  @Get('analytics/clicks/:campaignId')
  async getClickAnalytics(@Param('campaignId') campaignId: string) {
    try {
      const id = parseInt(campaignId);
      if (isNaN(id)) {
        return {
          success: false,
          message: 'Invalid campaign ID',
        };
      }

      const analytics = await this.emailTrackingService.getClickAnalytics(id);

      return {
        success: true,
        data: analytics,
      };
    } catch (error) {
      this.logger.error(
        `Error getting click analytics: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        message: error.message,
      };
    }
  }

  /**
   * Endpoint để lấy click heatmap cho campaign
   * @param campaignId Campaign ID
   * @returns Click heatmap data
   */
  @Get('analytics/heatmap/:campaignId')
  async getClickHeatmap(@Param('campaignId') campaignId: string) {
    try {
      const id = parseInt(campaignId);
      if (isNaN(id)) {
        return {
          success: false,
          message: 'Invalid campaign ID',
        };
      }

      const heatmap = await this.emailTrackingService.getClickHeatmap(id);

      return {
        success: true,
        data: heatmap,
      };
    } catch (error) {
      this.logger.error(
        `Error getting click heatmap: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        message: error.message,
      };
    }
  }

  /**
   * Endpoint để lấy advanced click statistics
   * @param campaignId Campaign ID
   * @returns Advanced click statistics
   */
  @Get('analytics/click-stats/:campaignId')
  async getAdvancedClickStats(@Param('campaignId') campaignId: string) {
    try {
      const id = parseInt(campaignId);
      if (isNaN(id)) {
        return {
          success: false,
          message: 'Invalid campaign ID',
        };
      }

      const stats = await this.emailClickTrackingService.getClickStatistics(id);

      return {
        success: true,
        data: stats,
      };
    } catch (error) {
      this.logger.error(
        `Error getting advanced click stats: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        message: error.message,
      };
    }
  }
}
