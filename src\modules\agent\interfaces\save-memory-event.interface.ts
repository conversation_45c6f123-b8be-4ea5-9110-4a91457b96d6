import { MemoryItem } from './memory-item.interface';

/**
 * @file Save Memory Event Interface
 *
 * Defines the structure for events related to saving or updating memory data during
 * asynchronous processing. This interface is used with NestJS EventEmitter for
 * handling memory operations in a non-blocking manner.
 */

/**
 * Event types for memory operations
 *
 * @description Defines the possible types of memory operations that can be performed.
 * Used to distinguish between different kinds of memory events in the system.
 */
export enum MemoryEventType {
  /**
   * Save a new memory item
   */
  SAVE = 'save',
  /**
   * Update an existing memory item
   */
  UPDATE = 'update',
}

/**
 * Event structure for memory save operations
 *
 * @interface SaveMemoryEvent
 * @description Standardizes the structure of events emitted when memory operations
 * need to be performed asynchronously. Used with NestJS EventEmitter pattern.
 *
 * @example
 * ```typescript
 * const saveEvent: SaveMemoryEvent = {
 *   userId: "user_123",
 *   memoryItem: {
 *     id: "mem_456",
 *     content: "User prefers dark mode",
 *     timestamp: new Date(),
 *     metadata: { source: "conversation", importance: 8 }
 *   },
 *   eventType: MemoryEventType.SAVE,
 *   requestId: "req_789",
 *   priority: "high"
 * };
 *
 * // Emit the event
 * this.eventEmitter.emit('memory.save', saveEvent);
 * ```
 */
export interface SaveMemoryEvent {
  /**
   * User ID associated with the memory operation
   *
   * @type {string}
   * @description Identifies which user the memory belongs to.
   * Used for user-specific memory storage and retrieval.
   * Should be a valid user identifier from the system.
   */
  userId: string;

  /**
   * Memory item to be processed
   *
   * @type {MemoryItem}
   * @description The memory data that needs to be saved.
   * Contains all the necessary information including content and timestamp.
   */
  memoryItem: MemoryItem;

  /**
   * Type of memory operation to perform
   *
   * @type {MemoryEventType}
   * @description Specifies what kind of operation should be performed on the memory item.
   * Determines how the event handler will process the memory data.
   */
  eventType: MemoryEventType;

  /**
   * Unique request identifier for tracking (optional)
   *
   * @type {string | undefined}
   * @description Used for correlating events with specific requests or operations.
   * Helpful for debugging, logging, and tracking the flow of memory operations.
   */
  requestId?: string;

  /**
   * Timestamp when the event was created (optional)
   *
   * @type {Date | undefined}
   * @description When the event was emitted, used for event processing metrics and debugging.
   * If not provided, the event processor should set it to the current time.
   */
  eventTimestamp?: Date;
}

/**
 * Individual memory update item for bulk operations
 */
export interface MemoryUpdateItem {
  /**
   * ID of the memory to update
   */
  memoryId: string;

  /**
   * Type of memory (user or agent)
   */
  memoryType: 'user' | 'agent';

  /**
   * What fields to update
   */
  operation: 'content' | 'title' | 'reason' | 'all';

  /**
   * New values for the memory fields
   */
  updates: {
    content?: string;
    title?: string;
    reason?: string;
  };
}

/**
 * Event structure for bulk memory update operations
 *
 * @interface UpdateMemoryEvent
 * @description Standardizes the structure of events emitted when memory update operations
 * need to be performed asynchronously. Supports bulk updates for efficiency.
 *
 * @example
 * ```typescript
 * const updateEvent: UpdateMemoryEvent = {
 *   memoryItems: [
 *     {
 *       memoryId: "mem_456",
 *       memoryType: "user",
 *       operation: "content",
 *       updates: { content: "Updated user preference" }
 *     },
 *     {
 *       memoryId: "mem_789",
 *       memoryType: "agent",
 *       operation: "title",
 *       updates: { title: "Updated title" }
 *     }
 *   ],
 *   agentId: "agent_123",
 *   userId: "user_789",
 *   eventType: MemoryEventType.UPDATE,
 *   requestId: "req_update_batch_101"
 * };
 *
 * // Emit the event
 * this.eventEmitter.emit('memory.update.batch', updateEvent);
 * ```
 */
export interface UpdateMemoryEvent {
  /**
   * Array of memory items to update
   *
   * @type {MemoryUpdateItem[]}
   * @description List of memory updates to process in a single batch operation.
   */
  memoryItems: MemoryUpdateItem[];

  /**
   * ID of the agent performing the updates
   *
   * @type {string}
   * @description Identifies which agent is requesting the memory updates.
   */
  agentId: string;

  /**
   * ID of the user (required for user memory updates)
   *
   * @type {string | undefined}
   * @description User ID, required when updating user memories.
   */
  userId?: string;

  /**
   * Type of memory operation to perform
   *
   * @type {MemoryEventType}
   * @description Should be MemoryEventType.UPDATE for update operations.
   */
  eventType: MemoryEventType;

  /**
   * Unique request identifier for tracking (optional)
   *
   * @type {string | undefined}
   * @description Used for correlating events with specific requests or operations.
   */
  requestId?: string;

  /**
   * Timestamp when the event was created (optional)
   *
   * @type {Date | undefined}
   * @description When the event was emitted, used for event processing metrics and debugging.
   */
  eventTimestamp?: Date;
}
