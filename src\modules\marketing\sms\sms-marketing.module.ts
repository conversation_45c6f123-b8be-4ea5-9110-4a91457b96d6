import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HttpModule, HttpService } from '@nestjs/axios';
import Redis from 'ioredis';
import { env } from '../../../config';
import { QueueName } from '../../../queue';
import { SmsCampaignUser } from '../entities/sms-campaign-user.entity';
import { UserTemplateSms } from '../entities/user-template-sms.entity';
import { SmsServerConfiguration } from '../entities/sms-server-configuration.entity';
import { UserAudienceCustomField } from '../entities/user-audience-custom-field.entity';
import { SmsMarketingHistory } from '../entities/sms-marketing-history.entity';
import { SmsCampaignAdmin } from '../entities/sms-campaign-admin.entity';
import { SmsCampaignProcessor } from './sms-campaign.processor';
import { SmsMarketingService, SmsTemplateService } from './services';
import {
  FptSmsBrandnameService,
  FptSmsConfig,
} from '../../../shared/services/sms/fpt-sms-brandname.service';
import { SmsModule } from '../../../shared/services/sms/sms.module';
import { SmsSystemModule } from '../../sms_system/sms-system.module';

/**
 * Module xử lý SMS marketing campaigns
 */
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([
      SmsCampaignUser,
      SmsCampaignAdmin,
      UserTemplateSms,
      SmsServerConfiguration,
      UserAudienceCustomField,
      SmsMarketingHistory,
    ]),
    BullModule.registerQueue({
      name: QueueName.SMS,
    }),
    HttpModule.register({
      timeout: 30000, // 30 seconds timeout
      maxRedirects: 5,
    }),
    SmsModule,
    SmsSystemModule,
  ],
  providers: [
    SmsCampaignProcessor,
    SmsMarketingService,
    SmsTemplateService,
    {
      provide: Redis,
      useFactory: () => {
        return new Redis(env.external.REDIS_URL);
      },
    },
  ],
  exports: [SmsMarketingService, SmsTemplateService, SmsCampaignProcessor],
})
export class SmsMarketingModule {}
