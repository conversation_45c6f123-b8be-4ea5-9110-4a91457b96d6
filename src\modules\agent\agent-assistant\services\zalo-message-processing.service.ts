import { Injectable, Logger } from '@nestjs/common';
import { ZaloAiMessageRepository } from '../repositories/zalo-ai-message.repository';
import { ZaloConversationThreadRepository } from '../repositories/zalo-conversation-thread.repository';
import { ZaloMediaRepository } from '../repositories/zalo-media.repository';
import { ZaloThreadMediaContextRepository } from '../repositories/zalo-thread-media-context.repository';
import { ZaloAiMessage, ZaloMedia, ZaloThreadMediaContext } from '../entities';
import { MessageDirectionEnum, MessageTypeEnum } from '../enums/message.enum';
import { ZaloThreadMediaContextTypeEnum } from '../enums/zalo-thread-media-context.enum';
import { S3Service } from '../../../../infra/s3/s3.service';
import {
  ZaloWebhookDto,
  UserSendTextWebhookDto,
  UserSendImageWebhookDto,
  UserSendStickerWebhookDto,
  UserSendLinkWebhookDto
} from '../../../../shared/dto/zalo-webhook-v2.dto';

/**
 * Service for processing and saving Zalo messages
 * Handles message parsing, validation, and database operations
 */
@Injectable()
export class ZaloMessageProcessingService {
  private readonly logger = new Logger(ZaloMessageProcessingService.name);

  /**
   * Function map for message processors - cleaner than switch statements
   */
  private readonly messageProcessors = {
    'user_send_text': this.processTextMessage.bind(this),
    'user_send_image': this.processImageMessage.bind(this),
    'user_send_sticker': this.processStickerMessage.bind(this),
    'user_send_link': this.processLinkMessage.bind(this),
  } as const;

  constructor(
    private readonly zaloMessageRepository: ZaloAiMessageRepository,
    private readonly zaloThreadRepository: ZaloConversationThreadRepository,
    private readonly zaloMediaRepository: ZaloMediaRepository,
    private readonly zaloThreadMediaContextRepository: ZaloThreadMediaContextRepository,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Save incoming message to database
   * @param threadId Thread ID in LangGraph format "zalo:userId:uuid"
   * @param messageData Webhook message data
   * @param oaId External Zalo Official Account ID (string)
   * @param customerUuid Customer UUID from zalo_customers table
   * @param internalOaId Internal database ID of the OA (number)
   * @returns Message ID
   */
  async saveIncomingMessage(
    threadId: string,
    messageData: ZaloWebhookDto,
    oaId: string,
    customerUuid: string,
    internalOaId: number
  ): Promise<string> {
    // 1. Extract UUID from LangGraph thread ID format
    const threadUuid = this.extractThreadUuid(threadId);

    // 2. Verify thread exists by UUID
    await this.validateThreadExists(threadUuid);

    // 3. Process message by type using function map
    return this.processMessageByType(messageData, threadUuid, oaId, customerUuid, internalOaId);
  }

  /**
   * Process message by type using function map pattern
   * @param messageData Webhook message data
   * @param threadUuid Thread UUID
   * @param oaId Official Account ID
   * @param customerUuid Customer UUID
   * @param internalOaId Internal database ID of the OA
   * @returns Message ID
   */
  private async processMessageByType(
    messageData: ZaloWebhookDto,
    threadUuid: string,
    oaId: string,
    customerUuid: string,
    internalOaId: number
  ): Promise<string> {
    const processor = this.messageProcessors[messageData.event_name];

    if (!processor) {
      throw new Error(`Unsupported message type: ${messageData.event_name}`);
    }

    this.logger.debug(`Processing ${messageData.event_name} message for thread ${threadUuid}`);
    return processor(messageData, threadUuid, oaId, customerUuid, internalOaId);
  }

  /**
   * Process text message
   * @param messageData Text message webhook data
   * @param threadUuid Thread UUID
   * @param oaId Official Account ID (unused for text messages)
   * @param zaloUserId Zalo User ID (unused for text messages)
   * @returns Message ID
   */
  private async processTextMessage(
    messageData: ZaloWebhookDto,
    threadUuid: string,
    oaId: string,
    customerUuid: string,
    internalOaId: number
  ): Promise<string> {
    // 1. Type guard and extract text content
    if (messageData.event_name !== 'user_send_text') {
      throw new Error(`Expected text message, got ${messageData.event_name}`);
    }

    const textWebhook = messageData as UserSendTextWebhookDto;
    const content = textWebhook.message.text;

    if (!content || content.trim().length === 0) {
      throw new Error('Text content is required for text messages');
    }

    // 2. Create message record
    const messageDataToSave: Partial<ZaloAiMessage> = {
      threadId: threadUuid,
      messageId: textWebhook.message.msg_id,
      direction: MessageDirectionEnum.INCOMING,
      messageType: MessageTypeEnum.TEXT,
      rawWebhookData: messageData,
      content: content.trim(),
      mediaIds: [], // Empty for text messages
      createdAt: Date.now(),
    };

    // 3. Save and return message ID
    const savedMessage = await this.zaloMessageRepository.saveZaloMessage(messageDataToSave);

    this.logger.debug(`Saved text message ${savedMessage.id} for thread ${threadUuid}`);
    return savedMessage.id;
  }

  /**
   * Process image message - downloads images, uploads to S3, creates media records
   * @param messageData Image message webhook data
   * @param threadUuid Thread UUID
   * @param oaId Official Account ID
   * @param zaloUserId Zalo User ID (sender)
   * @returns Message ID
   */
  private async processImageMessage(
    messageData: UserSendImageWebhookDto,
    threadUuid: string,
    oaId: string,
    customerUuid: string,
    internalOaId: number
  ): Promise<string> {
    // 1. Extract and validate image attachments
    if (!messageData.message.attachments || messageData.message.attachments.length === 0) {
      throw new Error('Image attachments are required for image messages');
    }

    const imageAttachments = messageData.message.attachments;
    const mediaIds: string[] = [];

    // 2. Process each image attachment
    for (const attachment of imageAttachments) {
      if (!attachment.payload?.url) {
        this.logger.warn(`Skipping image attachment without URL`);
        continue;
      }

      try {
        // 3. Get file info from URL and HTTP headers
        const fileInfo = await this.getFileInfoFromUrl(attachment.payload.url);

        // 4. Generate S3 key with proper extension
        const timestamp = Date.now();
        const s3Key = `zalo-images/${threadUuid}/${messageData.message.msg_id}-${timestamp}-${Math.random().toString(36).substring(7)}.${fileInfo.extension}`;

        // 5. Download from Zalo and upload to S3
        this.logger.debug(`Uploading ${fileInfo.mimeType} image (${fileInfo.fileSize} bytes) from ${attachment.payload.url} to S3: ${s3Key}`);
        await this.s3Service.uploadFromUrlStreaming(attachment.payload.url, s3Key);

        // 6. Create ZaloMedia record with proper file info
        const mediaRecord = await this.zaloMediaRepository.create({
          zaloMediaId: `${messageData.message.msg_id}-${timestamp}`, // Unique identifier
          fileName: fileInfo.fileName,
          fileSize: fileInfo.fileSize,
          mimeType: fileInfo.mimeType,
          s3Key: s3Key,
          zaloCustomerId: customerUuid, // Customer UUID from zalo_customers table
          zaloOaId: internalOaId, // Internal database ID of the OA (number)
          createdAt: Date.now(),
        });

        // 6. Create ZaloThreadMediaContext record to link media to thread
        await this.zaloThreadMediaContextRepository.create({
          threadId: threadUuid,
          zaloMediaId: mediaRecord.id,
          contextType: ZaloThreadMediaContextTypeEnum.IMAGE,
          humanNotes: null,
          createdAt: Date.now(),
        });

        // 7. Add media ID to the list
        mediaIds.push(mediaRecord.id);

        this.logger.debug(`Successfully processed ${fileInfo.mimeType} image: ${mediaRecord.id} (${fileInfo.fileSize} bytes) -> S3: ${s3Key}`);

      } catch (error) {
        this.logger.error(`Failed to process image attachment: ${error.message}`, error.stack);
        // Continue processing other images even if one fails
      }
    }

    // 8. Create message record with media IDs
    const messageDataToSave: Partial<ZaloAiMessage> = {
      threadId: threadUuid,
      messageId: messageData.message.msg_id,
      direction: MessageDirectionEnum.INCOMING,
      messageType: MessageTypeEnum.IMAGE,
      rawWebhookData: messageData,
      content: undefined, // No text content for image messages
      mediaIds: mediaIds, // Array of ZaloMedia IDs
      createdAt: Date.now(),
    };

    // 9. Save and return message ID
    const savedMessage = await this.zaloMessageRepository.saveZaloMessage(messageDataToSave);

    this.logger.debug(`Saved image message ${savedMessage.id} with ${mediaIds.length} media files for thread ${threadUuid}`);
    return savedMessage.id;
  }

  private async processStickerMessage(
    messageData: UserSendStickerWebhookDto,
    threadUuid: string,
    oaId: string,
    customerUuid: string,
    internalOaId: number
  ): Promise<string> {
    // 1. Extract sticker information from attachments
    if (!messageData.message.attachments || messageData.message.attachments.length === 0) {
      throw new Error('Sticker attachments are required for sticker messages');
    }

    const stickerAttachment = messageData.message.attachments[0];

    if (!stickerAttachment.payload?.id) {
      throw new Error('Sticker ID is required in attachment payload');
    }

    // 2. Create sticker info content (JSON format for structured data)
    const stickerInfo = {
      stickerId: stickerAttachment.payload.id,
      category: stickerAttachment.payload.category || undefined,
      type: 'sticker'
    };

    // 3. Create message record
    const messageDataToSave: Partial<ZaloAiMessage> = {
      threadId: threadUuid,
      messageId: messageData.message.msg_id,
      direction: MessageDirectionEnum.INCOMING,
      messageType: MessageTypeEnum.STICKER,
      rawWebhookData: messageData,
      content: JSON.stringify(stickerInfo), // Store sticker info as JSON
      mediaIds: [], // No media IDs for stickers (no S3 persistence)
      createdAt: Date.now(),
    };

    // 4. Save and return message ID
    const savedMessage = await this.zaloMessageRepository.saveZaloMessage(messageDataToSave);

    this.logger.debug(`Saved sticker message ${savedMessage.id} (sticker ID: ${stickerInfo.stickerId}) for thread ${threadUuid}`);
    return savedMessage.id;
  }

  private async processLinkMessage(
    messageData: UserSendLinkWebhookDto,
    threadUuid: string,
    oaId: string,
    customerUuid: string,
    internalOaId: number
  ): Promise<string> {
    // 1. Extract link information from attachments
    if (!messageData.message.attachments || messageData.message.attachments.length === 0) {
      throw new Error('Link attachments are required for link messages');
    }

    const linkAttachment = messageData.message.attachments[0];

    if (!linkAttachment.payload?.url) {
      throw new Error('Link URL is required in attachment payload');
    }

    // 2. Create link info content (JSON format for structured data)
    const linkInfo = {
      url: linkAttachment.payload.url,
      thumbnail: linkAttachment.payload.thumbnail || undefined,
      description: linkAttachment.payload.description || undefined,
      type: 'link'
    };

    // 3. Create message record
    const messageDataToSave: Partial<ZaloAiMessage> = {
      threadId: threadUuid,
      messageId: messageData.message.msg_id,
      direction: MessageDirectionEnum.INCOMING,
      messageType: MessageTypeEnum.LINK,
      rawWebhookData: messageData,
      content: JSON.stringify(linkInfo), // Store link info as JSON
      mediaIds: [], // No media IDs for links (no S3 persistence)
      createdAt: Date.now(),
    };

    // 4. Save and return message ID
    const savedMessage = await this.zaloMessageRepository.saveZaloMessage(messageDataToSave);

    this.logger.debug(`Saved link message ${savedMessage.id} (URL: ${linkInfo.url}) for thread ${threadUuid}`);
    return savedMessage.id;
  }

  /**
   * Extract thread UUID from LangGraph thread ID format
   * @param threadId Thread ID in format "zalo:userId:uuid"
   * @returns Thread UUID
   */
  private extractThreadUuid(threadId: string): string {
    const parts = threadId.split(':');
    if (parts.length !== 3 || parts[0] !== 'zalo') {
      throw new Error(`Invalid thread ID format: ${threadId}. Expected format: zalo:userId:uuid`);
    }
    return parts[2]; // Extract UUID from thread ID
  }

  /**
   * Validate that thread exists in database
   * @param threadUuid Thread UUID
   */
  private async validateThreadExists(threadUuid: string): Promise<void> {
    const thread = await this.zaloThreadRepository.findById(threadUuid);
    if (!thread) {
      throw new Error(`Thread not found for UUID: ${threadUuid}`);
    }
  }

  /**
   * Get file information from URL using HTTP HEAD request
   * @param url File URL
   * @returns File information
   */
  private async getFileInfoFromUrl(url: string): Promise<{
    fileName: string;
    fileSize: number | null;
    mimeType: string;
    extension: string;
  }> {
    try {
      // Make HEAD request to get file info without downloading
      const response = await fetch(url, { method: 'HEAD' });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Extract content type
      const contentType = response.headers.get('content-type') || 'image/jpeg';

      // Extract file size
      const contentLength = response.headers.get('content-length');
      const fileSize = contentLength ? parseInt(contentLength, 10) : null;

      // Determine file extension from content type
      const extension = this.getFileExtensionFromContentType(contentType);

      // Extract filename from URL or generate one
      const urlPath = new URL(url).pathname;
      const urlFileName = urlPath.split('/').pop() || '';
      const fileName = urlFileName.includes('.')
        ? urlFileName
        : `image-${Date.now()}.${extension}`;

      this.logger.debug(`File info for ${url}: ${contentType}, ${fileSize} bytes, .${extension}`);

      return {
        fileName,
        fileSize,
        mimeType: contentType,
        extension,
      };

    } catch (error) {
      this.logger.warn(`Failed to get file info from ${url}: ${error.message}`);

      // Fallback to defaults if HEAD request fails
      return {
        fileName: `image-${Date.now()}.jpg`,
        fileSize: null,
        mimeType: 'image/jpeg',
        extension: 'jpg',
      };
    }
  }

  /**
   * Get file extension from content type
   * @param contentType MIME type
   * @returns File extension
   */
  private getFileExtensionFromContentType(contentType: string): string {
    const mimeToExtension: Record<string, string> = {
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/gif': 'gif',
      'image/webp': 'webp',
      'image/bmp': 'bmp',
      'image/tiff': 'tiff',
      'image/svg+xml': 'svg',
    };

    // Extract base MIME type (remove charset, etc.)
    const baseMimeType = contentType.split(';')[0].trim().toLowerCase();

    return mimeToExtension[baseMimeType] || 'jpg'; // Default to jpg
  }
}
