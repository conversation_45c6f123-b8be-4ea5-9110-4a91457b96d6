/**
 * Enum định nghĩa context của fine-tuning (Admin hoặc User)
 */
export enum FineTuneContextEnum {
  /**
   * Fine-tuning được thực hiện bởi Admin
   */
  ADMIN = 'ADMIN',

  /**
   * Fine-tuning được thực hiện bởi User
   */
  USER = 'USER',
}

/**
 * Enum định nghĩa loại model đượ<PERSON> sử dụng
 */
export enum ModelTypeEnum {
  /**
   * System model (do hệ thống cung cấp)
   */
  SYSTEM = 'SYSTEM',

  /**
   * User model (do user tự cung cấp API key)
   */
  USER = 'USER',
}
