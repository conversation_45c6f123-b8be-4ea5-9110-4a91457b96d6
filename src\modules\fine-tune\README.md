# Fine-Tune System (Worker App) - Cải Tiến

**Worker app** nhận và xử lý fine-tuning jobs từ **BE app chính** qua Redis Queue. Hỗ trợ OpenAI và Google AI providers với hệ thống polling status tự động và quản lý R-Points.

## 🏗️ Kiến trúc Mới

```
BE App (Main) → Redis Queue → Worker App (này)
     ↓              ↓              ↓
  Tạo job    →   Lưu payload  →  Nhận & xử lý
  Trả jobId  ←   Job queued   ←  Bắt đầu polling
                                      ↓
                              Scheduler Service
                                      ↓
                              Polling mỗi 5 phút
                                      ↓
                              Cập nhật DB & dừng
```

## ✨ Tính năng

- ✅ Hỗ trợ OpenAI và Google AI fine-tuning
- ✅ **Scheduler tự động** polling status mỗi 5 phút
- ✅ **Khôi phục tự động** các job đang chạy khi restart
- ✅ Tự động dừng khi job thành công hoặc thất bại
- ✅ Cập nhật model_id vào user-model-fine-tune khi thành công
- ✅ Hoàn R-Points khi job thất bại (chỉ User + System Model)
- ✅ Timeout protection (24 giờ tối đa)
- ✅ Logging chi tiết và error handling

## Cấu trúc

```
src/modules/fine-tune/
├── constants/
│   ├── provider.enum.ts              # Enum providers
│   ├── fine-tune-job-status.enum.ts  # Enum trạng thái job
│   ├── fine-tune-job-name.enum.ts    # Enum tên job
│   ├── data-fine-tune-status.enum.ts # Enum trạng thái dataset
│   ├── model-capabilities.enum.ts    # Enum capabilities
│   └── index.ts
├── dto/
│   ├── fine-tune-job.dto.ts          # Interface job data
│   ├── create-fine-tune-job.dto.ts   # DTO tạo job
│   └── index.ts
├── entities/
│   ├── fine-tune-histories.entity.ts # Entity lưu history
│   ├── user-model-fine-tune.entity.ts # Entity user model
│   └── ... (các entity khác)
├── interfaces/
│   ├── pricing.interface.ts          # Interface pricing
│   └── index.ts
├── services/
│   ├── fine-tune-upload.service.ts   # Service upload file
│   ├── fine-tune-status.service.ts   # ✨ Service kiểm tra status
│   ├── fine-tune-scheduler.service.ts # ✨ Service polling scheduler
│   ├── openai-fine-tune.service.ts   # Service OpenAI
│   ├── google-fine-tune.service.ts   # Service Google AI
│   ├── r-point.service.ts            # Service R-Points
│   └── index.ts
├── fine-tune.processor.ts            # ✨ Simplified processor
├── fine-tune.module.ts               # Module definition
├── index.ts                          # Export tất cả
└── README.md
```

## 🚀 Thay Đổi Chính

### ❌ Đã Loại Bỏ
- `FineTuneQueueService` - Worker không tạo jobs
- Logic status check trong processor
- Tạo jobs mới cho polling status
- Dependency không cần thiết

### ✅ Đã Thêm Mới
- `FineTuneStatusService` - Chuyên kiểm tra status từ providers
- `FineTuneSchedulerService` - Quản lý polling tự động mỗi 5 phút
- Auto-restore jobs khi restart worker
- Timeout protection (24 giờ tối đa)
- Simplified processor chỉ tạo fine-tune jobs

## Cấu hình

### Biến môi trường

```env
# Redis Configuration
REDIS_URL=redis://localhost:6379

# OpenAI Configuration
OPENAI_API_KEY=sk-...

# Google AI Configuration
GOOGLE_CLOUD_PROJECT_ID=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_AI_API_KEY=your-api-key

# Database Configuration
DATABASE_URL=postgresql://user:pass@localhost:5432/redai_db
```

### Queue Configuration

Queue được tự động đăng ký trong `QueueModule` với tên `FINE_TUNE`.

## 🔄 Luồng Xử Lý Mới

```mermaid
graph TD
    A[BE App] --> B[Gửi job vào Redis Queue]
    B --> C[Worker nhận job]
    C --> D[FineTuneProcessor.process]
    D --> E{Có training data?}
    E -->|Yes| F[Upload data lên provider]
    E -->|No| G[Tạo fine-tune job]
    F --> G
    G --> H{Provider?}
    H -->|OpenAI| I[OpenAIFineTuneService.createFineTuningJob]
    H -->|Google| J[GoogleFineTuneService.createFineTuningJob]
    I --> K[Cập nhật providerJobId vào DB]
    J --> K
    K --> L[FineTuneSchedulerService.startPolling]
    L --> M[Polling mỗi 5 phút]
    M --> N[FineTuneStatusService.checkJobStatus]
    N --> O{Status?}
    O -->|Running| M
    O -->|Success| P[Cập nhật model_id + Dừng polling]
    O -->|Failed| Q[Hoàn R-Points + Dừng polling]
    O -->|Timeout| R[Mark failed + Dừng polling]
```

### 🔄 Khôi Phục Tự Động
```mermaid
graph TD
    A[Worker Restart] --> B[FineTuneSchedulerService.onModuleInit]
    B --> C[Tìm jobs đang chạy trong DB]
    C --> D[Khôi phục polling cho từng job]
    D --> E[Tiếp tục polling mỗi 5 phút]
```

## 📖 Cách Sử Dụng

### 1. Worker App Chỉ Nhận Jobs

**Worker app này chỉ nhận và xử lý jobs từ Redis Queue, không tạo jobs.**

Jobs được tạo từ **BE app chính** và gửi vào Redis Queue với payload:

```typescript
// Payload từ BE app chính
const jobData: FineTuneJobData = {
  historyId: 'uuid-history',
  userModelFineTuneId: 'uuid-user-model',
  context: FineTuneContextEnum.USER, // hoặc ADMIN
  userId: 123, // cho USER context
  employeeId: 456, // cho ADMIN context
  provider: ProviderFineTuneEnum.OPENAI,
  modelType: ModelTypeEnum.SYSTEM, // hoặc USER
  baseModel: 'gpt-3.5-turbo',
  trainingFileId: 'file-abc123', // hoặc trainingData
  validationFileId: 'file-def456', // optional
  hyperparameters: {
    nEpochs: 3,
    batchSize: 'auto',
    learningRateMultiplier: 'auto',
  },
  pointsToRefund: 1000, // cho USER + SYSTEM model
  userKeyId: 'uuid-user-key', // cho USER model
  timestamp: Date.now(),
};
```

### 2. Monitoring Active Jobs

```typescript
import { FineTuneSchedulerService } from '@modules/fine-tune';

// Inject service
constructor(private readonly schedulerService: FineTuneSchedulerService) {}

// Lấy danh sách jobs đang polling
const activeJobs = this.schedulerService.getActiveJobs();
console.log(activeJobs);
// [
//   {
//     historyId: 'uuid-1',
//     providerJobId: 'ftjob-abc123',
//     startTime: 1640995200000,
//     checkCount: 5,
//     elapsedMinutes: 25
//   }
// ]
```

### 3. Manual Control (Nếu Cần)

```typescript
// Bắt đầu polling cho job cụ thể
await this.schedulerService.startPolling(jobData);

// Dừng polling cho job cụ thể
this.schedulerService.stopPolling(historyId);
```

## Trạng thái Job

### OpenAI Status Mapping
- `validating_files` → `VALIDATING_FILES`
- `queued` → `QUEUED`
- `running` → `RUNNING`
- `succeeded` → `SUCCEEDED`
- `failed` → `FAILED`
- `cancelled` → `CANCELLED`

### Google AI Status Mapping
- `PENDING` → `PENDING`
- `RUNNING` → `RUNNING`
- `SUCCEEDED` → `SUCCEEDED`
- `FAILED` → `FAILED`
- `CANCELLED` → `CANCELLED`

## R-Points Management

### Trừ Points khi tạo job
```typescript
await this.rPointService.deductPoints(
  userId, 
  amount, 
  'Fine-tune job created', 
  historyId
);
```

### Hoàn Points khi job thất bại
```typescript
await this.rPointService.refundPoints(
  userId, 
  amount, 
  'Fine-tune job failed', 
  historyId
);
```

## Monitoring

### Bull Dashboard
- Truy cập: `http://localhost:3000/queues`
- Username: `admin`
- Password: `redai@123`

### Logging
- Tất cả operations được log với level INFO/ERROR
- Bao gồm job ID, user ID, provider, status changes
- Error stack traces cho debugging

## Database Schema

### fine_tune_histories
```sql
CREATE TABLE fine_tune_histories (
  id UUID PRIMARY KEY,
  model_name VARCHAR(255) NOT NULL,
  token BIGINT DEFAULT 0,
  method JSONB,
  metadata JSONB,
  user_id INTEGER,
  start_date BIGINT,
  end_date BIGINT,
  points_deducted BIGINT DEFAULT 0,
  points_refunded BOOLEAN DEFAULT FALSE,
  provider VARCHAR(50) DEFAULT 'OPENAI',
  provider_job_id VARCHAR(255),
  status VARCHAR(50) DEFAULT 'PENDING',
  fine_tuned_model_id VARCHAR(255),
  error_message TEXT,
  updated_at BIGINT
);
```

### user_model_fine_tune
```sql
CREATE TABLE user_model_fine_tune (
  id UUID PRIMARY KEY,
  model_id VARCHAR(255),
  model_base VARCHAR(255),
  model_registry_id UUID,
  llm_key_id UUID,
  detail_id UUID,
  user_id INTEGER NOT NULL,
  provider VARCHAR(50) DEFAULT 'OPENAI',
  status VARCHAR(50) DEFAULT 'PENDING',
  created_at BIGINT,
  updated_at BIGINT
);
```

## Error Handling

### Retry Logic
- Job retry 3 lần với exponential backoff
- Status check retry mỗi 5 phút nếu gặp lỗi network
- Automatic fallback cho provider errors

### Error Types
- `PROVIDER_ERROR`: Lỗi từ OpenAI/Google API
- `NETWORK_ERROR`: Lỗi kết nối
- `VALIDATION_ERROR`: Lỗi validate dữ liệu
- `INSUFFICIENT_POINTS`: Không đủ R-Points
- `USER_NOT_FOUND`: Không tìm thấy user

## Testing

### Unit Tests
```bash
npm run test src/modules/fine-tune
```

### Integration Tests
```bash
npm run test:e2e fine-tune
```

### Manual Testing
```bash
# Tạo test job
npx ts-node src/modules/fine-tune/scripts/test-fine-tune.ts
```

## ⚠️ Lưu Ý Quan Trọng

### Worker App
- **Worker chỉ nhận jobs từ Redis Queue, không tạo jobs**
- Tự động khôi phục polling khi restart
- Timeout protection: 24 giờ hoặc 288 lần check
- Polling interval: 5 phút để tránh rate limiting

### Fine-Tuning Process
- Fine-tuning job có thể mất vài giờ đến vài ngày
- Provider job ID được lưu để tracking và debugging
- Metadata được lưu để audit và analytics

### R-Points Management
- **Chỉ hoàn R-Points khi job thất bại** (không hoàn khi hủy)
- Chỉ áp dụng cho **User + System Model**
- Admin và User Model không có point management

### Error Handling
- Retry logic cho network errors
- Automatic failover cho provider errors
- Comprehensive logging cho debugging
