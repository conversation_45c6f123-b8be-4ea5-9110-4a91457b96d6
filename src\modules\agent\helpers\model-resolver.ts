// src/modules/worker/helpers/model-resolver.ts

import { ChatOpenAI } from '@langchain/openai';
import { ChatAnthropic } from '@langchain/anthropic';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { ChatXAI } from '@langchain/xai';
import { ChatDeepSeek } from '@langchain/deepseek';
import { ModelFeature, ModelProviderEnum } from '../enums';
import { SystemAgentConfig } from '../agent-system/interfaces/agent-system.interface';

export type ModelConfig = SystemAgentConfig['model'];

/**
 * Strip out any parameters for which the matching MODIFY_* capability is absent.
 * Always leaves `parameters` defined (possibly as an empty object).
 */
export function cleanModelConfig(config: ModelConfig): ModelConfig {
  // clone so we don’t mutate the original
  const cleaned: ModelConfig = {
    ...config,
    parameters: config.parameters ? { ...config.parameters } : {},
  };

  // Always leave parameters as an object (may now be empty)
  return cleaned;
}

export const resolveModels: {
  [key in ModelProviderEnum]: (
    cfg: ModelConfig,
  ) =>
    | ChatOpenAI
    | ChatAnthropic
    | ChatGoogleGenerativeAI
    | ChatXAI
    | ChatDeepSeek;
} = {
  [ModelProviderEnum.OPENAI]: (config) => {
    const cfg = cleanModelConfig(config);
    return new ChatOpenAI({
      model: cfg.name,
      ...cfg.parameters,
    });
  },

  [ModelProviderEnum.ANTHROPIC]: (config) => {
    const cfg = cleanModelConfig(config);
    return new ChatAnthropic({
      model: cfg.name,
      streaming: true,
      streamUsage: true,
      ...cfg.parameters,
    });
  },

  [ModelProviderEnum.GOOGLE]: (config) => {
    const cfg = cleanModelConfig(config);
    if (cfg.parameters?.maxTokens) {
      delete cfg.parameters.maxTokens;
      cfg.parameters['maxOutputTokens'] = cfg.parameters.maxTokens;
    }
    return new ChatGoogleGenerativeAI({
      model: cfg.name,
      streaming: true,
      streamUsage: true,
      ...cfg.parameters,
    });
  },

  [ModelProviderEnum.XAI]: (config) => {
    const cfg = cleanModelConfig(config);
    return new ChatXAI({
      model: cfg.name,
      streaming: true,
      ...cfg.parameters,
    });
  },

  [ModelProviderEnum.DEEPSEEK]: (config) => {
    const cfg = cleanModelConfig(config);
    return new ChatDeepSeek({
      model: cfg.name,
      streaming: true,
      streamUsage: true,
      ...cfg.parameters,
    });
  },
};
