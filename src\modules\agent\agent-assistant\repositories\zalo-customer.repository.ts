import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ZaloCustomer } from '../entities/zalo-customer.entity';
import { ZaloCustomerStatus } from '../enums';

@Injectable()
export class ZaloCustomerRepository {
  constructor(
    @InjectRepository(ZaloCustomer)
    private readonly repository: Repository<ZaloCustomer>,
  ) {}

  /**
   * Get existing customer by user ID and Zalo user ID
   * @param userId Internal user ID
   * @param zaloUserId Zalo user ID
   * @returns Existing customer or null
   */
  async getExistingCustomer(userId: number, zaloUserId: string): Promise<ZaloCustomer | null> {
    return this.repository.findOne({
      where: {
        zaloUserId,
        userId,
        status: ZaloCustomerStatus.ACTIVE,
      },
    });
  }

  /**
   * Create new customer record
   * @param customerData Customer data to create
   * @returns Created customer record
   */
  async createCustomer(customerData: Partial<ZaloCustomer>): Promise<ZaloCustomer> {
    const customer = this.repository.create(customerData);
    return this.repository.save(customer);
  }

  /**
   * Update last interaction timestamp
   * @param customerId Customer ID to update
   */
  async updateLastInteraction(customerId: string): Promise<void> {
    const now = Date.now();
    await this.repository.update(customerId, {
      lastInteractionAt: now,
      updatedAt: now,
    });
  }
}
