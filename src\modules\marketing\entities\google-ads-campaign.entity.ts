import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng google_ads_campaigns trong cơ sở dữ liệu
 * Lưu trữ thông tin chiến dịch Google Ads
 */
@Entity('google_ads_campaigns')
export class GoogleAdsCampaign {
  /**
   * ID của chiến dịch Google Ads trong hệ thống
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', nullable: false, comment: 'ID của người dùng' })
  userId: number;

  /**
   * ID của tài khoản Google Ads trong hệ thống
   */
  @Column({
    name: 'account_id',
    nullable: false,
    comment: 'ID của tài khoản Google Ads',
  })
  accountId: number;

  /**
   * ID của chiến dịch trên Google Ads
   */
  @Column({
    name: 'campaign_id',
    nullable: false,
    comment: 'ID của chiến dịch trên Google Ads',
  })
  campaignId: string;

  /**
   * Tên chiến dịch
   */
  @Column({
    name: 'name',
    length: 255,
    nullable: false,
    comment: 'Tên chiến dịch',
  })
  name: string;

  /**
   * Trạng thái chiến dịch
   */
  @Column({
    name: 'status',
    length: 20,
    nullable: false,
    comment: 'Trạng thái chiến dịch',
  })
  status: string;

  /**
   * Loại chiến dịch (SEARCH, DISPLAY, ...)
   */
  @Column({
    name: 'type',
    length: 50,
    nullable: false,
    comment: 'Loại chiến dịch (SEARCH, DISPLAY, ...)',
  })
  type: string;

  /**
   * Ngân sách hàng ngày (micro amount)
   */
  @Column({
    name: 'budget',
    type: 'bigint',
    nullable: false,
    comment: 'Ngân sách hàng ngày (micro amount)',
  })
  budget: number;

  /**
   * Ngày bắt đầu (YYYY-MM-DD)
   */
  @Column({
    name: 'start_date',
    length: 10,
    nullable: true,
    comment: 'Ngày bắt đầu (YYYY-MM-DD)',
  })
  startDate: string;

  /**
   * Ngày kết thúc (YYYY-MM-DD)
   */
  @Column({
    name: 'end_date',
    length: 10,
    nullable: true,
    comment: 'Ngày kết thúc (YYYY-MM-DD)',
  })
  endDate: string;

  /**
   * ID của chiến dịch marketing
   */
  @Column({
    name: 'user_campaign_id',
    type: 'bigint',
    nullable: true,
    comment: 'ID của chiến dịch marketing',
  })
  userCampaignId: number;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: false,
    comment: 'Thời gian tạo',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian cập nhật',
  })
  updatedAt: number;
}
