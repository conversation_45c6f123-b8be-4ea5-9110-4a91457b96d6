import { <PERSON>, Logger } from '@nestjs/common';
import { EventPattern, Payload } from '@nestjs/microservices';
import { REDIS_EVENTS, RunCancelEvent, RunTriggerEvent } from '../constants';
import { AgentSystemService } from './services/agent-system.service';

@Controller()
export class AgentSystemController {
  private readonly logger = new Logger(AgentSystemController.name);

  constructor(private readonly agentSystemService: AgentSystemService) {}

  /**
   * Handle run trigger events from backend
   * @param data Run trigger event payload
   */
  @EventPattern(REDIS_EVENTS.RUN_TRIGGER) async handleRunTrigger(
    @Payload() data: RunTriggerEvent,
  ): Promise<void> {
    this.logger.log(
      `[handleRunTrigger] received RunTriggerEvent with id = ${data.runId}`,
    );
    return await this.agentSystemService.triggerRun(data);
  }

  /**
   * <PERSON>le run cancel events from backend
   * @param data Run cancel event payload
   */
  @EventPattern(REDIS_EVENTS.RUN_CANCEL) async handleRunCancel(
    @Payload() data: RunCancelEvent,
  ): Promise<void> {
    this.logger.log(
      `
      --------------------------------------------------------------------------------------------------
      [handleRunCancel] received RunCancelEvent with id = ${data.runId}
      --------------------------------------------------------------------------------------------------
      `,
    );
    return await this.agentSystemService.cancelRun(data);
  }
}
