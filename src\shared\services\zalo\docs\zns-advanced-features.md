# Zalo Notification Service (ZNS) - Advanced Features

## Tổng quan

Tài liệu này mô tả các tính năng nâng cao của ZNS đã được triển khai dựa trên tài liệu chính thức của <PERSON>:

- **Component Management**: Quản lý và validation các component ZNS
- **Quality Assessment**: <PERSON><PERSON>h giá chất lượng và quyền lợi gửi ZNS  
- **Error Handling**: Xử lý lỗi và utilities
- **BIN Code Support**: Hỗ trợ thông tin ngân hàng

## 1. ZNS Component Service

### Mục đích
Service để quản lý và validation các component ZNS theo chuẩn của Zalo.

### Các loại Component được hỗ trợ

```typescript
enum ZnsComponentType {
  TEXT = 'text',
  HEADER = 'header', 
  BODY = 'body',
  FOOTER = 'footer',
  BUTTON = 'button',
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  FILE = 'file',
  LOCATION = 'location',
  CONTACT = 'contact',
  LIST = 'list',
  CAROUSEL = 'carousel',
  QUICK_REPLY = 'quick_reply',
  INTERACTIVE = 'interactive',
}
```

### Sử dụng

```typescript
import { ZaloZnsComponentService } from '@/shared/services/zalo';

// Validate component
const result = componentService.validateTemplateComponent(component);
if (!result.isValid) {
  console.log('Errors:', result.errors);
  console.log('Warnings:', result.warnings);
}

// Tạo component mẫu
const textComponent = componentService.createSampleComponent(
  ZnsComponentType.TEXT,
  'Xin chào {{1}}, đơn hàng {{2}} đã được xác nhận'
);

// Tạo template hoàn chỉnh
const template = componentService.createCompleteTemplate(
  'Order Confirmation',
  'Đơn hàng {{1}} của bạn đã được xác nhận',
  'Thông báo đơn hàng',
  'Cảm ơn bạn đã mua hàng',
  [
    { text: 'Xem chi tiết', url: 'https://example.com/order/{{1}}' },
    { text: 'Liên hệ hỗ trợ', phone: '+***********' }
  ]
);
```

### Validation Rules

- **Text Component**: Tối đa 1024 ký tự
- **Header Text**: Tối đa 60 ký tự  
- **Body**: Tối đa 1024 ký tự
- **Footer**: Tối đa 60 ký tự
- **Button Text**: Tối đa 20 ký tự
- **URL**: Tối đa 2000 ký tự, phải bắt đầu bằng http/https
- **Phone**: Định dạng quốc tế hợp lệ

## 2. ZNS Quality Service

### Mục đích
Service để quản lý chất lượng gửi ZNS và quyền lợi của Official Account.

### Các tính năng chính

#### Lấy đánh giá chất lượng hiện tại
```typescript
const assessment = await qualityService.getCurrentQualityAssessment(accessToken);
console.log('Quality Level:', assessment.quality_level);
console.log('Overall Score:', assessment.overall_score);
```

#### Lấy lịch sử đánh giá chất lượng
```typescript
const history = await qualityService.getQualityAssessmentHistory(accessToken, {
  start_time: startTimestamp,
  end_time: endTimestamp,
  quality_level: ZnsQualityLevel.HIGH,
  violations_only: false
});
```

#### Lấy danh sách vi phạm
```typescript
const violations = await qualityService.getQualityViolations(accessToken, 'ACTIVE');
violations.forEach(violation => {
  console.log(`Violation: ${violation.violation_type}`);
  console.log(`Severity: ${violation.severity_level}`);
  console.log(`Recommendations:`, violation.recommended_actions);
});
```

#### Xác nhận khắc phục vi phạm
```typescript
const result = await qualityService.resolveQualityViolation(
  accessToken,
  violationId,
  'Đã chỉnh sửa nội dung template theo yêu cầu'
);
```

### Mức độ chất lượng

```typescript
enum ZnsQualityLevel {
  HIGH = 'HIGH',      // Chất lượng cao
  MEDIUM = 'MEDIUM',  // Chất lượng trung bình  
  LOW = 'LOW',        // Chất lượng thấp
}
```

### Quyền lợi gửi ZNS

```typescript
enum ZnsPriorityLevel {
  HIGH = 'HIGH',      // Ưu tiên cao
  MEDIUM = 'MEDIUM',  // Ưu tiên trung bình
  LOW = 'LOW',        // Ưu tiên thấp
  BLOCKED = 'BLOCKED' // Bị chặn
}
```

## 3. ZNS Error Service

### Mục đích
Service để xử lý lỗi, validation và utilities cho ZNS.

### Xử lý mã lỗi

```typescript
// Lấy thông tin chi tiết về lỗi
const errorInfo = errorService.getErrorInfo(ZnsErrorCode.TEMPLATE_NOT_APPROVED);
console.log('Message:', errorInfo.error_message);
console.log('Description:', errorInfo.error_description);
console.log('Suggestion:', errorInfo.suggestion);

// Phân tích response lỗi từ API
const errorResponse = errorService.analyzeErrorResponse(apiResponse);

// Kiểm tra lỗi có thể retry không
if (errorService.isRetryableError(errorCode)) {
  const delay = errorService.getRetryDelay(errorCode, retryCount);
  setTimeout(() => retryRequest(), delay);
}
```

### Validation số điện thoại Việt Nam

```typescript
const phoneValidation = errorService.validateVietnamesePhoneNumber('0123456789');
if (phoneValidation.isValid) {
  console.log('Formatted:', phoneValidation.formattedNumber); // +***********
  console.log('Carrier:', phoneValidation.carrier); // Viettel, Vinaphone, etc.
} else {
  console.log('Errors:', phoneValidation.errors);
}
```

### Validation template data

```typescript
const validation = errorService.validateTemplateData(
  { param1: 'John', param2: 'ORDER123' },
  ['param1', 'param2', 'param3']
);

if (!validation.isValid) {
  console.log('Missing params:', validation.missingParams);
  console.log('Extra params:', validation.extraParams);
  console.log('Errors:', validation.errors);
}
```

### BIN Code Support

```typescript
// Lấy thông tin ngân hàng từ BIN code
const bankInfo = errorService.getBankInfoFromBinCode('970436'); // Vietcombank
if (bankInfo) {
  console.log('Bank:', bankInfo.bank_name);
  console.log('Supports ZNS:', bankInfo.supports_zns);
}

// Lấy danh sách ngân hàng hỗ trợ ZNS
const supportedBanks = errorService.getSupportedBanks();
```

## 4. Mã lỗi ZNS

### Các nhóm lỗi chính

#### Lỗi chung (General)
- `SUCCESS (0)`: Thành công
- `INVALID_PARAMETER (-1)`: Tham số không hợp lệ
- `INVALID_ACCESS_TOKEN (-2)`: Access token không hợp lệ
- `PERMISSION_DENIED (-3)`: Không có quyền truy cập
- `RATE_LIMIT_EXCEEDED (-4)`: Vượt quá giới hạn tần suất
- `INTERNAL_SERVER_ERROR (-5)`: Lỗi hệ thống

#### Lỗi template (-100 đến -199)
- `TEMPLATE_NOT_FOUND (-100)`: Không tìm thấy template
- `TEMPLATE_NOT_APPROVED (-101)`: Template chưa được duyệt
- `TEMPLATE_EXPIRED (-102)`: Template đã hết hạn
- `TEMPLATE_DISABLED (-103)`: Template đã bị vô hiệu hóa
- `TEMPLATE_INVALID_FORMAT (-104)`: Định dạng template không hợp lệ
- `TEMPLATE_PARAMETER_MISMATCH (-105)`: Tham số template không khớp

#### Lỗi tin nhắn (-200 đến -299)
- `INVALID_PHONE_NUMBER (-200)`: Số điện thoại không hợp lệ
- `PHONE_NUMBER_BLOCKED (-201)`: Số điện thoại bị chặn
- `MESSAGE_QUOTA_EXCEEDED (-202)`: Vượt quá hạn mức tin nhắn
- `MESSAGE_TOO_LONG (-203)`: Tin nhắn quá dài
- `MESSAGE_INVALID_CONTENT (-204)`: Nội dung tin nhắn không hợp lệ
- `MESSAGE_DUPLICATE (-205)`: Tin nhắn trùng lặp

#### Lỗi Official Account (-300 đến -399)
- `OA_NOT_FOUND (-300)`: Không tìm thấy Official Account
- `OA_NOT_APPROVED (-301)`: Official Account chưa được duyệt
- `OA_SUSPENDED (-302)`: Official Account bị tạm ngưng
- `OA_INSUFFICIENT_BALANCE (-303)`: Số dư không đủ
- `OA_FEATURE_NOT_ENABLED (-304)`: Tính năng chưa được kích hoạt

#### Lỗi file (-400 đến -499)
- `FILE_TOO_LARGE (-400)`: File quá lớn
- `FILE_INVALID_FORMAT (-401)`: Định dạng file không hợp lệ
- `FILE_UPLOAD_FAILED (-402)`: Upload file thất bại
- `FILE_NOT_FOUND (-403)`: Không tìm thấy file

## 5. BIN Code ngân hàng

### Ngân hàng hỗ trợ ZNS

| BIN Code | Ngân hàng | Tên viết tắt | Hỗ trợ ZNS |
|----------|-----------|--------------|------------|
| 970436 | Ngân hàng TMCP Ngoại thương Việt Nam | Vietcombank | ✅ |
| 970415 | Ngân hàng TMCP Công thương Việt Nam | VietinBank | ✅ |
| 970418 | Ngân hàng TMCP Đầu tư và Phát triển Việt Nam | BIDV | ✅ |
| 970405 | Ngân hàng Nông nghiệp và Phát triển Nông thôn Việt Nam | Agribank | ✅ |
| 970407 | Ngân hàng TMCP Kỹ thương Việt Nam | Techcombank | ✅ |
| 970422 | Ngân hàng TMCP Quân đội | MB Bank | ✅ |
| 970416 | Ngân hàng TMCP Á Châu | ACB | ✅ |
| 970432 | Ngân hàng TMCP Việt Nam Thịnh vượng | VPBank | ✅ |
| 970423 | Ngân hàng TMCP Tiên Phong | TPBank | ✅ |
| 970403 | Ngân hàng TMCP Sài Gòn Thương tín | Sacombank | ✅ |

## 6. Best Practices

### Component Design
1. **Giữ nội dung ngắn gọn**: Tuân thủ giới hạn ký tự cho từng loại component
2. **Sử dụng tham số động**: Cho phép tùy chỉnh nội dung cho từng người nhận
3. **Validate trước khi gửi**: Luôn validate component trước khi tạo template
4. **Test với dữ liệu thực**: Kiểm tra template với dữ liệu thực tế

### Quality Management
1. **Theo dõi chất lượng thường xuyên**: Kiểm tra điểm chất lượng hàng tuần
2. **Xử lý vi phạm kịp thời**: Khắc phục vi phạm ngay khi phát hiện
3. **Tối ưu nội dung**: Cải thiện nội dung dựa trên feedback khách hàng
4. **Tuân thủ chính sách**: Đảm bảo nội dung tuân thủ quy định của Zalo

### Error Handling
1. **Implement retry logic**: Cho các lỗi có thể retry
2. **Log chi tiết**: Ghi log đầy đủ thông tin lỗi để debug
3. **Fallback mechanism**: Có phương án dự phòng khi gặp lỗi
4. **User-friendly messages**: Hiển thị thông báo lỗi dễ hiểu cho người dùng

## 7. Tài liệu tham khảo

- [ZNS Component Documentation](https://developers.zalo.me/docs/zalo-notification-service/phu-luc/component)
- [Quality Rating Mechanism](https://developers.zalo.me/docs/zalo-notification-service/phu-luc/co-che-danh-gia-chat-luong-va-quyen-loi-gui-zns)
- [Error Codes](https://developers.zalo.me/docs/zalo-notification-service/phu-luc/bang-ma-loi)
- [BIN Code List](https://developers.zalo.me/docs/zalo-notification-service/phu-luc/danh-sach-bin-code)
