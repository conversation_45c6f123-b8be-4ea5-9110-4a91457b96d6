import { QueueEventsHost, QueueEventsListener } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { QueueName } from 'src/queue';

@Injectable()
@QueueEventsListener(QueueName.ZALO_AI_RESPONSE)
export class ZaloAiResponseListener extends QueueEventsHost {
  private readonly logger = new Logger(ZaloAiResponseListener.name);

  onApplicationShutdown(signal?: string): Promise<void> {
    this.logger.log(`Shutting down Zalo AI Response Listener`);
    return super.onApplicationShutdown(signal);
  }
}
