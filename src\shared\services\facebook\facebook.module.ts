import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';

import { FacebookAuthService } from './auth/facebook-auth.service';
import { FacebookPageService } from './page/facebook-page.service';
import { FacebookApiService } from './facebook-api.service';

// Business API Services - TODO: Implement Facebook services
// import { FacebookBusinessApiService } from './business/facebook-business-api.service';
// import { FacebookCampaignsService } from './business/facebook-campaigns.service';
// import { FacebookAdsService } from './business/facebook-ads.service';
// import { FacebookAudiencesService } from './business/facebook-audiences.service';
// import { FacebookConversionsService } from './business/facebook-conversions.service';
// import { FacebookCreativesService } from './business/facebook-creatives.service';
// import { FacebookInsightsService } from './business/facebook-insights.service';
// import { FacebookLeadsService } from './business/facebook-leads.service';
// import { FacebookMediaService } from './business/facebook-media.service';

@Module({
  imports: [
    HttpModule.register({
      timeout: 30000, // 30 seconds
      maxRedirects: 5,
    }),
    ConfigModule,
  ],
  providers: [
    // Auth and Page services
    FacebookAuthService,
    FacebookPageService,
    FacebookApiService,

    // Business API services - TODO: Implement Facebook services
    // FacebookBusinessApiService,
    // FacebookCampaignsService,
    // FacebookAdsService,
    // FacebookAudiencesService,
    // FacebookConversionsService,
    // FacebookCreativesService,
    // FacebookInsightsService,
    // FacebookLeadsService,
    // FacebookMediaService,
  ],
  exports: [
    FacebookAuthService,
    FacebookPageService,
    FacebookApiService,

    // Business API services - TODO: Implement Facebook services
    // FacebookBusinessApiService,
    // FacebookCampaignsService,
    // FacebookAdsService,
    // FacebookAudiencesService,
    // FacebookConversionsService,
    // FacebookCreativesService,
    // FacebookInsightsService,
    // FacebookLeadsService,
    // FacebookMediaService,
  ],
})
export class FacebookModule { }
