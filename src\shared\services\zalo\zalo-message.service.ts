import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloService } from './zalo.service';
import {
  ZaloOtherMessage,
  ZaloReactionMessage,
  ZaloMiniAppMessage,
} from './zalo.interface';

/**
 * Service xử lý các API tin nhắn đặc biệt của Zalo Official Account
 *
 * Điều kiện sử dụng:
 * - Reaction: Chỉ có thể thả cảm xúc vào tin nhắn trong vòng 24 giờ kể từ khi tin nhắn được gửi
 * - MiniApp: Cần có quyền gửi tin nhắn miniapp và miniapp phải được phê duyệt
 * - Người dùng phải đã quan tâm Official Account hoặc có tương tác gần đây
 */
@Injectable()
export class ZaloMessageService {
  private readonly logger = new Logger(ZaloMessageService.name);
  private readonly messageApiUrl = 'https://openapi.zalo.me/v3.0/oa/message';
  private readonly reactionApiUrl =
    'https://openapi.zalo.me/v3.0/oa/message/reaction';

  constructor(
    private readonly zaloService: ZaloService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Thả biểu tượng cảm xúc vào tin nhắn
   * @param accessToken Access token của Official Account
   * @param messageId ID của tin nhắn cần thả cảm xúc
   * @param reactionType Loại biểu tượng cảm xúc
   * @returns Kết quả thả cảm xúc
   */
  async sendReaction(
    accessToken: string,
    messageId: string,
    reactionType: 'heart' | 'like' | 'haha' | 'wow' | 'sad' | 'angry',
  ): Promise<{ success: boolean }> {
    try {
      const data = {
        message_id: messageId,
        reaction: reactionType,
      };

      this.logger.debug(
        `Sending reaction ${reactionType} to message ${messageId}`,
      );
      return await this.zaloService.post<{ success: boolean }>(
        this.reactionApiUrl,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(
        `Error sending reaction: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi thả biểu tượng cảm xúc vào tin nhắn',
      );
    }
  }

  /**
   * Gửi tin nhắn miniapp đến người dùng
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param appId ID của miniapp
   * @param title Tiêu đề tin nhắn
   * @param subtitle Mô tả ngắn gọn (nếu có)
   * @param imageUrl URL hình ảnh đại diện (nếu có)
   * @param data Dữ liệu truyền vào miniapp (nếu có)
   * @returns ID của tin nhắn
   */
  async sendMiniAppMessage(
    accessToken: string,
    userId: string,
    appId: string,
    title: string,
    subtitle?: string,
    imageUrl?: string,
    data?: Record<string, any>,
  ): Promise<{ message_id: string }> {
    try {
      const messageData = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'template',
            payload: {
              template_type: 'miniapp',
              elements: [
                {
                  app_id: appId,
                  title,
                  ...(subtitle && { subtitle }),
                  ...(imageUrl && { image_url: imageUrl }),
                  ...(data && { data }),
                },
              ],
            },
          },
        },
      };

      this.logger.debug(`Sending miniapp message to user ${userId}: ${title}`);
      return await this.zaloService.post<{ message_id: string }>(
        this.messageApiUrl,
        accessToken,
        messageData,
      );
    } catch (error) {
      this.logger.error(
        `Error sending miniapp message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn miniapp',
      );
    }
  }

  /**
   * Gửi tin nhắn văn bản sử dụng API v3
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param text Nội dung tin nhắn
   * @returns ID của tin nhắn
   */
  async sendTextMessage(
    accessToken: string,
    userId: string,
    text: string,
  ): Promise<{ message_id: string }> {
    try {
      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          text,
        },
      };

      this.logger.debug(`Sending text message to user ${userId}: ${text}`);
      return await this.zaloService.post<{ message_id: string }>(
        this.messageApiUrl,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(
        `Error sending text message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn văn bản',
      );
    }
  }

  /**
   * Gửi tin nhắn hình ảnh sử dụng API v3
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param imageUrl URL của hình ảnh
   * @param caption Chú thích cho hình ảnh (nếu có)
   * @returns ID của tin nhắn
   */
  async sendImageMessage(
    accessToken: string,
    userId: string,
    imageUrl: string,
    caption?: string,
  ): Promise<{ message_id: string }> {
    try {
      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'template',
            payload: {
              template_type: 'media',
              elements: [
                {
                  media_type: 'image',
                  url: imageUrl,
                  ...(caption && { caption }),
                },
              ],
            },
          },
        },
      };

      this.logger.debug(`Sending image message to user ${userId}: ${imageUrl}`);
      return await this.zaloService.post<{ message_id: string }>(
        this.messageApiUrl,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(
        `Error sending image message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn hình ảnh',
      );
    }
  }

  /**
   * Gửi tin nhắn file sử dụng API v3
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param fileUrl URL của file
   * @param fileName Tên file
   * @returns ID của tin nhắn
   */
  async sendFileMessage(
    accessToken: string,
    userId: string,
    fileUrl: string,
    fileName: string,
  ): Promise<{ message_id: string }> {
    try {
      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'file',
            payload: {
              url: fileUrl,
              name: fileName,
            },
          },
        },
      };

      this.logger.debug(`Sending file message to user ${userId}: ${fileName}`);
      return await this.zaloService.post<{ message_id: string }>(
        this.messageApiUrl,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(
        `Error sending file message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn file',
      );
    }
  }

  /**
   * Gửi tin nhắn đặc biệt dựa vào loại tin nhắn
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo (không cần cho reaction)
   * @param message Tin nhắn cần gửi
   * @returns Kết quả gửi tin nhắn
   */
  async sendOtherMessage(
    accessToken: string,
    userId: string | null,
    message: ZaloOtherMessage,
  ): Promise<{ message_id?: string; success?: boolean }> {
    try {
      switch (message.type) {
        case 'reaction':
          const reactionResult = await this.sendReaction(
            accessToken,
            message.message_id,
            message.reaction_type,
          );
          return { success: reactionResult.success };

        case 'miniapp':
          if (!userId) {
            throw new AppException(
              ErrorCode.VALIDATION_ERROR,
              'User ID là bắt buộc cho tin nhắn miniapp',
            );
          }
          const miniappResult = await this.sendMiniAppMessage(
            accessToken,
            userId,
            message.app_id,
            message.title,
            message.subtitle,
            message.image_url,
            message.data,
          );
          return { message_id: miniappResult.message_id };

        default:
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            'Loại tin nhắn đặc biệt không hợp lệ',
          );
      }
    } catch (error) {
      this.logger.error(
        `Error sending other message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn đặc biệt',
      );
    }
  }

  /**
   * Xóa biểu tượng cảm xúc khỏi tin nhắn
   * @param accessToken Access token của Official Account
   * @param messageId ID của tin nhắn cần xóa cảm xúc
   * @returns Kết quả xóa cảm xúc
   */
  async removeReaction(
    accessToken: string,
    messageId: string,
  ): Promise<{ success: boolean }> {
    try {
      const data = {
        message_id: messageId,
        reaction: null,
      };

      this.logger.debug(`Removing reaction from message ${messageId}`);
      return await this.zaloService.post<{ success: boolean }>(
        this.reactionApiUrl,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(
        `Error removing reaction: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xóa biểu tượng cảm xúc khỏi tin nhắn',
      );
    }
  }

  /**
   * Lấy danh sách các biểu tượng cảm xúc có sẵn
   * @param accessToken Access token của Official Account
   * @returns Danh sách các biểu tượng cảm xúc
   */
  async getAvailableReactions(accessToken: string): Promise<{
    reactions: Array<{ type: string; name: string; icon: string }>;
  }> {
    try {
      this.logger.debug('Getting available reactions');
      return await this.zaloService.get<{
        reactions: Array<{ type: string; name: string; icon: string }>;
      }>(`${this.reactionApiUrl}/available`, accessToken);
    } catch (error) {
      this.logger.error(
        `Error getting available reactions: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách biểu tượng cảm xúc có sẵn',
      );
    }
  }

  /**
   * Lấy thông tin miniapp
   * @param accessToken Access token của Official Account
   * @param appId ID của miniapp
   * @returns Thông tin miniapp
   */
  async getMiniAppInfo(
    accessToken: string,
    appId: string,
  ): Promise<{
    app_id: string;
    name: string;
    description: string;
    status: string;
  }> {
    try {
      this.logger.debug(`Getting miniapp info for ${appId}`);
      return await this.zaloService.get<{
        app_id: string;
        name: string;
        description: string;
        status: string;
      }>(`https://openapi.zalo.me/v2.0/oa/miniapp/${appId}`, accessToken);
    } catch (error) {
      this.logger.error(
        `Error getting miniapp info: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin miniapp',
      );
    }
  }
}
