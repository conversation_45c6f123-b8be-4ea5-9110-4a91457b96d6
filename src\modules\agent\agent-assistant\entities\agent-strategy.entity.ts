import { Column, Entity, PrimaryColumn } from 'typeorm';
import { IStrategyContentStep } from '../interfaces/strategy-content-step.interface';

/**
 * AgentStrategy entity
 * Stores agent strategy processing configuration, linked with specific model and corresponding API keys
 */
@Entity('agents_strategy')
export class AgentStrategy {
  /**
   * Primary key which is also the agent ID, 1-1 link with agents table
   */
  @PrimaryColumn({ name: 'id', type: 'uuid' })
  id: string;

  /**
   * Strategy content of agent (configuration, rules...) in JSON format
   */
  @Column({ name: 'content', type: 'jsonb', default: '[]' })
  content: IStrategyContentStep[];

  /**
   * Default examples to illustrate or guide strategy, JSON format
   */
  @Column({ name: 'example_default', type: 'jsonb', default: '[]' })
  exampleDefault: IStrategyContentStep[];

  /**
   * Employee ID who created the strategy, references employees table
   */
  @Column({ name: 'created_by', type: 'int', nullable: true })
  createdBy?: number;

  /**
   * Employee ID who last updated the strategy, references employees table
   */
  @Column({ name: 'updated_by', type: 'int', nullable: true })
  updatedBy?: number;

  /**
   * Employee ID who deleted the strategy, references employees table
   */
  @Column({ name: 'deleted_by', type: 'int', nullable: true })
  deletedBy?: number;

  /**
   * System model ID reference to system_models table
   */
  @Column({ name: 'system_model_id', type: 'uuid', nullable: true })
  systemModelId?: string;

  /**
   * Whether the strategy is being used
   */
  @Column({ name: 'using', type: 'boolean', default: false, nullable: false })
  using: boolean;

  /**
   * Additional metadata information
   */
  @Column({ name: 'metadata', type: 'jsonb', default: '{}' })
  metadata: any;
}
