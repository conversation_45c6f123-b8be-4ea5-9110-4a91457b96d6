import { Injectable } from '@nestjs/common';
import { BaseLangGraphEventHandler } from './base-event-handler';
import { EventProcessingContext } from '../schemas';
import { ROLE_TAGS, SUPERVISOR_TAG } from '../core/constants';

/**
 * Handler for on_chat_model_stream events from LangGraph
 * Handles token accumulation and stream event emission
 */
@Injectable()
export class ChatModelStreamHandler extends BaseLangGraphEventHandler {
  /**
   * Check if this handler can process the given event
   * @param event - LangGraph event type
   * @param data - Event data
   * @param tags - Event tags
   * @returns True if this is an on_chat_model_stream event with role tags
   */
  canHandle(event: string, _data: any, tags: string[]): boolean {
    return (
      event === 'on_chat_model_stream' &&
      tags.some((t: string) => ROLE_TAGS.includes(t))
    );
  }

  /**
   * Process on_chat_model_stream event
   * Accumulates tokens for supervisor role and emits stream events
   * @param context - Event processing context
   */
  async handle(context: EventProcessingContext): Promise<void> {
    const role = this.getRoleFromTags(context.tags, ROLE_TAGS)!;

    if (context.data.chunk?.content) {
      const text = context.data.chunk.content;

      // CRITICAL: Only accumulate tokens for supervisor role (from original logic)
      if (role === SUPERVISOR_TAG) {
        context.partialTokens.push(text);
      }

      // Smart truncation for logging (from original)
      const displayText =
        text.length > 50 ? `${text.substring(0, 50)}...` : text;
      const safeText = displayText.replace(/\n/g, '\\n').replace(/\r/g, '\\r');

      this.logEvent(
        '📝',
        `Streaming text token [${role}]: "${safeText}" (${text.length} chars)`,
        context,
        {
          role,
          textLength: text.length,
          fullText: text.length <= 20 ? text : undefined,
        },
      );

      // Emit stream_text_token event
      await context.emitEventCallback({
        type: 'stream_text_token',
        data: { role, text },
      });
    }
    // Note: Tool token events removed - not needed
  }
}
