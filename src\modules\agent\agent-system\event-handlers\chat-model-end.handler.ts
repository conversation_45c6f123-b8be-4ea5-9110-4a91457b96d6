import { Injectable } from '@nestjs/common';
import { BaseLangGraphEventHandler } from './base-event-handler';
import { EventProcessingContext } from '../schemas';
import { ROLE_TAGS } from '../core/constants';

/**
 * Handler for on_chat_model_end events from LangGraph
 * CRITICAL: This handler saves complete messages to the database
 */
@Injectable()
export class ChatModelEndHandler extends BaseLangGraphEventHandler {
  /**
   * Check if this handler can process the given event
   * @param event - LangGraph event type
   * @param data - Event data
   * @param tags - Event tags
   * @returns True if this is an on_chat_model_end event with role tags
   */
  canHandle(event: string, data: any, tags: string[]): boolean {
    return (
      event === 'on_chat_model_end' &&
      tags.some((t: string) => ROLE_TAGS.includes(t))
    );
  }

  /**
   * Process on_chat_model_end event
   * Saves complete LLM response to database and emits completion events
   * @param context - Event processing context
   */
  async handle(context: EventProcessingContext): Promise<void> {
    const role = this.getRoleFromTags(context.tags, ROLE_TAGS);

    this.logEvent('🏁', `LLM completion finished [${role}]`, context, {
      role,
      event: 'on_chat_model_end',
      partialTokensCount: context.partialTokens.length,
    });

    // Log completion detection for debugging
    const isCompleteResponse = context.partialTokens.length > 0;
    const completeResponseText = context.partialTokens.join('');

    if (isCompleteResponse && completeResponseText.trim().length > 0) {
      this.logger.debug(
        `✅ LLM completion detected: ${completeResponseText.length} characters`,
        {
          threadId: context.threadId,
          role,
          textLength: completeResponseText.length,
          textPreview: completeResponseText.substring(0, 100),
          runId: context.runData.id,
        },
      );

      // 🚨 CRITICAL FIX: Save message immediately to prevent concatenation with tool calls
      try {
        const messageId = await this.saveMessage(
          context.threadId,
          context.runData,
          completeResponseText,
          context.emitEventCallback,
          context.userMessagesQueries,
        );

        this.logger.log(
          `💾 Saved message ${messageId} on LLM completion [${role}]: "${completeResponseText.substring(0, 50)}${completeResponseText.length > 50 ? '...' : ''}"`,
          {
            threadId: context.threadId,
            messageId,
            role,
            textLength: completeResponseText.length,
            runId: context.runData.id,
          },
        );

        // 🧹 CRITICAL: Clear partialTokens to prevent concatenation with next message
        context.partialTokens.length = 0;

        this.logger.debug(
          `🧹 Cleared partialTokens array after saving message [${role}]`,
          {
            threadId: context.threadId,
            role,
            messageId,
          },
        );
      } catch (error) {
        this.logger.error(
          `Failed to save message on LLM completion [${role}]:`,
          {
            threadId: context.threadId,
            role,
            error: error.message,
            stack: error.stack,
          },
        );
        // Don't clear tokens if save failed - let handleFinalCleanup handle it
      }
    } else {
      this.logger.debug(`⚠️ LLM completion detected but no content`, {
        threadId: context.threadId,
        role,
        partialTokensCount: context.partialTokens.length,
        hasContent: completeResponseText.trim().length > 0,
      });
    }

    // 🚀 EMIT llm_stream_end LAST (after all processing is complete)
    await context.emitEventCallback({
      type: 'llm_stream_end',
      data: { role: role as 'supervisor' | 'worker' | undefined },
    });

    this.logger.debug(`📡 Emitted llm_stream_end event [${role}]`, {
      threadId: context.threadId,
      runId: context.runData.id,
      role,
    });
  }

  /**
   * Save message to database using the existing queries service
   * Replicates the logic from ThreadCompletionHandlerService.saveResponse
   * @param threadId - Thread ID
   * @param runData - Run data containing userId and other info
   * @param responseText - Response text content
   * @param emitEventCallback - Callback for event emission
   * @param userMessagesQueries - User messages queries service
   * @returns Message ID of saved message
   */
  private async saveMessage(
    threadId: string,
    runData: any,
    responseText: string,
    emitEventCallback: any,
    userMessagesQueries: any,
  ): Promise<string> {
    // Get userId from the run data - runData.created_by contains the userId
    const userId = runData.created_by;
    const runId = runData.id;
    // ✅ NEW: Extract agent ID from run payload
    const agentId = runData.payload?.primaryAgentId;

    // ✅ DEBUG: Log payload structure to understand why agentId might be null
    this.logger.debug(
      `🔍 DEBUG: Run payload structure for thread ${threadId}`,
      {
        threadId,
        runId,
        hasPayload: !!runData.payload,
        payloadKeys: runData.payload ? Object.keys(runData.payload) : [],
        primaryAgentId: runData.payload?.primaryAgentId,
        agentConfigMapKeys: runData.payload?.agentConfigMap
          ? Object.keys(runData.payload.agentConfigMap)
          : [],
      },
    );

    this.logger.log(`💾 Saving assistant response for thread ${threadId}`, {
      threadId,
      responseLength: responseText.length,
      runId,
      userId,
      agentId, // ✅ NEW: Log agent ID
      preview:
        responseText.substring(0, 100) +
        (responseText.length > 100 ? '...' : ''),
    });

    // Save response WITHOUT token usage data (no token counts, no balance updates)
    const messageId = await userMessagesQueries.createMessage({
      thread_id: threadId,
      role: 'assistant',
      content: {
        contentBlocks: [
          {
            type: 'text',
            content: responseText,
          },
        ],
      },
      created_by: userId,
      agent_id: agentId, // ✅ NEW: Include agent ID for assistant messages
    });

    this.logger.log(
      `✅ Successfully saved assistant response for thread ${threadId}`,
      {
        threadId,
        messageId,
        responseLength: responseText.length,
        runId,
        userId,
        agentId, // ✅ NEW: Log agent ID
        hasTokenData: false,
      },
    );

    // Emit message_created event for responses (but NO update_rpoint event)
    if (emitEventCallback) {
      try {
        await emitEventCallback({
          type: 'message_created',
          data: {
            message_id: messageId,
            thread_id: threadId,
            role: 'assistant',
            content_preview: responseText.substring(0, 100),
          },
        });

        this.logger.debug(`📡 Emitted message_created event for response`, {
          threadId,
          runId,
          messageId,
        });
      } catch (eventError) {
        this.logger.error(
          `Failed to emit message_created event for response:`,
          {
            threadId,
            runId,
            messageId,
            error: eventError.message,
          },
        );
        // Don't throw - event emission failure should not disrupt message saving
      }
    }

    return messageId;
  }
}
