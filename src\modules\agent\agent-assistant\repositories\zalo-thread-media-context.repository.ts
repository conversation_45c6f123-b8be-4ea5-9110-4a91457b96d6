import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ZaloThreadMediaContext } from '../entities/zalo-thread-media-context.entity';
import { MediaWithContextData } from '../interfaces/media-context-data.interface';


@Injectable()
export class ZaloThreadMediaContextRepository {
  constructor(
    @InjectRepository(ZaloThreadMediaContext)
    private readonly repository: Repository<ZaloThreadMediaContext>,
  ) {}

  async create(data: Partial<ZaloThreadMediaContext>): Promise<ZaloThreadMediaContext> {
    const entity = this.repository.create(data);
    return this.repository.save(entity);
  }

  async findByThreadId(threadId: string): Promise<ZaloThreadMediaContext[]> {
    return this.repository.find({ 
      where: { threadId },
      order: { createdAt: 'DESC' }
    });
  }

  async findByThreadAndMedia(threadId: string, zaloMediaId: string): Promise<ZaloThreadMediaContext | null> {
    return this.repository.findOne({ 
      where: { threadId, zaloMediaId } 
    });
  }

  async delete(threadId: string, zaloMediaId: string): Promise<void> {
    await this.repository.delete({ threadId, zaloMediaId });
  }

  async deleteByThreadId(threadId: string): Promise<void> {
    await this.repository.delete({ threadId });
  }

  /**
   * Get media with context data using proper JOIN query
   */
  async findMediaWithContextByThreadId(threadId: string): Promise<MediaWithContextData[]> {
    return this.repository
      .createQueryBuilder('ztmc')
      .leftJoin('zalo_media', 'zm', 'ztmc.zalo_media_id = zm.id')
      .select([
        // Context fields (composite key as contextId)
        'ztmc.zalo_media_id as "contextId"',
        'ztmc.thread_id as "threadId"',
        'ztmc.zalo_media_id as "zaloMediaId"',
        'ztmc.context_type as "contextType"',
        'ztmc.human_notes as "humanNotes"',
        'ztmc.created_at as "contextCreatedAt"',

        // Media fields (nullable) - removed width and height
        'zm.id as "mediaId"',
        'zm.file_name as "fileName"',
        'zm.description as "description"',
        'zm.s3_key as "s3Key"',
        'zm.mime_type as "mimeType"',
        'zm.tags as "tags"',
        'zm.created_at as "mediaCreatedAt"'
      ])
      .where('ztmc.thread_id = :threadId', { threadId })
      .orderBy('ztmc.created_at', 'DESC')
      .getRawMany();
  }
}
