/**
 * Input modality enum - supported input data types
 */
export enum InputModalityEnum {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
}

/**
 * Output modality enum - supported output data types
 */
export enum OutputModalityEnum {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
}

/**
 * Sampling parameter enum - model sampling parameters
 */
export enum SamplingParameterEnum {
  TEMPERATURE = 'temperature',
  TOP_P = 'top_p',
  TOP_K = 'top_k',
  MAX_TOKENS = 'max_tokens',
  MAX_OUTPUT_TOKENS = 'max_output_tokens',
}

/**
 * Model feature enum - special model capabilities
 */
export enum ModelFeatureEnum {
  TOOL_CALL = 'tool_call',
  PARALLEL_TOOL_CALL = 'parallel_tool_call',
  FORCED_TOOL_CALL = 'forced_tool_call',
}

/**
 * Model provider enum - AI model providers
 */
export enum ProviderEnum {
  OPENAI = 'OPENAI',
  ANTHROPIC = 'ANTHROPIC',
  GOOGLE = 'GOOGLE',
  XAI = 'XAI',
  DEEPSEEK = 'DEEPSEEK',
}
