# SMS Campaign Worker - RedAI

## Tổng quan

SMS Campaign Worker xử lý các job gửi SMS marketing campaigns từ queue. Worker hỗ trợ:

✅ **Batch Processing**: Xử lý nhiều recipients trong một job  
✅ **Template Support**: Hỗ trợ template SMS với variable replacement  
✅ **FPT SMS Integration**: Tích hợp với FPT SMS provider  
✅ **Campaign Types**: Hỗ trợ OTP và Campaign Ads  
✅ **Error Handling**: Retry logic và logging chi tiết  
✅ **Status Tracking**: Cập nhật trạng thái campaign real-time  

## Cấu trúc Implementation

### 1. Queue Configuration
- **Queue Name**: `QueueName.SMS = 'sms'`
- **Job Name**: `SmsJobName.SMS_MARKETING = 'sms-marketing'`
- **Concurrency**: 5 jobs đồng thời

### 2. Job Data Structure

```typescript
interface SmsMarketingJobDto {
  campaignId: number;
  content: string; // Nội dung SMS đã được xử lý sẵn từ app
  recipients: SmsRecipientDto[];
  smsServerConfig: Record<string, any>; // Pre-calculated SMS config
  campaignType: SmsCampaignType; // OTP | CAMPAIGN_ADS
  campaignName?: string; // Tên campaign cho FPT SMS
  createdAt: number;
  isAdminCampaign?: boolean; // Flag để phân biệt admin campaign
}

interface SmsRecipientDto {
  audienceId: number;
  phone: string;
  customFields?: Record<string, any>;
}
```

### 3. Campaign Types

```typescript
enum SmsCampaignType {
  OTP = 'OTP',                    // SMS OTP
  CAMPAIGN_ADS = 'CAMPAIGN_ADS'   // SMS quảng cáo
}
```

## Luồng xử lý

```
App chính → SmsQueue.add(SMS_MARKETING, jobData)
    ↓
Queue SMS (Bull/Redis)
    ↓
SmsCampaignProcessor.process()
    ↓
1. Validate job data
2. Update campaign status → SENDING
3. Get SMS content (template hoặc campaign)
4. Get SMS server config
5. Process each recipient:
   - Get audience custom fields
   - Combine variables
   - Process template
   - Send SMS (OTP hoặc Brandname)
6. Update campaign counts & status
    ↓
FPT SMS API
```

## Cách sử dụng

### 1. Gửi SMS Campaign với nội dung đã xử lý

```typescript
import { SmsMarketingJobDto, SmsCampaignType } from '@/modules/marketing/sms';

const jobData: SmsMarketingJobDto = {
  campaignId: 123,
  content: "Xin chào Nguyễn Văn A! Đơn hàng 500000đ đã được xác nhận.", // Đã xử lý sẵn
  recipients: [
    {
      audienceId: 456,
      phone: "**********",
      customFields: {
        customerName: "Nguyễn Văn A",
        orderTotal: "500000"
      }
    }
  ],
  smsServerConfig: {
    provider: 'FPT_SMS',
    apiUrl: 'https://api01.sms.fpt.net',
    brandName: 'REDAI',
    integrationId: 'uuid-integration-id'
  },
  campaignType: SmsCampaignType.CAMPAIGN_ADS,
  campaignName: "OrderConfirmation_Campaign_123",
  createdAt: Date.now(),
  isAdminCampaign: true
};

await smsQueue.add(SmsJobName.SMS_MARKETING, jobData);
```

### 2. Gửi SMS OTP

```typescript
const jobData: SmsMarketingJobDto = {
  campaignId: 125,
  content: "Mã xác thực của Phạm Thị D là: 123456. Vui lòng không chia sẻ mã này.", // Đã xử lý sẵn
  recipients: [
    {
      audienceId: 458,
      phone: "**********",
      customFields: {
        otpCode: "123456",
        userName: "Phạm Thị D"
      }
    }
  ],
  smsServerConfig: {
    provider: 'FPT_SMS',
    apiUrl: 'https://api01.sms.fpt.net',
    brandName: 'REDAI'
  },
  campaignType: SmsCampaignType.OTP,
  campaignName: "OTP_Verification_125", // Không bắt buộc cho OTP
  createdAt: Date.now()
};
```

## Content Processing

Worker nhận nội dung SMS đã được xử lý sẵn từ app, không cần xử lý template variables.

App sẽ:
1. Lấy template từ database (nếu có)
2. Xử lý template variables với custom fields
3. Gửi nội dung cuối cùng đến worker

Worker chỉ cần:
1. Validate nội dung SMS
2. Gửi SMS với nội dung đã sẵn sàng

## SMS Server Configuration

Worker chỉ hỗ trợ **FPT SMS** provider:

```typescript
// Trong bảng sms_server_configurations
{
  providerName: 'FPT_SMS',
  apiKey: 'client_secret',
  endpoint: 'https://api.fpt.net/api',
  additionalSettings: {
    clientId: 'your_client_id',
    brandName: 'REDAI'
  }
}
```

## Job Options

```typescript
{
  attempts: 3,
  backoff: {
    type: 'exponential',
    delay: 2000,
  },
  removeOnComplete: 100,
  removeOnFail: 50,
}
```

## Testing

Chạy script test:

```bash
npm run ts-node src/modules/marketing/sms/test-sms-campaign.ts
```

## Monitoring

- **Bull Dashboard**: `/queues` - Xem trạng thái jobs
- **Logs**: Chi tiết trong console với level DEBUG
- **Campaign Status**: Cập nhật real-time trong database

## Error Handling

- **Validation**: Kiểm tra job data trước khi xử lý
- **Retry**: 3 lần với exponential backoff
- **Logging**: Chi tiết lỗi cho debugging
- **Status Update**: Cập nhật campaign status khi lỗi

## Notes

- Worker chỉ xử lý job có name = `SMS_MARKETING`
- Hỗ trợ SMS dài (concatenated SMS)
- Validate số điện thoại Việt Nam
- Thread-safe khi cập nhật database
- Rate limiting được handle bởi FPT SMS provider
