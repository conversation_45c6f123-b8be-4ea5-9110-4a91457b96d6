import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { GOOGLE_ERROR_CODES, handleGoogleApiError } from '../exceptions/google.exception';
import { GoogleGmailApiService } from './google-gmail-api.service';
import {
  GmailTokens,
  GmailMessage,
  GmailSendResult,
  GmailEmailTemplate,
  GmailBulkEmail,
  GmailEmailStats,
  GmailSearchOptions,
  GmailSearchResult,
  GmailMessageInfo,
  GmailQuotaInfo,
} from '../interfaces/google-gmail.interface';

/**
 * Service xử lý gửi email qua Gmail API
 */
@Injectable()
export class GoogleGmailEmailService {
  private readonly logger = new Logger(GoogleGmailEmailService.name);

  constructor(private readonly gmailApiService: GoogleGmailApiService) {}

  /**
   * Gửi email đơn lẻ
   * @param tokens Gmail tokens
   * @param message Email message
   * @returns <PERSON>ế<PERSON> quả gửi email
   */
  async sendSingleEmail(tokens: GmailTokens, message: GmailMessage): Promise<GmailSendResult> {
    try {
      // Validate email addresses
      this.validateEmailAddress(message.to);
      if (message.cc) {
        message.cc.forEach(email => this.validateEmailAddress(email));
      }
      if (message.bcc) {
        message.bcc.forEach(email => this.validateEmailAddress(email));
      }

      // Sanitize content if HTML
      if (message.isHtml) {
        message.body = this.sanitizeContent(message.body);
      }

      this.gmailApiService.setCredentials(tokens);
      const result = await this.gmailApiService.sendEmail(message);
      
      this.logger.log(`Email sent successfully to ${message.to}, messageId: ${result.messageId}`);
      return result;
    } catch (error) {
      this.logger.error(`Error sending email to ${message.to}: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      const errorCode = handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_API_UNKNOWN_ERROR);
      throw new AppException(
        errorCode,
        `Không thể gửi email đến ${message.to}: ${error.message}`,
      );
    }
  }

  /**
   * Gửi email từ template
   * @param tokens Gmail tokens
   * @param to Email người nhận
   * @param template Email template
   * @param variables Variables để thay thế trong template
   * @returns Kết quả gửi email
   */
  async sendTemplateEmail(
    tokens: GmailTokens,
    to: string,
    template: GmailEmailTemplate,
    variables?: Record<string, string>,
  ): Promise<GmailSendResult> {
    try {
      const mergedVariables = { ...template.variables, ...variables };
      
      const message: GmailMessage = {
        to,
        subject: this.replaceVariables(template.subject, mergedVariables),
        body: this.replaceVariables(template.body, mergedVariables),
        isHtml: template.isHtml || false,
      };

      return await this.sendSingleEmail(tokens, message);
    } catch (error) {
      this.logger.error(`Error sending template email: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        GOOGLE_ERROR_CODES.GOOGLE_API_UNKNOWN_ERROR,
        `Không thể gửi template email: ${error.message}`,
      );
    }
  }

  /**
   * Gửi email hàng loạt
   * @param tokens Gmail tokens
   * @param bulkEmail Thông tin bulk email
   * @returns Stats gửi email
   */
  async sendBulkEmails(tokens: GmailTokens, bulkEmail: GmailBulkEmail): Promise<GmailEmailStats> {
    const stats: GmailEmailStats = {
      sent: 0,
      failed: 0,
      total: bulkEmail.recipients.length,
      errors: [],
      startTime: new Date(),
    };

    const batchSize = bulkEmail.batchSize || 10;
    const delayMs = bulkEmail.delayMs || 1000;
    const maxRetries = bulkEmail.maxRetries || 3;

    this.logger.log(`Starting bulk email send to ${stats.total} recipients`);

    try {
      // Process in batches
      for (let i = 0; i < bulkEmail.recipients.length; i += batchSize) {
        const batch = bulkEmail.recipients.slice(i, i + batchSize);
        
        const batchPromises = batch.map(async (recipient) => {
          let retryCount = 0;
          let lastError: Error | null = null;

          while (retryCount <= maxRetries) {
            try {
              await this.sendTemplateEmail(
                tokens,
                recipient.email,
                bulkEmail.template,
                recipient.variables,
              );
              stats.sent++;
              this.logger.debug(`Email sent successfully to ${recipient.email}`);
              return; // Success, exit retry loop
            } catch (error) {
              lastError = error;
              retryCount++;
              
              if (retryCount <= maxRetries && bulkEmail.retryOnFailure) {
                this.logger.warn(`Retry ${retryCount}/${maxRetries} for ${recipient.email}: ${error.message}`);
                await this.delay(1000 * retryCount); // Exponential backoff
              }
            }
          }

          // All retries failed
          stats.failed++;
          stats.errors.push({
            email: recipient.email,
            error: lastError?.message || 'Unknown error',
            retryCount: retryCount - 1,
          });
          this.logger.warn(`Failed to send email to ${recipient.email} after ${retryCount - 1} retries`);
        });

        await Promise.all(batchPromises);

        // Delay between batches to avoid rate limiting
        if (i + batchSize < bulkEmail.recipients.length && delayMs > 0) {
          await this.delay(delayMs);
        }
      }

      stats.endTime = new Date();
      stats.processingTime = stats.endTime.getTime() - stats.startTime.getTime();

      this.logger.log(`Bulk email completed. Sent: ${stats.sent}, Failed: ${stats.failed}, Time: ${stats.processingTime}ms`);
      return stats;
    } catch (error) {
      this.logger.error(`Error in bulk email send: ${error.message}`, error.stack);
      
      stats.endTime = new Date();
      stats.processingTime = stats.endTime.getTime() - stats.startTime.getTime();

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        GOOGLE_ERROR_CODES.GOOGLE_API_UNKNOWN_ERROR,
        `Lỗi trong quá trình gửi bulk email: ${error.message}`,
      );
    }
  }

  /**
   * Gửi email test
   * @param tokens Gmail tokens
   * @param to Email người nhận
   * @param customSubject Subject tùy chỉnh
   * @param customBody Body tùy chỉnh
   * @returns Kết quả gửi email
   */
  async sendTestEmail(
    tokens: GmailTokens,
    to: string,
    customSubject?: string,
    customBody?: string,
  ): Promise<GmailSendResult> {
    const message: GmailMessage = {
      to,
      subject: customSubject || 'Test Gmail API Connection',
      body: customBody || 'This is a test email to verify Gmail API connection. If you receive this email, the integration is working correctly.',
      isHtml: false,
    };

    return await this.sendSingleEmail(tokens, message);
  }

  /**
   * Lấy danh sách email đã gửi
   * @param tokens Gmail tokens
   * @param options Search options
   * @returns Danh sách emails
   */
  async getSentEmails(
    tokens: GmailTokens,
    options: GmailSearchOptions = {},
  ): Promise<GmailSearchResult> {
    try {
      this.gmailApiService.setCredentials(tokens);
      
      const searchOptions: GmailSearchOptions = {
        ...options,
        query: options.query ? `in:sent ${options.query}` : 'in:sent',
      };

      return await this.gmailApiService.listEmails(searchOptions);
    } catch (error) {
      this.logger.error(`Error getting sent emails: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      const errorCode = handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_API_UNKNOWN_ERROR);
      throw new AppException(
        errorCode,
        'Không thể lấy danh sách email đã gửi',
      );
    }
  }

  /**
   * Lấy chi tiết email
   * @param tokens Gmail tokens
   * @param messageId ID của email
   * @returns Chi tiết email
   */
  async getEmailDetails(tokens: GmailTokens, messageId: string): Promise<GmailMessageInfo> {
    try {
      this.gmailApiService.setCredentials(tokens);
      return await this.gmailApiService.getEmail(messageId);
    } catch (error) {
      this.logger.error(`Error getting email details: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }

      const errorCode = handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_API_UNKNOWN_ERROR);
      throw new AppException(
        errorCode,
        'Không thể lấy chi tiết email',
      );
    }
  }

  /**
   * Kiểm tra quota Gmail API
   * @param tokens Gmail tokens
   * @returns Thông tin quota
   */
  async checkQuota(tokens: GmailTokens): Promise<GmailQuotaInfo> {
    try {
      this.gmailApiService.setCredentials(tokens);
      const canSend = await this.gmailApiService.testConnection();
      
      return {
        canSend,
        dailyLimit: 1000000000, // 1 billion per day (default)
        perSecondLimit: 250, // 250 per second (default)
        resetTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // Next day
      };
    } catch (error) {
      this.logger.error(`Error checking Gmail quota: ${error.message}`, error.stack);
      
      return {
        canSend: false,
        dailyLimit: 0,
        perSecondLimit: 0,
      };
    }
  }

  /**
   * Thay thế variables trong string
   * @param text Text chứa variables
   * @param variables Object chứa variables
   * @returns Text đã thay thế variables
   */
  private replaceVariables(text: string, variables?: Record<string, string>): string {
    if (!variables || Object.keys(variables).length === 0) {
      return text;
    }

    let result = text;
    for (const [key, value] of Object.entries(variables)) {
      // Support both {{variable}} and {variable} formats
      const regex1 = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      const regex2 = new RegExp(`\\{${key}\\}`, 'g');
      result = result.replace(regex1, value).replace(regex2, value);
    }

    return result;
  }

  /**
   * Delay function
   * @param ms Milliseconds to delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Validate email address
   * @param email Email address
   * @returns True if valid
   */
  private validateEmailAddress(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(email);
    
    if (!isValid) {
      throw new AppException(
        GOOGLE_ERROR_CODES.GOOGLE_API_UNKNOWN_ERROR,
        `Email address không hợp lệ: ${email}`,
      );
    }
    
    return true;
  }

  /**
   * Sanitize email content
   * @param content Email content
   * @returns Sanitized content
   */
  private sanitizeContent(content: string): string {
    // Basic sanitization - remove potentially harmful content
    return content
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript:/gi, '');
  }
}
