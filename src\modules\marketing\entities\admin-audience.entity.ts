import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng admin_audience trong cơ sở dữ liệu
 * Bảng khách hàng của admin
 */
@Entity('admin_audience')
export class AdminAudience {
  /**
   * ID của audience
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Tên của khách hàng
   */
  @Column({
    name: 'name',
    length: 255,
    nullable: true,
    comment: 'Tên khách hàng',
  })
  name: string;

  /**
   * Email của khách hàng
   */
  @Column({
    name: 'email',
    length: 255,
    nullable: true,
    comment: 'Email người dùng',
  })
  email: string;

  /**
   * Mã quốc gia (số)
   * @example 84
   */
  @Column({
    name: 'country_code',
    type: 'integer',
    nullable: true,
    comment: 'Mã quốc gia',
  })
  countryCode: number | null;

  /**
   * <PERSON><PERSON> điện thoại (không bao gồm mã quốc gia)
   * @example "912345678"
   */
  @Column({
    name: 'phone_number',
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: 'Số điện thoại không bao gồm mã quốc gia',
  })
  phoneNumber: string | null;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', comment: 'Ngày tạo' })
  createdAt: number;

  /**
   * URL avatar của khách hàng (S3 key)
   */
  @Column({
    name: 'avatar',
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: 'URL avatar của khách hàng',
  })
  avatar: string | null;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: true,
    comment: 'Ngày cập nhật',
  })
  updatedAt: number;
}
