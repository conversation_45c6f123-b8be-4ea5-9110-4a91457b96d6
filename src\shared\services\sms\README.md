# Services SMS

Ce module fournit une intégration avec différents fournisseurs de services SMS, permettant d'envoyer des SMS, des SMS brandname et des OTP via une interface unifiée.

## Fournisseurs supportés

- **SpeedSMS**: Fournisseur SMS vietnamien
- **Twilio**: Fournisseur SMS international
- **Vonage** (anciennement Nexmo): Fournisseur SMS international
- **FPT SMS**: Fournisseur SMS vietnamien

## Installation

### Dépendances

Selon les fournisseurs que vous souhaitez utiliser, vous devrez installer les packages suivants:

```bash
# Pour Twilio
npm install twilio

# Pour Vonage
npm install @vonage/server-sdk
```

### Configuration

Ajoutez les variables d'environnement suivantes à votre fichier `.env`:

```env
# Fournisseur par défaut
SMS_DEFAULT_PROVIDER=SPEED_SMS

# SpeedSMS
SPEED_SMS_API_TOKEN=votre_token_api
SPEED_SMS_API_URL=https://api.speedsms.vn/index.php
SPEED_SMS_TYPE=2
SPEED_SMS_SENDER=

# Twilio
TWILIO_ACCOUNT_SID=votre_account_sid
TWILIO_AUTH_TOKEN=votre_auth_token
TWILIO_PHONE_NUMBER=votre_numero_de_telephone
TWILIO_MESSAGING_SERVICE_SID=votre_messaging_service_sid

# Vonage
VONAGE_API_KEY=votre_api_key
VONAGE_API_SECRET=votre_api_secret
VONAGE_FROM=Vonage

# FPT SMS
FPT_SMS_CLIENT_ID=votre_client_id
FPT_SMS_CLIENT_SECRET=votre_client_secret
FPT_SMS_SCOPE=send_brandname_otp send_brandname
FPT_SMS_API_URL=http://api.fpt.net/api
FPT_SMS_BRANDNAME=votre_brandname
```

## Utilisation

### Importer le module

```typescript
import { SmsModule } from '@shared/services/sms';
```

Le module est déjà importé dans le `ServicesModule` global, donc vous n'avez pas besoin de l'importer explicitement dans vos modules.

### Envoyer un SMS

```typescript
import { Injectable } from '@nestjs/common';
import { SmsService, SmsProviderType } from '@shared/services/sms';

@Injectable()
export class VotreService {
  constructor(private readonly smsService: SmsService) {}

  async envoyerSms() {
    const resultat = await this.smsService.sendSms(
      '**********',
      'Votre message ici',
      {
        // Optionnel: spécifier le fournisseur à utiliser
        providerType: SmsProviderType.SPEED_SMS,
        
        // Options spécifiques au fournisseur
        // Pour SpeedSMS
        smsType: 2, // 2: CSKH, 3: Brandname, 4: Notify, 5: App Android, 6: Numéro fixe
        sender: 'BRAND', // Obligatoire si smsType = 3 ou 5
        
        // Pour Twilio
        from: '+**********',
        messagingServiceSid: 'MGXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
        
        // Pour Vonage
        from: 'BRAND',
        type: 'text',
        
        // Pour FPT SMS
        brandName: 'BRAND',
      }
    );

    if (resultat.success) {
      console.log(`SMS envoyé avec succès, ID: ${resultat.messageId}`);
    } else {
      console.error(`Erreur lors de l'envoi du SMS: ${resultat.errorMessage}`);
    }
  }
}
```

### Envoyer un SMS avec un brandname

```typescript
const resultat = await this.smsService.sendBrandnameSms(
  '**********',
  'Votre message ici',
  'VOTRE_BRANDNAME',
  {
    // Options supplémentaires
    providerType: SmsProviderType.FPT_SMS,
    
    // Pour FPT SMS
    campaignName: 'Campagne_Test',
    scheduleTime: '2023-12-31 12:00',
    quota: 1,
  }
);
```

### Envoyer un SMS OTP

```typescript
const resultat = await this.smsService.sendOtp(
  '**********',
  '123456',
  {
    // Options supplémentaires
    providerType: SmsProviderType.SPEED_SMS,

    // Modèle de message OTP
    template: 'Votre code de vérification est: {code}',
  }
);
```

### Envoyer un SMS CSKH quốc tế (FPT SMS)

```typescript
// Sử dụng trực tiếp FptSmsProvider
import { FptSmsProvider } from '@shared/services/sms';

@Injectable()
export class VotreService {
  constructor(private readonly fptSmsProvider: FptSmsProvider) {}

  async envoyerSmsInternational() {
    const resultat = await this.fptSmsProvider.sendInternationalSms(
      '**********', // Số điện thoại quốc tế (US)
      'Hello! Your verification code is: 123456',
      {
        brandName: 'REDAI', // Brandname đã đăng ký với FPT
        requestId: 'unique-request-id-123', // Tùy chọn
      }
    );

    if (resultat.success) {
      console.log(`SMS quốc tế đã gửi thành công, ID: ${resultat.messageId}`);
      console.log('Response data:', resultat.rawResponse);
    } else {
      console.error(`Lỗi gửi SMS quốc tế: ${resultat.errorMessage}`);
    }
  }
}
```

**Lưu ý quan trọng cho SMS quốc tế:**
- Số điện thoại phải ở định dạng quốc tế (không có dấu + hoặc 00)
- Ví dụ: `**********` cho US, `**********` cho Singapore
- Nội dung tin nhắn sẽ được tự động mã hóa Base64
- Cần scope `send_brandname_otp` trong cấu hình FPT SMS

### Khởi tạo Campaign (FPT SMS)

```typescript
// Sử dụng trực tiếp FptSmsProvider
import { FptSmsProvider, FptCreateCampaignRequest } from '@shared/services/sms';

@Injectable()
export class VotreService {
  constructor(private readonly fptSmsProvider: FptSmsProvider) {}

  async taoMoiCampaign() {
    const campaignRequest: FptCreateCampaignRequest = {
      campaignName: `Campaign_${Date.now()}`, // Tên campaign duy nhất
      brandName: 'REDAI', // Brandname đã đăng ký với FPT
      message: 'Chương trình khuyến mãi đặc biệt! Giảm giá 50% cho đơn hàng trên 100k.',
      scheduleTime: '2024-12-31 09:00', // Thời gian gửi (yyyy-mm-dd HH:ii)
      quota: 1000, // Hạn mức gửi tin
    };

    const result = await this.fptSmsProvider.createCampaign(campaignRequest);

    if (result.success) {
      console.log(`Campaign đã được tạo thành công với mã: ${result.campaignCode}`);
      console.log('Response data:', result.rawResponse);

      // Lưu campaignCode để sử dụng cho việc gửi tin sau này
      return result.campaignCode;
    } else {
      console.error(`Lỗi tạo campaign: ${result.errorMessage}`);
      if (result.errorCode) {
        console.error(`Mã lỗi: ${result.errorCode}`);
      }
      throw new Error(result.errorMessage);
    }
  }
}
```

**Lưu ý quan trọng cho Campaign:**
- Tên campaign phải duy nhất và chỉ được sử dụng một lần
- Thời gian schedule phải theo định dạng `yyyy-mm-dd HH:ii`
- Quota phải đủ cho số lượng tin nhắn dự kiến gửi
- Cần scope `send_brandname` trong cấu hình FPT SMS
- Campaign code trả về sẽ được sử dụng để gửi tin nhắn thực tế

### Utility Methods cho Campaign

```typescript
import { FptSmsProvider } from '@shared/services/sms';

// Tạo tên campaign duy nhất
const campaignName = FptSmsProvider.generateUniqueCampaignName('KhuyenMai');
// Kết quả: "KhuyenMai_1703123456789_abc123"

// Validate định dạng thời gian
const isValidTime = FptSmsProvider.validateScheduleTime('2024-12-31 09:00');
console.log(isValidTime); // true

const isInvalidTime = FptSmsProvider.validateScheduleTime('2024/12/31 09:00');
console.log(isInvalidTime); // false

// Tính toán quota cần thiết
const messageLength = 200; // Tin nhắn dài 200 ký tự
const phoneCount = 1000; // Gửi tới 1000 số điện thoại
const requiredQuota = FptSmsProvider.calculateQuota(messageLength, phoneCount);
console.log(requiredQuota); // 2000 (vì tin nhắn 200 ký tự = 2 SMS, 1000 số × 2 = 2000)

// Ví dụ sử dụng kết hợp
async function taoVaGuiCampaign() {
  const message = 'Chương trình khuyến mãi đặc biệt! Giảm giá 50% cho đơn hàng trên 100k. Áp dụng từ ngày 1/1 đến 31/1.';
  const phoneList = ['**********', '**********']; // Danh sách số điện thoại

  const campaignRequest: FptCreateCampaignRequest = {
    campaignName: FptSmsProvider.generateUniqueCampaignName('TetSale'),
    brandName: 'REDAI',
    message: message,
    scheduleTime: '2024-01-01 08:00',
    quota: FptSmsProvider.calculateQuota(message.length, phoneList.length),
  };

  // Validate trước khi gửi
  if (!FptSmsProvider.validateScheduleTime(campaignRequest.scheduleTime)) {
    throw new Error('Định dạng thời gian không hợp lệ');
  }

  const result = await fptSmsProvider.createCampaign(campaignRequest);
  return result;
}
```

### Envoyer des SMS en masse

```typescript
const resultat = await this.smsService.sendBulkSms(
  ['**********', '**********'],
  'Votre message ici',
  {
    // Options supplémentaires
    providerType: SmsProviderType.TWILIO,
  }
);

console.log(`${resultat.successCount} SMS envoyés avec succès, ${resultat.failureCount} échecs`);
```

### Vérifier le statut d'un message

```typescript
const statut = await this.smsService.checkMessageStatus(
  'message_id',
  SmsProviderType.VONAGE
);

console.log(`Statut du message: ${statut.status}`);
```

### Tester la connexion avec un fournisseur

```typescript
const test = await this.smsService.testConnection(
  SmsProviderType.FPT_SMS,
  {
    // Configuration spécifique pour le test
    clientId: 'nouveau_client_id',
    clientSecret: 'nouveau_client_secret',
  }
);

if (test.success) {
  console.log(`Connexion réussie: ${test.message}`);
} else {
  console.error(`Échec de la connexion: ${test.message}`);
}
```

## Extension

Pour ajouter un nouveau fournisseur SMS:

1. Créez une nouvelle classe qui étend `BaseSmsProvider` et implémente les méthodes requises
2. Ajoutez le nouveau fournisseur à l'énumération `SmsProviderType`
3. Mettez à jour la méthode `createProvider` dans `SmsProviderFactory`
4. Ajoutez le nouveau fournisseur aux providers dans `SmsModule`

## Dépannage

### Problèmes courants

- **Erreur "Le client X n'est pas initialisé"**: Assurez-vous d'avoir installé les packages requis et configuré les variables d'environnement correctement.
- **Erreur d'authentification**: Vérifiez vos identifiants API dans le fichier `.env`.
- **Format de numéro de téléphone incorrect**: Les numéros sont automatiquement formatés au format international, mais assurez-vous qu'ils sont valides.

### Journalisation

Tous les services SMS utilisent le système de journalisation de NestJS. Pour voir les journaux détaillés, configurez le niveau de journalisation sur `debug` dans votre application.
