import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SystemModels } from '../entities/system-models.entity';

@Injectable()
export class SystemModelsRepository {
  constructor(
    @InjectRepository(SystemModels)
    private readonly repository: Repository<SystemModels>,
  ) {}

  async findById(id: string): Promise<SystemModels | null> {
    return this.repository.findOne({ 
      where: { id, active: true }
    });
  }
}
