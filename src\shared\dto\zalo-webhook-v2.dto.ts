/**
 * New Zalo Webhook DTO interfaces that match the backend's dto-v2 structure
 * These interfaces match the actual webhook payload structure from Zalo
 */

/**
 * Enum for Zalo webhook event names
 */
export enum ZaloEventName {
  UserSendText = 'user_send_text',
  UserSendImage = 'user_send_image',
  UserSendGif = 'user_send_gif',
  UserSendLink = 'user_send_link',
  UserSendAudio = 'user_send_audio',
  UserSendVideo = 'user_send_video',
  UserSendSticker = 'user_send_sticker',
  UserSendLocation = 'user_send_location',
  UserSendFile = 'user_send_file',
  UserSendBusinessCard = 'user_send_business_card',
}

/**
 * Enum for attachment types
 */
export enum ZaloAttachmentType {
  Image = 'image',
  Gif = 'gif',
  Link = 'link',
  Audio = 'audio',
  Video = 'video',
  Sticker = 'sticker',
  Location = 'location',
  File = 'file',
}

/**
 * Base webhook payload structure
 */
export interface ZaloWebhookBaseDto {
  app_id: string;
  event_name: ZaloEventName;
  timestamp: string;
  user_id_by_app?: string;
}

/**
 * User information in webhook
 */
export interface ZaloUserDto {
  id: string;
  name?: string;
  avatar?: string;
}

/**
 * Official Account information in webhook
 */
export interface ZaloOfficialAccountDto {
  id: string;
}

/**
 * Base message structure
 */
export interface ZaloMessageBaseDto {
  msg_id: string;
  quote_msg_id?: string;
}

/**
 * Text message structure
 */
export interface ZaloTextMessageDto extends ZaloMessageBaseDto {
  text: string;
}

/**
 * Attachment payload structures
 */
export interface ZaloLinkPayloadDto {
  url: string;
  thumbnail?: string;
  description?: string;
}

export interface ZaloImagePayloadDto {
  url: string;
  thumbnail?: string;
}

export interface ZaloLocationCoordinatesDto {
  latitude: string;
  longitude: string;
}

export interface ZaloLocationPayloadDto {
  coordinates: ZaloLocationCoordinatesDto;
}

export interface ZaloFilePayloadDto {
  url: string;
  name: string;
  size: string;
  checksum: string;
  type: string;
}

export interface ZaloStickerPayloadDto {
  id: string;
  category?: string;
}

/**
 * Attachment structures
 */
export interface ZaloLinkAttachmentDto {
  type: ZaloAttachmentType.Link;
  payload: ZaloLinkPayloadDto;
}

export interface ZaloImageAttachmentDto {
  type: ZaloAttachmentType.Image;
  payload: ZaloImagePayloadDto;
}

export interface ZaloLocationAttachmentDto {
  type: ZaloAttachmentType.Location;
  payload: ZaloLocationPayloadDto;
}

export interface ZaloStickerAttachmentDto {
  type: ZaloAttachmentType.Sticker;
  payload: ZaloStickerPayloadDto;
}

/**
 * Message structures with attachments
 */
export interface ZaloImageMessageDto extends ZaloMessageBaseDto {
  attachments: ZaloImageAttachmentDto[];
}

export interface ZaloLinkMessageDto extends ZaloMessageBaseDto {
  attachments: ZaloLinkAttachmentDto[];
}

export interface ZaloLocationMessageDto extends ZaloMessageBaseDto {
  attachments: ZaloLocationAttachmentDto[];
}

export interface ZaloStickerMessageDto extends ZaloMessageBaseDto {
  attachments: ZaloStickerAttachmentDto[];
}

export interface ZaloBusinessCardMessageDto extends ZaloMessageBaseDto {
  text?: string;
  client_msg_id?: string;
  attachments: ZaloLinkAttachmentDto[];
}

/**
 * Event-specific webhook DTOs
 */
export interface UserSendTextWebhookDto extends ZaloWebhookBaseDto {
  event_name: ZaloEventName.UserSendText;
  sender: ZaloUserDto;
  recipient: ZaloOfficialAccountDto;
  message: ZaloTextMessageDto;
}

export interface UserSendImageWebhookDto extends ZaloWebhookBaseDto {
  event_name: ZaloEventName.UserSendImage;
  sender: ZaloUserDto;
  recipient: ZaloOfficialAccountDto;
  message: ZaloImageMessageDto;
}

export interface UserSendLinkWebhookDto extends ZaloWebhookBaseDto {
  event_name: ZaloEventName.UserSendLink;
  sender: ZaloUserDto;
  recipient: ZaloOfficialAccountDto;
  message: ZaloLinkMessageDto;
}

export interface UserSendLocationWebhookDto extends ZaloWebhookBaseDto {
  event_name: ZaloEventName.UserSendLocation;
  sender: ZaloUserDto;
  recipient: ZaloOfficialAccountDto;
  message: ZaloLocationMessageDto;
}

export interface UserSendStickerWebhookDto extends ZaloWebhookBaseDto {
  event_name: ZaloEventName.UserSendSticker;
  sender: ZaloUserDto;
  recipient: ZaloOfficialAccountDto;
  message: ZaloStickerMessageDto;
}

export interface UserSendBusinessCardWebhookDto extends ZaloWebhookBaseDto {
  event_name: ZaloEventName.UserSendBusinessCard;
  sender: ZaloUserDto;
  recipient: ZaloOfficialAccountDto;
  message: ZaloBusinessCardMessageDto;
}

/**
 * Union type for all webhook events
 */
export type ZaloWebhookDto = 
  | UserSendTextWebhookDto
  | UserSendImageWebhookDto
  | UserSendLinkWebhookDto
  | UserSendLocationWebhookDto
  | UserSendStickerWebhookDto
  | UserSendBusinessCardWebhookDto;

/**
 * Type guard functions
 */
export function isUserSendTextWebhook(webhook: ZaloWebhookDto): webhook is UserSendTextWebhookDto {
  return webhook.event_name === ZaloEventName.UserSendText;
}

export function isUserSendImageWebhook(webhook: ZaloWebhookDto): webhook is UserSendImageWebhookDto {
  return webhook.event_name === ZaloEventName.UserSendImage;
}

export function isUserSendBusinessCardWebhook(webhook: ZaloWebhookDto): webhook is UserSendBusinessCardWebhookDto {
  return webhook.event_name === ZaloEventName.UserSendBusinessCard;
}

/**
 * Helper function to check if webhook is a user message event
 */
export function isUserMessageEvent(webhook: ZaloWebhookDto): boolean {
  return [
    ZaloEventName.UserSendText,
    ZaloEventName.UserSendImage,
    ZaloEventName.UserSendGif,
    ZaloEventName.UserSendLink,
    ZaloEventName.UserSendLocation,
    ZaloEventName.UserSendFile,
    ZaloEventName.UserSendAudio,
    ZaloEventName.UserSendVideo,
    ZaloEventName.UserSendSticker,
    ZaloEventName.UserSendBusinessCard,
  ].includes(webhook.event_name);
}
