import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ZaloOfficialAccount } from '../entities/zalo-official-account.entity';

@Injectable()
export class ZaloOfficialAccountRepository {
  constructor(
    @InjectRepository(ZaloOfficialAccount)
    private readonly repository: Repository<ZaloOfficialAccount>,
  ) {}

  /**
   * Get OA by OA ID
   * @param oaId Zalo OA ID
   * @returns OA data if found and active
   */
  async getOaByOaId(oaId: string): Promise<ZaloOfficialAccount | null> {
    return this.repository.findOne({
      where: {
        oaId,
        status: 'active',
      },
    });
  }

  /**
   * Get agent configuration from agents_user table
   * @param agentId Agent ID
   * @param userId User ID for security
   * @returns Agent configuration or null
   */
  async getAgentConfig(agentId: string, userId: string): Promise<any | null> {
    const result = await this.repository.query(
      `SELECT config FROM agents_user WHERE id = $1 AND user_id = $2`,
      [agentId, userId]
    );

    return result.length > 0 ? result[0].config : null;
  }
}
