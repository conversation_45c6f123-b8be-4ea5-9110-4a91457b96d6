import { Injectable } from '@nestjs/common';
import { NodeGroupEnum } from '../../../../../enums/node-group.enum';
import {
  EAuthType,
  IHttpRequestInput,
  IHttpRequestParameters,
  validateHttpRequestParameters
} from '../../../../../interfaces';
import { ENodeType } from '../../../../../interfaces/node-manager.interface';
import { DetailedNodeExecutionResult, NodeExecutionConfig } from '../../../types';
import { BaseNodeExecutor } from '../../base/base-node.executor';
import {
  ExecutorContext,
  ValidationResult,
} from '../../base/node-executor.interface';
import { HttpClientService } from '../../shared/http-client.service';
import { ValidationUtils } from '../../shared/validation.utils';

/**
 * Executor for HTTP_REQUEST node type
 * Handles HTTP requests with authentication, retry logic, and response processing
 */
@Injectable()
export class HttpRequestExecutor extends BaseNodeExecutor {
  readonly nodeGroup = NodeGroupEnum.HTTP;
  readonly supportedNodeTypes = [ENodeType.HTTP_REQUEST];
  readonly executorName = 'HttpRequestExecutor';
  readonly version = '1.0.0';

  constructor(
    private readonly httpClientService: HttpClientService
  ) {
    super();
  }

  /**
   * Execute HTTP request node
   */
  protected async executeNode(
    context: ExecutorContext,
    config: NodeExecutionConfig
  ): Promise<DetailedNodeExecutionResult> {
    const startTime = Date.now();

    // Parse and validate parameters
    const params = context.node.parameters as IHttpRequestParameters;
    const input = context.inputData as IHttpRequestInput;

    try {

      this.logger.debug(`Executing HTTP request: ${params.method} ${params.url}`);

      // Execute HTTP request using shared service
      const result = await this.httpClientService.executeHttpRequest(params, input);

      const executionTime = Date.now() - startTime;

      return {
        nodeType: 'HTTP_REQUEST',
        success: result.success,
        outputData: result,
        metadata: {
          executionTime,
          statusCode: result.status_code,
          responseTime: result.response_time,
          responseSize: result.metadata?.content_length || 0,
          retryCount: 1, // Single attempt for now
          httpMethod: params.method,
          finalUrl: result.final_url,
          customMetrics: {
            statusCode: result.status_code,
            responseTime: result.response_time,
            responseSize: result.metadata?.content_length || 0,
            requestId: result.metadata?.request_id || 'unknown',
            success: result.success,
          },
          logs: [
            `HTTP ${params.method} ${params.url}`,
            `Status: ${result.status_code}`,
            `Response time: ${result.response_time}ms`,
            `Success: ${result.success}`,
            `Execution time: ${executionTime}ms`,
          ],
        },
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;

      return {
        nodeType: 'HTTP_REQUEST',
        success: false,
        error,
        outputData: {
          status_code: 0,
          status_text: 'Request Failed',
          headers: {},
          body: null,
          body_text: error.message,
          response_time: executionTime,
          final_url: params.url,
          success: false,
        },
        metadata: {
          executionTime,
          statusCode: 0,
          responseTime: executionTime,
          responseSize: 0,
          retryCount: 1,
          httpMethod: params.method,
          finalUrl: params.url,
        },
      };
    }
  }

  /**
   * Validate HTTP request node input
   */
  protected async validateNodeSpecificInput(
    context: ExecutorContext,
    result: ValidationResult
  ): Promise<void> {
    const params = context.node.parameters as IHttpRequestParameters;

    // Use existing validation function from interface
    const interfaceValidation = validateHttpRequestParameters(params);
    if (!interfaceValidation.isValid) {
      for (const error of interfaceValidation.errors) {
        ValidationUtils.addError(
          result,
          'INTERFACE_VALIDATION_ERROR',
          error,
          'parameters'
        );
      }
    }

    // Additional custom validations
    this.validateHttpRequestSpecific(params, result);
  }

  /**
   * HTTP request specific validations
   */
  private validateHttpRequestSpecific(
    params: IHttpRequestParameters,
    result: ValidationResult
  ): void {
    // Validate URL
    ValidationUtils.validateUrl(result, params.url, 'URL', 'url');

    // Validate method
    ValidationUtils.validateHttpMethod(result, params.method, 'HTTP method', 'method');

    // Validate timeout
    if (params.timeout) {
      ValidationUtils.validateTimeout(result, params.timeout, 'Timeout', 'timeout');
    }

    // Validate retry config
    if (params.retry_config) {
      const retry = params.retry_config;

      if (retry.max_retries !== undefined) {
        ValidationUtils.validateNumberRange(
          result,
          retry.max_retries,
          'Max retries',
          0,
          10,
          'retry_config.max_retries'
        );
      }

      if (retry.delay !== undefined) {
        ValidationUtils.validateNumberRange(
          result,
          retry.delay,
          'Retry delay',
          100,
          60000,
          'retry_config.delay'
        );
      }
    }

    // Validate authentication
    if (params.auth_config && params.auth_type) {
      this.validateAuthentication(params, result);
    }

    // Validate headers
    if (params.headers) {
      ValidationUtils.validateObject(
        result,
        params.headers,
        'Headers',
        undefined,
        'headers'
      );
    }

    // Validate body
    if (params.body) {
      if (typeof params.body !== 'string' && typeof params.body !== 'object') {
        ValidationUtils.addError(
          result,
          'INVALID_BODY_TYPE',
          'Body must be a string or object',
          'body',
          params.body
        );
      }
    }

    // Performance warnings
    if (params.timeout && params.timeout > 30000) {
      ValidationUtils.addWarning(
        result,
        'HIGH_TIMEOUT',
        'High timeout value may cause workflow delays',
        'timeout',
        'Consider using a lower timeout value'
      );
    }

    if (params.retry_config?.max_retries && params.retry_config.max_retries > 5) {
      ValidationUtils.addWarning(
        result,
        'HIGH_RETRY_COUNT',
        'High retry count may cause long execution times',
        'retry_config.max_retries',
        'Consider using fewer retries'
      );
    }
  }

  /**
   * Validate authentication configuration
   */
  private validateAuthentication(params: IHttpRequestParameters, result: ValidationResult): void {
    ValidationUtils.validateRequired(result, params.auth_type, 'Authentication type', 'auth_type');

    if (!params.auth_config) {
      ValidationUtils.addError(
        result,
        'MISSING_AUTH_CONFIG',
        'Authentication config is required when auth_type is specified',
        'auth_config'
      );
      return;
    }

    const auth = params.auth_config;

    switch (params.auth_type) {
      case EAuthType.BEARER:
        ValidationUtils.validateRequired(
          result,
          auth.token,
          'Bearer token',
          'auth_config.token'
        );
        break;

      case EAuthType.API_KEY:
        ValidationUtils.validateRequired(
          result,
          auth.api_key,
          'API key',
          'auth_config.api_key'
        );
        break;

      case EAuthType.BASIC:
        ValidationUtils.validateRequired(
          result,
          auth.username,
          'Username',
          'auth_config.username'
        );
        ValidationUtils.validateRequired(
          result,
          auth.password,
          'Password',
          'auth_config.password'
        );
        break;

      case EAuthType.OAUTH2:
        ValidationUtils.validateRequired(
          result,
          auth.token,
          'OAuth2 access token',
          'auth_config.token'
        );
        break;

      case EAuthType.NONE:
        // No validation needed for NONE
        break;

      default:
        ValidationUtils.addError(
          result,
          'INVALID_AUTH_TYPE',
          `Unsupported authentication type: ${params.auth_type}`,
          'auth_type',
          params.auth_type
        );
        break;
    }
  }
}
