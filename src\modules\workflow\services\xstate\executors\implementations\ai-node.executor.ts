import { Injectable, Logger } from '@nestjs/common';
import { BaseNodeExecutor } from '../base/base-node.executor';
import { 
  Executor<PERSON>ontext, 
  ValidationResult,
  DetailedNodeExecutionResult 
} from '../base/node-executor.interface';
import { NodeExecutionConfig } from '../../types';
import { NodeGroupEnum } from '../../../../enums/node-group.enum';

/**
 * AI operation types
 */
type AIOperationType = 
  | 'text-generation'
  | 'text-completion'
  | 'text-analysis'
  | 'sentiment-analysis'
  | 'text-classification'
  | 'text-summarization'
  | 'text-translation'
  | 'text-extraction'
  | 'question-answering'
  | 'text-embedding'
  | 'image-analysis'
  | 'image-generation'
  | 'speech-to-text'
  | 'text-to-speech';

/**
 * AI model configuration
 */
interface AIModelConfig {
  /** Model provider */
  provider: 'openai' | 'anthropic' | 'google' | 'azure' | 'huggingface' | 'local';
  
  /** Model name */
  modelName: string;
  
  /** API key */
  apiKey?: string;
  
  /** API endpoint */
  endpoint?: string;
  
  /** Model parameters */
  parameters: {
    /** Temperature for randomness */
    temperature?: number;
    
    /** Maximum tokens to generate */
    maxTokens?: number;
    
    /** Top-p sampling */
    topP?: number;
    
    /** Frequency penalty */
    frequencyPenalty?: number;
    
    /** Presence penalty */
    presencePenalty?: number;
    
    /** Stop sequences */
    stop?: string[];
    
    /** Custom parameters */
    [key: string]: any;
  };
}

/**
 * AI node configuration
 */
interface AINodeConfig {
  /** Type of AI operation */
  operation: AIOperationType;
  
  /** Model configuration */
  model: AIModelConfig;
  
  /** Input configuration */
  input: {
    /** Input text/prompt */
    prompt?: string;
    
    /** System prompt */
    systemPrompt?: string;
    
    /** Input data field */
    inputField?: string;
    
    /** Context data */
    context?: any;
    
    /** Examples for few-shot learning */
    examples?: Array<{
      input: string;
      output: string;
    }>;
  };
  
  /** Output configuration */
  output: {
    /** Output format */
    format?: 'text' | 'json' | 'structured';
    
    /** JSON schema for structured output */
    schema?: any;
    
    /** Post-processing */
    postProcess?: {
      trim?: boolean;
      lowercase?: boolean;
      removeSpecialChars?: boolean;
      extractJson?: boolean;
    };
  };
  
  /** Processing options */
  options?: {
    /** Streaming response */
    streaming?: boolean;
    
    /** Retry on failure */
    retryOnFailure?: boolean;
    
    /** Max retries */
    maxRetries?: number;
    
    /** Timeout (ms) */
    timeout?: number;
    
    /** Cache responses */
    enableCache?: boolean;
    
    /** Cache TTL (seconds) */
    cacheTTL?: number;
  };
}

/**
 * AI execution result
 */
interface AIExecutionResult {
  /** Generated text/result */
  result: string | any;
  
  /** Token usage */
  tokenUsage: {
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
    cost?: number;
  };
  
  /** Model information */
  modelInfo: {
    provider: string;
    modelName: string;
    version?: string;
  };
  
  /** Processing metadata */
  metadata: {
    operationType: AIOperationType;
    executionTime: number;
    cached?: boolean;
    retryCount?: number;
    confidence?: number;
  };
  
  /** Raw response from provider */
  rawResponse?: any;
}

/**
 * Executor cho AI nodes - handle AI/LLM calls, text processing, analysis
 */
@Injectable()
export class AINodeExecutor extends BaseNodeExecutor {
  readonly nodeGroup = NodeGroupEnum.AI;
  readonly supportedNodeTypes = [
    'ai-text-generation',
    'ai-completion',
    'ai-analysis',
    'sentiment-analysis',
    'text-classification',
    'text-summarization',
    'text-translation',
    'text-extraction',
    'question-answering',
    'text-embedding',
    'image-analysis',
    'image-generation',
    'speech-to-text',
    'text-to-speech',
    'llm-call',
    'openai-call',
    'anthropic-call',
  ];
  readonly executorName = 'AINodeExecutor';
  readonly version = '1.0.0';
  
  /**
   * Execute AI node
   */
  protected async executeNode(
    context: ExecutorContext,
    config: NodeExecutionConfig
  ): Promise<DetailedNodeExecutionResult> {
    const startTime = Date.now();
    
    try {
      // Parse AI configuration
      const aiConfig = this.parseAIConfig(context);
      
      // Prepare input
      const input = await this.prepareInput(aiConfig, context);
      
      // Execute AI operation
      const result = await this.executeAIOperation(aiConfig, input, config);
      
      // Process output
      const outputData = this.processOutput(result, aiConfig);
      
      const executionTime = Date.now() - startTime;
      
      return {
        success: true,
        outputData,
        metadata: {
          executionTime,
          tokenUsage: result.tokenUsage,
          customMetrics: {
            operationType: result.metadata.operationType,
            provider: result.modelInfo.provider,
            modelName: result.modelInfo.modelName,
            inputTokens: result.tokenUsage.inputTokens,
            outputTokens: result.tokenUsage.outputTokens,
            totalTokens: result.tokenUsage.totalTokens,
            cost: result.tokenUsage.cost || 0,
            cached: result.metadata.cached || false,
            retryCount: result.metadata.retryCount || 0,
          },
          logs: [
            `AI operation: ${result.metadata.operationType}`,
            `Provider: ${result.modelInfo.provider}`,
            `Model: ${result.modelInfo.modelName}`,
            `Tokens: ${result.tokenUsage.totalTokens} (${result.tokenUsage.inputTokens} in, ${result.tokenUsage.outputTokens} out)`,
            `Cost: $${result.tokenUsage.cost || 0}`,
            `Execution time: ${executionTime}ms`,
          ],
        },
      };
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        success: false,
        error,
        shouldRetry: await this.shouldRetryAIError(error, context),
        metadata: {
          executionTime,
          logs: [
            `AI operation failed: ${error.message}`,
            `Execution time: ${executionTime}ms`,
          ],
        },
      };
    }
  }
  
  /**
   * Validate AI node input
   */
  protected async validateNodeSpecificInput(
    context: ExecutorContext,
    result: ValidationResult
  ): Promise<void> {
    const params = context.node.parameters as any;
    
    // Validate operation type
    if (!params.operation) {
      result.errors.push({
        code: 'MISSING_OPERATION',
        message: 'AI node requires an operation type',
        field: 'operation',
        severity: 'error',
      });
    } else if (!this.supportedNodeTypes.includes(params.operation)) {
      result.errors.push({
        code: 'INVALID_OPERATION',
        message: 'Invalid AI operation type',
        field: 'operation',
        currentValue: params.operation,
        expectedValue: this.supportedNodeTypes,
        severity: 'error',
      });
    }
    
    // Validate model configuration
    if (!params.model) {
      result.errors.push({
        code: 'MISSING_MODEL',
        message: 'AI node requires model configuration',
        field: 'model',
        severity: 'error',
      });
    } else {
      if (!params.model.provider) {
        result.errors.push({
          code: 'MISSING_PROVIDER',
          message: 'Model configuration requires provider',
          field: 'model.provider',
          severity: 'error',
        });
      }
      
      if (!params.model.modelName) {
        result.errors.push({
          code: 'MISSING_MODEL_NAME',
          message: 'Model configuration requires modelName',
          field: 'model.modelName',
          severity: 'error',
        });
      }
      
      // Validate model parameters
      if (params.model.parameters) {
        const modelParams = params.model.parameters;
        
        if (modelParams.temperature !== undefined && (modelParams.temperature < 0 || modelParams.temperature > 2)) {
          result.warnings.push({
            code: 'INVALID_TEMPERATURE',
            message: 'Temperature should be between 0 and 2',
            field: 'model.parameters.temperature',
            suggestion: 'Use a value between 0 (deterministic) and 2 (very random)',
          });
        }
        
        if (modelParams.maxTokens !== undefined && modelParams.maxTokens > 4096) {
          result.warnings.push({
            code: 'HIGH_MAX_TOKENS',
            message: 'High maxTokens may cause performance issues',
            field: 'model.parameters.maxTokens',
            suggestion: 'Consider reducing maxTokens for better performance',
          });
        }
        
        if (modelParams.topP !== undefined && (modelParams.topP < 0 || modelParams.topP > 1)) {
          result.warnings.push({
            code: 'INVALID_TOP_P',
            message: 'TopP should be between 0 and 1',
            field: 'model.parameters.topP',
            suggestion: 'Use a value between 0 and 1',
          });
        }
      }
    }
    
    // Validate input configuration
    if (!params.input) {
      result.errors.push({
        code: 'MISSING_INPUT',
        message: 'AI node requires input configuration',
        field: 'input',
        severity: 'error',
      });
    } else {
      if (!params.input.prompt && !params.input.inputField) {
        result.errors.push({
          code: 'MISSING_INPUT_SOURCE',
          message: 'Input configuration requires either prompt or inputField',
          field: 'input',
          severity: 'error',
        });
      }
    }
    
    // Validate API key for external providers
    if (params.model?.provider && ['openai', 'anthropic', 'google', 'azure'].includes(params.model.provider)) {
      if (!params.model.apiKey && !process.env[`${params.model.provider.toUpperCase()}_API_KEY`]) {
        result.warnings.push({
          code: 'MISSING_API_KEY',
          message: `API key required for ${params.model.provider}`,
          field: 'model.apiKey',
          suggestion: `Set apiKey in model config or ${params.model.provider.toUpperCase()}_API_KEY environment variable`,
        });
      }
    }
  }
  
  // Private helper methods
  
  private parseAIConfig(context: ExecutorContext): AINodeConfig {
    const params = context.node.parameters as any;
    
    return {
      operation: params.operation,
      model: {
        provider: params.model.provider,
        modelName: params.model.modelName,
        apiKey: params.model.apiKey || process.env[`${params.model.provider.toUpperCase()}_API_KEY`],
        endpoint: params.model.endpoint,
        parameters: {
          temperature: params.model.parameters?.temperature || 0.7,
          maxTokens: params.model.parameters?.maxTokens || 1000,
          topP: params.model.parameters?.topP || 1.0,
          frequencyPenalty: params.model.parameters?.frequencyPenalty || 0,
          presencePenalty: params.model.parameters?.presencePenalty || 0,
          stop: params.model.parameters?.stop,
          ...params.model.parameters,
        },
      },
      input: {
        prompt: params.input.prompt,
        systemPrompt: params.input.systemPrompt,
        inputField: params.input.inputField,
        context: params.input.context,
        examples: params.input.examples,
      },
      output: {
        format: params.output?.format || 'text',
        schema: params.output?.schema,
        postProcess: params.output?.postProcess,
      },
      options: {
        streaming: params.options?.streaming || false,
        retryOnFailure: params.options?.retryOnFailure !== false,
        maxRetries: params.options?.maxRetries || 3,
        timeout: params.options?.timeout || 60000,
        enableCache: params.options?.enableCache || false,
        cacheTTL: params.options?.cacheTTL || 3600,
      },
    };
  }
  
  private async prepareInput(config: AINodeConfig, context: ExecutorContext): Promise<string> {
    let input = '';
    
    // Build prompt
    if (config.input.systemPrompt) {
      input += `System: ${config.input.systemPrompt}\n\n`;
    }
    
    // Add examples for few-shot learning
    if (config.input.examples && config.input.examples.length > 0) {
      input += 'Examples:\n';
      for (const example of config.input.examples) {
        input += `Input: ${example.input}\nOutput: ${example.output}\n\n`;
      }
    }
    
    // Add main prompt
    if (config.input.prompt) {
      input += config.input.prompt;
    }
    
    // Add input data from field
    if (config.input.inputField) {
      const inputData = this.getNestedValue(context.inputData, config.input.inputField);
      if (inputData) {
        input += `\n\nInput Data: ${typeof inputData === 'string' ? inputData : JSON.stringify(inputData)}`;
      }
    }
    
    // Add context data
    if (config.input.context) {
      input += `\n\nContext: ${JSON.stringify(config.input.context)}`;
    }
    
    // Replace variables in prompt
    input = this.replaceVariables(input, context);
    
    return input.trim();
  }
  
  private async executeAIOperation(
    config: AINodeConfig,
    input: string,
    nodeConfig: NodeExecutionConfig
  ): Promise<AIExecutionResult> {
    const startTime = Date.now();
    
    try {
      // Check cache first
      if (config.options?.enableCache) {
        const cachedResult = await this.getCachedResult(input, config);
        if (cachedResult) {
          return {
            ...cachedResult,
            metadata: {
              ...cachedResult.metadata,
              cached: true,
              executionTime: Date.now() - startTime,
            },
          };
        }
      }
      
      // Execute based on provider
      let result: AIExecutionResult;
      
      switch (config.model.provider) {
        case 'openai':
          result = await this.executeOpenAI(config, input);
          break;
          
        case 'anthropic':
          result = await this.executeAnthropic(config, input);
          break;
          
        case 'google':
          result = await this.executeGoogle(config, input);
          break;
          
        case 'azure':
          result = await this.executeAzure(config, input);
          break;
          
        case 'huggingface':
          result = await this.executeHuggingFace(config, input);
          break;
          
        case 'local':
          result = await this.executeLocal(config, input);
          break;
          
        default:
          throw new Error(`Unsupported AI provider: ${config.model.provider}`);
      }
      
      result.metadata.executionTime = Date.now() - startTime;
      
      // Cache result if enabled
      if (config.options?.enableCache) {
        await this.cacheResult(input, config, result);
      }
      
      return result;
      
    } catch (error) {
      throw new Error(`AI operation failed: ${error.message}`);
    }
  }
  
  private async executeOpenAI(config: AINodeConfig, input: string): Promise<AIExecutionResult> {
    // Mock implementation - replace with actual OpenAI API call
    const mockResult: AIExecutionResult = {
      result: `Mock OpenAI response for: ${input.substring(0, 50)}...`,
      tokenUsage: {
        inputTokens: Math.floor(input.length / 4), // Rough estimate
        outputTokens: 100,
        totalTokens: Math.floor(input.length / 4) + 100,
        cost: 0.002,
      },
      modelInfo: {
        provider: 'openai',
        modelName: config.model.modelName,
        version: '1.0',
      },
      metadata: {
        operationType: config.operation,
        executionTime: 0,
        confidence: 0.95,
      },
    };
    
    return mockResult;
  }
  
  private async executeAnthropic(config: AINodeConfig, input: string): Promise<AIExecutionResult> {
    // Mock implementation - replace with actual Anthropic API call
    const mockResult: AIExecutionResult = {
      result: `Mock Anthropic response for: ${input.substring(0, 50)}...`,
      tokenUsage: {
        inputTokens: Math.floor(input.length / 4),
        outputTokens: 120,
        totalTokens: Math.floor(input.length / 4) + 120,
        cost: 0.003,
      },
      modelInfo: {
        provider: 'anthropic',
        modelName: config.model.modelName,
        version: '1.0',
      },
      metadata: {
        operationType: config.operation,
        executionTime: 0,
        confidence: 0.92,
      },
    };
    
    return mockResult;
  }
  
  private async executeGoogle(config: AINodeConfig, input: string): Promise<AIExecutionResult> {
    // Mock implementation - replace with actual Google AI API call
    const mockResult: AIExecutionResult = {
      result: `Mock Google AI response for: ${input.substring(0, 50)}...`,
      tokenUsage: {
        inputTokens: Math.floor(input.length / 4),
        outputTokens: 80,
        totalTokens: Math.floor(input.length / 4) + 80,
        cost: 0.001,
      },
      modelInfo: {
        provider: 'google',
        modelName: config.model.modelName,
        version: '1.0',
      },
      metadata: {
        operationType: config.operation,
        executionTime: 0,
        confidence: 0.88,
      },
    };
    
    return mockResult;
  }
  
  private async executeAzure(config: AINodeConfig, input: string): Promise<AIExecutionResult> {
    // Mock implementation - replace with actual Azure OpenAI API call
    return this.executeOpenAI(config, input); // Azure uses OpenAI models
  }
  
  private async executeHuggingFace(config: AINodeConfig, input: string): Promise<AIExecutionResult> {
    // Mock implementation - replace with actual HuggingFace API call
    const mockResult: AIExecutionResult = {
      result: `Mock HuggingFace response for: ${input.substring(0, 50)}...`,
      tokenUsage: {
        inputTokens: Math.floor(input.length / 4),
        outputTokens: 60,
        totalTokens: Math.floor(input.length / 4) + 60,
        cost: 0.0005,
      },
      modelInfo: {
        provider: 'huggingface',
        modelName: config.model.modelName,
        version: '1.0',
      },
      metadata: {
        operationType: config.operation,
        executionTime: 0,
        confidence: 0.85,
      },
    };
    
    return mockResult;
  }
  
  private async executeLocal(config: AINodeConfig, input: string): Promise<AIExecutionResult> {
    // Mock implementation - replace with actual local model call
    const mockResult: AIExecutionResult = {
      result: `Mock local model response for: ${input.substring(0, 50)}...`,
      tokenUsage: {
        inputTokens: Math.floor(input.length / 4),
        outputTokens: 40,
        totalTokens: Math.floor(input.length / 4) + 40,
        cost: 0,
      },
      modelInfo: {
        provider: 'local',
        modelName: config.model.modelName,
        version: '1.0',
      },
      metadata: {
        operationType: config.operation,
        executionTime: 0,
        confidence: 0.80,
      },
    };
    
    return mockResult;
  }
  
  private processOutput(result: AIExecutionResult, config: AINodeConfig): any {
    let output = result.result;
    
    // Apply post-processing
    if (config.output.postProcess) {
      const postProcess = config.output.postProcess;
      
      if (typeof output === 'string') {
        if (postProcess.trim) {
          output = output.trim();
        }
        
        if (postProcess.lowercase) {
          output = output.toLowerCase();
        }
        
        if (postProcess.removeSpecialChars) {
          output = output.replace(/[^\w\s]/gi, '');
        }
        
        if (postProcess.extractJson) {
          try {
            const jsonMatch = output.match(/\{.*\}/s);
            if (jsonMatch) {
              output = JSON.parse(jsonMatch[0]);
            }
          } catch (error) {
            this.logger.warn('Failed to extract JSON from output:', error);
          }
        }
      }
    }
    
    // Format output
    if (config.output.format === 'json' && typeof output === 'string') {
      try {
        output = JSON.parse(output);
      } catch (error) {
        this.logger.warn('Failed to parse output as JSON:', error);
      }
    }
    
    return {
      result: output,
      tokenUsage: result.tokenUsage,
      modelInfo: result.modelInfo,
      metadata: result.metadata,
      rawResponse: result.rawResponse,
    };
  }
  
  private async shouldRetryAIError(error: any, context: ExecutorContext): Promise<boolean> {
    // Retry on rate limits, timeouts, and temporary failures
    const retryableErrors = [
      'rate_limit',
      'timeout',
      'service_unavailable',
      'internal_error',
      'network_error',
      'quota_exceeded',
    ];
    
    const errorMessage = error.message.toLowerCase();
    
    return retryableErrors.some(retryableError => 
      errorMessage.includes(retryableError)
    );
  }
  
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }
  
  private replaceVariables(text: string, context: ExecutorContext): string {
    return text.replace(/\{\{(\w+(?:\.\w+)*)\}\}/g, (match, path) => {
      const value = this.getNestedValue(context.inputData, path);
      return value != null ? String(value) : match;
    });
  }
  
  private async getCachedResult(input: string, config: AINodeConfig): Promise<AIExecutionResult | null> {
    // TODO: Implement caching logic (Redis, memory, etc.)
    return null;
  }
  
  private async cacheResult(input: string, config: AINodeConfig, result: AIExecutionResult): Promise<void> {
    // TODO: Implement caching logic
  }
}
