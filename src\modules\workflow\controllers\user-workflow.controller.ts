import { UserWorkflowService } from './../service/user-workflow.service';
import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { NodeResponseDto, UserNodeExecuteDto } from '../dto/node-execute.dto';

@Controller()
export class UserWorkflowController {
    constructor(private readonly userWorkflowService: UserWorkflowService) { }

    // Bất kỳ tin nhắn nào gửi đến pattern này sẽ được hàm này xử lý
    @MessagePattern({ cmd: 'user_execute_node' })
    async handleExecuteNode(@Payload() data: UserNodeExecuteDto): Promise<NodeResponseDto> {
        console.log(`Server B received data:`, data);


        console.log('Server B finished processing. Sending result back.');

        // Trả về kết quả. Server A sẽ nhận được giá trị này.
        return { result: { message: 'Hello from Server B!' } };
    }

    // Bất kỳ tin nhắn nào gửi đến pattern này sẽ được hàm này xử lý
    @MessagePattern({ cmd: 'user_execute_workflow' })
    async handleExecuteWorkflow(@Payload() data: UserNodeExecuteDto): Promise<NodeResponseDto> {
        console.log(`Server B received data:`, data);


        console.log('Server B finished processing. Sending result back.');

        // Trả về kết quả. Server A sẽ nhận được giá trị này.
        return { result: { message: 'Hello from Server B!' } };
    }
}