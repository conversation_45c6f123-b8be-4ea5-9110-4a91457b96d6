
/** Payload cho việc thực thi node của user */
export class UserNodeExecuteDto {
    userId: number;
    workflowId: string;
    nodeId: string | null;
    type: 'test' | 'execute';
}

export class AdminNodeExecuteDto {
    employeeId: number;
    workflowId: string;
    nodeId: string | null;
    type: 'test' | 'execute';
}

/** Response cho việc thực thi node */
type Result = Record<string, any>;

export class NodeResponseDto {
    result: Result;
}