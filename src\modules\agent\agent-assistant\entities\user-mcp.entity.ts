import { Entity, PrimaryGeneratedColumn, Column, Unique } from 'typeorm';
import { UserMcpConfig } from '../interfaces/user-mcp-config.interface';

/**
 * UserMcp entity
 * Stores user-specific MCP (Model Context Protocol) configurations
 */
@Entity('user_mcp')
@Unique(['nameServer', 'userId'])
export class UserMcp {
  /**
   * UUID unique identifier for user MCP
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * MCP server name
   */
  @Column({ name: 'name_server', type: 'varchar', length: 255, nullable: false })
  nameServer: string;

  /**
   * MCP server description
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description?: string;

  /**
   * MCP configuration in JSONB format
   */
  @Column({ name: 'config', type: 'jsonb', nullable: false })
  config: UserMcpConfig;

  /**
   * Creation timestamp (timestamp millis)
   */
  @Column({ 
    name: 'created_at', 
    type: 'bigint', 
    nullable: false,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint'
  })
  createdAt: number;

  /**
   * Last update timestamp (timestamp millis)
   */
  @Column({ 
    name: 'updated_at', 
    type: 'bigint', 
    nullable: false,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint'
  })
  updatedAt: number;

  /**
   * Soft delete timestamp (timestamp millis)
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt?: number;

  /**
   * User ID who owns this MCP configuration
   */
  @Column({ name: 'user_id', type: 'int', nullable: true })
  userId?: number;

  /**
   * Encrypted JSON string of headers
   */
  @Column({ name: 'encrypted_headers', type: 'text', nullable: true })
  encryptedHeaders?: string;
}
