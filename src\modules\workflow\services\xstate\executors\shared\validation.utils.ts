import { ValidationResult } from '../../base/node-executor.interface';

/**
 * Shared validation utilities
 * Used by all executors for common validation patterns
 */
export class ValidationUtils {
  
  /**
   * Create a new validation result
   */
  static createValidationResult(): ValidationResult {
    return {
      isValid: true,
      errors: [],
      warnings: []
    };
  }

  /**
   * Add error to validation result
   */
  static addError(
    result: ValidationResult,
    code: string,
    message: string,
    field?: string,
    currentValue?: any,
    expectedValue?: any,
    severity: 'error' | 'warning' = 'error'
  ): void {
    const error = {
      code,
      message,
      field,
      currentValue,
      expectedValue,
      severity
    };

    if (severity === 'error') {
      result.errors.push(error);
      result.isValid = false;
    } else {
      result.warnings.push(error);
    }
  }

  /**
   * Add warning to validation result
   */
  static addWarning(
    result: ValidationResult,
    code: string,
    message: string,
    field?: string,
    suggestion?: string
  ): void {
    result.warnings.push({
      code,
      message,
      field,
      suggestion
    });
  }

  /**
   * Validate required field
   */
  static validateRequired(
    result: ValidationResult,
    value: any,
    fieldName: string,
    fieldPath?: string
  ): boolean {
    if (value === undefined || value === null || value === '') {
      this.addError(
        result,
        'REQUIRED_FIELD_MISSING',
        `Required field '${fieldName}' is missing`,
        fieldPath || fieldName,
        value
      );
      return false;
    }
    return true;
  }

  /**
   * Validate URL format
   */
  static validateUrl(
    result: ValidationResult,
    url: string,
    fieldName: string,
    fieldPath?: string,
    required: boolean = true
  ): boolean {
    if (!url) {
      if (required) {
        this.addError(
          result,
          'MISSING_URL',
          `${fieldName} is required`,
          fieldPath || fieldName
        );
        return false;
      }
      return true;
    }

    try {
      new URL(url);
      return true;
    } catch {
      this.addError(
        result,
        'INVALID_URL',
        `Invalid URL format in ${fieldName}`,
        fieldPath || fieldName,
        url
      );
      return false;
    }
  }

  /**
   * Validate email format
   */
  static validateEmail(
    result: ValidationResult,
    email: string,
    fieldName: string,
    fieldPath?: string
  ): boolean {
    if (!email) {
      return true; // Allow empty if not required
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      this.addError(
        result,
        'INVALID_EMAIL',
        `Invalid email format in ${fieldName}`,
        fieldPath || fieldName,
        email
      );
      return false;
    }
    return true;
  }

  /**
   * Validate number range
   */
  static validateNumberRange(
    result: ValidationResult,
    value: number,
    fieldName: string,
    min?: number,
    max?: number,
    fieldPath?: string
  ): boolean {
    if (typeof value !== 'number' || isNaN(value)) {
      this.addError(
        result,
        'INVALID_NUMBER',
        `${fieldName} must be a valid number`,
        fieldPath || fieldName,
        value
      );
      return false;
    }

    if (min !== undefined && value < min) {
      this.addError(
        result,
        'NUMBER_TOO_SMALL',
        `${fieldName} must be at least ${min}`,
        fieldPath || fieldName,
        value,
        `>= ${min}`
      );
      return false;
    }

    if (max !== undefined && value > max) {
      this.addError(
        result,
        'NUMBER_TOO_LARGE',
        `${fieldName} must be at most ${max}`,
        fieldPath || fieldName,
        value,
        `<= ${max}`
      );
      return false;
    }

    return true;
  }

  /**
   * Validate string length
   */
  static validateStringLength(
    result: ValidationResult,
    value: string,
    fieldName: string,
    minLength?: number,
    maxLength?: number,
    fieldPath?: string
  ): boolean {
    if (typeof value !== 'string') {
      this.addError(
        result,
        'INVALID_STRING',
        `${fieldName} must be a string`,
        fieldPath || fieldName,
        value
      );
      return false;
    }

    if (minLength !== undefined && value.length < minLength) {
      this.addError(
        result,
        'STRING_TOO_SHORT',
        `${fieldName} must be at least ${minLength} characters`,
        fieldPath || fieldName,
        value,
        `length >= ${minLength}`
      );
      return false;
    }

    if (maxLength !== undefined && value.length > maxLength) {
      this.addError(
        result,
        'STRING_TOO_LONG',
        `${fieldName} must be at most ${maxLength} characters`,
        fieldPath || fieldName,
        value,
        `length <= ${maxLength}`
      );
      return false;
    }

    return true;
  }

  /**
   * Validate array
   */
  static validateArray(
    result: ValidationResult,
    value: any,
    fieldName: string,
    minItems?: number,
    maxItems?: number,
    fieldPath?: string
  ): boolean {
    if (!Array.isArray(value)) {
      this.addError(
        result,
        'INVALID_ARRAY',
        `${fieldName} must be an array`,
        fieldPath || fieldName,
        value
      );
      return false;
    }

    if (minItems !== undefined && value.length < minItems) {
      this.addError(
        result,
        'ARRAY_TOO_SHORT',
        `${fieldName} must have at least ${minItems} items`,
        fieldPath || fieldName,
        value,
        `length >= ${minItems}`
      );
      return false;
    }

    if (maxItems !== undefined && value.length > maxItems) {
      this.addError(
        result,
        'ARRAY_TOO_LONG',
        `${fieldName} must have at most ${maxItems} items`,
        fieldPath || fieldName,
        value,
        `length <= ${maxItems}`
      );
      return false;
    }

    return true;
  }

  /**
   * Validate enum value
   */
  static validateEnum<T>(
    result: ValidationResult,
    value: any,
    enumObject: Record<string, T>,
    fieldName: string,
    fieldPath?: string
  ): boolean {
    const validValues = Object.values(enumObject);
    
    if (!validValues.includes(value)) {
      this.addError(
        result,
        'INVALID_ENUM_VALUE',
        `${fieldName} must be one of: ${validValues.join(', ')}`,
        fieldPath || fieldName,
        value,
        validValues
      );
      return false;
    }

    return true;
  }

  /**
   * Validate object properties
   */
  static validateObject(
    result: ValidationResult,
    value: any,
    fieldName: string,
    requiredProperties?: string[],
    fieldPath?: string
  ): boolean {
    if (!value || typeof value !== 'object' || Array.isArray(value)) {
      this.addError(
        result,
        'INVALID_OBJECT',
        `${fieldName} must be an object`,
        fieldPath || fieldName,
        value
      );
      return false;
    }

    if (requiredProperties) {
      for (const prop of requiredProperties) {
        if (!(prop in value) || value[prop] === undefined || value[prop] === null) {
          this.addError(
            result,
            'MISSING_REQUIRED_PROPERTY',
            `${fieldName} is missing required property '${prop}'`,
            fieldPath ? `${fieldPath}.${prop}` : `${fieldName}.${prop}`,
            value[prop]
          );
        }
      }
    }

    return result.isValid;
  }

  /**
   * Validate regex pattern
   */
  static validateRegex(
    result: ValidationResult,
    pattern: string,
    fieldName: string,
    fieldPath?: string
  ): boolean {
    if (!pattern) {
      return true; // Allow empty if not required
    }

    try {
      new RegExp(pattern);
      return true;
    } catch (error) {
      this.addError(
        result,
        'INVALID_REGEX',
        `Invalid regex pattern in ${fieldName}: ${error.message}`,
        fieldPath || fieldName,
        pattern
      );
      return false;
    }
  }

  /**
   * Validate JSON string
   */
  static validateJson(
    result: ValidationResult,
    jsonString: string,
    fieldName: string,
    fieldPath?: string
  ): boolean {
    if (!jsonString) {
      return true; // Allow empty if not required
    }

    try {
      JSON.parse(jsonString);
      return true;
    } catch (error) {
      this.addError(
        result,
        'INVALID_JSON',
        `Invalid JSON in ${fieldName}: ${error.message}`,
        fieldPath || fieldName,
        jsonString
      );
      return false;
    }
  }

  /**
   * Validate timeout value
   */
  static validateTimeout(
    result: ValidationResult,
    timeout: number,
    fieldName: string,
    fieldPath?: string
  ): boolean {
    if (!this.validateNumberRange(result, timeout, fieldName, 1, 300000, fieldPath)) {
      return false;
    }

    if (timeout > 60000) {
      this.addWarning(
        result,
        'HIGH_TIMEOUT',
        `${fieldName} is quite high (${timeout}ms)`,
        fieldPath || fieldName,
        'Consider using a lower timeout for better performance'
      );
    }

    return true;
  }

  /**
   * Validate HTTP method
   */
  static validateHttpMethod(
    result: ValidationResult,
    method: string,
    fieldName: string,
    fieldPath?: string
  ): boolean {
    const validMethods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'];
    
    if (!method) {
      this.addError(
        result,
        'MISSING_HTTP_METHOD',
        `${fieldName} is required`,
        fieldPath || fieldName
      );
      return false;
    }

    if (!validMethods.includes(method.toUpperCase())) {
      this.addError(
        result,
        'INVALID_HTTP_METHOD',
        `${fieldName} must be one of: ${validMethods.join(', ')}`,
        fieldPath || fieldName,
        method,
        validMethods
      );
      return false;
    }

    return true;
  }

  /**
   * Validate field path (dot notation)
   */
  static validateFieldPath(
    result: ValidationResult,
    fieldPath: string,
    fieldName: string,
    fieldPathParam?: string
  ): boolean {
    if (!fieldPath) {
      this.addError(
        result,
        'MISSING_FIELD_PATH',
        `${fieldName} is required`,
        fieldPathParam || fieldName
      );
      return false;
    }

    // Basic validation for field path format
    const pathRegex = /^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)*$/;
    if (!pathRegex.test(fieldPath)) {
      this.addError(
        result,
        'INVALID_FIELD_PATH',
        `${fieldName} has invalid format. Use dot notation like 'field.subfield'`,
        fieldPathParam || fieldName,
        fieldPath
      );
      return false;
    }

    return true;
  }

  /**
   * Merge multiple validation results
   */
  static mergeValidationResults(...results: ValidationResult[]): ValidationResult {
    const merged = this.createValidationResult();
    
    for (const result of results) {
      merged.errors.push(...result.errors);
      merged.warnings.push(...result.warnings);
    }
    
    merged.isValid = merged.errors.length === 0;
    
    return merged;
  }
}
