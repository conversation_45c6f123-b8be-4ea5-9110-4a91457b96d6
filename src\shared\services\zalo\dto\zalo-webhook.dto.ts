/**
 * Zalo Webhook DTO Interfaces
 *
 * Tài liệu tham khảo:
 * - Tổng quan: https://developers.zalo.me/docs/official-account/webhook/tong-quan
 * - Sự kiện tin nhắn: https://developers.zalo.me/docs/official-account/webhook/tin-nhan/
 */

// ===== BASE WEBHOOK INTERFACES =====

/**
 * Interface cơ bản cho tất cả webhook events
 */
export interface ZaloWebhookBaseEvent {
  /**
   * ID của sự kiện
   */
  event_id: string;

  /**
   * Tên của sự kiện
   */
  event_name: string;

  /**
   * Thời gian xảy ra sự kiện (Unix timestamp)
   */
  timestamp: number;

  /**
   * ID của Official Account
   */
  oa_id: string;
}

/**
 * Interface định nghĩa thông tin người gửi trong webhook
 */
export interface ZaloWebhookSender {
  /**
   * ID của người gửi
   */
  id: string;

  /**
   * Tên hiển thị của người gửi
   */
  name?: string;

  /**
   * URL avatar của người gửi
   */
  avatar?: string;
}

/**
 * Interface định nghĩa thông tin người nhận trong webhook
 */
export interface ZaloWebhookRecipient {
  /**
   * ID của người nhận
   */
  id: string;
}

/**
 * Interface định nghĩa attachment trong tin nhắn
 */
export interface ZaloWebhookAttachment {
  /**
   * Loại attachment
   */
  type: 'image' | 'file' | 'audio' | 'video' | 'location' | 'sticker' | 'gif';

  /**
   * Payload chứa thông tin chi tiết
   */
  payload: {
    /**
     * URL của file (cho image, file, audio, video, gif)
     */
    url?: string;

    /**
     * Tên file (cho file)
     */
    name?: string;

    /**
     * Kích thước file (bytes)
     */
    size?: number;

    /**
     * Loại MIME
     */
    type?: string;

    /**
     * Thông tin vị trí (cho location)
     */
    coordinates?: {
      latitude: number;
      longitude: number;
    };

    /**
     * ID sticker (cho sticker)
     */
    sticker_id?: string;

    /**
     * Danh mục sticker
     */
    sticker_category?: string;
  };
}

/**
 * Interface định nghĩa tin nhắn trong webhook
 */
export interface ZaloWebhookMessage {
  /**
   * ID của tin nhắn
   */
  msg_id: string;

  /**
   * Nội dung tin nhắn văn bản (nếu có)
   */
  text?: string;

  /**
   * Danh sách attachments (nếu có)
   */
  attachments?: ZaloWebhookAttachment[];

  /**
   * Thời gian gửi tin nhắn (Unix timestamp)
   */
  timestamp: number;
}

/**
 * Interface định nghĩa thông tin reaction
 */
export interface ZaloWebhookReaction {
  /**
   * ID của tin nhắn được react
   */
  msg_id: string;

  /**
   * Loại reaction
   */
  reaction: string;

  /**
   * Hành động (add/remove)
   */
  action: 'add' | 'remove';
}

// ===== USER MESSAGE EVENTS =====

/**
 * Sự kiện người dùng gửi tin nhắn văn bản
 * Event: user_send_text
 */
export interface ZaloUserSendTextEvent extends ZaloWebhookBaseEvent {
  event_name: 'user_send_text';
  data: {
    sender: ZaloWebhookSender;
    recipient: ZaloWebhookRecipient;
    message: {
      msg_id: string;
      text: string;
      timestamp: number;
    };
  };
}

/**
 * Sự kiện người dùng gửi hình ảnh
 * Event: user_send_image
 */
export interface ZaloUserSendImageEvent extends ZaloWebhookBaseEvent {
  event_name: 'user_send_image';
  data: {
    sender: ZaloWebhookSender;
    recipient: ZaloWebhookRecipient;
    message: {
      msg_id: string;
      attachments: ZaloWebhookAttachment[];
      timestamp: number;
    };
  };
}

/**
 * Sự kiện người dùng gửi file
 * Event: user_send_file
 */
export interface ZaloUserSendFileEvent extends ZaloWebhookBaseEvent {
  event_name: 'user_send_file';
  data: {
    sender: ZaloWebhookSender;
    recipient: ZaloWebhookRecipient;
    message: {
      msg_id: string;
      attachments: ZaloWebhookAttachment[];
      timestamp: number;
    };
  };
}

/**
 * Sự kiện người dùng gửi sticker
 * Event: user_send_sticker
 */
export interface ZaloUserSendStickerEvent extends ZaloWebhookBaseEvent {
  event_name: 'user_send_sticker';
  data: {
    sender: ZaloWebhookSender;
    recipient: ZaloWebhookRecipient;
    message: {
      msg_id: string;
      attachments: ZaloWebhookAttachment[];
      timestamp: number;
    };
  };
}

/**
 * Sự kiện người dùng gửi GIF
 * Event: user_send_gif
 */
export interface ZaloUserSendGifEvent extends ZaloWebhookBaseEvent {
  event_name: 'user_send_gif';
  data: {
    sender: ZaloWebhookSender;
    recipient: ZaloWebhookRecipient;
    message: {
      msg_id: string;
      attachments: ZaloWebhookAttachment[];
      timestamp: number;
    };
  };
}

/**
 * Sự kiện người dùng gửi audio
 * Event: user_send_audio
 */
export interface ZaloUserSendAudioEvent extends ZaloWebhookBaseEvent {
  event_name: 'user_send_audio';
  data: {
    sender: ZaloWebhookSender;
    recipient: ZaloWebhookRecipient;
    message: {
      msg_id: string;
      attachments: ZaloWebhookAttachment[];
      timestamp: number;
    };
  };
}

/**
 * Sự kiện người dùng gửi video
 * Event: user_send_video
 */
export interface ZaloUserSendVideoEvent extends ZaloWebhookBaseEvent {
  event_name: 'user_send_video';
  data: {
    sender: ZaloWebhookSender;
    recipient: ZaloWebhookRecipient;
    message: {
      msg_id: string;
      attachments: ZaloWebhookAttachment[];
      timestamp: number;
    };
  };
}

/**
 * Sự kiện người dùng gửi vị trí
 * Event: user_send_location
 */
export interface ZaloUserSendLocationEvent extends ZaloWebhookBaseEvent {
  event_name: 'user_send_location';
  data: {
    sender: ZaloWebhookSender;
    recipient: ZaloWebhookRecipient;
    message: {
      msg_id: string;
      attachments: ZaloWebhookAttachment[];
      timestamp: number;
    };
  };
}

// ===== INTERACTION EVENTS =====

/**
 * Sự kiện người dùng click nút "Nhắn tin" trên Official Account
 * Event: user_click_chatnow
 */
export interface ZaloUserClickChatNowEvent extends ZaloWebhookBaseEvent {
  event_name: 'user_click_chatnow';
  data: {
    sender: ZaloWebhookSender;
    recipient: ZaloWebhookRecipient;
    timestamp: number;
  };
}

/**
 * Sự kiện người dùng thả biểu tượng cảm xúc lên tin nhắn
 * Event: user_reaction
 */
export interface ZaloUserReactionEvent extends ZaloWebhookBaseEvent {
  event_name: 'user_reaction';
  data: {
    sender: ZaloWebhookSender;
    recipient: ZaloWebhookRecipient;
    reaction: ZaloWebhookReaction;
    timestamp: number;
  };
}

/**
 * Sự kiện OA thả biểu tượng cảm xúc lên tin nhắn
 * Event: oa_reaction
 */
export interface ZaloOaReactionEvent extends ZaloWebhookBaseEvent {
  event_name: 'oa_reaction';
  data: {
    sender: ZaloWebhookRecipient; // OA là sender
    recipient: ZaloWebhookSender; // User là recipient
    reaction: ZaloWebhookReaction;
    timestamp: number;
  };
}

/**
 * Sự kiện người dùng đã xem tin nhắn được gửi từ Official Account
 * Event: user_seen_message
 */
export interface ZaloUserSeenMessageEvent extends ZaloWebhookBaseEvent {
  event_name: 'user_seen_message';
  data: {
    sender: ZaloWebhookSender;
    recipient: ZaloWebhookRecipient;
    message: {
      msg_id: string;
      timestamp: number;
    };
    seen_timestamp: number;
  };
}

/**
 * Sự kiện người dùng nhận tin nhắn từ Official Account
 * Event: user_received_message
 */
export interface ZaloUserReceivedMessageEvent extends ZaloWebhookBaseEvent {
  event_name: 'user_received_message';
  data: {
    sender: ZaloWebhookRecipient; // OA là sender
    recipient: ZaloWebhookSender; // User là recipient
    message: {
      msg_id: string;
      timestamp: number;
    };
    received_timestamp: number;
  };
}

// ===== OA MESSAGE EVENTS =====

/**
 * Sự kiện Official Account gửi tin nhắn cho người dùng
 * Event: oa_send_text
 */
export interface ZaloOaSendTextEvent extends ZaloWebhookBaseEvent {
  event_name: 'oa_send_text';
  data: {
    sender: ZaloWebhookRecipient; // OA là sender
    recipient: ZaloWebhookSender; // User là recipient
    message: ZaloWebhookMessage;
  };
}

/**
 * Sự kiện Official Account gửi tin nhắn cho người dùng ẩn danh
 * Event: oa_send_anonymous
 */
export interface ZaloOaSendAnonymousEvent extends ZaloWebhookBaseEvent {
  event_name: 'oa_send_anonymous';
  data: {
    sender: ZaloWebhookRecipient; // OA là sender
    recipient: {
      /**
       * ID người dùng ẩn danh
       */
      id: string;
    };
    message: ZaloWebhookMessage;
  };
}

// ===== ANONYMOUS USER EVENTS =====

/**
 * Sự kiện người dùng ẩn danh gửi tin nhắn
 * Event: anonymous_send_text
 */
export interface ZaloAnonymousSendTextEvent extends ZaloWebhookBaseEvent {
  event_name: 'anonymous_send_text';
  data: {
    sender: {
      /**
       * ID người dùng ẩn danh
       */
      id: string;
    };
    recipient: ZaloWebhookRecipient;
    message: {
      msg_id: string;
      text: string;
      timestamp: number;
    };
  };
}

// ===== FOLLOW EVENTS =====

/**
 * Sự kiện người dùng theo dõi Official Account
 * Event: user_follow
 */
export interface ZaloUserFollowEvent extends ZaloWebhookBaseEvent {
  event_name: 'user_follow';
  data: {
    follower: ZaloWebhookSender;
    timestamp: number;
  };
}

/**
 * Sự kiện người dùng hủy theo dõi Official Account
 * Event: user_unfollow
 */
export interface ZaloUserUnfollowEvent extends ZaloWebhookBaseEvent {
  event_name: 'user_unfollow';
  data: {
    follower: ZaloWebhookSender;
    timestamp: number;
  };
}

// ===== UNION TYPES =====

/**
 * Union type cho tất cả các sự kiện webhook của Zalo
 */
export type ZaloWebhookEvent =
  // User message events
  | ZaloUserSendTextEvent
  | ZaloUserSendImageEvent
  | ZaloUserSendFileEvent
  | ZaloUserSendStickerEvent
  | ZaloUserSendGifEvent
  | ZaloUserSendAudioEvent
  | ZaloUserSendVideoEvent
  | ZaloUserSendLocationEvent
  // Interaction events
  | ZaloUserClickChatNowEvent
  | ZaloUserReactionEvent
  | ZaloOaReactionEvent
  | ZaloUserSeenMessageEvent
  | ZaloUserReceivedMessageEvent
  // OA message events
  | ZaloOaSendTextEvent
  | ZaloOaSendAnonymousEvent
  // Anonymous user events
  | ZaloAnonymousSendTextEvent
  // Follow events
  | ZaloUserFollowEvent
  | ZaloUserUnfollowEvent;

/**
 * Union type cho các sự kiện tin nhắn từ người dùng
 */
export type ZaloUserMessageEvent =
  | ZaloUserSendTextEvent
  | ZaloUserSendImageEvent
  | ZaloUserSendFileEvent
  | ZaloUserSendStickerEvent
  | ZaloUserSendGifEvent
  | ZaloUserSendAudioEvent
  | ZaloUserSendVideoEvent
  | ZaloUserSendLocationEvent;

/**
 * Union type cho các sự kiện tương tác
 */
export type ZaloInteractionEvent =
  | ZaloUserClickChatNowEvent
  | ZaloUserReactionEvent
  | ZaloOaReactionEvent
  | ZaloUserSeenMessageEvent
  | ZaloUserReceivedMessageEvent;

/**
 * Union type cho các sự kiện từ OA
 */
export type ZaloOaMessageEvent = ZaloOaSendTextEvent | ZaloOaSendAnonymousEvent;

/**
 * Union type cho các sự kiện theo dõi
 */
export type ZaloFollowEvent = ZaloUserFollowEvent | ZaloUserUnfollowEvent;

// ===== UTILITY TYPES =====

/**
 * Type guard để kiểm tra xem event có phải là user message event không
 */
export function isUserMessageEvent(
  event: ZaloWebhookEvent,
): event is ZaloUserMessageEvent {
  return [
    'user_send_text',
    'user_send_image',
    'user_send_file',
    'user_send_sticker',
    'user_send_gif',
    'user_send_audio',
    'user_send_video',
    'user_send_location',
  ].includes(event.event_name);
}

/**
 * Type guard để kiểm tra xem event có phải là interaction event không
 */
export function isInteractionEvent(
  event: ZaloWebhookEvent,
): event is ZaloInteractionEvent {
  return [
    'user_click_chatnow',
    'user_reaction',
    'oa_reaction',
    'user_seen_message',
    'user_received_message',
  ].includes(event.event_name);
}

/**
 * Type guard để kiểm tra xem event có phải là OA message event không
 */
export function isOaMessageEvent(
  event: ZaloWebhookEvent,
): event is ZaloOaMessageEvent {
  return ['oa_send_text', 'oa_send_anonymous'].includes(event.event_name);
}

/**
 * Type guard để kiểm tra xem event có phải là follow event không
 */
export function isFollowEvent(
  event: ZaloWebhookEvent,
): event is ZaloFollowEvent {
  return ['user_follow', 'user_unfollow'].includes(event.event_name);
}

/**
 * Enum cho các loại sự kiện webhook
 */
export enum ZaloWebhookEventType {
  // User message events
  USER_SEND_TEXT = 'user_send_text',
  USER_SEND_IMAGE = 'user_send_image',
  USER_SEND_FILE = 'user_send_file',
  USER_SEND_STICKER = 'user_send_sticker',
  USER_SEND_GIF = 'user_send_gif',
  USER_SEND_AUDIO = 'user_send_audio',
  USER_SEND_VIDEO = 'user_send_video',
  USER_SEND_LOCATION = 'user_send_location',

  // Interaction events
  USER_CLICK_CHATNOW = 'user_click_chatnow',
  USER_REACTION = 'user_reaction',
  OA_REACTION = 'oa_reaction',
  USER_SEEN_MESSAGE = 'user_seen_message',
  USER_RECEIVED_MESSAGE = 'user_received_message',

  // OA message events
  OA_SEND_TEXT = 'oa_send_text',
  OA_SEND_ANONYMOUS = 'oa_send_anonymous',

  // Anonymous user events
  ANONYMOUS_SEND_TEXT = 'anonymous_send_text',

  // Follow events
  USER_FOLLOW = 'user_follow',
  USER_UNFOLLOW = 'user_unfollow',
}
