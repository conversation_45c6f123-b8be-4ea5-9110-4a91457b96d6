/**
 * Enum định nghĩa các loại chi tiêu của assistant AI
 * Đ<PERSON><PERSON><PERSON> sử dụng để phân loại chi tiêu trong bảng assistant_spending_history
 */
export enum AssistantSpendingType {
  /**
   * Chi tiêu cho chat trong ứng dụng
   */
  IN_APP_CHAT = 'IN_APP_CHAT',

  /**
   * Chi tiêu cho chat qua Facebook Messenger
   */
  MESSENGER = 'MESSENGER',

  /**
   * Chi tiêu cho chat trên website (widget chat)
   */
  WEBSITE = 'WEBSITE',

  /**
   * Chi tiêu cho chat qua Zalo
   */
  ZALO = 'ZALO',

  /**
   * Chi tiêu cho chat qua Telegram
   */
  TELEGRAM = 'TELEGRAM',
}

/**
 * Mảng chứa tất cả các loại chi tiêu hợp lệ
 * Sử dụng để validation và kiểm tra
 */
export const VALID_SPENDING_TYPES = Object.values(AssistantSpendingType);

/**
 * Kiểm tra xem một giá trị có phải là loại chi tiêu hợp lệ không
 * @param value Giá trị cần kiểm tra
 * @returns true nếu là loại chi tiêu hợp lệ, false nếu không
 */
export function isValidSpendingType(
  value: string,
): value is AssistantSpendingType {
  return VALID_SPENDING_TYPES.includes(value as AssistantSpendingType);
}

/**
 * Lấy mô tả tiếng Việt cho loại chi tiêu
 * @param type Loại chi tiêu
 * @returns Mô tả tiếng Việt
 */
export function getSpendingTypeDescription(
  type: AssistantSpendingType,
): string {
  const descriptions: Record<AssistantSpendingType, string> = {
    [AssistantSpendingType.IN_APP_CHAT]: 'Chat trong ứng dụng',
    [AssistantSpendingType.MESSENGER]: 'Chat qua Facebook Messenger',
    [AssistantSpendingType.WEBSITE]: 'Chat trên website',
    [AssistantSpendingType.ZALO]: 'Chat qua Zalo',
    [AssistantSpendingType.TELEGRAM]: 'Chat qua Telegram',
  };

  return descriptions[type] || 'Không xác định';
}
