import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_template_sms trong cơ sở dữ liệu
 * Template SMS mà user tự tạo hoặc tùy chỉnh từ admin template
 */
@Entity('user_template_sms')
export class UserTemplateSms {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của user sử dụng template
   */
  @Column({ name: 'user_id', type: 'bigint' })
  userId: number;

  /**
   * Tên template do user tự đặt
   */
  @Column({ name: 'custom_name', type: 'varchar', length: 100, nullable: true })
  customName: string | null;

  /**
   * Nội dung SMS được user tùy chỉnh lại
   */
  @Column({ name: 'custom_content', type: 'text', nullable: true })
  customContent: string | null;

  /**
   * Thời gian tạo
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number | null;

  /**
   * Thời gian cập nhật
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number | null;

  /**
   * Danh sách các placeholder nếu user muốn tuỳ chỉnh
   */
  @Column({ name: 'placeholders', type: 'json', nullable: true })
  placeholders: Record<string, any> | null;
}
