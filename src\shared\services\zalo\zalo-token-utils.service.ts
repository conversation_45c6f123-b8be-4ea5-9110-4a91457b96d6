import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { backOff } from 'exponential-backoff';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloOATokenAdapterService } from './zalo-oa-token-adapter.service';

interface ZaloRefreshTokenResponse {
  access_token: string;
  refresh_token: string;
  expires_in: string;
}

/**
 * Service utils quản lý access token cho Zalo Official Account trong Worker
 * Bao gồm logic lấy token từ database, refresh token khi hết hạn và retry mechanism
 */
@Injectable()
export class ZaloTokenUtilsService {
  private readonly logger = new Logger(ZaloTokenUtilsService.name);
  private readonly zaloAppId: string;
  private readonly zaloAppSecret: string;

  constructor(
    private readonly zaloOATokenAdapter: ZaloOATokenAdapterService,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.zaloAppId = this.configService.get<string>('ZALO_APP_ID') || '';
    this.zaloAppSecret = this.configService.get<string>('ZALO_APP_SECRET') || '';

    if (!this.zaloAppId || !this.zaloAppSecret) {
      this.logger.warn('ZALO_APP_ID or ZALO_APP_SECRET is not configured');
    }
  }

  /**
   * Lấy access token hợp lệ cho Official Account với retry mechanism
   * Tự động refresh token nếu hết hạn
   * @param oaId ID của Official Account trên Zalo
   * @param maxRetries Số lần retry tối đa (mặc định: 3)
   * @returns Access token hợp lệ
   * @throws AppException nếu không tìm thấy OA hoặc không thể refresh token
   */
  async getValidAccessTokenWithRetry(oaId: string, maxRetries?: number): Promise<string>;
  /**
   * Lấy access token hợp lệ cho Official Account theo userId và zaloOfficialAccountId với retry
   * @param userId ID của user
   * @param zaloOfficialAccountId ID của Zalo Official Account trong database
   * @param maxRetries Số lần retry tối đa (mặc định: 3)
   * @returns Access token hợp lệ
   */
  async getValidAccessTokenWithRetry(userId: string, zaloOfficialAccountId: string, maxRetries?: number): Promise<string>;
  async getValidAccessTokenWithRetry(param1: string, param2?: string | number, param3?: number): Promise<string> {
    let maxRetries: number;
    let userId: string | undefined;
    let zaloOfficialAccountId: string | undefined;
    let oaId: string | undefined;

    // Xử lý overload parameters
    if (typeof param2 === 'string') {
      // Trường hợp: userId, zaloOfficialAccountId, maxRetries
      userId = param1;
      zaloOfficialAccountId = param2;
      maxRetries = param3 || 3;
    } else {
      // Trường hợp: oaId, maxRetries
      oaId = param1;
      maxRetries = param2 || 3;
    }

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        this.logger.debug(`Attempt ${attempt}/${maxRetries} to get access token for ${oaId || `user:${userId}, oa:${zaloOfficialAccountId}`}`);
        
        if (userId && zaloOfficialAccountId) {
          return await this.getValidAccessToken(userId, zaloOfficialAccountId);
        } else if (oaId) {
          return await this.getValidAccessToken(oaId);
        } else {
          throw new AppException(ErrorCode.VALIDATION_ERROR, 'Invalid parameters provided');
        }
      } catch (error) {
        lastError = error;
        this.logger.warn(`Attempt ${attempt}/${maxRetries} failed: ${error.message}`);
        
        // Nếu là lỗi không thể retry (như không tìm thấy OA), không retry nữa
        if (error instanceof AppException &&
            (error.message.includes('RESOURCE_NOT_FOUND') ||
             error.message.includes('CONFIGURATION_ERROR'))) {
          throw error;
        }

        // Nếu chưa hết số lần retry, đợi một chút trước khi retry
        if (attempt < maxRetries) {
          const delayMs = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // Exponential backoff, max 5s
          this.logger.debug(`Waiting ${delayMs}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delayMs));
        }
      }
    }

    // Nếu đã hết số lần retry
    this.logger.error(`Failed to get access token after ${maxRetries} attempts. Last error: ${lastError?.message}`);
    throw new AppException(
      ErrorCode.EXTERNAL_SERVICE_ERROR,
      `Không thể lấy access token sau ${maxRetries} lần thử. Lỗi cuối: ${lastError?.message}`
    );
  }

  /**
   * Lấy access token hợp lệ cho Official Account
   * Tự động refresh token nếu hết hạn
   * @param oaId ID của Official Account trên Zalo
   * @returns Access token hợp lệ
   * @throws AppException nếu không tìm thấy OA hoặc không thể refresh token
   */
  async getValidAccessToken(oaId: string): Promise<string>;
  /**
   * Lấy access token hợp lệ cho Official Account theo userId và zaloOfficialAccountId
   * @param userId ID của user
   * @param zaloOfficialAccountId ID của Zalo Official Account trong database
   * @returns Access token hợp lệ
   */
  async getValidAccessToken(userId: string, zaloOfficialAccountId: string): Promise<string>;
  async getValidAccessToken(param1: string, param2?: string): Promise<string> {
    let oa: any | null = null;
    try {
      if (param2) {
        // Trường hợp gọi với userId và zaloOfficialAccountId - không hỗ trợ với Integration
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Method với userId và zaloOfficialAccountId không được hỗ trợ với Integration pattern`
        );
      } else {
        // Trường hợp gọi với oaId - lấy token đã giải mã
        oa = await this.zaloOATokenAdapter.findOneWithTokens({
          where: { oaId: param1 }
        });
      }

      if (!oa) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy Official Account với ID: ${param1}`
        );
      }

      // Kiểm tra trạng thái OA
      if (oa.status === 'DECRYPT_ERROR') {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          `Official Account ${oa.oaId || param1} có lỗi giải mã token. Vui lòng liên hệ admin để khắc phục.`
        );
      }

      if (oa.status === 'NEEDS_REAUTH') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Official Account ${oa.oaId || param1} cần xác thực lại. Vui lòng đăng nhập lại Zalo.`
        );
      }

      if (oa.status !== 'active') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Official Account ${oa.oaId || param1} không ở trạng thái active`
        );
      }

      // Kiểm tra xem có token không (sau khi giải mã)
      if (!oa.accessToken || !oa.refreshToken) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Official Account ${oa.oaId || param1} không có token hợp lệ`
        );
      }

      // Kiểm tra xem token có hết hạn không (thêm buffer 5 phút)
      const now = Date.now();
      const tokenExpiryBuffer = 5 * 60 * 1000; // 5 phút
      const isTokenExpired = oa.expiresAt <= (now + tokenExpiryBuffer);

      if (isTokenExpired) {
        this.logger.log(`Access token for OA ${oa.oaId || param1} is expired, refreshing...`);
        return await this.refreshAccessToken(oa);
      }

      this.logger.debug(`Using existing access token for OA ${oa.oaId || param1}`);
      return oa.accessToken;
    } catch (error) {
      this.logger.error(`Error getting access token for OA ${oa?.oaId || param1}: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Lỗi khi lấy access token cho Official Account ${oa?.oaId || param1}`
      );
    }
  }

  /**
   * Refresh access token cho Official Account
   * @param oa Official Account entity
   * @returns Access token mới
   * @throws AppException nếu không thể refresh token
   */
  private async refreshAccessToken(oa: any): Promise<string> {
    try {
      if (!this.zaloAppId || !this.zaloAppSecret) {
        throw new AppException(
          ErrorCode.CONFIGURATION_ERROR,
          'ZALO_APP_ID hoặc ZALO_APP_SECRET không được cấu hình'
        );
      }

      if (!oa.refreshToken) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Official Account ${oa.oaId} không có refresh token`
        );
      }

      // Chuẩn bị form data
      const formData = new URLSearchParams();
      formData.append('refresh_token', oa.refreshToken);
      formData.append('app_id', this.zaloAppId);
      formData.append('grant_type', 'refresh_token');

      // Cấu hình request
      const config = {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          secret_key: this.zaloAppSecret,
        },
      };

      // Gọi API để lấy token mới
      const response = await lastValueFrom(
        this.httpService.post<ZaloRefreshTokenResponse>(
          'https://oauth.zaloapp.com/v4/oa/access_token',
          formData.toString(),
          config,
        ),
      );

      // Kiểm tra phản hồi
      if (!response.data || !response.data.access_token) {
        throw new Error('Không nhận được access token mới từ Zalo API');
      }

      const { access_token, refresh_token, expires_in } = response.data;

      // Cập nhật token mới vào database
      const expiresInMs = parseInt(expires_in) * 1000; // Chuyển giây thành mili giây
      const now = Date.now();
      const newExpiresAt = now + expiresInMs;

      // Cập nhật token mới vào Integration entity
      const updatedOA = await this.zaloOATokenAdapter.save({
        id: oa.id,
        accessToken: access_token,
        refreshToken: refresh_token,
        expiresAt: newExpiresAt,
        status: 'active',
      });

      if (!updatedOA) {
        throw new AppException(
          ErrorCode.DATABASE_ERROR,
          'Không thể cập nhật access token mới vào database'
        );
      }

      this.logger.log(`Successfully refreshed access token for OA ${oa.oaId}`);
      return access_token;
    } catch (error) {
      this.logger.error(`Error refreshing access token for OA ${oa.oaId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý các lỗi từ Zalo API
      if (error.response?.data?.error) {
        const zaloError = error.response.data;
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          `Lỗi từ Zalo API khi refresh token: ${zaloError.message || zaloError.error}`
        );
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Không thể refresh access token cho Official Account ${oa.oaId}`
      );
    }
  }

  /**
   * Kiểm tra xem Official Account có tồn tại và active không
   * @param oaId ID của Official Account
   * @returns true nếu OA tồn tại và active
   */
  async isOfficialAccountActive(oaId: string): Promise<boolean> {
    try {
      const oa = await this.zaloOATokenAdapter.findOne({
        where: { oaId, status: 'active' }
      });
      return oa !== null;
    } catch (error) {
      this.logger.error(`Error checking OA status for ${oaId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Lấy thông tin Official Account
   * @param oaId ID của Official Account
   * @returns Official Account entity hoặc null
   */
  async getOfficialAccount(oaId: string): Promise<any | null> {
    try {
      return await this.zaloOATokenAdapter.findOne({
        where: { oaId }
      });
    } catch (error) {
      this.logger.error(`Error getting OA info for ${oaId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Lấy thông tin Official Account theo userId và zaloOfficialAccountId
   * @param userId ID của user
   * @param zaloOfficialAccountId ID của Zalo Official Account trong database
   * @returns Official Account entity hoặc null
   */
  async getOfficialAccountByUserAndId(userId: string, zaloOfficialAccountId: string): Promise<any | null> {
    try {
      // Với Integration pattern, không hỗ trợ tìm theo userId và ID
      this.logger.warn(`Method getOfficialAccountByUserAndId không được hỗ trợ với Integration pattern`);
      return null;
    } catch (error) {
      this.logger.error(`Error getting OA info for user ${userId}, oa ${zaloOfficialAccountId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Vô hiệu hóa Official Account (đặt status = inactive)
   * Sử dụng khi refresh token thất bại hoàn toàn
   * @param oaId ID của Official Account
   */
  async deactivateOfficialAccount(oaId: string): Promise<void> {
    try {
      const oa = await this.zaloOATokenAdapter.findOne({
        where: { oaId }
      });
      if (oa) {
        await this.zaloOATokenAdapter.save({
          id: oa.id,
          status: 'inactive',
        });
        this.logger.warn(`Deactivated Official Account ${oaId} due to token issues`);
      }
    } catch (error) {
      this.logger.error(`Error deactivating OA ${oaId}: ${error.message}`);
    }
  }

  /**
   * Kiểm tra và làm sạch các token hết hạn
   * Method này có thể được gọi định kỳ để dọn dẹp
   */
  async cleanupExpiredTokens(): Promise<void> {
    try {
      // Với Integration pattern, không hỗ trợ cleanup tự động
      // Vì token được mã hóa và cần logic phức tạp hơn
      this.logger.warn('cleanupExpiredTokens không được hỗ trợ với Integration pattern');
    } catch (error) {
      this.logger.error(`Error during token cleanup: ${error.message}`);
    }
  }

  /**
   * Thực hiện API call với retry mechanism và tự động refresh token
   * @param apiCall Function thực hiện API call
   * @param oaId ID của Official Account
   * @param maxRetries Số lần retry tối đa
   * @returns Kết quả của API call
   */
  async executeWithTokenRetry<T>(
    apiCall: (accessToken: string) => Promise<T>,
    oaId: string,
    maxRetries: number = 3
  ): Promise<T> {
    this.logger.debug(`Executing API call with token retry for OA ${oaId}`);

    let tokenRefreshed = false;
    let currentAccessToken: string | null = null;

    return await backOff(
      async () => {
        try {
          // Get fresh token if we don't have one or if we just refreshed
          if (!currentAccessToken || tokenRefreshed) {
            currentAccessToken = await this.getValidAccessToken(oaId);
            tokenRefreshed = false;
          }

          // Execute the API call
          return await apiCall(currentAccessToken);
        } catch (error) {
          // Handle token-related errors (401, 403)
          if ((error.response?.status === 401 || error.response?.status === 403) && !tokenRefreshed) {
            this.logger.log(`Token error detected, forcing token refresh for OA ${oaId}`);

            try {
              const oa = await this.getOfficialAccount(oaId);
              if (oa) {
                currentAccessToken = await this.refreshAccessToken(oa);
                tokenRefreshed = true;

                // Retry immediately with the new token
                return await apiCall(currentAccessToken);
              }
            } catch (refreshError) {
              this.logger.error(`Failed to refresh token: ${refreshError.message}`);
              throw new AppException(
                ErrorCode.EXTERNAL_SERVICE_ERROR,
                `Không thể refresh access token: ${refreshError.message}`
              );
            }
          }

          // Re-throw the error for backOff to handle
          throw error;
        }
      },
      {
        numOfAttempts: maxRetries,
        startingDelay: 1000, // 1 second
        timeMultiple: 2, // Double the delay each time
        maxDelay: 10000, // Max 10 seconds
        jitter: 'full', // Add jitter to prevent thundering herd
        retry: (error: any, attemptNumber: number) => {
          this.logger.warn(`API call attempt ${attemptNumber}/${maxRetries} failed: ${error.message}`);

          // Don't retry on certain non-recoverable errors
          if (error instanceof AppException) {
            const errorMessage = error.message.toLowerCase();
            if (errorMessage.includes('resource_not_found') ||
                errorMessage.includes('configuration_error') ||
                errorMessage.includes('validation_error')) {
              this.logger.error(`Non-recoverable error, stopping retries: ${error.message}`);
              return false;
            }
          }

          // Don't retry on 4xx errors (except 401, 403, 429)
          if (error.response?.status >= 400 && error.response?.status < 500) {
            const status = error.response.status;
            if (status !== 401 && status !== 403 && status !== 429) {
              this.logger.error(`Client error ${status}, stopping retries: ${error.message}`);
              return false;
            }
          }

          // Reset token for next attempt if it's not a token refresh attempt
          if (!tokenRefreshed) {
            currentAccessToken = null;
          }

          return true; // Continue retrying
        }
      }
    ).catch((error) => {
      this.logger.error(`API call failed after ${maxRetries} attempts. Final error: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `API call thất bại sau ${maxRetries} lần thử. Lỗi cuối: ${error.message}`
      );
    });
  }

  /**
   * Thực hiện API call với retry mechanism cho user và zaloOfficialAccountId
   * @param apiCall Function thực hiện API call
   * @param userId ID của user
   * @param zaloOfficialAccountId ID của Zalo Official Account trong database
   * @param maxRetries Số lần retry tối đa
   * @returns Kết quả của API call
   */
  async executeWithTokenRetryByUserAndId<T>(
    apiCall: (accessToken: string) => Promise<T>,
    userId: string,
    zaloOfficialAccountId: string,
    maxRetries: number = 3
  ): Promise<T> {
    this.logger.debug(`Executing API call with token retry for user ${userId}, oa ${zaloOfficialAccountId}`);

    let tokenRefreshed = false;
    let currentAccessToken: string | null = null;

    return await backOff(
      async () => {
        try {
          // Get fresh token if we don't have one or if we just refreshed
          if (!currentAccessToken || tokenRefreshed) {
            currentAccessToken = await this.getValidAccessToken(userId, zaloOfficialAccountId);
            tokenRefreshed = false;
          }

          // Execute the API call
          return await apiCall(currentAccessToken);
        } catch (error) {
          // Handle token-related errors (401, 403)
          if ((error.response?.status === 401 || error.response?.status === 403) && !tokenRefreshed) {
            this.logger.log(`Token error detected, forcing token refresh for user ${userId}, oa ${zaloOfficialAccountId}`);

            try {
              const oa = await this.getOfficialAccountByUserAndId(userId, zaloOfficialAccountId);
              if (oa) {
                currentAccessToken = await this.refreshAccessToken(oa);
                tokenRefreshed = true;

                // Retry immediately with the new token
                return await apiCall(currentAccessToken);
              }
            } catch (refreshError) {
              this.logger.error(`Failed to refresh token: ${refreshError.message}`);
              throw new AppException(
                ErrorCode.EXTERNAL_SERVICE_ERROR,
                `Không thể refresh access token: ${refreshError.message}`
              );
            }
          }

          // Re-throw the error for backOff to handle
          throw error;
        }
      },
      {
        numOfAttempts: maxRetries,
        startingDelay: 1000, // 1 second
        timeMultiple: 2, // Double the delay each time
        maxDelay: 10000, // Max 10 seconds
        jitter: 'full', // Add jitter to prevent thundering herd
        retry: (error: any, attemptNumber: number) => {
          this.logger.warn(`API call attempt ${attemptNumber}/${maxRetries} failed: ${error.message}`);

          // Don't retry on certain non-recoverable errors
          if (error instanceof AppException) {
            const errorMessage = error.message.toLowerCase();
            if (errorMessage.includes('resource_not_found') ||
                errorMessage.includes('configuration_error') ||
                errorMessage.includes('validation_error')) {
              this.logger.error(`Non-recoverable error, stopping retries: ${error.message}`);
              return false;
            }
          }

          // Don't retry on 4xx errors (except 401, 403, 429)
          if (error.response?.status >= 400 && error.response?.status < 500) {
            const status = error.response.status;
            if (status !== 401 && status !== 403 && status !== 429) {
              this.logger.error(`Client error ${status}, stopping retries: ${error.message}`);
              return false;
            }
          }

          // Reset token for next attempt if it's not a token refresh attempt
          if (!tokenRefreshed) {
            currentAccessToken = null;
          }

          return true; // Continue retrying
        }
      }
    ).catch((error) => {
      this.logger.error(`API call failed after ${maxRetries} attempts. Final error: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `API call thất bại sau ${maxRetries} lần thử. Lỗi cuối: ${error.message}`
      );
    });
  }
}
