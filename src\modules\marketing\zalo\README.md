# Zalo ZNS Worker Mo<PERSON>le

<PERSON> xử lý các job gửi tin nhắn <PERSON> (Zalo Notification Service) trong worker.

## Tổng quan

Module này xử lý các loại job:
- `SEND_ZNS`: G<PERSON>i ZNS đơn lẻ
- `SEND_ZNS_CAMPAIGN`: <PERSON><PERSON><PERSON> theo chiến dịch
- `SEND_BATCH_ZNS`: Gửi batch ZNS

## Các loại tin nhắn được hỗ trợ

### 1. Tin Truyền thông (PROMOTION)
- Sử dụng `ZaloPromotionService`
- C<PERSON> các ràng buộc về thời gian gửi (8:00-22:00)
- Giới hạn tần suất (1 tin/tuần, 3 tin/tháng)
- Cần chuyển đổi phone thành userId

### 2. Tin Giao dịch (TRANSACTION)
- Sử dụng `ZaloZnsService`
- Không có ràng buộc thời gian
- <PERSON><PERSON><PERSON> cho thông báo giao dị<PERSON>, đ<PERSON><PERSON> hàng

### 3. <PERSON> Chăm sóc khách hàng (CUSTOMER_CARE)
- <PERSON><PERSON> dụng `ZaloZnsService`
- Dùng cho hỗ trợ khách hàng, thông báo dịch vụ

## Cấu trúc Job Data

### Single ZNS Job
```typescript
interface ZaloZnsSingleJobData {
  oaId: string;
  phone: string;
  templateId: string;
  templateData: Record<string, any>;
  campaignId?: number;
  trackingId?: string;
  messageType?: ZnsMessageType;
  retryCount?: number;
}
```

### Campaign ZNS Job
```typescript
interface ZaloZnsCampaignJobData {
  campaignId: number;
  oaId: string;
  templateId: string;
  templateData: Record<string, any>;
  phoneList: string[];
  messageType?: ZnsMessageType;
  batchSize?: number;
  batchDelay?: number;
}
```

### Batch ZNS Job
```typescript
interface ZaloZnsBatchJobData {
  oaId: string;
  messages: {
    phone: string;
    templateId: string;
    templateData: Record<string, any>;
    trackingId?: string;
    messageType?: ZnsMessageType;
  }[];
  campaignId?: number;
  batchIndex?: number;
  totalBatches?: number;
}
```

## Cách sử dụng

### 1. Từ App Module (redai-v201-be-app)

Job được tạo từ controller trong app module:

```typescript
// Trong ZaloZnsCampaignController
const jobData: SendZnsCampaignJobData = {
  campaignId: campaign.id,
  oaId: campaign.oaId,
  templateId: campaign.templateId,
  templateData: campaign.templateData,
  phoneList: phoneList,
  messageType: ZnsMessageType.PROMOTION, // Chỉ định loại tin nhắn
  batchSize: 10,
  batchDelay: 1000,
};

const job = await this.znsQueue.add(ZaloZnsJobName.SEND_ZNS_CAMPAIGN, jobData);
```

### 2. Xử lý trong Worker

Worker sẽ tự động xử lý job dựa trên `messageType`:

- **PROMOTION**: Sử dụng `ZaloPromotionService.sendPromotionMessage()`
- **TRANSACTION**: Sử dụng `ZaloZnsService.sendZnsMessage()`
- **CUSTOMER_CARE**: Sử dụng `ZaloZnsService.sendZnsMessage()`
- **Mặc định**: Sử dụng `ZaloZnsService.sendZnsMessage()`

## Cấu hình

### Environment Variables

```env
# Access token cho từng OA
ZALO_OA_{OA_ID}_ACCESS_TOKEN=your_access_token

# Template IDs cho tin truyền thông
ZALO_PROMOTION_CAMPAIGN_TEMPLATE_ID=template_id
ZALO_EVENT_NOTIFICATION_TEMPLATE_ID=template_id
```

## Logging và Monitoring

- Tất cả job được log với level INFO
- Lỗi được log với level ERROR và stack trace
- Có thể monitor qua Bull Dashboard tại `/queues`

## Xử lý lỗi

- Job sẽ retry tối đa 3 lần với exponential backoff
- Lỗi từ Zalo API được map thành AppException với message tiếng Việt
- Lỗi tin truyền thông có xử lý đặc biệt cho các mã lỗi 2001-2008

## Testing

Chạy test để kiểm tra processor:

```bash
# Từ thư mục worker
cd redai-v201-be-worker

# Chạy test
npx ts-node src/modules/marketing/zalo/test-zalo-zns.ts
```

## Monitoring

- Truy cập Bull Dashboard: `http://localhost:3000/queues`
- Username: `admin`
- Password: `redai@123`

## Integration với App Module

Processor này sẽ tự động xử lý các job được tạo từ:

1. **ZaloZnsCampaignController** trong `redai-v201-be-app`
2. **ZaloZnsCampaignService** trong `redai-v201-be-app`

Khi tạo job từ app module, chỉ cần chỉ định `messageType` để processor xử lý đúng:

```typescript
// Trong app module
const jobData = {
  // ... other data
  messageType: ZnsMessageType.PROMOTION, // Quan trọng!
};

await this.znsQueue.add(ZaloZnsJobName.SEND_ZNS, jobData);
```

## Lưu ý quan trọng

1. **Tin Truyền thông (PROMOTION)**:
   - Chỉ gửi được 8:00-22:00
   - Tối đa 1 tin/tuần, 3 tin/tháng cho mỗi user
   - Cần chuyển đổi phone thành userId

2. **Tin Giao dịch (TRANSACTION)**:
   - Không có giới hạn thời gian
   - Dùng cho thông báo đơn hàng, giao dịch

3. **Tin Chăm sóc khách hàng (CUSTOMER_CARE)**:
   - Dùng cho hỗ trợ, thông báo dịch vụ

## TODO

1. Implement logic lấy access token từ database
2. Implement API chuyển đổi phone thành Zalo userId
3. Thêm logging vào database cho tracking
4. Thêm metrics và monitoring
5. Implement retry logic cho từng loại lỗi khác nhau
