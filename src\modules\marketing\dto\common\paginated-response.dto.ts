import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin phân trang
 */
export class PaginationMetaDto {
  /**
   * Tổng số item
   * @example 100
   */
  @ApiProperty({
    description: 'Tổng số item',
    example: 100,
  })
  total: number;

  /**
   * Trang hiện tại
   * @example 1
   */
  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
  })
  page: number;

  /**
   * Số lượng item trên mỗi trang
   * @example 10
   */
  @ApiProperty({
    description: 'Số lượng item trên mỗi trang',
    example: 10,
  })
  limit: number;

  /**
   * Tổng số trang
   * @example 10
   */
  @ApiProperty({
    description: 'Tổng số trang',
    example: 10,
  })
  totalPages: number;

  /**
   * Có trang trước không
   * @example false
   */
  @ApiProperty({
    description: 'Có trang trước không',
    example: false,
  })
  hasPreviousPage: boolean;

  /**
   * <PERSON><PERSON> trang sau không
   * @example true
   */
  @ApiProperty({
    description: 'Có trang sau không',
    example: true,
  })
  hasNextPage: boolean;
}

/**
 * DTO cho phản hồi phân trang
 */
export class PaginatedResponseDto<T> {
  /**
   * Dữ liệu
   */
  @ApiProperty({
    description: 'Dữ liệu',
    isArray: true,
  })
  data: T[];

  /**
   * Thông tin phân trang
   */
  @ApiProperty({
    description: 'Thông tin phân trang',
    type: () => PaginationMetaDto,
  })
  meta: PaginationMetaDto;
}
