import { Injectable, Logger } from '@nestjs/common';
import { 
  IBaseNodeExecutor, 
  ValidationResult, 
  ExecutorContext, 
  ExecutionMetrics,
  ExecutorCapabilities,
  ValidationError,
  ValidationWarning
} from './node-executor.interface';
import { 
  NodeExecutionContext, 
  DetailedNodeExecutionResult, 
  NodeExecutionConfig,
  NodeExecutionHelper
} from '../../types';
import { NodeGroupEnum } from '../../../../enums/node-group.enum';

/**
 * Abstract base class cho tất cả node executors
 * Cung cấp common logic: validation, error handling, retry, logging, metrics
 */
@Injectable()
export abstract class BaseNodeExecutor implements IBaseNodeExecutor {
  protected readonly logger = new Logger(this.constructor.name);
  protected metrics: ExecutionMetrics = {
    startTime: 0,
  };
  
  // Abstract properties - ph<PERSON><PERSON> đ<PERSON> implement bởi subclasses
  abstract readonly nodeGroup: NodeGroupEnum;
  abstract readonly supportedNodeTypes: string[];
  abstract readonly executorName: string;
  abstract readonly version: string;
  
  // Default capabilities - có thể override bởi subclasses
  readonly capabilities: ExecutorCapabilities = {
    supportsAsync: true,
    supportsStreaming: false,
    supportsCaching: true,
    supportsRetry: true,
    supportsTimeout: true,
    supportsCancellation: true,
    supportsProgress: false,
    isThreadSafe: true,
    isStateless: true,
  };
  
  /**
   * Initialize executor
   */
  async initialize(): Promise<void> {
    this.logger.log(`Initializing ${this.executorName} v${this.version}`);
    await this.onInitialize();
  }
  
  /**
   * Destroy executor
   */
  async destroy(): Promise<void> {
    this.logger.log(`Destroying ${this.executorName}`);
    await this.onDestroy();
  }
  
  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      return await this.onHealthCheck();
    } catch (error) {
      this.logger.error(`Health check failed for ${this.executorName}:`, error);
      return false;
    }
  }
  
  /**
   * Kiểm tra xem có thể handle node type không
   */
  canHandle(nodeType: string): boolean {
    return this.supportedNodeTypes.includes(nodeType);
  }
  
  /**
   * Get default configuration
   */
  getDefaultConfig(): NodeExecutionConfig {
    return NodeExecutionHelper.createDefaultConfig(this.nodeGroup);
  }
  
  /**
   * Validate input data
   */
  async validateInput(context: NodeExecutionContext): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      canAutoFix: false,
    };
    
    try {
      // Basic validation
      await this.validateBasicInput(context, result);
      
      // Node-specific validation
      await this.validateNodeSpecificInput(context, result);
      
      // Check if there are any errors
      result.isValid = result.errors.length === 0;
      
      this.logger.debug(`Input validation for ${context.node.id}: ${result.isValid ? 'PASSED' : 'FAILED'}`);
      
      return result;
    } catch (error) {
      this.logger.error(`Input validation error for ${context.node.id}:`, error);
      result.isValid = false;
      result.errors.push({
        code: 'VALIDATION_EXCEPTION',
        message: `Validation failed: ${error.message}`,
        severity: 'error',
      });
      return result;
    }
  }
  
  /**
   * Main execution method
   */
  async execute(
    context: NodeExecutionContext, 
    config?: NodeExecutionConfig
  ): Promise<DetailedNodeExecutionResult> {
    const executorContext = this.createExecutorContext(context);
    const finalConfig = { ...this.getDefaultConfig(), ...config };
    
    this.startMetrics();
    
    try {
      this.logger.log(`Executing node ${context.node.id} (${context.node.name})`);
      
      // Pre-execution validation
      if (!finalConfig.skipValidation) {
        const validationResult = await this.validateInput(context);
        if (!validationResult.isValid) {
          return this.createFailureResult(
            new Error(`Input validation failed: ${validationResult.errors.map(e => e.message).join(', ')}`),
            executorContext
          );
        }
      }
      
      // Execute with timeout
      const result = await this.executeWithTimeout(executorContext, finalConfig);
      
      // Post-execution validation
      if (!finalConfig.skipValidation && result.success) {
        const outputValidation = await this.validateOutput(result.outputData, context);
        if (!outputValidation.isValid) {
          return this.createFailureResult(
            new Error(`Output validation failed: ${outputValidation.errors.map(e => e.message).join(', ')}`),
            executorContext
          );
        }
      }
      
      this.endMetrics();
      result.metadata = {
        ...result.metadata,
        executionTime: this.metrics.executionTime || 0,
        memoryUsage: this.metrics.memoryUsage,
      };
      
      this.logger.log(`Node ${context.node.id} executed successfully in ${result.metadata.executionTime}ms`);
      return result;
      
    } catch (error) {
      this.endMetrics();
      this.logger.error(`Node ${context.node.id} execution failed:`, error);
      return this.createFailureResult(error, executorContext);
    } finally {
      await this.cleanup(context);
    }
  }
  
  /**
   * Validate output data
   */
  async validateOutput(outputData: any, context: NodeExecutionContext): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      canAutoFix: false,
    };
    
    try {
      // Basic output validation
      await this.validateBasicOutput(outputData, context, result);
      
      // Node-specific output validation
      await this.validateNodeSpecificOutput(outputData, context, result);
      
      result.isValid = result.errors.length === 0;
      
      this.logger.debug(`Output validation for ${context.node.id}: ${result.isValid ? 'PASSED' : 'FAILED'}`);
      
      return result;
    } catch (error) {
      this.logger.error(`Output validation error for ${context.node.id}:`, error);
      result.isValid = false;
      result.errors.push({
        code: 'OUTPUT_VALIDATION_EXCEPTION',
        message: `Output validation failed: ${error.message}`,
        severity: 'error',
      });
      return result;
    }
  }
  
  /**
   * Cleanup resources
   */
  async cleanup(context: NodeExecutionContext): Promise<void> {
    try {
      await this.onCleanup(context);
    } catch (error) {
      this.logger.warn(`Cleanup failed for ${context.node.id}:`, error);
    }
  }
  
  /**
   * Should retry logic
   */
  async shouldRetry(
    context: NodeExecutionContext, 
    error: Error, 
    retryCount: number
  ): Promise<boolean> {
    const config = context.options;
    
    if (!config?.retryOnFail) {
      return false;
    }
    
    if (retryCount >= (config.maxRetries || 3)) {
      return false;
    }
    
    // Check if error is retryable
    const isRetryable = await this.isErrorRetryable(error, context);
    
    this.logger.debug(`Retry check for ${context.node.id}: ${isRetryable} (attempt ${retryCount + 1})`);
    
    return isRetryable;
  }
  
  /**
   * Get execution metrics
   */
  getMetrics(): ExecutionMetrics {
    return { ...this.metrics };
  }
  
  /**
   * Reset metrics
   */
  resetMetrics(): void {
    this.metrics = {
      startTime: 0,
    };
  }
  
  // Protected methods để override bởi subclasses
  
  /**
   * Hook được gọi khi initialize
   */
  protected async onInitialize(): Promise<void> {
    // Override in subclasses
  }
  
  /**
   * Hook được gọi khi destroy
   */
  protected async onDestroy(): Promise<void> {
    // Override in subclasses
  }
  
  /**
   * Hook cho health check
   */
  protected async onHealthCheck(): Promise<boolean> {
    return true; // Default implementation
  }
  
  /**
   * Hook cho cleanup
   */
  protected async onCleanup(context: NodeExecutionContext): Promise<void> {
    // Override in subclasses
  }
  
  /**
   * Abstract method - phải implement bởi subclasses
   */
  protected abstract executeNode(
    context: ExecutorContext, 
    config: NodeExecutionConfig
  ): Promise<DetailedNodeExecutionResult>;
  
  /**
   * Node-specific input validation - override in subclasses
   */
  protected async validateNodeSpecificInput(
    context: NodeExecutionContext, 
    result: ValidationResult
  ): Promise<void> {
    // Override in subclasses
  }
  
  /**
   * Node-specific output validation - override in subclasses
   */
  protected async validateNodeSpecificOutput(
    outputData: any, 
    context: NodeExecutionContext, 
    result: ValidationResult
  ): Promise<void> {
    // Override in subclasses
  }
  
  /**
   * Check if error is retryable - override in subclasses
   */
  protected async isErrorRetryable(error: Error, context: NodeExecutionContext): Promise<boolean> {
    // Default: retry on network errors, timeouts, temporary failures
    const retryableErrors = [
      'ECONNRESET',
      'ECONNREFUSED', 
      'ETIMEDOUT',
      'ENOTFOUND',
      'TIMEOUT',
      'RATE_LIMIT',
      'TEMPORARY_FAILURE',
    ];
    
    return retryableErrors.some(code => 
      error.message.includes(code) || 
      error.name.includes(code)
    );
  }
  
  // Private helper methods
  
  private createExecutorContext(context: NodeExecutionContext): ExecutorContext {
    return {
      ...context,
      startTime: Date.now(),
      currentRetryCount: 0,
      status: 'running',
      priority: 'normal',
      isRetrying: false,
      isDebugMode: context.options?.enableSSE || false,
      logger: this.logger,
      eventEmitter: null, // Will be injected by service
    };
  }
  
  private async executeWithTimeout(
    context: ExecutorContext, 
    config: NodeExecutionConfig
  ): Promise<DetailedNodeExecutionResult> {
    const timeout = config.timeout || 30000;
    
    return Promise.race([
      this.executeNode(context, config),
      new Promise<DetailedNodeExecutionResult>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Node execution timeout after ${timeout}ms`));
        }, timeout);
      }),
    ]);
  }
  
  private createFailureResult(
    error: Error, 
    context: ExecutorContext
  ): DetailedNodeExecutionResult {
    return {
      success: false,
      error,
      shouldRetry: false, // Will be determined by retry logic
      metadata: {
        executionTime: this.metrics.executionTime || 0,
        logs: [`Execution failed: ${error.message}`],
      },
    };
  }
  
  private async validateBasicInput(
    context: NodeExecutionContext, 
    result: ValidationResult
  ): Promise<void> {
    // Validate required fields
    if (!context.node) {
      result.errors.push({
        code: 'MISSING_NODE',
        message: 'Node is required',
        field: 'node',
        severity: 'error',
      });
    }
    
    if (!context.nodeDefinition) {
      result.errors.push({
        code: 'MISSING_NODE_DEFINITION',
        message: 'Node definition is required',
        field: 'nodeDefinition',
        severity: 'error',
      });
    }
    
    // Validate node type compatibility
    if (context.nodeDefinition && !this.canHandle(context.nodeDefinition.typeName)) {
      result.errors.push({
        code: 'INCOMPATIBLE_NODE_TYPE',
        message: `Executor ${this.executorName} cannot handle node type ${context.nodeDefinition.typeName}`,
        field: 'nodeDefinition.typeName',
        currentValue: context.nodeDefinition.typeName,
        expectedValue: this.supportedNodeTypes,
        severity: 'error',
      });
    }
  }
  
  private async validateBasicOutput(
    outputData: any, 
    context: NodeExecutionContext, 
    result: ValidationResult
  ): Promise<void> {
    // Basic output validation
    if (outputData === undefined) {
      result.warnings.push({
        code: 'UNDEFINED_OUTPUT',
        message: 'Output data is undefined',
        field: 'outputData',
        suggestion: 'Consider returning null or empty object instead',
      });
    }
  }
  
  private startMetrics(): void {
    this.metrics.startTime = Date.now();
    this.metrics.memoryUsage = process.memoryUsage().heapUsed;
  }
  
  private endMetrics(): void {
    this.metrics.endTime = Date.now();
    this.metrics.executionTime = this.metrics.endTime - this.metrics.startTime;
  }
}
