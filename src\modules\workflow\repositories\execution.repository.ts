import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, In, Between } from 'typeorm';
import { Execution } from '../entities/execution.entity';
import { ExecutionStatusEnum } from '../enums/execution-status.enum';
import { IExecutionData, IExecutionErrorDetails } from '../interfaces';

/**
 * Repository cho Execution entity
 * Chứa các method CRUD và business logic cho workflow executions
 */
@Injectable()
export class ExecutionRepository {
  constructor(
    @InjectRepository(Execution)
    private readonly repository: Repository<Execution>,
  ) {}

  /**
   * Tạo execution mới
   */
  async create(executionData: Partial<Execution>): Promise<Execution> {
    const execution = this.repository.create({
      ...executionData,
      startedAt: Date.now(),
    });
    return await this.repository.save(execution);
  }

  /**
   * Tìm execution theo ID
   */
  async findById(id: string): Promise<Execution | null> {
    return await this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm nhiều executions theo IDs
   */
  async findByIds(ids: string[]): Promise<Execution[]> {
    return await this.repository.find({
      where: { id: In(ids) },
      order: { startedAt: 'DESC' },
    });
  }

  /**
   * Tìm tất cả executions của một workflow
   */
  async findByWorkflowId(workflowId: string): Promise<Execution[]> {
    return await this.repository.find({
      where: { workflowId },
      order: { startedAt: 'DESC' },
    });
  }

  /**
   * Tìm executions theo status
   */
  async findByStatus(status: ExecutionStatusEnum): Promise<Execution[]> {
    return await this.repository.find({
      where: { status },
      order: { startedAt: 'DESC' },
    });
  }

  /**
   * Tìm executions theo workflow và status
   */
  async findByWorkflowIdAndStatus(
    workflowId: string,
    status: ExecutionStatusEnum,
  ): Promise<Execution[]> {
    return await this.repository.find({
      where: { workflowId, status },
      order: { startedAt: 'DESC' },
    });
  }

  /**
   * Tìm executions đang chạy
   */
  async findRunningExecutions(): Promise<Execution[]> {
    return await this.repository.find({
      where: { status: ExecutionStatusEnum.RUNNING },
      order: { startedAt: 'ASC' },
    });
  }

  /**
   * Tìm executions thành công
   */
  async findSuccessfulExecutions(workflowId?: string): Promise<Execution[]> {
    const where: FindOptionsWhere<Execution> = { status: ExecutionStatusEnum.SUCCEEDED };
    if (workflowId) {
      where.workflowId = workflowId;
    }

    return await this.repository.find({
      where,
      order: { startedAt: 'DESC' },
    });
  }

  /**
   * Tìm executions thất bại
   */
  async findFailedExecutions(workflowId?: string): Promise<Execution[]> {
    const where: FindOptionsWhere<Execution> = { status: ExecutionStatusEnum.FAILED };
    if (workflowId) {
      where.workflowId = workflowId;
    }

    return await this.repository.find({
      where,
      order: { startedAt: 'DESC' },
    });
  }

  /**
   * Tìm execution gần nhất của workflow
   */
  async findLatestByWorkflowId(workflowId: string): Promise<Execution | null> {
    return await this.repository.findOne({
      where: { workflowId },
      order: { startedAt: 'DESC' },
    });
  }

  /**
   * Cập nhật execution
   */
  async update(id: string, updateData: Partial<Execution>): Promise<Execution | null> {
    const updateResult = await this.repository.update(id, updateData);

    if ((updateResult.affected || 0) === 0) {
      return null;
    }

    return await this.findById(id);
  }

  /**
   * Cập nhật status của execution
   */
  async updateStatus(id: string, status: ExecutionStatusEnum): Promise<Execution | null> {
    const updateData: Partial<Execution> = { status };
    
    // Cập nhật thời gian kết thúc nếu execution hoàn thành
    if (status === ExecutionStatusEnum.SUCCEEDED || 
        status === ExecutionStatusEnum.FAILED || 
        status === ExecutionStatusEnum.CANCELLED) {
      updateData.finishedAt = Date.now();
    }

    return await this.update(id, updateData);
  }

  /**
   * Cập nhật output của execution
   */
  async updateOutput(id: string, output: IExecutionData): Promise<Execution | null> {
    return await this.update(id, { output });
  }

  /**
   * Cập nhật error details
   */
  async updateErrorDetails(id: string, errorDetails: IExecutionErrorDetails): Promise<Execution | null> {
    return await this.update(id, { 
      errorDetails,
      status: ExecutionStatusEnum.FAILED,
      finishedAt: Date.now(),
    });
  }

  /**
   * Bắt đầu execution
   */
  async startExecution(id: string): Promise<Execution | null> {
    return await this.update(id, {
      status: ExecutionStatusEnum.RUNNING,
      startedAt: Date.now(),
    });
  }

  /**
   * Hoàn thành execution thành công
   */
  async completeExecution(id: string, output?: IExecutionData): Promise<Execution | null> {
    const updateData: Partial<Execution> = {
      status: ExecutionStatusEnum.SUCCEEDED,
      finishedAt: Date.now(),
    };

    if (output) {
      updateData.output = output;
    }

    return await this.update(id, updateData);
  }

  /**
   * Thất bại execution
   */
  async failExecution(
    id: string,
    errorDetails: IExecutionErrorDetails,
    output?: IExecutionData,
  ): Promise<Execution | null> {
    const updateData: Partial<Execution> = {
      status: ExecutionStatusEnum.FAILED,
      errorDetails,
      finishedAt: Date.now(),
    };

    if (output) {
      updateData.output = output;
    }

    return await this.update(id, updateData);
  }

  /**
   * Hủy execution
   */
  async cancelExecution(id: string): Promise<Execution | null> {
    return await this.update(id, {
      status: ExecutionStatusEnum.CANCELLED,
      finishedAt: Date.now(),
    });
  }

  /**
   * Xóa execution
   */
  async delete(id: string): Promise<boolean> {
    const deleteResult = await this.repository.delete(id);
    return (deleteResult.affected || 0) > 0;
  }

  /**
   * Xóa tất cả executions của một workflow
   */
  async deleteByWorkflowId(workflowId: string): Promise<number> {
    const deleteResult = await this.repository.delete({ workflowId });
    return deleteResult.affected || 0;
  }

  /**
   * Đếm số lượng executions
   */
  async count(where?: FindOptionsWhere<Execution>): Promise<number> {
    return await this.repository.count({ where });
  }

  /**
   * Đếm executions theo workflow
   */
  async countByWorkflowId(workflowId: string): Promise<number> {
    return await this.repository.count({
      where: { workflowId },
    });
  }

  /**
   * Đếm executions theo status
   */
  async countByStatus(status: ExecutionStatusEnum): Promise<number> {
    return await this.repository.count({
      where: { status },
    });
  }

  /**
   * Đếm executions theo workflow và status
   */
  async countByWorkflowIdAndStatus(
    workflowId: string,
    status: ExecutionStatusEnum,
  ): Promise<number> {
    return await this.repository.count({
      where: { workflowId, status },
    });
  }

  /**
   * Tìm executions trong khoảng thời gian
   */
  async findByDateRange(startDate: number, endDate: number): Promise<Execution[]> {
    return await this.repository.find({
      where: {
        startedAt: Between(startDate, endDate),
      },
      order: { startedAt: 'DESC' },
    });
  }

  /**
   * Tìm executions với pagination
   */
  async findWithPagination(
    page: number = 1,
    limit: number = 10,
    where?: FindOptionsWhere<Execution>,
  ): Promise<{ executions: Execution[]; total: number; totalPages: number }> {
    const skip = (page - 1) * limit;
    
    const [executions, total] = await this.repository.findAndCount({
      where,
      skip,
      take: limit,
      order: { startedAt: 'DESC' },
    });

    const totalPages = Math.ceil(total / limit);

    return {
      executions,
      total,
      totalPages,
    };
  }

  /**
   * Thống kê executions theo status
   */
  async getExecutionStats(workflowId?: string): Promise<{
    total: number;
    succeeded: number;
    failed: number;
    running: number;
    cancelled: number;
  }> {
    const baseWhere: FindOptionsWhere<Execution> = {};
    if (workflowId) {
      baseWhere.workflowId = workflowId;
    }

    const [total, succeeded, failed, running, cancelled] = await Promise.all([
      this.count(baseWhere),
      this.count({ ...baseWhere, status: ExecutionStatusEnum.SUCCEEDED }),
      this.count({ ...baseWhere, status: ExecutionStatusEnum.FAILED }),
      this.count({ ...baseWhere, status: ExecutionStatusEnum.RUNNING }),
      this.count({ ...baseWhere, status: ExecutionStatusEnum.CANCELLED }),
    ]);

    return {
      total,
      succeeded,
      failed,
      running,
      cancelled,
    };
  }

  /**
   * Tìm executions cũ để cleanup
   */
  async findOldExecutions(olderThanDays: number): Promise<Execution[]> {
    const cutoffDate = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
    
    return await this.repository
      .createQueryBuilder('execution')
      .where('execution.startedAt < :cutoffDate', { cutoffDate })
      .andWhere('execution.status != :runningStatus', {
        runningStatus: ExecutionStatusEnum.RUNNING
      })
      .orderBy('execution.startedAt', 'ASC')
      .getMany();
  }

  /**
   * Cleanup executions cũ
   */
  async cleanupOldExecutions(olderThanDays: number): Promise<number> {
    const cutoffDate = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
    
    const deleteResult = await this.repository
      .createQueryBuilder()
      .delete()
      .from(Execution)
      .where('startedAt < :cutoffDate', { cutoffDate })
      .andWhere('status != :runningStatus', { 
        runningStatus: ExecutionStatusEnum.RUNNING 
      })
      .execute();

    return deleteResult.affected || 0;
  }
}
