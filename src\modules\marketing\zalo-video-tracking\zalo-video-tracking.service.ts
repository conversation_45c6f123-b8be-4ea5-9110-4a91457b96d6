import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { QueueName, ZaloVideoTrackingJobName } from '../../../queue/queue-name.enum';
import { ZaloVideoTrackingJobData } from '../../../queue/queue.types';
import { ZaloArticleService } from '../../../shared/services/zalo/zalo-article.service';
import { ZaloTokenUtilsService } from '../../../shared/services/zalo/zalo-token-utils.service';

/**
 * Service xử lý tracking video Zalo
 */
@Injectable()
export class ZaloVideoTrackingService {
  private readonly logger = new Logger(ZaloVideoTrackingService.name);

  constructor(
    @InjectQueue(QueueName.ZALO_VIDEO_TRACKING)
    private readonly zaloVideoTrackingQueue: Queue,
    private readonly zaloArticleService: ZaloArticleService,
  ) {}

  /**
   * <PERSON>ểm tra và cập nhật trạng thái video
   * @param token Token của video
   * @param accessToken Access token của Zalo OA
   * @param userId ID của user
   * @param integrationId ID của integration
   * @param oaId ID của OA (tương thích với hệ thống cũ)
   * @returns Kết quả check video status
   */
  async checkAndUpdateVideoStatus(
    token: string,
    accessToken: string,
    userId: number,
    integrationId: string,
    oaId?: string,
  ): Promise<any> {
    try {
      this.logger.debug(`Checking video status for token: ${token}`);

      // Debug log access token để so sánh với app
      this.logger.debug(`[WORKER] Using access token: ${accessToken.substring(0, 50)}...`);
      this.logger.debug(`[WORKER] Video token: ${token.substring(0, 50)}...`);

      // Gọi Zalo API để check video status
      const zaloResult = await this.zaloArticleService.checkVideoStatus(
        accessToken,
        token,
      );

      this.logger.debug('Zalo API result:', JSON.stringify(zaloResult, null, 2));

      // Cập nhật tracking information trong database
      try {
        await this.zaloArticleService.updateVideoUploadStatus(token, zaloResult);
        this.logger.debug(`Updated video upload status for token: ${token}`);
        this.logger.debug(`Status: ${zaloResult.status}, Video ID: ${zaloResult.video_id}`);
      } catch (error) {
        // Log lỗi nhưng không throw để không ảnh hưởng đến response chính
        this.logger.warn(`Failed to update video upload status: ${error.message}`);
      }

      return zaloResult;
    } catch (error) {
      this.logger.error(`Error checking video status: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lên lịch check video status lần tiếp theo
   * @param jobData Dữ liệu job
   * @returns Promise với ID của job đã tạo
   */
  async scheduleNextCheck(jobData: ZaloVideoTrackingJobData): Promise<string | undefined> {
    try {
      const job = await this.zaloVideoTrackingQueue.add(
        ZaloVideoTrackingJobName.CHECK_VIDEO_STATUS,
        jobData,
        {
          delay: jobData.delayMs || 30000, // Delay mặc định 30 giây
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
          removeOnComplete: 10,
          removeOnFail: 20,
        }
      );

      this.logger.log(`Scheduled next video check: ${job.id} (token: ${jobData.token})`);
      return job.id;
    } catch (error) {
      this.logger.error(`Error scheduling next video check: ${error.message}`, error.stack);
      throw error;
    }
  }
}
