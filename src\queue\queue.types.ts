/**
 * Interface cho job đồng bộ người dùng Zalo vào audience
 */
export interface ZaloAudienceSyncJobData {
  /**
   * ID của người dùng thực hiện đồng bộ
   */
  userId: number;

  /**
   * ID của Zalo Official Account
   */
  oaId: string;

  /**
   * Dữ liệu cấu hình đồng bộ
   */
  syncDto: any; // Tạm thời dùng any, sẽ thay bằng SyncZaloUsersToAudienceDto sau

  /**
   * ID để tracking job
   */
  syncId: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;
}

/**
 * Interface cho job data của Zalo Video Tracking
 */
export interface ZaloVideoTrackingJobData {
  /**
   * Token của video upload từ Zalo API
   */
  token: string;

  /**
   * Access token của Zalo Official Account
   */
  accessToken: string;

  /**
   * ID của user sở hữu video
   */
  userId: number;

  /**
   * ID của integration (Zalo OA)
   */
  integrationId: string;

  /**
   * ID của Official Account (để tương thích với hệ thống cũ)
   */
  oaId?: string;

  /**
   * Thời gian tạo job
   */
  timestamp: number;

  /**
   * Số lần đã check (để tránh check vô hạn)
   */
  checkCount?: number;

  /**
   * Thời gian delay giữa các lần check (milliseconds)
   */
  delayMs?: number;
}

/**
 * Dữ liệu job thực thi workflow
 */
export interface WorkflowExecutionJobData {
  /**
   * ID của workflow execution
   */
  executionId: string;

  /**
   * ID của workflow
   */
  workflowId: string;

  /**
   * ID của user thực thi
   */
  userId: number;

  /**
   * Dữ liệu trigger
   */
  triggerData: any;

  /**
   * Loại trigger (manual, webhook, schedule)
   */
  triggerType: 'manual' | 'webhook' | 'schedule';

  /**
   * Metadata bổ sung
   */
  metadata?: {
    source?: string;
    webhookId?: string;
    scheduleId?: string;
    priority?: number;
  };

  /**
   * Tùy chọn thực thi
   */
  options?: {
    enableSSE?: boolean;
    timeout?: number;
    retryOnFailure?: boolean;
  };
}

/**
 * Dữ liệu job thực thi node đơn lẻ
 */
export interface WorkflowNodeExecutionJobData {
  /**
   * ID của workflow execution
   */
  executionId: string;

  /**
   * ID của node cần thực thi
   */
  nodeId: string;

  /**
   * Loại node
   */
  nodeType: string;

  /**
   * Cấu hình node
   */
  nodeConfig: Record<string, any>;

  /**
   * Dữ liệu đầu vào cho node
   */
  inputData: Record<string, any>;

  /**
   * Context từ các node trước đó
   */
  executionContext: Record<string, any>;

  /**
   * Tùy chọn thực thi
   */
  options?: {
    enableSSE?: boolean;
    timeout?: number;
    skipValidation?: boolean;
  };
}

// Node Test job data interface removed - focusing on real execution only
