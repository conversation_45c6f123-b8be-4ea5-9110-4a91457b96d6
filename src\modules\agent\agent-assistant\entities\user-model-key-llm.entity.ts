import { Entity, PrimaryColumn } from 'typeorm';

/**
 * UserModelKeyLlm entity
 * Junction table linking user models to user API keys (many-to-many)
 */
@Entity('user_model_key_llm')
export class UserModelKeyLlm {
  /**
   * User model ID reference to user_models
   */
  @PrimaryColumn({ name: 'model_id', type: 'uuid' })
  modelId: string;

  /**
   * User LLM key ID reference to user_key_llm
   */
  @PrimaryColumn({ name: 'llm_key_id', type: 'uuid' })
  llmKeyId: string;
}
