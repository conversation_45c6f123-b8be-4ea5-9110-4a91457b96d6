import { NestFactory } from '@nestjs/core';
import { AppModule } from '../../../app.module';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AdminTemplateSms } from '../entities/admin-template-sms.entity';
import { SmsTypeEnum } from '../constants';

/**
 * Script để seed dữ liệu template SMS mẫu
 */
async function seedSmsTemplates() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const adminTemplateSmsRepository = app.get<Repository<AdminTemplateSms>>(
    getRepositoryToken(AdminTemplateSms),
  );

  try {
    console.log('🚀 Bắt đầu seed SMS templates...');

    const templates = [
      {
        name: 'Template SMS OTP',
        category: SmsTypeEnum.OTP,
        content:
          'Ma xac thuc cua ban la: {{OTP_CODE}}. Vui long khong chia se ma nay voi ai khac. Ma co hieu luc trong 5 phut.',
        placeholders: ['OTP_CODE'],
      },
      {
        name: 'Template SMS OTP 2FA',
        category: 'SMS_OTP_VERIFY_2FA',
        content:
          'Ma xac thuc 2FA cua ban la: {{TWO_FA_CODE}}. Ma co hieu luc trong 5 phut.',
        placeholders: ['TWO_FA_CODE'],
      },
      {
        name: 'Template SMS Đăng ký',
        category: SmsTypeEnum.REGISTRATION,
        content:
          'Chao mung {{USER_NAME}} da dang ky thanh cong tai {{COMPANY_NAME}}! Cam on ban da tin tuong va su dung dich vu cua chung toi.',
        placeholders: ['USER_NAME', 'COMPANY_NAME'],
      },
      {
        name: 'Template SMS Đăng nhập',
        category: SmsTypeEnum.LOGIN,
        content:
          'Chao {{USER_NAME}}! Ban vua dang nhap thanh cong vao he thong {{COMPANY_NAME}} luc {{LOGIN_TIME}}.',
        placeholders: ['USER_NAME', 'COMPANY_NAME', 'LOGIN_TIME'],
      },
      {
        name: 'Template SMS Quên mật khẩu',
        category: SmsTypeEnum.FORGOT_PASSWORD,
        content:
          'Ma reset mat khau cua ban la: {{RESET_CODE}}. Vui long nhap ma nay de dat lai mat khau. Ma co hieu luc trong 10 phut.',
        placeholders: ['RESET_CODE'],
      },
      {
        name: 'Template SMS Thay đổi mật khẩu',
        category: SmsTypeEnum.CHANGE_PASSWORD,
        content:
          'Mat khau cua ban da duoc thay doi thanh cong luc {{CHANGE_TIME}}. Neu ban khong thuc hien thao tac nay, vui long lien he ho tro.',
        placeholders: ['CHANGE_TIME'],
      },
      {
        name: 'Template SMS Xác thực email',
        category: SmsTypeEnum.EMAIL_VERIFICATION,
        content:
          'Ma xac thuc email cua ban la: {{VERIFICATION_CODE}}. Vui long nhap ma nay de xac thuc email {{EMAIL}}.',
        placeholders: ['VERIFICATION_CODE', 'EMAIL'],
      },
      {
        name: 'Template SMS Xác thực số điện thoại',
        category: SmsTypeEnum.PHONE_VERIFICATION,
        content:
          'Ma xac thuc so dien thoai cua ban la: {{VERIFICATION_CODE}}. Vui long nhap ma nay de xac thuc so dien thoai.',
        placeholders: ['VERIFICATION_CODE'],
      },
      {
        name: 'Template SMS Giao dịch',
        category: SmsTypeEnum.TRANSACTION,
        content:
          'Giao dich {{TRANSACTION_TYPE}} so tien {{AMOUNT}} VND da duoc thuc hien thanh cong luc {{TRANSACTION_TIME}}. Ma giao dich: {{TRANSACTION_ID}}.',
        placeholders: [
          'TRANSACTION_TYPE',
          'AMOUNT',
          'TRANSACTION_TIME',
          'TRANSACTION_ID',
        ],
      },
      {
        name: 'Template SMS Khuyến mãi',
        category: SmsTypeEnum.PROMOTION,
        content:
          'Chuong trinh khuyen mai dac biet! {{PROMOTION_TITLE}} - Giam gia {{DISCOUNT_PERCENT}}% cho don hang tren {{MIN_ORDER}} VND. Ma: {{PROMO_CODE}}. Het han: {{EXPIRY_DATE}}.',
        placeholders: [
          'PROMOTION_TITLE',
          'DISCOUNT_PERCENT',
          'MIN_ORDER',
          'PROMO_CODE',
          'EXPIRY_DATE',
        ],
      },
      {
        name: 'Template SMS Thông báo hệ thống',
        category: SmsTypeEnum.SYSTEM_NOTIFICATION,
        content:
          'Thong bao tu he thong {{COMPANY_NAME}}: {{NOTIFICATION_MESSAGE}}. Thoi gian: {{NOTIFICATION_TIME}}.',
        placeholders: [
          'COMPANY_NAME',
          'NOTIFICATION_MESSAGE',
          'NOTIFICATION_TIME',
        ],
      },
      {
        name: 'Template SMS Cảnh báo bảo mật',
        category: SmsTypeEnum.SECURITY_ALERT,
        content:
          'Canh bao bao mat! Phat hien dang nhap bat thuong vao tai khoan cua ban tu {{IP_ADDRESS}} luc {{ALERT_TIME}}. Neu khong phai ban, vui long doi mat khau ngay.',
        placeholders: ['IP_ADDRESS', 'ALERT_TIME'],
      },
      {
        name: 'Template SMS Thông báo tài khoản',
        category: SmsTypeEnum.ACCOUNT_NOTIFICATION,
        content:
          'Tai khoan cua ban co cap nhat moi: {{NOTIFICATION_MESSAGE}}. Thoi gian: {{UPDATE_TIME}}. Lien he ho tro neu can thiet.',
        placeholders: ['NOTIFICATION_MESSAGE', 'UPDATE_TIME'],
      },
    ];

    const currentTime = Date.now();

    for (const template of templates) {
      // Kiểm tra xem template đã tồn tại chưa
      const existingTemplate = await adminTemplateSmsRepository.findOne({
        where: { category: template.category },
      });

      if (existingTemplate) {
        console.log(`⚠️  Template ${template.name} đã tồn tại, bỏ qua...`);
        continue;
      }

      // Tạo template mới
      const newTemplate = adminTemplateSmsRepository.create({
        name: template.name,
        category: template.category,
        content: template.content,
        placeholders: template.placeholders,
        createdAt: currentTime,
        updatedAt: currentTime,
      });

      await adminTemplateSmsRepository.save(newTemplate);
      console.log(`✅ Đã tạo template: ${template.name}`);
    }

    console.log('🎉 Seed SMS templates hoàn thành!');
  } catch (error) {
    console.error('❌ Lỗi khi seed SMS templates:', error.message);
    console.error(error.stack);
  } finally {
    await app.close();
  }
}

// Chạy seed nếu file được gọi trực tiếp
if (require.main === module) {
  seedSmsTemplates();
}
