import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_segments trong cơ sở dữ liệu
 * Phân khúc khách hàng của người dùng
 */
@Entity('user_segments')
export class UserSegment {
  /**
   * ID của segment
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', comment: 'Mã khách hàng' })
  userId: number;

  // Không sử dụng quan hệ với bảng User, chỉ lưu ID

  /**
   * Tên segment
   */
  @Column({
    name: 'name',
    length: 255,
    nullable: true,
    comment: 'Tên tập khách hàng',
  })
  name: string;

  /**
   * <PERSON>ô tả segment
   */
  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
    comment: '<PERSON><PERSON> tả',
  })
  description: string;

  /**
   * <PERSON>i<PERSON>u kiện lọc kh<PERSON>ch hàng
   */
  @Column({
    name: 'criteria',
    type: 'jsonb',
    nullable: true,
    comment: '<PERSON><PERSON><PERSON> trữ điều kiện lọc khách hàng khi tạo segment',
  })
  criteria: any;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian tạo',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian cập nhật',
  })
  updatedAt: number;
}
