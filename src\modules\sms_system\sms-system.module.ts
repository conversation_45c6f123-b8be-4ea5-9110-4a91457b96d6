import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HttpModule, HttpService } from '@nestjs/axios';
import Redis from 'ioredis';
import { env } from '../../config';
import { QueueName } from '../../queue';
import { AdminTemplateSms } from './entities/admin-template-sms.entity';
import { SmsSystemService } from './sms-system.service';
import { SmsSystemProcessor } from './sms-system.processor';
import { SmsTemplateService } from './services/sms-template.service';
import { SmsEncryptionService } from './services/sms-encryption.service';
import {
  FptSmsBrandnameService,
  FptSmsConfig,
} from '../../shared/services/sms/fpt-sms-brandname.service';
import { SmsModule } from '../../shared/services/sms/sms.module';

/**
 * Module xử lý hệ thống SMS
 */
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([AdminTemplateSms]),
    BullModule.registerQueue({
      name: QueueName.SMS,
    }),
    HttpModule.register({
      timeout: 30000, // 30 seconds timeout
      maxRedirects: 5,
    }),
    SmsModule,
  ],
  providers: [
    SmsSystemService,
    SmsSystemProcessor,
    SmsTemplateService,
    SmsEncryptionService,
    {
      provide: Redis,
      useFactory: () => {
        return new Redis(env.external.REDIS_URL);
      },
    },
  ],
  exports: [SmsSystemService, SmsTemplateService, SmsEncryptionService],
})
export class SmsSystemModule {}
