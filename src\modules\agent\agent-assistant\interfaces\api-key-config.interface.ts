import { ProviderEnum } from '../enums/model-capabilities.enum';

/**
 * API key configuration interface
 * Defines the structure for API key data used in model configurations
 */
export interface ApiKeyConfig {
  /**
   * Unique identifier for the API key
   */
  keyId: string;

  /**
   * Encrypted API key value
   */
  encryptedKey: string;

  /**
   * AI model provider (OPENAI, ANTHROPIC, etc.)
   */
  provider: ProviderEnum;

  /**
   * Whether the API key is currently active
   */
  isActive: boolean;
}
