/**
 * @file Interface cho HTTP Request node
 * 
 * Định nghĩa type-safe interface cho node HTTP Request bao gồm:
 * - Parameters structure với validation
 * - Properties definition
 * - Input/Output data types
 * - HTTP methods và headers
 * 
 */

import {
    IBaseNodeInput,
    IBaseNodeOutput,
    ITypedNodeExecution
} from '../../execute.interface';

// =================================================================
// SECTION 1: ENUMS & TYPES
// Định nghĩa các enum và type cho HTTP Request
// =================================================================

/**
 * HTTP Methods được hỗ trợ
 */
export enum EHttpMethod {
    GET = 'GET',
    POST = 'POST',
    PUT = 'PUT',
    PATCH = 'PATCH',
    DELETE = 'DELETE',
    HEAD = 'HEAD',
    OPTIONS = 'OPTIONS'
}

/**
 * Content Types phổ biến
 */
export enum EContentType {
    JSON = 'application/json',
    FORM_URLENCODED = 'application/x-www-form-urlencoded',
    FORM_DATA = 'multipart/form-data',
    TEXT = 'text/plain',
    XML = 'application/xml',
    HTML = 'text/html'
}

/**
 * Authentication types
 */
export enum EAuthType {
    NONE = 'none',
    BASIC = 'basic',
    BEARER = 'bearer',
    API_KEY = 'apiKey',
    OAUTH2 = 'oauth2'
}

// =================================================================
// SECTION 2: PARAMETERS INTERFACE
// Định nghĩa cấu trúc parameters cho HTTP Request node
// =================================================================

/**
 * Interface cho parameters của HTTP Request node
 */
export interface IHttpRequestParameters {
    /** URL endpoint */
    url: string;

    /** HTTP method */
    method: EHttpMethod;

    /** Request headers */
    headers?: Record<string, string>;

    /** Query parameters */
    query_params?: Record<string, string>;

    /** Request body (for POST, PUT, PATCH) */
    body?: string | Record<string, any>;

    /** Content type */
    content_type?: EContentType;

    /** Authentication type */
    auth_type: EAuthType;

    /** Authentication credentials */
    auth_config?: {
        username?: string;
        password?: string;
        token?: string;
        api_key?: string;
        api_key_header?: string;
    };

    /** Timeout in milliseconds */
    timeout?: number;

    /** Follow redirects */
    follow_redirects?: boolean;

    /** SSL verification */
    verify_ssl?: boolean;

    /** Retry configuration */
    retry_config?: {
        enabled: boolean;
        max_retries: number;
        delay: number;
        backoff_factor: number;
    };
}

// =================================================================
// SECTION 3: INPUT/OUTPUT INTERFACES
// Định nghĩa cấu trúc dữ liệu đầu vào và đầu ra
// =================================================================

/**
 * Interface cho input data của HTTP Request node
 */
export interface IHttpRequestInput extends IBaseNodeInput {
    /** Dynamic URL parameters */
    url_params?: Record<string, string>;

    /** Dynamic query parameters */
    query_params?: Record<string, string>;

    /** Dynamic headers */
    headers?: Record<string, string>;

    /** Dynamic body data */
    body?: any;

    /** Variables để thay thế trong URL/body */
    variables?: Record<string, string>;
}

/**
 * Interface cho output data của HTTP Request node
 */
export interface IHttpRequestOutput extends IBaseNodeOutput {
    /** Response status code */
    status_code: number;

    /** Response status text */
    status_text: string;

    /** Response headers */
    headers: Record<string, string>;

    /** Response body */
    body: any;

    /** Response body as text */
    body_text: string;

    /** Response time in milliseconds */
    response_time: number;

    /** Request URL (final URL after redirects) */
    final_url: string;

    /** Có thành công không (2xx status) */
    success: boolean;

    /** Metadata bổ sung */
    metadata: {
        request_id: string;
        timestamp: number;
        redirects_count: number;
        content_length: number;
        content_type: string;
    };
}

/**
 * Type-safe node execution cho HTTP Request
 */
export type IHttpRequestNodeExecution = ITypedNodeExecution<
    IHttpRequestInput,
    IHttpRequestOutput,
    IHttpRequestParameters
>;

/**
 * Validation function cho HTTP Request parameters
 */
export function validateHttpRequestParameters(params: Partial<IHttpRequestParameters>): {
    isValid: boolean;
    errors: string[];
} {
    const errors: string[] = [];

    if (!params.url) {
        errors.push('URL is required');
    } else {
        try {
            new URL(params.url);
        } catch {
            errors.push('URL must be a valid URL');
        }
    }

    if (!params.method) {
        errors.push('HTTP method is required');
    }

    if (!params.auth_type) {
        errors.push('Authentication type is required');
    }

    if (params.timeout !== undefined && (params.timeout < 1000 || params.timeout > 300000)) {
        errors.push('Timeout must be between 1000 and 300000 milliseconds');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}
