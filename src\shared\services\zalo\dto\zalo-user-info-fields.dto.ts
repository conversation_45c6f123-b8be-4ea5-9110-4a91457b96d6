import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsNumber,
  IsEnum,
  IsBoolean,
  IsArray,
  <PERSON>,
  <PERSON>,
  IsNotEmpty,
} from 'class-validator';
import { ZaloUserInfoFieldType } from '../zalo.interface';

/**
 * DTO cho tùy chọn của trường select
 */
export class ZaloFieldOptionDto {
  @ApiProperty({
    description: 'Giá trị của tùy chọn',
    example: 'option_1',
  })
  @IsString()
  @IsNotEmpty()
  value: string;

  @ApiProperty({
    description: 'Nhãn hiển thị của tùy chọn',
    example: 'Tùy chọn 1',
  })
  @IsString()
  @IsNotEmpty()
  label: string;
}

/**
 * DTO cho yêu cầu tạo trường thông tin tùy biến
 */
export class ZaloCreateUserInfoFieldRequestDto {
  @ApiProperty({
    description: 'Tên trường thông tin',
    example: 'Công ty',
  })
  @IsString()
  @IsNotEmpty()
  field_name: string;

  @ApiProperty({
    description: 'Kiểu dữ liệu của trường',
    enum: ZaloUserInfoFieldType,
    example: ZaloUserInfoFieldType.TEXT,
  })
  @IsEnum(ZaloUserInfoFieldType)
  field_type: ZaloUserInfoFieldType;

  @ApiPropertyOptional({
    description: 'Mô tả trường thông tin',
    example: 'Tên công ty nơi khách hàng làm việc',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Trường có bắt buộc không',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  required?: boolean;

  @ApiPropertyOptional({
    description: 'Danh sách tùy chọn (chỉ áp dụng cho kiểu select)',
    type: [ZaloFieldOptionDto],
  })
  @IsArray()
  @IsOptional()
  options?: ZaloFieldOptionDto[];

  @ApiPropertyOptional({
    description: 'Giá trị mặc định',
    example: 'Không xác định',
  })
  @IsString()
  @IsOptional()
  default_value?: string;

  @ApiPropertyOptional({
    description: 'Thứ tự hiển thị',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  @Min(1)
  display_order?: number;
}

/**
 * DTO cho yêu cầu cập nhật trường thông tin
 */
export class ZaloUpdateUserInfoFieldRequestDto {
  @ApiPropertyOptional({
    description: 'Tên trường thông tin',
    example: 'Công ty làm việc',
  })
  @IsString()
  @IsOptional()
  field_name?: string;

  @ApiPropertyOptional({
    description: 'Mô tả trường thông tin',
    example: 'Tên công ty nơi khách hàng đang làm việc',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Trường có bắt buộc không',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  required?: boolean;

  @ApiPropertyOptional({
    description: 'Danh sách tùy chọn (chỉ áp dụng cho kiểu select)',
    type: [ZaloFieldOptionDto],
  })
  @IsArray()
  @IsOptional()
  options?: ZaloFieldOptionDto[];

  @ApiPropertyOptional({
    description: 'Giá trị mặc định',
    example: 'Chưa cập nhật',
  })
  @IsString()
  @IsOptional()
  default_value?: string;

  @ApiPropertyOptional({
    description: 'Thứ tự hiển thị',
    example: 2,
  })
  @IsNumber()
  @IsOptional()
  @Min(1)
  display_order?: number;
}

/**
 * DTO cho thông tin trường thông tin người dùng
 */
export class ZaloUserInfoFieldDto {
  @ApiProperty({
    description: 'ID của trường thông tin',
    example: 'field_123456789',
  })
  field_id: string;

  @ApiProperty({
    description: 'Tên trường thông tin',
    example: 'Công ty',
  })
  field_name: string;

  @ApiProperty({
    description: 'Kiểu dữ liệu của trường',
    enum: ZaloUserInfoFieldType,
    example: ZaloUserInfoFieldType.TEXT,
  })
  field_type: ZaloUserInfoFieldType;

  @ApiPropertyOptional({
    description: 'Mô tả trường thông tin',
    example: 'Tên công ty nơi khách hàng làm việc',
  })
  description?: string;

  @ApiPropertyOptional({
    description: 'Trường có bắt buộc không',
    example: false,
  })
  required?: boolean;

  @ApiPropertyOptional({
    description: 'Danh sách tùy chọn (chỉ áp dụng cho kiểu select)',
    type: [ZaloFieldOptionDto],
  })
  options?: ZaloFieldOptionDto[];

  @ApiPropertyOptional({
    description: 'Giá trị mặc định',
    example: 'Không xác định',
  })
  default_value?: string;

  @ApiPropertyOptional({
    description: 'Thứ tự hiển thị',
    example: 1,
  })
  display_order?: number;

  @ApiPropertyOptional({
    description: 'Trường có phải là trường hệ thống không',
    example: false,
  })
  is_system_field?: boolean;

  @ApiPropertyOptional({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1625097600000,
  })
  created_time?: number;

  @ApiPropertyOptional({
    description: 'Thời gian cập nhật cuối (Unix timestamp)',
    example: 1625097600000,
  })
  updated_time?: number;
}

/**
 * DTO cho danh sách trường thông tin
 */
export class ZaloUserInfoFieldListDto {
  @ApiProperty({
    description: 'Danh sách trường thông tin',
    type: [ZaloUserInfoFieldDto],
  })
  fields: ZaloUserInfoFieldDto[];

  @ApiProperty({
    description: 'Tổng số trường thông tin',
    example: 15,
  })
  total: number;

  @ApiProperty({
    description: 'Có còn dữ liệu tiếp theo không',
    example: false,
  })
  has_more: boolean;

  @ApiPropertyOptional({
    description: 'Offset tiếp theo',
    example: 20,
  })
  next_offset?: number;
}

/**
 * DTO cho tham số phân trang danh sách trường thông tin
 */
export class ZaloUserInfoFieldListParamsDto {
  @ApiPropertyOptional({
    description: 'Vị trí bắt đầu',
    example: 0,
    minimum: 0,
  })
  @IsNumber()
  @IsOptional()
  @Min(0)
  offset?: number = 0;

  @ApiPropertyOptional({
    description: 'Số lượng trường tối đa trả về (1-50)',
    example: 20,
    minimum: 1,
    maximum: 50,
  })
  @IsNumber()
  @IsOptional()
  @Min(1)
  @Max(50)
  count?: number = 20;
}

/**
 * DTO cho phản hồi thành công
 */
export class ZaloUserInfoFieldSuccessResponseDto {
  @ApiProperty({
    description: 'Trạng thái thành công',
    example: true,
  })
  success: boolean;
}
