/**
 * Interface cho cấu hình Google Sheets API
 */
export interface GoogleSheetsConfig {
  /**
   * Client ID từ Google Cloud Console
   */
  clientId: string;

  /**
   * Client Secret từ Google Cloud Console
   */
  clientSecret: string;

  /**
   * Redirect URI cho OAuth
   */
  redirectUri: string;
}

/**
 * Interface cho thông tin xác thực Google Sheets
 */
export interface GoogleSheetsCredentials {
  /**
   * Access Token
   */
  accessToken: string;

  /**
   * Refresh Token
   */
  refreshToken?: string;

  /**
   * Thời gian hết hạn của Access Token (Unix timestamp)
   */
  expiresAt?: number;
}

/**
 * Interface cho thông tin spreadsheet
 */
export interface SpreadsheetInfo {
  /**
   * ID của spreadsheet
   */
  spreadsheetId: string;

  /**
   * Tiêu đề của spreadsheet
   */
  title: string;

  /**
   * URL của spreadsheet
   */
  url: string;

  /**
   * Danh sách các sheet
   */
  sheets: SheetInfo[];

  /**
   * Thời gian tạo
   */
  createdTime?: string;

  /**
   * Thời gian cập nhật cuối
   */
  modifiedTime?: string;
}

/**
 * Interface cho thông tin sheet
 */
export interface SheetInfo {
  /**
   * ID của sheet
   */
  sheetId: number;

  /**
   * Tên của sheet
   */
  title: string;

  /**
   * Chỉ số của sheet
   */
  index: number;

  /**
   * Số hàng
   */
  rowCount: number;

  /**
   * Số cột
   */
  columnCount: number;
}

/**
 * Interface cho dữ liệu cell
 */
export interface CellData {
  /**
   * Giá trị của cell
   */
  value: string | number | boolean;

  /**
   * Công thức (nếu có)
   */
  formula?: string;

  /**
   * Định dạng của cell
   */
  format?: CellFormat;
}

/**
 * Interface cho định dạng cell
 */
export interface CellFormat {
  /**
   * Màu nền
   */
  backgroundColor?: SheetsColor;

  /**
   * Màu chữ
   */
  textColor?: SheetsColor;

  /**
   * Font chữ
   */
  fontFamily?: string;

  /**
   * Kích thước font
   */
  fontSize?: number;

  /**
   * In đậm
   */
  bold?: boolean;

  /**
   * In nghiêng
   */
  italic?: boolean;

  /**
   * Gạch chân
   */
  underline?: boolean;
}

/**
 * Interface cho màu sắc
 */
export interface SheetsColor {
  /**
   * Giá trị red (0-1)
   */
  red: number;

  /**
   * Giá trị green (0-1)
   */
  green: number;

  /**
   * Giá trị blue (0-1)
   */
  blue: number;

  /**
   * Giá trị alpha (0-1)
   */
  alpha?: number;
}

/**
 * Interface cho range dữ liệu
 */
export interface DataRange {
  /**
   * Tên sheet
   */
  sheetName: string;

  /**
   * Hàng bắt đầu (1-indexed)
   */
  startRow: number;

  /**
   * Cột bắt đầu (1-indexed)
   */
  startColumn: number;

  /**
   * Hàng kết thúc (1-indexed)
   */
  endRow?: number;

  /**
   * Cột kết thúc (1-indexed)
   */
  endColumn?: number;
}

/**
 * Interface cho kết quả đọc dữ liệu
 */
export interface ReadDataResult {
  /**
   * Range đã đọc
   */
  range: string;

  /**
   * Dữ liệu đã đọc
   */
  values: (string | number | boolean)[][];

  /**
   * Số hàng đã đọc
   */
  rowCount: number;

  /**
   * Số cột đã đọc
   */
  columnCount: number;
}

/**
 * Interface cho kết quả ghi dữ liệu
 */
export interface WriteDataResult {
  /**
   * Range đã ghi
   */
  updatedRange: string;

  /**
   * Số hàng đã ghi
   */
  updatedRows: number;

  /**
   * Số cột đã ghi
   */
  updatedColumns: number;

  /**
   * Số cell đã ghi
   */
  updatedCells: number;
}

/**
 * Interface cho tạo spreadsheet mới
 */
export interface CreateSpreadsheetRequest {
  /**
   * Tiêu đề của spreadsheet
   */
  title: string;

  /**
   * Danh sách sheet ban đầu
   */
  sheets?: CreateSheetRequest[];
}

/**
 * Interface cho tạo sheet mới
 */
export interface CreateSheetRequest {
  /**
   * Tiêu đề của sheet
   */
  title: string;

  /**
   * Số hàng
   */
  rowCount?: number;

  /**
   * Số cột
   */
  columnCount?: number;
}

/**
 * Interface cho batch update request
 */
export interface BatchUpdateRequest {
  /**
   * Danh sách các request
   */
  requests: BatchUpdateRequestItem[];

  /**
   * Có bao gồm response trong kết quả không
   */
  includeSpreadsheetInResponse?: boolean;

  /**
   * Các field cần trả về
   */
  responseRanges?: string[];
}

/**
 * Interface cho batch update request item
 */
export interface BatchUpdateRequestItem {
  /**
   * Loại request
   */
  type: 'updateCells' | 'addSheet' | 'deleteSheet' | 'updateSheetProperties';

  /**
   * Dữ liệu của request
   */
  data: Record<string, unknown>;
}

/**
 * Interface cho chart data
 */
export interface ChartData {
  /**
   * Loại chart
   */
  type: 'LINE' | 'BAR' | 'COLUMN' | 'PIE' | 'SCATTER';

  /**
   * Tiêu đề chart
   */
  title: string;

  /**
   * Range dữ liệu
   */
  dataRange: DataRange;

  /**
   * Vị trí chart
   */
  position: ChartPosition;
}

/**
 * Interface cho vị trí chart
 */
export interface ChartPosition {
  /**
   * ID sheet chứa chart
   */
  sheetId: number;

  /**
   * Hàng bắt đầu
   */
  startRow: number;

  /**
   * Cột bắt đầu
   */
  startColumn: number;

  /**
   * Số hàng
   */
  rowCount: number;

  /**
   * Số cột
   */
  columnCount: number;
}
