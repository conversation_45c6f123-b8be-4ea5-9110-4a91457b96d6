import { McpTransportType } from '../enums/mcp-transport-type.enum';

/**
 * UserMcp configuration interface
 * Defines the structure for MCP server configuration
 */
export interface UserMcpConfig {
  /**
   * MCP server URL endpoint
   */
  url: string;

  /**
   * Transport protocol for MCP communication
   */
  transport: McpTransportType;

  /**
   * Whether to automatically fallback to SSE if other transports fail
   */
  automaticSSEFallback?: boolean;
}
