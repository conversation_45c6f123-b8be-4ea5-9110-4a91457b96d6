import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';

@Module({
  imports: [
    EventEmitterModule.forRoot({
      wildcard: true,
      delimiter: '.',
      maxListeners: 20,
    }),
  ],
  providers: [
    // Pre-stream services will be added here
    // Streaming services will be added here
    // Post-stream services will be added here
  ],
  exports: [
    // Public API services will be exported here
  ],
})
export class WorkflowModule {}
