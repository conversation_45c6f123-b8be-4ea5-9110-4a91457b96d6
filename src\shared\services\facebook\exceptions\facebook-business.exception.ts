import { HttpStatus } from '@nestjs/common';
import { AppException, ErrorCode } from '@common/exceptions';

/**
 * Facebook Business API Error Codes
 * Phạm vi: 15000-15999
 */
export const FACEBOOK_BUSINESS_ERROR_CODES = {
  // Configuration errors (15000-15099)
  FACEBOOK_BUSINESS_CONFIGURATION_ERROR: new ErrorCode(
    15000,
    'Lỗi cấu hình Facebook Business API',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  FACEBOOK_BUSINESS_INVALID_ACCESS_TOKEN: new ErrorCode(
    15001,
    'Access token Facebook không hợp lệ',
    HttpStatus.UNAUTHORIZED,
  ),
  FACEBOOK_BUSINESS_MISSING_PERMISSIONS: new ErrorCode(
    15002,
    'Thiếu quyền truy cập Facebook Business API',
    HttpStatus.FORBIDDEN,
  ),

  // API errors (15100-15199)
  FACEBOOK_BUSINESS_API_ERROR: new ErrorCode(
    15100,
    'Lỗi Facebook Business API',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  FACEBOOK_BUSINESS_RATE_LIMIT: new ErrorCode(
    15101,
    'Vượt quá giới hạn tần suất Facebook API',
    HttpStatus.TOO_MANY_REQUESTS,
  ),
  FACEBOOK_BUSINESS_QUOTA_EXCEEDED: new ErrorCode(
    15102,
    'Vượt quá quota Facebook API',
    HttpStatus.TOO_MANY_REQUESTS,
  ),
  FACEBOOK_BUSINESS_UNKNOWN_ERROR: new ErrorCode(
    15103,
    'Lỗi không xác định từ Facebook Business API',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Account errors (15200-15299)
  FACEBOOK_BUSINESS_ACCOUNT_NOT_FOUND: new ErrorCode(
    15200,
    'Không tìm thấy Business Account',
    HttpStatus.NOT_FOUND,
  ),
  FACEBOOK_BUSINESS_AD_ACCOUNT_NOT_FOUND: new ErrorCode(
    15201,
    'Không tìm thấy Ad Account',
    HttpStatus.NOT_FOUND,
  ),
  FACEBOOK_BUSINESS_ACCOUNT_DISABLED: new ErrorCode(
    15202,
    'Tài khoản Facebook đã bị vô hiệu hóa',
    HttpStatus.FORBIDDEN,
  ),

  // Campaign errors (15300-15399)
  FACEBOOK_BUSINESS_CAMPAIGN_NOT_FOUND: new ErrorCode(
    15300,
    'Không tìm thấy campaign',
    HttpStatus.NOT_FOUND,
  ),
  FACEBOOK_BUSINESS_CAMPAIGN_CREATION_FAILED: new ErrorCode(
    15301,
    'Tạo campaign thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  FACEBOOK_BUSINESS_CAMPAIGN_UPDATE_FAILED: new ErrorCode(
    15302,
    'Cập nhật campaign thất bại',
    HttpStatus.BAD_REQUEST,
  ),

  // Ad Set errors (15400-15499)
  FACEBOOK_BUSINESS_ADSET_NOT_FOUND: new ErrorCode(
    15400,
    'Không tìm thấy ad set',
    HttpStatus.NOT_FOUND,
  ),
  FACEBOOK_BUSINESS_ADSET_CREATION_FAILED: new ErrorCode(
    15401,
    'Tạo ad set thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  FACEBOOK_BUSINESS_ADSET_UPDATE_FAILED: new ErrorCode(
    15402,
    'Cập nhật ad set thất bại',
    HttpStatus.BAD_REQUEST,
  ),

  // Ad errors (15500-15599)
  FACEBOOK_BUSINESS_AD_NOT_FOUND: new ErrorCode(
    15500,
    'Không tìm thấy ad',
    HttpStatus.NOT_FOUND,
  ),
  FACEBOOK_BUSINESS_AD_CREATION_FAILED: new ErrorCode(
    15501,
    'Tạo ad thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  FACEBOOK_BUSINESS_AD_UPDATE_FAILED: new ErrorCode(
    15502,
    'Cập nhật ad thất bại',
    HttpStatus.BAD_REQUEST,
  ),

  // Creative errors (15600-15699)
  FACEBOOK_BUSINESS_CREATIVE_NOT_FOUND: new ErrorCode(
    15600,
    'Không tìm thấy creative',
    HttpStatus.NOT_FOUND,
  ),
  FACEBOOK_BUSINESS_CREATIVE_CREATION_FAILED: new ErrorCode(
    15601,
    'Tạo creative thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  FACEBOOK_BUSINESS_CREATIVE_UPDATE_FAILED: new ErrorCode(
    15602,
    'Cập nhật creative thất bại',
    HttpStatus.BAD_REQUEST,
  ),

  // Audience errors (15700-15799)
  FACEBOOK_BUSINESS_AUDIENCE_NOT_FOUND: new ErrorCode(
    15700,
    'Không tìm thấy audience',
    HttpStatus.NOT_FOUND,
  ),
  FACEBOOK_BUSINESS_AUDIENCE_CREATION_FAILED: new ErrorCode(
    15701,
    'Tạo audience thất bại',
    HttpStatus.BAD_REQUEST,
  ),
  FACEBOOK_BUSINESS_AUDIENCE_UPDATE_FAILED: new ErrorCode(
    15702,
    'Cập nhật audience thất bại',
    HttpStatus.BAD_REQUEST,
  ),

  // Insights errors (15800-15899)
  FACEBOOK_BUSINESS_INSIGHTS_NOT_AVAILABLE: new ErrorCode(
    15800,
    'Insights không khả dụng',
    HttpStatus.NOT_FOUND,
  ),
  FACEBOOK_BUSINESS_INSIGHTS_QUERY_FAILED: new ErrorCode(
    15801,
    'Truy vấn insights thất bại',
    HttpStatus.BAD_REQUEST,
  ),

  // Validation errors (15900-15999)
  FACEBOOK_BUSINESS_INVALID_PARAMETERS: new ErrorCode(
    15900,
    'Tham số không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  FACEBOOK_BUSINESS_MISSING_REQUIRED_FIELDS: new ErrorCode(
    15901,
    'Thiếu trường bắt buộc',
    HttpStatus.BAD_REQUEST,
  ),
  FACEBOOK_BUSINESS_INVALID_DATE_RANGE: new ErrorCode(
    15902,
    'Khoảng thời gian không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
};

/**
 * Map Facebook Business API error codes to internal error codes
 * @param error Facebook API error
 * @param defaultCode Default error code if mapping not found
 * @returns Mapped error code
 */
export function handleFacebookBusinessApiError(
  error: any,
  defaultCode: ErrorCode = FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_UNKNOWN_ERROR,
): ErrorCode {
  // Check if error has Facebook API error structure
  if (error?.response?.data?.error) {
    const facebookError = error.response.data.error;
    const errorCode = facebookError.code;
    const errorSubcode = facebookError.error_subcode;

    // Map specific Facebook error codes
    switch (errorCode) {
      case 1:
        return FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_UNKNOWN_ERROR;

      case 2:
        return FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_API_ERROR;

      case 4:
        return FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_RATE_LIMIT;

      case 17:
        return FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_QUOTA_EXCEEDED;

      case 100:
        // OAuth errors
        if (errorSubcode === 458 || errorSubcode === 459 || errorSubcode === 460) {
          return FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_INVALID_ACCESS_TOKEN;
        }
        return FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_API_ERROR;

      case 190:
        return FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_INVALID_ACCESS_TOKEN;

      case 200:
        return FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_MISSING_PERMISSIONS;

      case 368:
        return FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_RATE_LIMIT;

      case 803:
        return FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_ACCOUNT_NOT_FOUND;

      case 1487:
        return FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_AD_ACCOUNT_NOT_FOUND;

      case 1498:
        return FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_ACCOUNT_DISABLED;

      default:
        return defaultCode;
    }
  }

  // Check HTTP status codes
  if (error?.response?.status) {
    switch (error.response.status) {
      case 400:
        return FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_INVALID_PARAMETERS;

      case 401:
        return FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_INVALID_ACCESS_TOKEN;

      case 403:
        return FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_MISSING_PERMISSIONS;

      case 404:
        return FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_ACCOUNT_NOT_FOUND;

      case 429:
        return FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_RATE_LIMIT;

      case 500:
      case 502:
      case 503:
      case 504:
        return FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_API_ERROR;

      default:
        return defaultCode;
    }
  }

  return defaultCode;
}

/**
 * Create Facebook Business API exception with proper error mapping
 * @param error Original error
 * @param message Custom error message
 * @param metadata Additional metadata
 * @returns AppException with mapped error code
 */
export function createFacebookBusinessException(
  error: any,
  message: string,
  metadata?: Record<string, unknown>,
): AppException {
  const errorCode = handleFacebookBusinessApiError(error);

  // Extract Facebook error details if available
  let facebookErrorDetails: Record<string, unknown> = {};
  if (error?.response?.data?.error) {
    const facebookError = error.response.data.error;
    facebookErrorDetails = {
      facebookErrorCode: facebookError.code,
      facebookErrorSubcode: facebookError.error_subcode,
      facebookErrorMessage: facebookError.message,
      facebookErrorType: facebookError.type,
      facebookTraceId: facebookError.fbtrace_id,
    };
  }

  return new AppException(errorCode, message, {
    ...metadata,
    ...facebookErrorDetails,
    originalError: error.message,
  });
}

/**
 * Validate Facebook Business API response
 * @param response API response
 * @param expectedFields Expected fields in response
 * @throws AppException if validation fails
 */
export function validateFacebookBusinessResponse(
  response: any,
  expectedFields: string[] = [],
): void {
  if (!response) {
    throw new AppException(
      FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_API_ERROR,
      'Facebook Business API response is empty',
    );
  }

  if (response.error) {
    throw createFacebookBusinessException(
      { response: { data: response } },
      'Facebook Business API returned error',
    );
  }

  // Validate expected fields
  for (const field of expectedFields) {
    if (!(field in response)) {
      throw new AppException(
        FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_MISSING_REQUIRED_FIELDS,
        `Missing required field: ${field}`,
        { missingField: field, response },
      );
    }
  }
}

/**
 * Validate date range for Facebook Business API
 * @param dateStart Start date
 * @param dateStop End date
 * @throws AppException if date range is invalid
 */
export function validateDateRange(dateStart: string, dateStop: string): void {
  const start = new Date(dateStart);
  const stop = new Date(dateStop);
  const now = new Date();

  if (isNaN(start.getTime()) || isNaN(stop.getTime())) {
    throw new AppException(
      FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_INVALID_DATE_RANGE,
      'Invalid date format. Use YYYY-MM-DD format.',
    );
  }

  if (start > stop) {
    throw new AppException(
      FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_INVALID_DATE_RANGE,
      'Start date must be before or equal to end date',
    );
  }

  if (stop > now) {
    throw new AppException(
      FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_INVALID_DATE_RANGE,
      'End date cannot be in the future',
    );
  }

  // Facebook has a limit on how far back you can query data (usually 37 months)
  const maxPastDate = new Date();
  maxPastDate.setMonth(maxPastDate.getMonth() - 37);

  if (start < maxPastDate) {
    throw new AppException(
      FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_INVALID_DATE_RANGE,
      'Start date is too far in the past. Maximum is 37 months ago.',
    );
  }
}

/**
 * Validate Facebook Business API parameters
 * @param params Parameters to validate
 * @param requiredParams Required parameter names
 * @throws AppException if validation fails
 */
export function validateFacebookBusinessParams(
  params: Record<string, unknown>,
  requiredParams: string[] = [],
): void {
  for (const param of requiredParams) {
    if (!(param in params) || params[param] === null || params[param] === undefined) {
      throw new AppException(
        FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_MISSING_REQUIRED_FIELDS,
        `Missing required parameter: ${param}`,
        { missingParameter: param, providedParams: Object.keys(params) },
      );
    }
  }
}

/**
 * Check if error is retryable
 * @param error Error to check
 * @returns True if error is retryable
 */
export function isFacebookBusinessErrorRetryable(error: any): boolean {
  const errorCode = handleFacebookBusinessApiError(error);
  
  const retryableErrors = [
    FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_RATE_LIMIT,
    FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_API_ERROR,
  ];

  return retryableErrors.includes(errorCode);
}

/**
 * Get retry delay for Facebook Business API errors
 * @param error Error object
 * @param attempt Retry attempt number
 * @returns Delay in milliseconds
 */
export function getFacebookBusinessRetryDelay(error: any, attempt: number): number {
  const errorCode = handleFacebookBusinessApiError(error);
  
  if (errorCode === FACEBOOK_BUSINESS_ERROR_CODES.FACEBOOK_BUSINESS_RATE_LIMIT) {
    // For rate limit errors, use exponential backoff with longer delays
    return Math.min(1000 * Math.pow(2, attempt), 60000); // Max 1 minute
  }
  
  // For other retryable errors, use shorter delays
  return Math.min(500 * Math.pow(2, attempt), 10000); // Max 10 seconds
}
