import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProviderFineTuneEnum } from '../constants/provider.enum';
import { Models, ModelDetail } from '../entities';
import { FineTuneJobInfo, StatusUpdateData } from '../interfaces/fine-tune-polling.interface';

/**
 * Repository để xử lý các truy vấn database cho fine-tune polling
 */
@Injectable()
export class FineTunePollingRepository {
  private readonly logger = new Logger(FineTunePollingRepository.name);

  constructor(
    @InjectRepository(Models)
    private readonly modelsRepository: Repository<Models>,

    @InjectRepository(ModelDetail)
    private readonly modelDetailRepository: Repository<ModelDetail>,
  ) {}

  /**
   * Lấy thông tin fine-tune job từ database
   * Sử dụng cấu trúc mới với Models và ModelDetail
   */
  async getFineTuneJobInfo(
    userId: string,
    modelFineTuneId: string,
    provider: ProviderFineTuneEnum,
  ): Promise<FineTuneJobInfo | null> {
    try {
      this.logger.debug('Getting fine-tune job info', {
        userId,
        modelFineTuneId,
        provider,
      });

      // Lấy thông tin model
      const whereCondition: any = {
        id: modelFineTuneId,
      };

      if (userId) {
        whereCondition.userId = parseInt(userId);
      }

      const model = await this.modelsRepository.findOne({
        where: whereCondition,
      });

      if (!model) {
        this.logger.warn('Model not found', {
          userId,
          modelFineTuneId,
        });
        return null;
      }

      if (!model.detailId) {
        this.logger.warn('Model detail ID not found', {
          modelFineTuneId,
        });
        return null;
      }

      // Lấy model detail để có metadata
      const modelDetail = await this.modelDetailRepository.findOne({
        where: {
          id: model.detailId,
        },
      });

      if (!modelDetail || !modelDetail.metadata) {
        this.logger.warn('Model detail or metadata not found', {
          modelFineTuneId,
          detailId: model.detailId,
        });
        return null;
      }

      const metadata = modelDetail.metadata;
      const jobId = metadata.jobId;

      // Lấy encrypted API key và public key cho KeyPairEncryptionService
      const encryptedApiKey = metadata.openaiEncryptedApiKey || metadata.googleEncryptedApiKey || metadata.encryptedApiKey;
      const publicKey = metadata.openaiPublicKey || metadata.googlePublicKey || metadata.publicKey;

      if (!jobId) {
        this.logger.warn('Job ID not found in metadata', {
          modelFineTuneId,
          provider,
        });
        return null;
      }

      if (!encryptedApiKey || !publicKey) {
        this.logger.warn('Encrypted API key or public key not found in metadata', {
          modelFineTuneId,
          provider,
          hasEncryptedApiKey: !!encryptedApiKey,
          hasPublicKey: !!publicKey,
        });
        return null;
      }

      const jobInfo: FineTuneJobInfo = {
        jobId,
        modelFineTuneId,
        userId,
        provider,
        encryptedApiKey,
        publicKey,
        currentStatus: metadata.status || 'unknown',
        systemModelId: model.userId ? undefined : model.id,
        userModelId: model.userId ? model.id : undefined,
        metadata,
      };

      this.logger.debug('Fine-tune job info retrieved successfully', {
        jobId: jobInfo.jobId,
        modelFineTuneId,
        hasApiKey: !!encryptedApiKey,
        currentStatus: jobInfo.currentStatus,
      });

      return jobInfo;
    } catch (error) {
      this.logger.error('Failed to get fine-tune job info', {
        error: error.message,
        userId,
        modelFineTuneId,
        provider,
      });
      return null;
    }
  }

  /**
   * Cập nhật trạng thái fine-tune job
   * Sử dụng cấu trúc mới với Models và ModelDetail
   */
  async updateFineTuneStatus(updateData: StatusUpdateData): Promise<boolean> {
    try {
      this.logger.debug('Updating fine-tune status', updateData);

      // Cập nhật model nếu có model_id từ provider
      if (updateData.modelId) {
        await this.modelsRepository.update(
          { id: updateData.modelFineTuneId },
          {
            modelId: updateData.modelId,
            active: updateData.isSuccess,
          },
        );
      }

      // Cập nhật metadata trong model detail
      const model = await this.modelsRepository.findOne({
        where: { id: updateData.modelFineTuneId },
      });

      if (model?.detailId) {
        const modelDetail = await this.modelDetailRepository.findOne({
          where: { id: model.detailId },
        });

        if (modelDetail) {
          const updatedMetadata = {
            ...modelDetail.metadata,
            status: updateData.status,
            isSuccess: updateData.isSuccess,
            updatedAt: Date.now(),
          } as any;

          if (updateData.modelId) {
            updatedMetadata.fineTunedModelId = updateData.modelId;
          }

          if (updateData.error) {
            updatedMetadata.error = updateData.error;
          }

          if (updateData.metadata) {
            Object.assign(updatedMetadata, updateData.metadata);
          }

          await this.modelDetailRepository.update(
            model.detailId,
            { metadata: updatedMetadata } as any,
          );
        }
      }

      this.logger.log('Fine-tune status updated successfully', {
        modelFineTuneId: updateData.modelFineTuneId,
        status: updateData.status,
        isSuccess: updateData.isSuccess,
        modelId: updateData.modelId,
      });

      return true;
    } catch (error) {
      this.logger.error('Failed to update fine-tune status', {
        error: error.message,
        updateData,
      });
      return false;
    }
  }

  /**
   * Cập nhật trạng thái trong model detail metadata
   * Thay thế cho updateFineTuneHistoryStatus
   */
  async updateFineTuneHistoryStatus(
    modelFineTuneId: string,
    provider: ProviderFineTuneEnum,
    status: string,
    error?: string,
  ): Promise<boolean> {
    try {
      this.logger.debug('Updating fine-tune history status', {
        modelFineTuneId,
        provider,
        status,
      });

      // Cập nhật metadata trong model detail
      const model = await this.modelsRepository.findOne({
        where: { id: modelFineTuneId },
      });

      if (model?.detailId) {
        const modelDetail = await this.modelDetailRepository.findOne({
          where: { id: model.detailId },
        });

        if (modelDetail) {
          const updatedMetadata: any = {
            ...modelDetail.metadata,
            status,
            lastPolledAt: Date.now(),
          };

          if (error) {
            updatedMetadata.error = error;
          }

          await this.modelDetailRepository.update(
            model.detailId,
            { metadata: updatedMetadata },
          );
        }
      }

      this.logger.debug('Fine-tune history status updated successfully');
      return true;
    } catch (error) {
      this.logger.error('Failed to update fine-tune history status', {
        error: error.message,
        modelFineTuneId,
        provider,
        status,
      });
      return false;
    }
  }

  /**
   * Kiểm tra xem fine-tune job có đang được polling không
   * Sử dụng cấu trúc mới với Models và ModelDetail
   */
  async isJobBeingPolled(
    modelFineTuneId: string,
    provider: ProviderFineTuneEnum,
  ): Promise<boolean> {
    try {
      const model = await this.modelsRepository.findOne({
        where: {
          id: modelFineTuneId,
        },
      });

      if (!model || !model.detailId) {
        return false;
      }

      // Lấy metadata từ model detail
      const modelDetail = await this.modelDetailRepository.findOne({
        where: { id: model.detailId },
      });

      if (!modelDetail?.metadata) {
        return false;
      }

      const metadata = modelDetail.metadata;
      const currentStatus = metadata.status?.toLowerCase();

      // Nếu đã hoàn thành hoặc thất bại, không cần polling
      if (
        currentStatus === 'succeeded' ||
        currentStatus === 'failed' ||
        currentStatus === 'cancelled' ||
        model.active === true // Model đã active nghĩa là đã hoàn thành
      ) {
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error('Failed to check if job is being polled', {
        error: error.message,
        modelFineTuneId,
        provider,
      });
      return false;
    }
  }

  /**
   * Lấy danh sách các job đang pending để polling
   * Sử dụng cấu trúc mới với Models và ModelDetail
   */
  async getPendingJobs(provider?: ProviderFineTuneEnum): Promise<FineTuneJobInfo[]> {
    try {
      this.logger.debug('Getting pending jobs for polling', { provider });

      // Lấy các models đang fine-tune (chưa active)
      const models = await this.modelsRepository.find({
        where: {
          isFineTune: true,
          active: false,
        },
      });

      const jobInfos: FineTuneJobInfo[] = [];

      for (const model of models) {
        if (!model.detailId) continue;

        // Lấy metadata từ model detail
        const modelDetail = await this.modelDetailRepository.findOne({
          where: { id: model.detailId },
        });

        if (!modelDetail?.metadata) continue;

        const metadata = modelDetail.metadata;
        const jobId = metadata.jobId;
        const encryptedApiKey = metadata.openaiEncryptedApiKey || metadata.googleEncryptedApiKey || metadata.encryptedApiKey;
        const publicKey = metadata.openaiPublicKey || metadata.googlePublicKey || metadata.publicKey;
        const currentStatus = metadata.status;

        // Kiểm tra xem có cần polling không
        if (
          !jobId ||
          !encryptedApiKey ||
          !publicKey ||
          currentStatus === 'succeeded' ||
          currentStatus === 'failed' ||
          currentStatus === 'cancelled'
        ) {
          continue;
        }

        // Xác định provider từ metadata nếu không được cung cấp
        const jobProvider = provider || metadata.provider || ProviderFineTuneEnum.OPENAI;

        jobInfos.push({
          jobId,
          modelFineTuneId: model.id,
          userId: model.userId?.toString() || '',
          provider: jobProvider,
          encryptedApiKey,
          publicKey,
          currentStatus,
          systemModelId: model.userId ? undefined : model.id,
          userModelId: model.userId ? model.id : undefined,
          metadata,
        });
      }

      this.logger.debug(`Found ${jobInfos.length} pending jobs`);
      return jobInfos;
    } catch (error) {
      this.logger.error('Failed to get pending jobs', {
        error: error.message,
        provider,
      });
      return [];
    }
  }
}
