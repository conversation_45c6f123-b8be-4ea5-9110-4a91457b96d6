import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { ThreadStatusEnum } from '../enums/thread-status.enum';

/**
 * Zalo Conversation Thread entity
 * Tracks conversation threads between users and customers
 * Uses UUID primary key with computed thread ID for LangGraph
 */
@Entity('zalo_conversation_threads')
export class ZaloConversationThread {
  /**
   * Primary key - DB-generated UUID
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * User ID who owns this thread (for isolation)
   */
  @Column({ name: 'user_id', type: 'int', nullable: false })
  userId: number;

  /**
   * Thread status
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: ThreadStatusEnum,
    default: ThreadStatusEnum.ACTIVE,
  })
  status: ThreadStatusEnum;

  /**
   * First message timestamp
   */
  @Column({
    name: 'first_message_at',
    type: 'bigint',
    nullable: false,
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  firstMessageAt: number;

  /**
   * Last message timestamp
   */
  @Column({
    name: 'last_message_at',
    type: 'bigint',
    nullable: false,
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  lastMessageAt: number;

  /**
   * Total message count
   */
  @Column({ name: 'message_count', type: 'int', default: 0 })
  messageCount: number;

  /**
   * AI response count
   */
  @Column({ name: 'ai_response_count', type: 'int', default: 0 })
  aiResponseCount: number;

  /**
   * Additional metadata
   */
  @Column({ name: 'metadata', type: 'jsonb', default: '{}' })
  metadata: any;

  /**
   * Creation timestamp
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;

  /**
   * Zalo customer ID reference
   */
  @Column({ name: 'zalo_customer_id', type: 'uuid', nullable: false })
  zaloCustomerId: string;

  /**
   * Generate thread ID for LangGraph
   * Format: "zalo:userId:uuid"
   */
  getThreadId(): string {
    return `zalo:${this.userId}:${this.id}`;
  }

  /**
   * Static method to generate thread ID for LangGraph
   * Format: "zalo:userId:uuid"
   */
  static generateThreadId(userId: number, uuid: string): string {
    return `zalo:${userId}:${uuid}`;
  }
}
