import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Zalo media entity - represents images uploaded by Zalo customers
 */
@Entity('zalo_media')
export class ZaloMedia {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'zalo_media_id', type: 'varchar', length: 255 })
  zaloMediaId: string;

  @Column({ name: 'file_name', type: 'varchar', length: 500, nullable: true })
  fileName: string | null;

  @Column({ name: 'file_size', type: 'bigint', nullable: true })
  fileSize: number | null;

  @Column({ name: 'mime_type', type: 'varchar', length: 100 })
  mimeType: string;

  @Column({ name: 's3_key', type: 'varchar', length: 1000 })
  s3Key: string;

  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  @Column({ name: 'tags', type: 'jsonb', default: '[]' })
  tags: string[];

  @Column({ name: 'metadata', type: 'jsonb', default: '{}' })
  metadata: Record<string, any>;

  @Column({ name: 'zalo_customer_id', type: 'uuid' })
  zaloCustomerId: string;

  @Column({ name: 'zalo_oa_id', type: 'integer' })
  zaloOaId: number;

  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;
}
