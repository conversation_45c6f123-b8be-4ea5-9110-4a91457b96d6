import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { ScheduleModule } from '@nestjs/schedule';
import { QueueName } from '../../../queue';
import { ZaloZnsProcessor } from './zalo-zns.processor';
import { ZaloTokenRefresherService } from './zalo-token-refresher.service';
import { ZaloModule } from '../../../shared/services/zalo/zalo.module';

/**
 * Module cho Zalo ZNS Worker
 * Xử lý các job gửi tin nhắn Zalo ZNS
 */
@Module({
  imports: [
    ConfigModule,
    BullModule.registerQueue({
      name: QueueName.ZALO_ZNS,
    }),
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    ScheduleModule.forRoot(),
    ZaloModule,
  ],
  providers: [ZaloZnsProcessor, ZaloTokenRefresherService],
  exports: [ZaloZnsProcessor, ZaloTokenRefresherService],
})
export class ZaloZnsModule {}
