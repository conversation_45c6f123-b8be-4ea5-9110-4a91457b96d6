import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdminEmailCampaign } from '../entities/admin-email-campaign.entity';
import { AdminEmailCampaignStatus } from '../types/admin-email-campaign.types';

/**
 * Repository cho AdminEmailCampaign entity trong worker
 * Chỉ cần các method cơ bản để tracking và update status
 */
@Injectable()
export class AdminEmailCampaignRepository {
  constructor(
    @InjectRepository(AdminEmailCampaign)
    private readonly repository: Repository<AdminEmailCampaign>,
  ) {}

  /**
   * Tìm campaign theo ID
   */
  async findById(id: number): Promise<AdminEmailCampaign | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Cập nhật status của campaign
   */
  async updateStatus(id: number, status: AdminEmailCampaignStatus, updatedBy: number): Promise<void> {
    await this.repository.update(id, {
      status,
      updatedBy,
      updatedAt: Math.floor(Date.now() / 1000)
    });
  }

  /**
   * Cập nhật thời gian bắt đầu gửi
   */
  async updateStartedAt(id: number, startedAt: number, updatedBy: number): Promise<void> {
    await this.repository.update(id, {
      startedAt,
      status: AdminEmailCampaignStatus.SENDING,
      updatedBy,
      updatedAt: Math.floor(Date.now() / 1000)
    });
  }

  /**
   * Cập nhật thời gian hoàn thành
   */
  async updateCompletedAt(id: number, completedAt: number, updatedBy: number): Promise<void> {
    await this.repository.update(id, {
      completedAt,
      status: AdminEmailCampaignStatus.COMPLETED,
      updatedBy,
      updatedAt: Math.floor(Date.now() / 1000)
    });
  }

  /**
   * Cập nhật campaign thành failed
   */
  async markAsFailed(id: number, updatedBy: number): Promise<void> {
    await this.repository.update(id, {
      status: AdminEmailCampaignStatus.FAILED,
      updatedBy,
      updatedAt: Math.floor(Date.now() / 1000)
    });
  }

  /**
   * Tìm campaigns theo status
   */
  async findByStatus(status: AdminEmailCampaignStatus): Promise<AdminEmailCampaign[]> {
    return this.repository.find({ where: { status } });
  }

  /**
   * Đếm campaigns theo status
   */
  async countByStatus(status: AdminEmailCampaignStatus): Promise<number> {
    return this.repository.count({ where: { status } });
  }
}
