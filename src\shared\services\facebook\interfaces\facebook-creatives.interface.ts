/**
 * Interface cho Ad Creative
 */
export interface AdCreative {
  id: string;
  name: string;
  object_story_spec?: ObjectStorySpec;
  image_hash?: string;
  image_url?: string;
  video_id?: string;
  body?: string;
  title?: string;
  link_url?: string;
  call_to_action?: CallToAction;
  object_type?: string;
  status?: string;
  thumbnail_url?: string;
  created_time?: string;
  updated_time?: string;
  account_id?: string;
  actor_id?: string;
  adlabels?: AdLabel[];
  applink_treatment?: string;
  asset_feed_spec?: AssetFeedSpec;
  authorization_category?: string;
  auto_update?: boolean;
  branded_content_sponsor_page_id?: string;
  bundle_folder_id?: string;
  categorization_criteria?: string;
  category_media_source?: string;
  destination_set_id?: string;
  dynamic_ad_voice?: string;
  effective_authorization_category?: string;
  effective_instagram_media_id?: string;
  effective_instagram_story_id?: string;
  effective_object_story_id?: string;
  enable_direct_install?: boolean;
  enable_launch_instant_app?: boolean;
  image_crops?: ImageCrops;
  image_file?: string;
  instagram_actor_id?: string;
  instagram_permalink_url?: string;
  instagram_story_id?: string;
  interactive_components_spec?: InteractiveComponentsSpec;
  link_deep_link_url?: string;
  link_destination_display_url?: string;
  link_og_id?: string;
  messenger_sponsored_message?: string;
  object_id?: string;
  object_story_id?: string;
  object_url?: string;
  place_page_set_id?: string;
  platform_customizations?: PlatformCustomizations;
  playable_asset_id?: string;
  portrait_customizations?: PortraitCustomizations;
  product_set_id?: string;
  recommender_settings?: RecommenderSettings;
  source_instagram_media_id?: string;
  template_url?: string;
  template_url_spec?: TemplateUrlSpec;
  url_tags?: string;
  use_page_actor_override?: boolean;
  video_data?: VideoData;
}

/**
 * Interface cho Object Story Spec
 */
export interface ObjectStorySpec {
  page_id: string;
  link_data?: LinkData;
  photo_data?: PhotoData;
  video_data?: VideoData;
  text_data?: TextData;
  template_data?: TemplateData;
  instagram_actor_id?: string;
}

/**
 * Interface cho Link Data
 */
export interface LinkData {
  link: string;
  message?: string;
  name?: string;
  description?: string;
  caption?: string;
  picture?: string;
  call_to_action?: CallToAction;
  child_attachments?: ChildAttachment[];
  multi_share_optimized?: boolean;
  multi_share_end_card?: boolean;
  format_option?: string;
  image_overlay_spec?: ImageOverlaySpec;
}

/**
 * Interface cho Photo Data
 */
export interface PhotoData {
  image_hash?: string;
  url?: string;
  caption?: string;
  branded_content_shared_to_sponsor_status?: string;
  branded_content_sponsor_page_id?: string;
  branded_content_sponsor_relationship?: string;
}

/**
 * Interface cho Video Data
 */
export interface VideoData {
  video_id?: string;
  image_hash?: string;
  image_url?: string;
  message?: string;
  title?: string;
  call_to_action?: CallToAction;
  branded_content_shared_to_sponsor_status?: string;
  branded_content_sponsor_page_id?: string;
  branded_content_sponsor_relationship?: string;
}

/**
 * Interface cho Text Data
 */
export interface TextData {
  message: string;
  link?: string;
}

/**
 * Interface cho Template Data
 */
export interface TemplateData {
  name: string;
  format_option?: string;
  call_to_action?: CallToAction;
  message?: string;
  link?: string;
  description?: string;
  retailer_item_ids?: string[];
  customization_rules_spec?: CustomizationRulesSpec[];
}

/**
 * Interface cho Call To Action
 */
export interface CallToAction {
  type: 'OPEN_LINK' | 'LIKE_PAGE' | 'SHOP_NOW' | 'PLAY_GAME' | 'INSTALL_APP' | 'USE_APP' | 'CALL' | 'CALL_ME' | 'INSTALL_MOBILE_APP' | 'USE_MOBILE_APP' | 'MOBILE_DOWNLOAD' | 'BOOK_TRAVEL' | 'LISTEN_MUSIC' | 'WATCH_VIDEO' | 'LEARN_MORE' | 'SIGN_UP' | 'DOWNLOAD' | 'WATCH_MORE' | 'NO_BUTTON' | 'VISIT_PAGES_FEED' | 'APPLY_NOW' | 'BUY_NOW' | 'GET_OFFER' | 'GET_OFFER_VIEW' | 'BUY_TICKETS' | 'UPDATE_APP' | 'GET_DIRECTIONS' | 'BUY' | 'MESSAGE_PAGE' | 'SUBSCRIBE' | 'SELL_NOW' | 'DONATE_NOW' | 'GET_QUOTE' | 'CONTACT_US' | 'START_ORDER' | 'CONTINUE_SHOPPING' | 'ORDER_NOW';
  value?: CallToActionValue;
}

/**
 * Interface cho Call To Action Value
 */
export interface CallToActionValue {
  link?: string;
  link_caption?: string;
  link_description?: string;
  link_format?: string;
  link_title?: string;
  page?: string;
  product_link?: string;
  lead_gen_form_id?: string;
  app_destination?: string;
  app_link?: string;
  application?: string;
  event_id?: string;
  offer_id?: string;
  page_welcome_message?: string;
}

/**
 * Interface cho Child Attachment
 */
export interface ChildAttachment {
  link: string;
  name?: string;
  description?: string;
  image_hash?: string;
  picture?: string;
  place_data?: PlaceData;
  static_card?: boolean;
  video_id?: string;
  call_to_action?: CallToAction;
}

/**
 * Interface cho Place Data
 */
export interface PlaceData {
  page_id?: string;
  address?: Address;
  latitude?: number;
  longitude?: number;
}

/**
 * Interface cho Address
 */
export interface Address {
  street1?: string;
  street2?: string;
  city?: string;
  region?: string;
  country?: string;
  postal_code?: string;
}

/**
 * Interface cho Image Overlay Spec
 */
export interface ImageOverlaySpec {
  overlay_template?: string;
  position?: string;
  text_font?: string;
  text_template_tags?: string[];
  text_type?: string;
  theme_color?: string;
  float_with_margin?: boolean;
  overlay_target?: string;
}

/**
 * Interface cho Ad Label
 */
export interface AdLabel {
  id: string;
  name: string;
  created_time?: string;
  updated_time?: string;
}

/**
 * Interface cho Asset Feed Spec
 */
export interface AssetFeedSpec {
  videos?: AssetFeedSpecVideo[];
  images?: AssetFeedSpecImage[];
  bodies?: AssetFeedSpecBody[];
  titles?: AssetFeedSpecTitle[];
  descriptions?: AssetFeedSpecDescription[];
  ad_formats?: string[];
  call_to_action_types?: string[];
  link_urls?: AssetFeedSpecLinkUrl[];
  groups?: AssetFeedSpecGroup[];
}

/**
 * Interface cho Asset Feed Spec Video
 */
export interface AssetFeedSpecVideo {
  video_id: string;
  thumbnail_hash?: string;
  url_tags?: string;
  adlabels?: AdLabel[];
}

/**
 * Interface cho Asset Feed Spec Image
 */
export interface AssetFeedSpecImage {
  hash: string;
  url_tags?: string;
  adlabels?: AdLabel[];
}

/**
 * Interface cho Asset Feed Spec Body
 */
export interface AssetFeedSpecBody {
  text: string;
  url_tags?: string;
  adlabels?: AdLabel[];
}

/**
 * Interface cho Asset Feed Spec Title
 */
export interface AssetFeedSpecTitle {
  text: string;
  url_tags?: string;
  adlabels?: AdLabel[];
}

/**
 * Interface cho Asset Feed Spec Description
 */
export interface AssetFeedSpecDescription {
  text: string;
  url_tags?: string;
  adlabels?: AdLabel[];
}

/**
 * Interface cho Asset Feed Spec Link URL
 */
export interface AssetFeedSpecLinkUrl {
  website_url: string;
  url_tags?: string;
  adlabels?: AdLabel[];
}

/**
 * Interface cho Asset Feed Spec Group
 */
export interface AssetFeedSpecGroup {
  rule: AssetFeedSpecGroupRule;
}

/**
 * Interface cho Asset Feed Spec Group Rule
 */
export interface AssetFeedSpecGroupRule {
  body_label?: AdLabel;
  title_label?: AdLabel;
  description_label?: AdLabel;
  image_label?: AdLabel;
  video_label?: AdLabel;
  link_url_label?: AdLabel;
}

/**
 * Interface cho Image Crops
 */
export interface ImageCrops {
  '100x100'?: number[][];
  '100x72'?: number[][];
  '191x100'?: number[][];
  '400x150'?: number[][];
  '400x500'?: number[][];
  '600x360'?: number[][];
  '90x160'?: number[][];
}

/**
 * Interface cho Interactive Components Spec
 */
export interface InteractiveComponentsSpec {
  components?: InteractiveComponent[];
}

/**
 * Interface cho Interactive Component
 */
export interface InteractiveComponent {
  component_type: string;
  child_components?: InteractiveComponent[];
  action?: InteractiveComponentAction;
  style?: InteractiveComponentStyle;
  text?: string;
  tracking_spec?: InteractiveComponentTrackingSpec;
}

/**
 * Interface cho Interactive Component Action
 */
export interface InteractiveComponentAction {
  action_type: string;
  action_data?: Record<string, any>;
}

/**
 * Interface cho Interactive Component Style
 */
export interface InteractiveComponentStyle {
  background_color?: string;
  border_color?: string;
  border_radius?: number;
  border_width?: number;
  font_color?: string;
  font_size?: number;
  font_weight?: string;
  height?: number;
  width?: number;
  margin?: number[];
  padding?: number[];
}

/**
 * Interface cho Interactive Component Tracking Spec
 */
export interface InteractiveComponentTrackingSpec {
  tracking_pixels?: string[];
  custom_events?: CustomEvent[];
}

/**
 * Interface cho Custom Event
 */
export interface CustomEvent {
  event_name: string;
  event_data?: Record<string, any>;
}

/**
 * Interface cho Platform Customizations
 */
export interface PlatformCustomizations {
  instagram?: InstagramCustomization;
  facebook?: FacebookCustomization;
}

/**
 * Interface cho Instagram Customization
 */
export interface InstagramCustomization {
  image_hash?: string;
  video_id?: string;
}

/**
 * Interface cho Facebook Customization
 */
export interface FacebookCustomization {
  image_hash?: string;
  video_id?: string;
}

/**
 * Interface cho Portrait Customizations
 */
export interface PortraitCustomizations {
  aspect_ratio?: string;
  image_hash?: string;
  video_id?: string;
}

/**
 * Interface cho Recommender Settings
 */
export interface RecommenderSettings {
  priority?: number;
  goals?: string[];
}

/**
 * Interface cho Template URL Spec
 */
export interface TemplateUrlSpec {
  web?: TemplateUrlSpecPlatform;
  android?: TemplateUrlSpecPlatform;
  ios?: TemplateUrlSpecPlatform;
  ipad?: TemplateUrlSpecPlatform;
  iphone?: TemplateUrlSpecPlatform;
  windows_phone?: TemplateUrlSpecPlatform;
}

/**
 * Interface cho Template URL Spec Platform
 */
export interface TemplateUrlSpecPlatform {
  url?: string;
  config?: Record<string, any>;
}

/**
 * Interface cho Customization Rules Spec
 */
export interface CustomizationRulesSpec {
  customization_spec?: Record<string, any>;
  priority?: number;
}

/**
 * Interface cho yêu cầu tạo Ad Creative
 */
export interface CreateAdCreativeRequest {
  name: string;
  object_story_spec?: ObjectStorySpec;
  image_hash?: string;
  image_url?: string;
  video_id?: string;
  body?: string;
  title?: string;
  link_url?: string;
  call_to_action?: CallToAction;
  object_type?: string;
  adlabels?: string[];
  asset_feed_spec?: AssetFeedSpec;
  authorization_category?: string;
  auto_update?: boolean;
  branded_content_sponsor_page_id?: string;
  bundle_folder_id?: string;
  categorization_criteria?: string;
  category_media_source?: string;
  destination_set_id?: string;
  dynamic_ad_voice?: string;
  enable_direct_install?: boolean;
  enable_launch_instant_app?: boolean;
  image_crops?: ImageCrops;
  image_file?: string;
  instagram_actor_id?: string;
  instagram_story_id?: string;
  interactive_components_spec?: InteractiveComponentsSpec;
  link_deep_link_url?: string;
  link_destination_display_url?: string;
  link_og_id?: string;
  messenger_sponsored_message?: string;
  object_id?: string;
  object_story_id?: string;
  object_url?: string;
  place_page_set_id?: string;
  platform_customizations?: PlatformCustomizations;
  playable_asset_id?: string;
  portrait_customizations?: PortraitCustomizations;
  product_set_id?: string;
  recommender_settings?: RecommenderSettings;
  source_instagram_media_id?: string;
  template_url?: string;
  template_url_spec?: TemplateUrlSpec;
  url_tags?: string;
  use_page_actor_override?: boolean;
  video_data?: VideoData;
}

/**
 * Interface cho yêu cầu cập nhật Ad Creative
 */
export interface UpdateAdCreativeRequest {
  name?: string;
  adlabels?: string[];
  status?: string;
}

/**
 * Interface cho phản hồi danh sách Ad Creatives
 */
export interface GetAdCreativesResponse {
  data: AdCreative[];
  paging?: {
    cursors?: {
      before?: string;
      after?: string;
    };
    next?: string;
    previous?: string;
  };
}

/**
 * Interface cho Creative Preview
 */
export interface CreativePreview {
  body: string;
  ad_format: string;
  creative_id: string;
}

/**
 * Interface cho yêu cầu Creative Preview
 */
export interface GetCreativePreviewRequest {
  ad_format: 'DESKTOP_FEED_STANDARD' | 'MOBILE_FEED_STANDARD' | 'MOBILE_FEED_BASIC' | 'MOBILE_INTERSTITIAL' | 'MOBILE_BANNER' | 'MOBILE_MEDIUM_RECTANGLE' | 'MOBILE_FULLWIDTH' | 'MOBILE_LEADERBOARD' | 'SUGGESTED_VIDEO_MOBILE' | 'SUGGESTED_VIDEO_DESKTOP' | 'INSTAGRAM_STANDARD' | 'INSTAGRAM_STORY' | 'INSTANT_ARTICLE_STANDARD' | 'INSTANT_ARTICLE_RECIRCULATION' | 'IN_STREAM_VIDEO_DESKTOP' | 'IN_STREAM_VIDEO_MOBILE' | 'MARKETPLACE_MOBILE' | 'MESSENGER_MOBILE_INBOX_MEDIA' | 'MESSENGER_MOBILE_STORY_MEDIA' | 'MESSENGER_DESKTOP_INBOX_MEDIA' | 'MESSENGER_MOBILE_INBOX_TEXT' | 'MESSENGER_MOBILE_STORY_TEXT' | 'MESSENGER_DESKTOP_INBOX_TEXT' | 'RIGHT_COLUMN_STANDARD' | 'AUDIENCE_NETWORK_OUTSTREAM_VIDEO' | 'AUDIENCE_NETWORK_INSTREAM_VIDEO' | 'AUDIENCE_NETWORK_BANNER' | 'AUDIENCE_NETWORK_INTERSTITIAL' | 'AUDIENCE_NETWORK_NATIVE' | 'AUDIENCE_NETWORK_REWARDED_VIDEO' | 'WATCH_FEED_MOBILE' | 'WATCH_FEED_DESKTOP';
  product_item_ids?: string[];
  place_page_id?: string;
  post_id?: string;
  start_date?: string;
  end_date?: string;
}

/**
 * Interface cho phản hồi Creative Preview
 */
export interface GetCreativePreviewResponse {
  data: CreativePreview[];
}
