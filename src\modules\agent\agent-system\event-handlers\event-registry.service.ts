import { Injectable, Logger } from '@nestjs/common';
import {
  LangGraphEventHandler,
  EventHandlerRegistry,
} from '../interfaces/event-handler.interface';

/**
 * Registry service for managing LangGraph event handlers
 * Implements the Registry pattern for dynamic handler lookup
 */
@Injectable()
export class LangGraphEventRegistry implements EventHandlerRegistry {
  private readonly logger = new Logger(LangGraphEventRegistry.name);
  private handlers: LangGraphEventHandler[] = [];

  /**
   * Register an event handler
   * @param handler - Handler to register
   */
  register(handler: LangGraphEventHandler): void {
    this.handlers.push(handler);
    this.logger.debug(`Registered event handler: ${handler.constructor.name}`);
  }

  /**
   * Register multiple handlers at once
   * @param handlers - Array of handlers to register
   */
  registerAll(handlers: LangGraphEventHandler[]): void {
    handlers.forEach((handler) => this.register(handler));
    this.logger.log(`Registered ${handlers.length} event handlers`);
  }

  /**
   * Get handler for specific event
   * @param event - Event type
   * @param data - Event data
   * @param tags - Event tags
   * @returns Handler if found, undefined otherwise
   */
  getHandler(
    event: string,
    data: any,
    tags: string[],
  ): LangGraphEventHandler | undefined {
    for (const handler of this.handlers) {
      if (handler.canHandle(event, data, tags)) {
        this.logger.debug(
          `Found handler for event ${event}: ${handler.constructor.name}`,
        );
        return handler;
      }
    }

    this.logger.debug(`No handler found for event: ${event}`, {
      event,
      tags,
      availableHandlers: this.handlers.map((h) => h.constructor.name),
    });

    return undefined;
  }

  /**
   * Get all registered handlers
   * @returns Array of all handlers
   */
  getAllHandlers(): LangGraphEventHandler[] {
    return [...this.handlers]; // Return a copy to prevent external modification
  }

  /**
   * Get count of registered handlers
   * @returns Number of registered handlers
   */
  getHandlerCount(): number {
    return this.handlers.length;
  }

  /**
   * Check if a handler is registered for a specific event type
   * @param event - Event type to check
   * @param data - Event data
   * @param tags - Event tags
   * @returns True if handler exists, false otherwise
   */
  hasHandler(event: string, data: any, tags: string[]): boolean {
    return this.getHandler(event, data, tags) !== undefined;
  }

  /**
   * Clear all registered handlers (useful for testing)
   */
  clear(): void {
    const count = this.handlers.length;
    this.handlers = [];
    this.logger.debug(`Cleared ${count} event handlers`);
  }

  /**
   * Get handler statistics
   * @returns Statistics about registered handlers
   */
  getStatistics(): {
    totalHandlers: number;
    handlerNames: string[];
  } {
    return {
      totalHandlers: this.handlers.length,
      handlerNames: this.handlers.map((h) => h.constructor.name),
    };
  }
}
