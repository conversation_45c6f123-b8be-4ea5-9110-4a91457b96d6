/**
 * DTO cho job admin email campaign trong queue (single email)
 */
export interface AdminEmailCampaignJobDto {
  /**
   * ID của admin email campaign
   */
  campaignId: number;

  /**
   * Thông tin audience nhận email
   */
  audience: {
    name: string;
    email: string;
  };

  /**
   * Email người nhận (đã validate)
   */
  email: string;

  /**
   * Tiêu đề email (đã xử lý template variables)
   */
  subject: string;

  /**
   * Nội dung email (HTML và text)
   */
  content?: {
    html?: string;
    text?: string;
  };

  /**
   * Dữ liệu để thay thế variables trong template
   */
  templateVariables?: Record<string, any>;

  /**
   * Thông tin server gửi email (đã decrypt và validate từ backend)
   */
  serverConfig: {
    host: string;
    port: number;
    secure?: boolean;
    useSsl?: boolean;
    user?: string;
    username?: string;
    password: string;
    from?: string;
    senderEmail?: string;
    senderName?: string;
    replyTo?: string;
  };

  /**
   * ID tracking duy nhất cho email này
   */
  trackingId: string;

  /**
   * Timestamp tạo job
   */
  createdAt: number;
}

/**
 * DTO cho người nhận email trong batch job
 */
export interface AdminEmailRecipientDto {
  /**
   * Tên người nhận
   */
  name: string;

  /**
   * Email người nhận
   */
  email: string;

  /**
   * ID tracking duy nhất cho email này
   */
  trackingId: string;

  /**
   * Dữ liệu tùy chỉnh cho người nhận này (để thay thế variables)
   */
  customData?: Record<string, any>;
}

/**
 * DTO cho batch admin email campaign job (chứa nhiều email)
 */
export interface BatchAdminEmailCampaignJobDto {
  /**
   * ID của admin email campaign
   */
  campaignId: number;

  /**
   * Tiêu đề email
   */
  subject?: string;

  /**
   * Nội dung email (HTML và text)
   */
  content?: {
    html?: string;
    text?: string;
  };

  /**
   * Template variables áp dụng cho tất cả recipients
   */
  templateVariables?: Record<string, any>;

  /**
   * Danh sách người nhận
   */
  recipients: AdminEmailRecipientDto[];

  /**
   * Cấu hình email server (đã decrypt từ backend)
   */
  emailServerConfig?: {
    host: string;
    port: number;
    secure?: boolean;
    useSsl?: boolean;
    user?: string;
    username?: string;
    password: string;
    from?: string;
    senderEmail?: string;
    senderName?: string;
    replyTo?: string;
  };

  /**
   * Timestamp tạo job
   */
  createdAt: number;
}
