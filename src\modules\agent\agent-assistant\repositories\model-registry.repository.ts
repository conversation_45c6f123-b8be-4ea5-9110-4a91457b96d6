import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { ModelRegistry } from '../entities/model-registry.entity';

@Injectable()
export class ModelRegistryRepository {
  constructor(
    @InjectRepository(ModelRegistry)
    private readonly repository: Repository<ModelRegistry>,
  ) {}

  async findById(id: string): Promise<ModelRegistry | null> {
    return this.repository.findOne({ 
      where: { 
        id,
        deletedAt: IsNull()
      }
    });
  }
}
