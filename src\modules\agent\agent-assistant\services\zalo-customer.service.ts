import { Injectable, Logger } from '@nestjs/common';
import { ZaloCustomerRepository } from '../repositories/zalo-customer.repository';
import { UserConvertCustomerRepository } from '../repositories/user-convert-customer.repository';
import { ZaloCustomer, UserConvertCustomer } from '../entities';
import { ZaloCustomerStatus } from '../enums';
import { ZaloUserManagementService } from '../../../../shared/services/zalo/zalo-user-management.service';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { S3Service } from 'src/infra';
import { ZaloUserDetail } from 'src/shared/services/zalo';
import { Platform } from '../../enums/platform.enum';
/**
 * Service for managing Zalo customers
 * Handles customer creation, updates, and avatar processing
 */
@Injectable()
export class ZaloCustomerService {
  private readonly logger = new Logger(ZaloCustomerService.name);

  constructor(
    private readonly zaloCustomerRepository: ZaloCustomerRepository,
    private readonly userConvertCustomerRepository: UserConvertCustomerRepository,
    private readonly zaloUserManagementService: ZaloUserManagementService,
    private readonly httpService: HttpService,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Map or create customer from Zalo user
   * @param userId Internal user ID (OA owner)
   * @param zaloUserId Zalo user ID
   * @param oaId Zalo OA ID
   * @param accessToken Zalo access token
   * @returns Customer record or null if failed
   */
  async mapOrCreateCustomer(
    userId: number,
    zaloUserId: string,
    oaId: string,
    accessToken: string,
  ): Promise<ZaloCustomer | null> {
    try {
      // 1. Check if customer already exists
      let customer = await this.getExistingCustomer(userId, zaloUserId);

      if (customer) {
        // Update last interaction timestamp
        await this.updateLastInteraction(customer.id);
        this.logger.debug(`Found existing customer ${customer.id} for Zalo user ${zaloUserId}`);
        return customer;
      }

      // 2. Fetch profile from Zalo API
      const profile: ZaloUserDetail = await this.zaloUserManagementService.getUserDetail(accessToken, zaloUserId);
      if (!profile) {
        this.logger.error(`Failed to fetch Zalo profile for user ${zaloUserId}`);
        return null;
      }

      // 3. Process avatar if available
      let avatarS3Key: string | null = null;
      if (profile.avatars?.['240']) {
        avatarS3Key = await this.processAvatar(userId, zaloUserId, oaId, profile.avatars['240']);
      }

      // 4. Create UserConvertCustomer first (parent record)
      const convertCustomer = await this.createConvertCustomer(userId, profile, avatarS3Key);

      // 5. Create new ZaloCustomer record with reference to convert customer
      customer = await this.createCustomer(userId, oaId, profile, avatarS3Key, convertCustomer.id);

      this.logger.log(`Created new customer ${customer.id} for Zalo user ${zaloUserId}`);
      return customer;

    } catch (error) {
      this.logger.error(`Failed to map/create Zalo customer for user ${zaloUserId}:`, error);
      throw error;
    }
  }

  /**
   * Get existing customer by user ID and Zalo user ID
   * @param userId Internal user ID
   * @param zaloUserId Zalo user ID
   * @returns Existing customer or null
   */
  private async getExistingCustomer(userId: number, zaloUserId: string): Promise<ZaloCustomer | null> {
    return this.zaloCustomerRepository.getExistingCustomer(userId, zaloUserId);
  }

  /**
   * Create new customer record
   * @param userId Internal user ID
   * @param profile Zalo user profile
   * @param avatarS3Key S3 key for avatar (optional)
   * @returns Created customer record
   */
  private async createCustomer(
    userId: number,
    oaId: string,
    profile: ZaloUserDetail,
    avatarS3Key: string | null,
    convertCustomerId: string
  ): Promise<ZaloCustomer> {
    const now = Date.now();

    const customerData: Partial<ZaloCustomer> = {
      userId,
      oaId: oaId,
      zaloUserId: profile.user_id,
      userIdByApp: profile.user_id_by_app || undefined,
      userExternalId: profile.user_external_id || undefined,
      displayName: profile.display_name || 'Unknown User',
      userAlias: profile.user_alias || undefined,
      isSensitive: profile.is_sensitive || false,
      userIsFollower: profile.user_is_follower || false,
      userLastInteractionDate: profile.user_last_interaction_date || undefined,
      avatar: avatarS3Key || undefined,
      tagsAndNotesInfo: profile.tags_and_notes_info || undefined,
      sharedInfo: profile.shared_info || {},
      dynamicParam: profile.dynamic_param || undefined,
      rawApiResponse: profile,
      customerId: convertCustomerId, // Reference to UserConvertCustomer
      firstInteractionAt: now,
      lastInteractionAt: now,
      interactionCount: 1,
      status: ZaloCustomerStatus.ACTIVE,
      createdAt: now,
      updatedAt: now,
    };

    return this.zaloCustomerRepository.createCustomer(customerData);
  }

  /**
   * Create UserConvertCustomer record (parent record)
   * @param userId Internal user ID
   * @param profile Zalo user profile
   * @param avatarS3Key S3 key for avatar (optional)
   * @returns Created convert customer record
   */
  private async createConvertCustomer(
    userId: number,
    profile: ZaloUserDetail,
    avatarS3Key: string | null
  ): Promise<UserConvertCustomer> {
    const now = Date.now();

    const convertCustomerData: Partial<UserConvertCustomer> = {
      userId,
      name: profile.display_name || 'Zalo User',
      avatar: avatarS3Key || undefined,
      platform: Platform.ZALO,
      timezone: 'Asia/Ho_Chi_Minh',
      metadata: [],
      tags: ['zalo'],
      createdAt: now,
      updatedAt: now,
    };

    return this.userConvertCustomerRepository.createCustomer(convertCustomerData);
  }

  /**
   * Update last interaction timestamp
   * @param customerId Customer ID to update
   */
  private async updateLastInteraction(customerId: string): Promise<void> {
    await this.zaloCustomerRepository.updateLastInteraction(customerId);
  }

  /**
   * Process avatar from Zalo URL and upload to S3
   * @param userId Internal user ID
   * @param zaloUserId Zalo user ID
   * @param oaId Zalo OA ID
   * @param avatarUrl Zalo avatar URL
   * @returns S3 key or null if failed
   */
  private async processAvatar(
    userId: number,
    zaloUserId: string,
    oaId: string,
    avatarUrl: string
  ): Promise<string | null> {
    if (!avatarUrl) return null;

    try {
      // 1. Get response headers first to determine content type
      const headResponse = await firstValueFrom(
        this.httpService.head(avatarUrl)
      );

      // 2. Get content type and determine file extension
      const contentType = headResponse.headers['content-type'] || 'image/jpeg';
      const extension = this.getFileExtensionFromContentType(contentType);

      // 3. Generate S3 key using userId + zaloUserId + oaId combination
      const timestamp = Date.now();
      const s3Key = `zalo-avatars/${userId}/${oaId}/${zaloUserId}-${timestamp}.${extension}`;

      // 4. Stream upload to S3 (no buffering)
      await this.s3Service.uploadFromUrlStreaming(avatarUrl, s3Key);

      this.logger.debug(`Successfully uploaded avatar to S3: ${s3Key}`);
      return s3Key;

    } catch (error) {
      this.logger.warn(`Failed to process avatar from ${avatarUrl}:`, error);
      return null; // Non-critical failure
    }
  }

  /**
   * Get file extension from content type
   * @param contentType MIME content type
   * @returns File extension
   */
  private getFileExtensionFromContentType(contentType: string): string {
    const typeMap: Record<string, string> = {
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/gif': 'gif',
      'image/webp': 'webp',
      'image/bmp': 'bmp',
    };

    return typeMap[contentType.toLowerCase()] || 'jpg'; // Default to jpg
  }
}
