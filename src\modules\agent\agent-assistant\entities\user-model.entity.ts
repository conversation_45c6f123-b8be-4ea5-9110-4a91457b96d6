import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * UserModel entity
 * Stores user-specific model configurations
 */
@Entity('user_models')
export class UserModel {
  /**
   * UUID unique identifier for user model
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Model identifier (unique)
   */
  @Column({ name: 'model_id', type: 'varchar', length: 255, nullable: false, unique: true })
  modelId: string;

  /**
   * Model registry ID reference to model_registry
   */
  @Column({ name: 'model_registry_id', type: 'uuid', nullable: true })
  modelRegistryId?: string;
}
