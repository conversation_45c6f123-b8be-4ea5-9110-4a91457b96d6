# Fine-Tune Monitoring System

Hệ thống monitoring fine-tune progress từ BE app đến Worker app qua Redis Queue.

## 🏗️ Kiến trúc

```
BE App (Main) → Redis Queue → Worker App
     ↓              ↓              ↓
  Tạo job    →   Lưu payload  →  Nhận & xử lý
  Lưu DB     ←   Job queued   ←  Polling mỗi 5 phút
                                      ↓
                              Cập nhật model_id khi thành công
```

## 📋 Luồng xử lý

### 1. BE App tạo fine-tune job

```typescript
// 7. Tạo model mới trong database
const newModel = this.modelsRepository.create({
  modelId: jobId, // Tạm thời sử dụng jobId, sẽ được cập nhật khi job hoàn thành
  modelRegistryId: model.registry_id,
  active: false, // Chưa active cho đến khi fine-tuning hoàn thành
  userId,
  isFineTune: true
});

const savedModel = await this.modelsRepository.save(newModel);

// 8. Tạo model detail để lưu metadata từ provider
const modelDetail = this.modelDetailRepository.create({
  metadata: {
    jobId,
    provider,
    status: providerJobData.status || 'running',
    datasetId,
    baseModelId: modelId,
    hyperparameters: costEstimate.hyperparameters,
    costDeducted: costEstimate.estimatedCost,
    createdAt: Date.now(),
    providerJobData
  }
});

const savedModelDetail = await this.modelDetailRepository.save(modelDetail);

// Cập nhật model với detailId
await this.modelsRepository.update(savedModel.id, {
  detailId: savedModelDetail.id
});
```

### 2. BE App thêm monitoring job vào queue

```typescript
// 10. Tạo Redis job để monitor fine-tuning progress
const monitoringJobData = {
  historyId: savedModel.id, // Sử dụng model ID làm history ID
  providerJobId: jobId,
  provider: this.mapToFineTuneProvider(provider),
  userId
};

// Thêm monitoring job vào Redis queue
await this.fineTuneQueueService.addMonitoringJob(monitoringJobData, {
  delay: 30000, // Delay 30 giây trước khi bắt đầu monitor
  attempts: 10, // Nhiều lần thử lại cho monitoring
  backoff: {
    type: 'exponential',
    delay: 5000
  }
});
```

### 3. Worker App xử lý monitoring job

Worker app sẽ:
1. Nhận job từ Redis queue
2. Lấy thông tin model từ database
3. Gọi API provider để check status mỗi 5 phút
4. Cập nhật database dựa trên kết quả
5. Dừng polling khi nhận được trạng thái cuối cùng

## 🔧 Cấu hình

### Job Data Structure

```typescript
interface MonitoringJobData {
  historyId: string;        // ID của model trong bảng models
  providerJobId: string;    // ID của job từ provider
  provider: ProviderFineTuneEnum; // OPENAI hoặc GOOGLE
  userId?: number;          // ID người dùng (nullable cho system models)
  timestamp?: number;       // Thời gian tạo job
}
```

### Database Schema

**Bảng `models`:**
- `id`: UUID (historyId)
- `model_id`: String (sẽ được cập nhật khi thành công)
- `model_registry_id`: UUID
- `detail_id`: UUID (liên kết đến model_detail)
- `active`: Boolean (false cho đến khi hoàn thành)
- `user_id`: Integer (nullable)
- `is_fine_tune`: Boolean

**Bảng `model_detail`:**
- `id`: UUID
- `metadata`: JSONB chứa thông tin từ provider

## 🚀 Sử dụng

### Trong BE App

```typescript
import { FineTuneQueueService } from './modules/fine-tune/services';

// Inject service
constructor(
  private readonly fineTuneQueueService: FineTuneQueueService,
) {}

// Thêm monitoring job
await this.fineTuneQueueService.addMonitoringJob({
  historyId: modelId,
  providerJobId: jobId,
  provider: ProviderFineTuneEnum.OPENAI,
  userId: userId,
});
```

### Kiểm tra trạng thái

```typescript
// Kiểm tra job trong queue
const jobInfo = await this.fineTuneQueueService.getJob(jobId);

// Kiểm tra có job monitoring nào đang chạy không
const hasActiveJob = await this.fineTuneQueueService.hasActiveMonitoringJob(historyId);

// Lấy thống kê queue
const stats = await this.fineTuneQueueService.getQueueStats();
```

## 📊 Monitoring

### Status Flow

1. **running** → Đang fine-tune, tiếp tục polling
2. **succeeded** → Thành công, cập nhật `model_id` và `active = true`
3. **failed** → Thất bại, cập nhật metadata với error
4. **cancelled** → Đã hủy, cập nhật metadata
5. **timeout** → Quá thời gian (24h), đánh dấu failed

### Logging

Tất cả hoạt động được log với các level:
- `LOG`: Thông tin quan trọng
- `DEBUG`: Chi tiết quá trình
- `WARN`: Cảnh báo
- `ERROR`: Lỗi cần xử lý

### Queue Management

- **Polling interval**: 5 phút
- **Max attempts**: 288 (24 giờ)
- **Cleanup**: Tự động dọn dẹp job cũ
- **Retry**: Exponential backoff

## 🔍 Troubleshooting

### Job không được xử lý

1. Kiểm tra Redis connection
2. Kiểm tra queue registration trong module
3. Kiểm tra processor được import đúng

### API key không hoạt động

1. Kiểm tra encryption/decryption
2. Kiểm tra metadata trong model_detail
3. Kiểm tra provider configuration

### Database không cập nhật

1. Kiểm tra entity mapping
2. Kiểm tra repository methods
3. Kiểm tra transaction handling

## 📝 Logs Example

```
[FineTuneMonitoringProcessor] Processing fine-tune monitoring job {
  jobId: "123",
  historyId: "uuid-123",
  providerJobId: "ftjob-abc",
  provider: "OPENAI",
  attempt: 1
}

[FineTuneMonitoringService] Starting job monitoring {
  historyId: "uuid-123",
  providerJobId: "ftjob-abc",
  provider: "OPENAI"
}

[OpenAIFineTuneService] Getting OpenAI fine-tune job status for job: ftjob-abc
[OpenAIFineTuneService] OpenAI job status: running

[ModelsMonitoringRepository] Updated polling status for model uuid-123: running
[FineTuneMonitoringProcessor] Scheduled next monitoring job {
  historyId: "uuid-123",
  delayMs: 300000
}
```
