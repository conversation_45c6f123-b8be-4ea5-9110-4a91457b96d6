import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';

/**
 * DTO para actualizar un template de email existente
 */
export class UpdateTemplateEmailDto {
  @ApiProperty({
    description: 'Nombre del template',
    example: 'Plantilla de bienvenida actualizada',
    maxLength: 255,
    required: false,
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  name?: string;

  @ApiProperty({
    description: 'Asunto del email',
    example: 'Bienvenido a nuestra plataforma - Actualizado',
    maxLength: 255,
    required: false,
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  subject?: string;

  @ApiProperty({
    description: 'Contenido HTML del email',
    example:
      '<h1>Bienvenido</h1><p>Gracias por registrarte en nuestra plataforma actualizada.</p>',
    required: false,
  })
  @IsString()
  @IsOptional()
  content?: string;

  @ApiProperty({
    description: 'Tags asociados al template',
    example: ['bienvenida', 'registro', 'actualizado'],
    required: false,
  })
  @IsArray()
  @IsOptional()
  tags?: string[];

  @ApiProperty({
    description: 'Placeholders utilizados en el template',
    example: ['userName', 'companyName', 'date', 'orderNumber'],
    required: false,
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  placeholders?: string[];
}
