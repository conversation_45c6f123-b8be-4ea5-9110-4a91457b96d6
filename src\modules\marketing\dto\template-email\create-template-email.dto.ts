import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';

/**
 * DTO para crear un nuevo template de email
 */
export class CreateTemplateEmailDto {
  @ApiProperty({
    description: 'Nombre del template',
    example: 'Plantilla de bienvenida',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Asunto del email',
    example: 'Bienvenido a nuestra plataforma',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  subject: string;

  @ApiProperty({
    description: 'Contenido HTML del email',
    example:
      '<h1>Bienvenido</h1><p>Gracias por registrarte en nuestra plataforma.</p>',
  })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({
    description: 'Tags asociados al template',
    example: ['bienvenida', 'registro'],
    required: false,
  })
  @IsArray()
  @IsOptional()
  tags?: string[];

  @ApiProperty({
    description: 'Placeholders utilizados en el template',
    example: ['userName', 'companyName', 'date'],
    required: false,
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  placeholders?: string[];
}
