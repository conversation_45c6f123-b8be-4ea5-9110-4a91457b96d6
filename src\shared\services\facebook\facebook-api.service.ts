import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

/**
 * Facebook API Service
 * Provides basic Facebook API functionality for workflow executors
 */
@Injectable()
export class FacebookApiService {
  private readonly logger = new Logger(FacebookApiService.name);
  private readonly baseUrl = 'https://graph.facebook.com/v18.0';

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Make authenticated request to Facebook API
   */
  async makeRequest(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    accessToken: string,
    data?: any,
    params?: any,
  ): Promise<any> {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      const config = {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        params: params || {},
      };

      let response;
      switch (method) {
        case 'GET':
          response = await firstValueFrom(this.httpService.get(url, config));
          break;
        case 'POST':
          response = await firstValueFrom(this.httpService.post(url, data, config));
          break;
        case 'PUT':
          response = await firstValueFrom(this.httpService.put(url, data, config));
          break;
        case 'DELETE':
          response = await firstValueFrom(this.httpService.delete(url, config));
          break;
        default:
          throw new Error(`Unsupported HTTP method: ${method}`);
      }

      return response.data;
    } catch (error) {
      this.logger.error(`Facebook API request failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get Facebook Page information
   */
  async getPageInfo(pageId: string, accessToken: string): Promise<any> {
    return this.makeRequest('GET', `/${pageId}`, accessToken, null, {
      fields: 'id,name,about,category,fan_count,followers_count,link,picture,cover',
    });
  }

  /**
   * Post to Facebook Page
   */
  async postToPage(pageId: string, accessToken: string, message: string, link?: string): Promise<any> {
    const data: any = { message };
    if (link) {
      data.link = link;
    }

    return this.makeRequest('POST', `/${pageId}/feed`, accessToken, data);
  }

  /**
   * Get Facebook Page posts
   */
  async getPagePosts(pageId: string, accessToken: string, limit = 25): Promise<any> {
    return this.makeRequest('GET', `/${pageId}/posts`, accessToken, null, {
      fields: 'id,message,created_time,likes.summary(true),comments.summary(true),shares',
      limit,
    });
  }

  /**
   * Get Facebook Ad Campaigns
   */
  async getAdCampaigns(adAccountId: string, accessToken: string): Promise<any> {
    return this.makeRequest('GET', `/act_${adAccountId}/campaigns`, accessToken, null, {
      fields: 'id,name,status,objective,created_time,updated_time,start_time,stop_time,budget_remaining,daily_budget,lifetime_budget',
    });
  }

  /**
   * Create Facebook Ad Campaign
   */
  async createAdCampaign(
    adAccountId: string,
    accessToken: string,
    campaignData: {
      name: string;
      objective: string;
      status?: string;
      daily_budget?: number;
      lifetime_budget?: number;
    },
  ): Promise<any> {
    const data = {
      name: campaignData.name,
      objective: campaignData.objective,
      status: campaignData.status || 'PAUSED',
      ...((campaignData.daily_budget && { daily_budget: campaignData.daily_budget }) || {}),
      ...((campaignData.lifetime_budget && { lifetime_budget: campaignData.lifetime_budget }) || {}),
    };

    return this.makeRequest('POST', `/act_${adAccountId}/campaigns`, accessToken, data);
  }

  /**
   * Get Facebook Ad Sets
   */
  async getAdSets(adAccountId: string, accessToken: string, campaignId?: string): Promise<any> {
    const endpoint = campaignId 
      ? `/${campaignId}/adsets`
      : `/act_${adAccountId}/adsets`;

    return this.makeRequest('GET', endpoint, accessToken, null, {
      fields: 'id,name,status,campaign_id,created_time,updated_time,start_time,end_time,daily_budget,lifetime_budget,bid_amount,optimization_goal,billing_event',
    });
  }

  /**
   * Get Facebook Ads
   */
  async getAds(adAccountId: string, accessToken: string, adSetId?: string): Promise<any> {
    const endpoint = adSetId 
      ? `/${adSetId}/ads`
      : `/act_${adAccountId}/ads`;

    return this.makeRequest('GET', endpoint, accessToken, null, {
      fields: 'id,name,status,adset_id,campaign_id,created_time,updated_time,creative',
    });
  }

  /**
   * Get Facebook Ad Insights
   */
  async getAdInsights(
    adId: string,
    accessToken: string,
    dateRange?: { since: string; until: string },
  ): Promise<any> {
    const params: any = {
      fields: 'impressions,clicks,ctr,cpc,cpm,spend,reach,frequency,actions,cost_per_action_type',
    };

    if (dateRange) {
      params.time_range = JSON.stringify(dateRange);
    }

    return this.makeRequest('GET', `/${adId}/insights`, accessToken, null, params);
  }

  /**
   * Get Facebook Page Insights
   */
  async getPageInsights(
    pageId: string,
    accessToken: string,
    metrics: string[] = ['page_fans', 'page_impressions', 'page_engaged_users'],
    period = 'day',
    since?: string,
    until?: string,
  ): Promise<any> {
    const params: any = {
      metric: metrics.join(','),
      period,
    };

    if (since) params.since = since;
    if (until) params.until = until;

    return this.makeRequest('GET', `/${pageId}/insights`, accessToken, null, params);
  }

  /**
   * Send Facebook Messenger message
   */
  async sendMessage(
    pageId: string,
    accessToken: string,
    recipientId: string,
    message: string,
  ): Promise<any> {
    const data = {
      recipient: { id: recipientId },
      message: { text: message },
    };

    return this.makeRequest('POST', `/${pageId}/messages`, accessToken, data);
  }

  /**
   * Get Facebook Lead Forms
   */
  async getLeadForms(pageId: string, accessToken: string): Promise<any> {
    return this.makeRequest('GET', `/${pageId}/leadgen_forms`, accessToken, null, {
      fields: 'id,name,status,leads_count,created_time,updated_time,questions',
    });
  }

  /**
   * Get Facebook Leads
   */
  async getLeads(formId: string, accessToken: string): Promise<any> {
    return this.makeRequest('GET', `/${formId}/leads`, accessToken, null, {
      fields: 'id,created_time,field_data',
    });
  }

  /**
   * Upload media to Facebook
   */
  async uploadMedia(
    pageId: string,
    accessToken: string,
    mediaUrl: string,
    mediaType: 'image' | 'video',
  ): Promise<any> {
    const data = {
      url: mediaUrl,
      published: false,
    };

    const endpoint = mediaType === 'image' ? 'photos' : 'videos';
    return this.makeRequest('POST', `/${pageId}/${endpoint}`, accessToken, data);
  }

  /**
   * Get Facebook Events
   */
  async getEvents(pageId: string, accessToken: string): Promise<any> {
    return this.makeRequest('GET', `/${pageId}/events`, accessToken, null, {
      fields: 'id,name,description,start_time,end_time,place,attending_count,interested_count',
    });
  }

  /**
   * Create Facebook Event
   */
  async createEvent(
    pageId: string,
    accessToken: string,
    eventData: {
      name: string;
      description?: string;
      start_time: string;
      end_time?: string;
      location?: string;
    },
  ): Promise<any> {
    return this.makeRequest('POST', `/${pageId}/events`, accessToken, eventData);
  }
}
