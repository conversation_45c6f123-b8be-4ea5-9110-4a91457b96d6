# Core Node Interfaces

Thư mục này chứa các interface type-safe cho từng loại node cụ thể trong workflow system.

## 📁 Cấu Trúc <PERSON>h<PERSON>

```
core/
├── base-node.interface.ts          # Base interfaces cho tất cả node
├── index.ts                        # Export tất cả interfaces và registry
├── ai/
│   └── openai-chat.interface.ts    # OpenAI Chat node
├── http/
│   └── http-request.interface.ts   # HTTP Request node
├── logic/
│   └── if-condition.interface.ts   # If Condition node
└── README.md                       # File này
```

## 🎯 Mục Đích

### 1. **Type Safety**
- Mỗi node có interface riêng với parameters được định nghĩa rõ ràng
- TypeScript sẽ kiểm tra type và cung cấp intellisense
- Tránh lỗi runtime do sai cấu trúc dữ liệu

### 2. **Validation**
- Mỗi node có validation function riêng
- Kiểm tra parameters trước khi lưu hoặc thực thi
- Error messages chi tiết và dễ hiểu

### 3. **Maintainability**
- Dễ dàng thêm node mới mà không ảnh hưởng code cũ
- Cấu trúc rõ ràng, dễ tìm và sửa
- Documentation tự động từ TypeScript types

## 🏗️ Cấu Trúc Interface Cho Mỗi Node

Mỗi node interface file bao gồm:

### 1. **Parameters Interface**
```typescript
export interface INodeNameParameters extends IBaseNodeParameters {
    // Các trường cấu hình cụ thể của node
    field1: string;
    field2: number;
    // ...
}
```

### 2. **Input/Output Interfaces**
```typescript
export interface INodeNameInput extends IBaseNodeInput {
    // Dữ liệu đầu vào từ node trước
}

export interface INodeNameOutput extends IBaseNodeOutput {
    // Dữ liệu đầu ra cho node tiếp theo
}
```

### 3. **Properties Definition**
```typescript
export const NODE_NAME_PROPERTIES = [
    {
        name: 'field1',
        displayName: 'Field 1',
        type: EPropertyType.String,
        description: 'Mô tả field',
        required: true,
        default: 'default_value'
    },
    // ...
] as const;
```

### 4. **Typed Interfaces**
```typescript
export type INodeNameNodeDefinition = ITypedNodeDefinition<
    INodeNameParameters,
    typeof NODE_NAME_PROPERTIES
>;

export type INodeNameNodeInstance = ITypedNodeInstance<INodeNameParameters>;

export type INodeNameNodeExecution = ITypedNodeExecution<
    INodeNameInput,
    INodeNameOutput,
    INodeNameParameters
>;
```

### 5. **Factory & Validation Functions**
```typescript
export function createNodeNameNodeDefinition(): INodeNameNodeDefinition {
    // Tạo node definition
}

export function validateNodeNameParameters(params: Partial<INodeNameParameters>): {
    isValid: boolean;
    errors: string[];
} {
    // Validate parameters
}
```

## 🚀 Cách Sử Dụng

### 1. **Import Interface**
```typescript
import { 
    IOpenAIChatParameters,
    IOpenAIChatNodeDefinition,
    createOpenAIChatNodeDefinition,
    validateOpenAIChatParameters
} from '@/modules/workflow/interfaces/core';
```

### 2. **Tạo Node Definition**
```typescript
const nodeDefinition = createOpenAIChatNodeDefinition();
```

### 3. **Validate Parameters**
```typescript
const validation = validateOpenAIChatParameters(userInput);
if (!validation.isValid) {
    console.error('Validation errors:', validation.errors);
}
```

### 4. **Type-Safe Usage**
```typescript
// TypeScript sẽ kiểm tra type
const parameters: IOpenAIChatParameters = {
    model: 'gpt-4',
    prompt: 'Hello',
    temperature: 0.7,
    // ... các trường khác
};
```

## 📝 Thêm Node Mới

### Bước 1: Tạo Interface File
Tạo file mới trong thư mục phù hợp (ai/, http/, logic/, etc.):
```
src/modules/workflow/interfaces/core/[category]/[node-name].interface.ts
```

### Bước 2: Implement Interface
Sử dụng template từ các node hiện có, bao gồm:
- Parameters interface
- Input/Output interfaces  
- Properties definition
- Typed interfaces
- Factory & validation functions

### Bước 3: Export trong index.ts
Thêm export và registry trong `core/index.ts`:
```typescript
// Export
export * from './[category]/[node-name].interface';

// Registry
export const NODE_FACTORIES = {
    // ...existing
    '[node-type]': createNodeNameNodeDefinition
};

export const NODE_VALIDATORS = {
    // ...existing
    '[node-type]': validateNodeNameParameters
};
```

### Bước 4: Update Union Types
Thêm vào union types:
```typescript
export type AllNodeParameters = 
    | IExistingParameters
    | INewNodeParameters; // ← Thêm dòng này
```

## 🔍 Ví Dụ Cụ Thể

### OpenAI Chat Node
```typescript
// Parameters
const params: IOpenAIChatParameters = {
    model: 'gpt-4',
    prompt: 'Summarize this text: {{input.text}}',
    temperature: 0.7,
    max_tokens: 1000,
    credential_id: 'openai-cred-123'
};

// Validation
const validation = validateOpenAIChatParameters(params);

// Factory
const definition = createOpenAIChatNodeDefinition();
```

### HTTP Request Node
```typescript
// Parameters
const params: IHttpRequestParameters = {
    url: 'https://api.example.com/users',
    method: EHttpMethod.GET,
    auth_type: EAuthType.BEARER,
    timeout: 30000
};

// Validation & Factory
const validation = validateHttpRequestParameters(params);
const definition = createHttpRequestNodeDefinition();
```

## 🎨 Best Practices

1. **Naming Convention**: Sử dụng PascalCase cho interfaces, camelCase cho functions
2. **Documentation**: Thêm JSDoc comments chi tiết cho mọi interface và function
3. **Validation**: Luôn validate parameters trước khi sử dụng
4. **Type Safety**: Sử dụng generic types để đảm bảo type safety
5. **Consistency**: Giữ cấu trúc nhất quán giữa các node interfaces

## 🔧 Debugging & Testing

### Type Checking
```typescript
// Kiểm tra type của parameters
function isValidNodeType<T extends IBaseNodeParameters>(
    params: any,
    validator: (p: Partial<T>) => { isValid: boolean; errors: string[] }
): params is T {
    return validator(params).isValid;
}
```

### Runtime Validation
```typescript
// Validate tại runtime
const result = validateNodeParameters('openai-chat', userInput);
if (!result.isValid) {
    throw new Error(`Invalid parameters: ${result.errors.join(', ')}`);
}
```

Cấu trúc này đảm bảo workflow system có type safety cao, dễ maintain và mở rộng! 🚀
