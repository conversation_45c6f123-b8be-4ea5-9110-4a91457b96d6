import { AssistantSpendingType } from '../../enums/assistant-spending-type.enum';

/**
 * Interface for assistant spending history record
 * Maps to the assistant_spending_history database table
 */
export interface AssistantSpendingRecord {
  /**
   * UUID of the AI agent that performed the action
   */
  agent_id: string;

  /**
   * ID of the user who initiated the interaction
   */
  user_id: number;

  /**
   * Number of points spent for this interaction
   */
  point: number;

  /**
   * Actual model name used (e.g., "gpt-4", "claude-3-sonnet")
   */
  model_id: string;

  /**
   * Platform type where the interaction occurred
   */
  type: AssistantSpendingType;
}
