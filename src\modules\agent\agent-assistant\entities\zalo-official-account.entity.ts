import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Zalo Official Account entity
 * Stores OA credentials and agent configuration
 */
@Entity('zalo_official_accounts')
export class ZaloOfficialAccount {
  /**
   * Primary key - auto-generated serial ID
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * User ID who owns this OA
   */
  @Column({ name: 'user_id', type: 'integer', nullable: false })
  userId: number;

  /**
   * Zalo OA ID
   */
  @Column({ name: 'oa_id', type: 'varchar', length: 50, nullable: false })
  oaId: string;

  /**
   * OA display name
   */
  @Column({ name: 'name', type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * OA description
   */
  @Column({ name: 'description', type: 'varchar', length: 500, nullable: true })
  description?: string;

  /**
   * OA avatar URL
   */
  @Column({ name: 'avatar_url', type: 'varchar', length: 500, nullable: true })
  avatarUrl?: string;

  /**
   * Zalo access token
   */
  @Column({ name: 'access_token', type: 'varchar', length: 500, nullable: false })
  accessToken: string;

  /**
   * Zalo refresh token
   */
  @Column({ name: 'refresh_token', type: 'varchar', length: 500, nullable: true })
  refreshToken?: string;

  /**
   * Token expiration timestamp
   */
  @Column({ name: 'expires_at', type: 'bigint', nullable: false })
  expiresAt: number;

  /**
   * OA status
   */
  @Column({ name: 'status', type: 'varchar', length: 20, default: 'active' })
  status: string;

  /**
   * Creation timestamp
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;

  /**
   * Last update timestamp
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: false })
  updatedAt: number;

  /**
   * Associated agent ID for AI responses
   */
  @Column({ name: 'agent_id', type: 'uuid', nullable: true })
  agentId?: string;
}
