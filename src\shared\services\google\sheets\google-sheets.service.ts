import { AppException, ErrorCode } from '../../../../common/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OAuth2Client } from 'google-auth-library';
import { google, sheets_v4 } from 'googleapis';
import { GOOGLE_ERROR_CODES, handleGoogleApiError } from '../exceptions/google.exception';
import {
  CreateSheetRequest,
  CreateSpreadsheetRequest,
  GoogleSheetsConfig,
  GoogleSheetsCredentials,
  ReadDataResult,
  SheetInfo,
  SpreadsheetInfo,
  WriteDataResult
} from '../interfaces/google-sheets.interface';

/**
 * Service để tương tác với Google Sheets API
 */
@Injectable()
export class GoogleSheetsService {
  private readonly logger = new Logger(GoogleSheetsService.name);
  private oauth2Client: OAuth2Client;
  private sheetsConfig: GoogleSheetsConfig;

  constructor(private readonly configService: ConfigService) {
    this.initializeConfig();
  }

  /**
   * Khởi tạo cấu hình từ environment variables
   */
  private initializeConfig(): void {
    this.sheetsConfig = {
      clientId: this.configService.get<string>('GOOGLE_CLIENT_ID') || '',
      clientSecret: this.configService.get<string>('GOOGLE_CLIENT_SECRET') || '',
      redirectUri: this.configService.get<string>('GOOGLE_REDIRECT_URI') || '',
    };

    if (!this.sheetsConfig.clientId || !this.sheetsConfig.clientSecret) {
      this.logger.warn('Google Sheets configuration is incomplete');
    }
  }

  /**
   * Thiết lập credentials cho OAuth2 client
   * @param credentials Thông tin xác thực
   */
  private setCredentials(credentials: GoogleSheetsCredentials): void {
    this.oauth2Client.setCredentials({
      access_token: credentials.accessToken,
      refresh_token: credentials.refreshToken,
      expiry_date: credentials.expiresAt,
    });
  }

  /**
   * Lấy instance của Sheets API
   * @param accessToken Access token
   * @returns Sheets API instance
   */
  private getSheetsInstance(accessToken: string): sheets_v4.Sheets {
    // Use access token directly to avoid OAuth2Client version conflicts
    // But keep OAuth2Client for other operations that need it
    return google.sheets({
      version: 'v4',
      auth: accessToken
    });
  }

  /**
   * Lấy instance của Sheets API với OAuth2Client (cho operations cần refresh token)
   * @param accessToken Access token
   * @param refreshToken Refresh token (optional)
   * @returns Sheets API instance
   */
  private getSheetsInstanceWithOAuth(accessToken: string, refreshToken?: string): sheets_v4.Sheets {
    // Set credentials for OAuth2Client
    this.setCredentials({
      accessToken,
      refreshToken,
      expiresAt: Date.now() + 3600000 // 1 hour from now
    });

    // Note: This may have version conflicts, use getSheetsInstance() instead for most operations
    return google.sheets({
      version: 'v4',
      auth: accessToken // Still use access token to avoid conflicts
    });
  }

  /**
   * Tạo spreadsheet mới
   * @param accessToken Access token
   * @param request Thông tin tạo spreadsheet
   * @returns Thông tin spreadsheet đã tạo
   */
  async createSpreadsheet(
    accessToken: string,
    request: CreateSpreadsheetRequest,
  ): Promise<SpreadsheetInfo> {
    try {
      const sheets = this.getSheetsInstance(accessToken);

      const resource: sheets_v4.Schema$Spreadsheet = {
        properties: {
          title: request.title,
        },
        sheets: request.sheets?.map((sheet) => ({
          properties: {
            title: sheet.title,
            gridProperties: {
              rowCount: sheet.rowCount || 1000,
              columnCount: sheet.columnCount || 26,
            },
          },
        })),
      };

      const response = await sheets.spreadsheets.create({
        requestBody: resource,
      });

      const spreadsheet = response.data;

      this.logger.log(`Spreadsheet created: ${spreadsheet.spreadsheetId}`);

      return this.mapToSpreadsheetInfo(spreadsheet);
    } catch (error) {
      this.logger.error(`Error creating spreadsheet: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      const errorCode = handleGoogleApiError(error, GOOGLE_ERROR_CODES.GOOGLE_SHEETS_API_ERROR);
      throw new AppException(
        errorCode,
        `Không thể tạo spreadsheet: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin spreadsheet
   * @param accessToken Access token
   * @param spreadsheetId ID của spreadsheet
   * @returns Thông tin spreadsheet
   */
  async getSpreadsheet(
    accessToken: string,
    spreadsheetId: string,
  ): Promise<SpreadsheetInfo> {
    try {
      const sheets = this.getSheetsInstance(accessToken);

      const response = await sheets.spreadsheets.get({
        spreadsheetId,
      });

      return this.mapToSpreadsheetInfo(response.data);
    } catch (error) {
      this.logger.error(`Error getting spreadsheet: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      if (error.response?.status === 404) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy spreadsheet',
        );
      }

      if (error.response?.status === 401) {
        throw new AppException(
          ErrorCode.TOKEN_INVALID_OR_EXPIRED,
          'Google access token không hợp lệ hoặc đã hết hạn',
        );
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Không thể lấy thông tin spreadsheet: ${error.message}`,
      );
    }
  }

  /**
   * Đọc dữ liệu từ spreadsheet
   * @param accessToken Access token
   * @param spreadsheetId ID của spreadsheet
   * @param range Range để đọc (ví dụ: 'Sheet1!A1:D5')
   * @returns Dữ liệu đã đọc
   */
  async readData(
    accessToken: string,
    spreadsheetId: string,
    range: string,
  ): Promise<ReadDataResult> {
    try {
      const sheets = this.getSheetsInstance(accessToken);

      const response = await sheets.spreadsheets.values.get({
        spreadsheetId,
        range,
      });

      const values = response.data.values || [];

      this.logger.log(`Read ${values.length} rows from ${range}`);

      return {
        range: response.data.range || range,
        values,
        rowCount: values.length,
        columnCount: values.length > 0 ? Math.max(...values.map(row => row.length)) : 0,
      };
    } catch (error) {
      this.logger.error(`Error reading data: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      if (error.response?.status === 400) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Range không hợp lệ hoặc spreadsheet không tồn tại',
        );
      }

      if (error.response?.status === 401) {
        throw new AppException(
          ErrorCode.TOKEN_INVALID_OR_EXPIRED,
          'Google access token không hợp lệ hoặc đã hết hạn',
        );
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Không thể đọc dữ liệu: ${error.message}`,
      );
    }
  }

  /**
   * Ghi dữ liệu vào spreadsheet
   * @param accessToken Access token
   * @param spreadsheetId ID của spreadsheet
   * @param range Range để ghi
   * @param values Dữ liệu để ghi
   * @param valueInputOption Cách xử lý input ('RAW' hoặc 'USER_ENTERED')
   * @returns Kết quả ghi dữ liệu
   */
  async writeData(
    accessToken: string,
    spreadsheetId: string,
    range: string,
    values: (string | number | boolean)[][],
    valueInputOption: 'RAW' | 'USER_ENTERED' = 'USER_ENTERED',
  ): Promise<WriteDataResult> {
    try {
      const sheets = this.getSheetsInstance(accessToken);

      const response = await sheets.spreadsheets.values.update({
        spreadsheetId,
        range,
        valueInputOption,
        requestBody: {
          values,
        },
      });

      const result = response.data;

      this.logger.log(`Updated ${result.updatedCells} cells in ${result.updatedRange}`);

      return {
        updatedRange: result.updatedRange || range,
        updatedRows: result.updatedRows || 0,
        updatedColumns: result.updatedColumns || 0,
        updatedCells: result.updatedCells || 0,
      };
    } catch (error) {
      this.logger.error(`Error writing data: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      if (error.response?.status === 400) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Dữ liệu hoặc range không hợp lệ',
        );
      }

      if (error.response?.status === 401) {
        throw new AppException(
          ErrorCode.TOKEN_INVALID_OR_EXPIRED,
          'Google access token không hợp lệ hoặc đã hết hạn',
        );
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Không thể ghi dữ liệu: ${error.message}`,
      );
    }
  }

  /**
   * Thêm dữ liệu vào cuối spreadsheet
   * @param accessToken Access token
   * @param spreadsheetId ID của spreadsheet
   * @param range Range để thêm
   * @param values Dữ liệu để thêm
   * @param valueInputOption Cách xử lý input
   * @returns Kết quả thêm dữ liệu
   */
  async appendData(
    accessToken: string,
    spreadsheetId: string,
    range: string,
    values: (string | number | boolean)[][],
    valueInputOption: 'RAW' | 'USER_ENTERED' = 'USER_ENTERED',
  ): Promise<WriteDataResult> {
    try {
      const sheets = this.getSheetsInstance(accessToken);

      const response = await sheets.spreadsheets.values.append({
        spreadsheetId,
        range,
        valueInputOption,
        insertDataOption: 'INSERT_ROWS',
        requestBody: {
          values,
        },
      });

      const result = response.data.updates;

      this.logger.log(`Appended ${result?.updatedCells} cells to ${result?.updatedRange}`);

      return {
        updatedRange: result?.updatedRange || range,
        updatedRows: result?.updatedRows || 0,
        updatedColumns: result?.updatedColumns || 0,
        updatedCells: result?.updatedCells || 0,
      };
    } catch (error) {
      this.logger.error(`Error appending data: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      if (error.response?.status === 400) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Dữ liệu hoặc range không hợp lệ',
        );
      }

      if (error.response?.status === 401) {
        throw new AppException(
          ErrorCode.TOKEN_INVALID_OR_EXPIRED,
          'Google access token không hợp lệ hoặc đã hết hạn',
        );
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Không thể thêm dữ liệu: ${error.message}`,
      );
    }
  }

  /**
   * Xóa dữ liệu trong range
   * @param accessToken Access token
   * @param spreadsheetId ID của spreadsheet
   * @param range Range để xóa
   * @returns Kết quả xóa dữ liệu
   */
  async clearData(
    accessToken: string,
    spreadsheetId: string,
    range: string,
  ): Promise<{ clearedRange: string }> {
    try {
      const sheets = this.getSheetsInstance(accessToken);

      const response = await sheets.spreadsheets.values.clear({
        spreadsheetId,
        range,
      });

      this.logger.log(`Cleared data in ${response.data.clearedRange}`);

      return {
        clearedRange: response.data.clearedRange || range,
      };
    } catch (error) {
      this.logger.error(`Error clearing data: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      if (error.response?.status === 400) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Range không hợp lệ',
        );
      }

      if (error.response?.status === 401) {
        throw new AppException(
          ErrorCode.TOKEN_INVALID_OR_EXPIRED,
          'Google access token không hợp lệ hoặc đã hết hạn',
        );
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Không thể xóa dữ liệu: ${error.message}`,
      );
    }
  }

  /**
   * Tạo sheet mới trong spreadsheet
   * @param accessToken Access token
   * @param spreadsheetId ID của spreadsheet
   * @param request Thông tin tạo sheet
   * @returns Thông tin sheet đã tạo
   */
  async createSheet(
    accessToken: string,
    spreadsheetId: string,
    request: CreateSheetRequest,
  ): Promise<SheetInfo> {
    try {
      const sheets = this.getSheetsInstance(accessToken);

      const batchUpdateRequest: sheets_v4.Schema$BatchUpdateSpreadsheetRequest = {
        requests: [
          {
            addSheet: {
              properties: {
                title: request.title,
                gridProperties: {
                  rowCount: request.rowCount || 1000,
                  columnCount: request.columnCount || 26,
                },
              },
            },
          },
        ],
      };

      const response = await sheets.spreadsheets.batchUpdate({
        spreadsheetId,
        requestBody: batchUpdateRequest,
      });

      const addedSheet = response.data.replies?.[0]?.addSheet?.properties;

      if (!addedSheet) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Không thể tạo sheet mới',
        );
      }

      this.logger.log(`Sheet created: ${addedSheet.title} (ID: ${addedSheet.sheetId})`);

      return {
        sheetId: addedSheet.sheetId || 0,
        title: addedSheet.title || '',
        index: addedSheet.index || 0,
        rowCount: addedSheet.gridProperties?.rowCount || 0,
        columnCount: addedSheet.gridProperties?.columnCount || 0,
      };
    } catch (error) {
      this.logger.error(`Error creating sheet: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      if (error.response?.status === 400) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Thông tin sheet không hợp lệ hoặc tên sheet đã tồn tại',
        );
      }

      if (error.response?.status === 401) {
        throw new AppException(
          ErrorCode.TOKEN_INVALID_OR_EXPIRED,
          'Google access token không hợp lệ hoặc đã hết hạn',
        );
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Không thể tạo sheet: ${error.message}`,
      );
    }
  }

  /**
   * Map từ Google Sheets API response sang SpreadsheetInfo
   * @param spreadsheet Spreadsheet từ API
   * @returns SpreadsheetInfo
   */
  private mapToSpreadsheetInfo(spreadsheet: sheets_v4.Schema$Spreadsheet): SpreadsheetInfo {
    return {
      spreadsheetId: spreadsheet.spreadsheetId || '',
      title: spreadsheet.properties?.title || '',
      url: spreadsheet.spreadsheetUrl || '',
      sheets: spreadsheet.sheets?.map(sheet => ({
        sheetId: sheet.properties?.sheetId || 0,
        title: sheet.properties?.title || '',
        index: sheet.properties?.index || 0,
        rowCount: sheet.properties?.gridProperties?.rowCount || 0,
        columnCount: sheet.properties?.gridProperties?.columnCount || 0,
      })) || [],
    };
  }

  /**
   * Delete rows from spreadsheet
   */
  async deleteRows(accessToken: string, spreadsheetId: string, sheetId: number, startIndex: number, endIndex: number): Promise<any> {
    const sheets = this.getSheetsInstance(accessToken);
    const result = await sheets.spreadsheets.batchUpdate({
      spreadsheetId,
      requestBody: {
        requests: [{
          deleteDimension: {
            range: {
              sheetId,
              dimension: 'ROWS',
              startIndex,
              endIndex,
            },
          },
        }],
      },
    });
    return result.data;
  }

  /**
   * Format cells in spreadsheet
   */
  async formatCells(accessToken: string, spreadsheetId: string, sheetId: number, range: string, format: any): Promise<any> {
    const sheets = this.getSheetsInstance(accessToken);
    const result = await sheets.spreadsheets.batchUpdate({
      spreadsheetId,
      requestBody: {
        requests: [{
          repeatCell: {
            range: { sheetId },
            cell: { userEnteredFormat: format },
            fields: 'userEnteredFormat',
          },
        }],
      },
    });
    return result.data;
  }

  /**
   * Add sheet to spreadsheet
   */
  async addSheet(accessToken: string, spreadsheetId: string, sheetProperties: any): Promise<any> {
    const sheets = this.getSheetsInstance(accessToken);
    const result = await sheets.spreadsheets.batchUpdate({
      spreadsheetId,
      requestBody: {
        requests: [{
          addSheet: { properties: sheetProperties },
        }],
      },
    });
    return result.data.replies?.[0]?.addSheet?.properties;
  }

  /**
   * Delete sheet from spreadsheet
   */
  async deleteSheet(accessToken: string, spreadsheetId: string, sheetId: number): Promise<any> {
    const sheets = this.getSheetsInstance(accessToken);
    const result = await sheets.spreadsheets.batchUpdate({
      spreadsheetId,
      requestBody: {
        requests: [{ deleteSheet: { sheetId } }],
      },
    });
    return result.data;
  }

  /**
   * Copy sheet
   */
  async copySheet(accessToken: string, spreadsheetId: string, sourceSheetId: number, destinationSpreadsheetId: string): Promise<any> {
    const sheets = this.getSheetsInstance(accessToken);
    const result = await sheets.spreadsheets.sheets.copyTo({
      spreadsheetId,
      sheetId: sourceSheetId,
      requestBody: { destinationSpreadsheetId },
    });
    return result.data;
  }

  /**
   * Kiểm tra kết nối với Google Sheets API
   * @param accessToken Access token
   * @returns True nếu kết nối thành công
   */
  async testConnection(accessToken: string): Promise<boolean> {
    try {
      const sheets = this.getSheetsInstance(accessToken);

      // Thử tạo một spreadsheet test và xóa ngay
      const testSpreadsheet = await sheets.spreadsheets.create({
        requestBody: {
          properties: {
            title: 'Test Connection - ' + new Date().toISOString(),
          },
        },
      });

      if (testSpreadsheet.data.spreadsheetId) {
        // Xóa spreadsheet test
        const drive = google.drive({ version: 'v3', auth: accessToken });
        await drive.files.delete({
          fileId: testSpreadsheet.data.spreadsheetId,
        });

        this.logger.log('Google Sheets connection test successful');
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Google Sheets connection test failed: ${error.message}`);
      return false;
    }
  }
}
