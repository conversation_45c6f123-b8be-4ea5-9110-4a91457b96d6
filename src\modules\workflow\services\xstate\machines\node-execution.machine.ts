import { createMachine, assign, sendParent } from 'xstate';
import { 
  NodeExecutionContext, 
  DetailedNodeExecutionResult,
  NodeExecutionConfig 
} from '../types';

/**
 * Node execution machine states
 */
export type NodeExecutionState = 
  | 'idle'
  | 'preparing'
  | 'validating'
  | 'executing'
  | 'retrying'
  | 'paused'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'cleanup';

/**
 * Node execution machine events
 */
export type NodeExecutionMachineEvent = 
  | { type: 'START_EXECUTION'; context: NodeExecutionContext; config?: NodeExecutionConfig }
  | { type: 'PAUSE_EXECUTION' }
  | { type: 'RESUME_EXECUTION' }
  | { type: 'CANCEL_EXECUTION' }
  | { type: 'RETRY_EXECUTION' }
  | { type: 'EXECUTION_COMPLETED'; result: DetailedNodeExecutionResult }
  | { type: 'EXECUTION_FAILED'; error: Error }
  | { type: 'VALIDATION_FAILED'; errors: string[] }
  | { type: 'TIMEOUT' }
  | { type: 'CLEANUP_COMPLETED' };

/**
 * Node execution context
 */
interface NodeExecutionMachineContext {
  /** Node execution context */
  nodeContext?: NodeExecutionContext;
  
  /** Execution configuration */
  config?: NodeExecutionConfig;
  
  /** Execution result */
  result?: DetailedNodeExecutionResult;
  
  /** Execution error */
  error?: Error;
  
  /** Retry count */
  retryCount: number;
  
  /** Max retries allowed */
  maxRetries: number;
  
  /** Start time */
  startTime?: number;
  
  /** End time */
  endTime?: number;
  
  /** Pause time */
  pauseTime?: number;
  
  /** Resume time */
  resumeTime?: number;
  
  /** Validation errors */
  validationErrors: string[];
  
  /** Execution metadata */
  metadata: {
    nodeId: string;
    nodeName: string;
    nodeType: string;
    executionId: string;
    workflowId: string;
    attempts: number;
    totalExecutionTime: number;
    pausedDuration: number;
  };
}

/**
 * Node execution state machine
 */
export const nodeExecutionMachine = createMachine({
  id: 'nodeExecution',
  initial: 'idle',
  context: {
    retryCount: 0,
    maxRetries: 3,
    validationErrors: [],
    metadata: {
      nodeId: '',
      nodeName: '',
      nodeType: '',
      executionId: '',
      workflowId: '',
      attempts: 0,
      totalExecutionTime: 0,
      pausedDuration: 0,
    },
  } as NodeExecutionMachineContext,

  states: {
    idle: {
      on: {
        START_EXECUTION: {
          target: 'preparing',
          actions: assign({
            nodeContext: ({ event }) => event.context,
            config: ({ event }) => event.config,
            startTime: () => Date.now(),
            metadata: ({ event }) => ({
              nodeId: event.context.node.id,
              nodeName: event.context.node.name,
              nodeType: event.context.nodeDefinition?.typeName || 'unknown',
              executionId: event.context.executionId,
              workflowId: event.context.workflowId,
              attempts: 1,
              totalExecutionTime: 0,
              pausedDuration: 0,
            }),
          }),
        },
      },
    },

    preparing: {
      invoke: {
        id: 'prepareExecution',
        src: 'prepareExecutionService',
        input: ({ context }) => ({
          nodeContext: context.nodeContext,
          config: context.config,
        }),
        onDone: {
          target: 'validating',
          actions: assign({
            nodeContext: ({ event }) => event.output.nodeContext,
            config: ({ event }) => event.output.config,
          }),
        },
        onError: {
          target: 'failed',
          actions: assign({
            error: ({ event }) => event.error,
          }),
        },
      },
      
      on: {
        CANCEL_EXECUTION: 'cancelled',
      },
    },

    validating: {
      invoke: {
        id: 'validateNode',
        src: 'validateNodeService',
        input: ({ context }) => ({
          nodeContext: context.nodeContext,
          config: context.config,
        }),
        onDone: [
          {
            target: 'executing',
            guard: 'isValidationSuccessful',
          },
          {
            target: 'failed',
            actions: assign({
              validationErrors: ({ event }) => event.output.errors,
              error: ({ event }) => new Error(`Validation failed: ${event.output.errors.join(', ')}`),
            }),
          },
        ],
        onError: {
          target: 'failed',
          actions: assign({
            error: ({ event }) => event.error,
          }),
        },
      },
      
      on: {
        CANCEL_EXECUTION: 'cancelled',
      },
    },

    executing: {
      invoke: {
        id: 'executeNode',
        src: 'executeNodeService',
        input: ({ context }) => ({
          nodeContext: context.nodeContext,
          config: context.config,
        }),
        onDone: [
          {
            target: 'completed',
            guard: 'isExecutionSuccessful',
            actions: [
              assign({
                result: ({ event }) => event.output,
                endTime: () => Date.now(),
                metadata: ({ context }) => ({
                  ...context.metadata,
                  totalExecutionTime: Date.now() - (context.startTime || 0),
                }),
              }),
              'notifyExecutionCompleted',
            ],
          },
          {
            target: 'retrying',
            guard: 'shouldRetryExecution',
            actions: assign({
              error: ({ event }) => event.output.error,
              retryCount: ({ context }) => context.retryCount + 1,
              metadata: ({ context }) => ({
                ...context.metadata,
                attempts: context.metadata.attempts + 1,
              }),
            }),
          },
          {
            target: 'failed',
            actions: [
              assign({
                error: ({ event }) => event.output.error,
                endTime: () => Date.now(),
                metadata: ({ context }) => ({
                  ...context.metadata,
                  totalExecutionTime: Date.now() - (context.startTime || 0),
                }),
              }),
              'notifyExecutionFailed',
            ],
          },
        ],
        onError: {
          target: 'failed',
          actions: [
            assign({
              error: ({ event }) => event.error,
              endTime: () => Date.now(),
              metadata: ({ context }) => ({
                ...context.metadata,
                totalExecutionTime: Date.now() - (context.startTime || 0),
              }),
            }),
            'notifyExecutionFailed',
          ],
        },
      },
      
      on: {
        PAUSE_EXECUTION: {
          target: 'paused',
          actions: assign({
            pauseTime: () => Date.now(),
          }),
        },
        CANCEL_EXECUTION: 'cancelled',
        TIMEOUT: {
          target: 'retrying',
          guard: 'shouldRetryOnTimeout',
          actions: assign({
            error: () => new Error('Node execution timeout'),
            retryCount: ({ context }) => context.retryCount + 1,
            metadata: ({ context }) => ({
              ...context.metadata,
              attempts: context.metadata.attempts + 1,
            }),
          }),
        },
      },
    },

    retrying: {
      after: {
        RETRY_DELAY: {
          target: 'executing',
          actions: assign({
            startTime: () => Date.now(), // Reset start time for retry
          }),
        },
      },
      
      on: {
        CANCEL_EXECUTION: 'cancelled',
      },
    },

    paused: {
      entry: assign({
        pauseTime: () => Date.now(),
      }),
      
      on: {
        RESUME_EXECUTION: {
          target: 'executing',
          actions: assign({
            resumeTime: () => Date.now(),
            metadata: ({ context }) => ({
              ...context.metadata,
              pausedDuration: context.metadata.pausedDuration + 
                (Date.now() - (context.pauseTime || 0)),
            }),
          }),
        },
        CANCEL_EXECUTION: 'cancelled',
      },
    },

    completed: {
      entry: [
        'updateNodeState',
        'saveExecutionResult',
      ],
      always: 'cleanup',
    },

    failed: {
      entry: [
        'updateNodeStateToFailed',
        'saveExecutionError',
      ],
      
      on: {
        RETRY_EXECUTION: {
          target: 'retrying',
          guard: 'canRetry',
          actions: assign({
            retryCount: ({ context }) => context.retryCount + 1,
            error: undefined,
            metadata: ({ context }) => ({
              ...context.metadata,
              attempts: context.metadata.attempts + 1,
            }),
          }),
        },
      },
      
      always: 'cleanup',
    },

    cancelled: {
      entry: [
        'updateNodeStateToCancelled',
        'notifyExecutionCancelled',
      ],
      always: 'cleanup',
    },

    cleanup: {
      invoke: {
        id: 'cleanupExecution',
        src: 'cleanupExecutionService',
        input: ({ context }) => context,
        onDone: {
          type: 'final',
        },
        onError: {
          type: 'final',
        },
      },
    },
  },
}, {
  // Guards
  guards: {
    isValidationSuccessful: ({ event }) => {
      return event.output && event.output.isValid;
    },
    
    isExecutionSuccessful: ({ event }) => {
      return event.output && event.output.success;
    },
    
    shouldRetryExecution: ({ context, event }) => {
      const result = event.output as DetailedNodeExecutionResult;
      return (
        !result.success &&
        result.shouldRetry !== false &&
        context.retryCount < context.maxRetries
      );
    },
    
    shouldRetryOnTimeout: ({ context }) => {
      return context.retryCount < context.maxRetries;
    },
    
    canRetry: ({ context }) => {
      return context.retryCount < context.maxRetries;
    },
  },

  // Actions
  actions: {
    updateNodeState: ({ context }) => {
      if (context.nodeContext) {
        // Update node state in workflow context
        const nodeState = context.nodeContext.nodes?.get(context.metadata.nodeId);
        if (nodeState) {
          nodeState.status = 'completed';
          nodeState.endTime = Date.now();
          nodeState.outputData = context.result?.outputData;
        }
      }
    },

    updateNodeStateToFailed: ({ context }) => {
      if (context.nodeContext) {
        const nodeState = context.nodeContext.nodes?.get(context.metadata.nodeId);
        if (nodeState) {
          nodeState.status = 'failed';
          nodeState.endTime = Date.now();
          nodeState.error = context.error;
          nodeState.retryCount = context.retryCount;
        }
      }
    },

    updateNodeStateToCancelled: ({ context }) => {
      if (context.nodeContext) {
        const nodeState = context.nodeContext.nodes?.get(context.metadata.nodeId);
        if (nodeState) {
          nodeState.status = 'cancelled';
          nodeState.endTime = Date.now();
        }
      }
    },

    saveExecutionResult: ({ context }) => {
      if (context.nodeContext && context.result) {
        // Save execution result to workflow context
        context.nodeContext.executionData.set(
          context.metadata.nodeId, 
          context.result.outputData
        );
      }
    },

    saveExecutionError: ({ context }) => {
      if (context.nodeContext && context.error) {
        // Save execution error to workflow context
        context.nodeContext.errors.set(
          context.metadata.nodeId, 
          context.error
        );
      }
    },

    notifyExecutionCompleted: sendParent(({ context }) => ({
      type: 'NODE_COMPLETED',
      nodeId: context.metadata.nodeId,
      result: context.result!,
    })),

    notifyExecutionFailed: sendParent(({ context }) => ({
      type: 'NODE_FAILED',
      nodeId: context.metadata.nodeId,
      error: context.error!,
    })),

    notifyExecutionCancelled: sendParent(({ context }) => ({
      type: 'NODE_CANCELLED',
      nodeId: context.metadata.nodeId,
    })),
  },

  // Delays
  delays: {
    RETRY_DELAY: ({ context }) => {
      // Exponential backoff: 1s, 2s, 4s, 8s...
      return Math.min(1000 * Math.pow(2, context.retryCount), 30000);
    },
  },
});
