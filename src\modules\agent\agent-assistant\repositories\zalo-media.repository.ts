import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { ZaloMedia } from '../entities/zalo-media.entity';

@Injectable()
export class ZaloMediaRepository {
  constructor(
    @InjectRepository(ZaloMedia)
    private readonly repository: Repository<ZaloMedia>,
  ) {}

  async create(data: Partial<ZaloMedia>): Promise<ZaloMedia> {
    const entity = this.repository.create(data);
    return this.repository.save(entity);
  }

  async findById(id: string): Promise<ZaloMedia | null> {
    return this.repository.findOne({ where: { id } });
  }

  async findByIds(ids: string[]): Promise<ZaloMedia[]> {
    if (ids.length === 0) {
      return [];
    }
    return this.repository.find({ where: { id: In(ids) } });
  }

  async findByZaloMediaId(zaloMediaId: string, zaloOaId: number): Promise<ZaloMedia | null> {
    return this.repository.findOne({ 
      where: { zaloMediaId, zaloOaId } 
    });
  }

  async findByCustomerId(zaloCustomerId: string): Promise<ZaloMedia[]> {
    return this.repository.find({ 
      where: { zaloCustomerId },
      order: { createdAt: 'DESC' }
    });
  }

  async delete(id: string): Promise<void> {
    await this.repository.delete(id);
  }
}
