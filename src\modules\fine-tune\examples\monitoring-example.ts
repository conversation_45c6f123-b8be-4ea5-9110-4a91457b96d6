/**
 * Example usage của Fine-Tune Monitoring System
 * 
 * File này minh họa cách sử dụng hệ thống monitoring fine-tune
 * từ BE app đến Worker app qua Redis Queue.
 */

import { FineTuneQueueService } from '../services/fine-tune-queue.service';
import { ProviderFineTuneEnum } from '../constants/provider.enum';

/**
 * Example: BE App tạo monitoring job
 */
export class FineTuneMonitoringExample {
  constructor(
    private readonly fineTuneQueueService: FineTuneQueueService,
  ) {}

  /**
   * Ví dụ tạo monitoring job sau khi BE app đã tạo fine-tune job với provider
   */
  async createMonitoringJobExample() {
    // Giả sử BE app đã tạo model và model detail
    const savedModelId = 'uuid-model-123';
    const providerJobId = 'ftjob-abc123'; // Từ OpenAI hoặc Google
    const provider = ProviderFineTuneEnum.OPENAI;
    const userId = 12345;

    try {
      // Thêm monitoring job vào queue
      const jobId = await this.fineTuneQueueService.addMonitoringJob(
        {
          historyId: savedModelId,
          providerJobId: providerJobId,
          provider: provider,
          userId: userId,
        },
        {
          delay: 30000, // 30 giây delay
          attempts: 1, // Mỗi job chỉ thử 1 lần
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
        },
      );

      console.log(`Monitoring job created with ID: ${jobId}`);
      return jobId;
    } catch (error) {
      console.error('Error creating monitoring job:', error);
      throw error;
    }
  }

  /**
   * Ví dụ kiểm tra trạng thái job
   */
  async checkJobStatusExample(jobId: string) {
    try {
      const jobInfo = await this.fineTuneQueueService.getJob(jobId);
      
      if (!jobInfo) {
        console.log(`Job ${jobId} not found`);
        return null;
      }

      console.log('Job Info:', {
        id: jobInfo.id,
        name: jobInfo.name,
        progress: jobInfo.progress,
        attemptsMade: jobInfo.attemptsMade,
        processedOn: jobInfo.processedOn,
        finishedOn: jobInfo.finishedOn,
        failedReason: jobInfo.failedReason,
      });

      return jobInfo;
    } catch (error) {
      console.error('Error checking job status:', error);
      return null;
    }
  }

  /**
   * Ví dụ kiểm tra có job monitoring nào đang chạy không
   */
  async checkActiveMonitoringExample(historyId: string) {
    try {
      const hasActiveJob = await this.fineTuneQueueService.hasActiveMonitoringJob(historyId);
      
      console.log(`Model ${historyId} has active monitoring job: ${hasActiveJob}`);
      return hasActiveJob;
    } catch (error) {
      console.error('Error checking active monitoring:', error);
      return false;
    }
  }

  /**
   * Ví dụ lấy thống kê queue
   */
  async getQueueStatsExample() {
    try {
      const stats = await this.fineTuneQueueService.getQueueStats();
      
      if (!stats) {
        console.log('Unable to get queue stats');
        return null;
      }

      console.log('Queue Statistics:', {
        waiting: stats.waiting,
        active: stats.active,
        completed: stats.completed,
        failed: stats.failed,
        delayed: stats.delayed,
        total: stats.total,
      });

      return stats;
    } catch (error) {
      console.error('Error getting queue stats:', error);
      return null;
    }
  }

  /**
   * Ví dụ hủy job
   */
  async cancelJobExample(jobId: string) {
    try {
      const cancelled = await this.fineTuneQueueService.cancelJob(jobId);
      
      if (cancelled) {
        console.log(`Job ${jobId} cancelled successfully`);
      } else {
        console.log(`Failed to cancel job ${jobId}`);
      }

      return cancelled;
    } catch (error) {
      console.error('Error cancelling job:', error);
      return false;
    }
  }

  /**
   * Ví dụ dọn dẹp job cũ
   */
  async cleanOldJobsExample() {
    try {
      const maxAge = 24 * 60 * 60 * 1000; // 24 giờ
      const maxCount = 100;

      await this.fineTuneQueueService.cleanOldJobs(maxAge, maxCount);
      console.log('Old jobs cleaned successfully');
    } catch (error) {
      console.error('Error cleaning old jobs:', error);
    }
  }
}

/**
 * Example data structures
 */
export const exampleMonitoringJobData = {
  historyId: 'uuid-model-123',
  providerJobId: 'ftjob-abc123',
  provider: ProviderFineTuneEnum.OPENAI,
  userId: 12345,
  timestamp: Date.now(),
};

export const exampleModelData = {
  id: 'uuid-model-123',
  modelId: 'ftjob-abc123', // Tạm thời, sẽ được cập nhật
  modelRegistryId: 'uuid-registry-456',
  detailId: 'uuid-detail-789',
  active: false,
  userId: 12345,
  isFineTune: true,
};

export const exampleModelDetailData = {
  id: 'uuid-detail-789',
  metadata: {
    jobId: 'ftjob-abc123',
    provider: 'OPENAI',
    status: 'running',
    datasetId: 'dataset-123',
    baseModelId: 'gpt-3.5-turbo',
    hyperparameters: {
      nEpochs: 3,
      batchSize: 'auto',
      learningRateMultiplier: 'auto',
    },
    costDeducted: 100,
    createdAt: Date.now(),
    providerJobData: {
      // Raw data từ provider
    },
  },
};

/**
 * Example usage trong BE app
 */
export const beAppUsageExample = `
// Trong BE app service
import { FineTuneQueueService } from './modules/fine-tune/services';

class FineTuneService {
  constructor(
    private readonly fineTuneQueueService: FineTuneQueueService,
  ) {}

  async createFineTuneJob(params) {
    // 1. Tạo fine-tune job với provider
    const providerResponse = await this.createProviderJob(params);
    
    // 2. Lưu model và model detail vào database
    const savedModel = await this.saveModelToDatabase(providerResponse);
    
    // 3. Thêm monitoring job vào queue
    await this.fineTuneQueueService.addMonitoringJob({
      historyId: savedModel.id,
      providerJobId: providerResponse.jobId,
      provider: params.provider,
      userId: params.userId,
    });
    
    return savedModel;
  }
}
`;
