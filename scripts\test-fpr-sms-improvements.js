/**
 * Script test các cải tiến của FprSmsBrandnameService
 */

console.log('=== Test FPR SMS Service Improvements ===\n');

// Test 1: Session ID Generation
console.log('--- Test 1: Session ID Generation ---');

function generateSessionId() {
  // Tạo chuỗi ngẫu nhiên từ timestamp và random string
  const timestamp = Date.now().toString(36);
  const randomStr = Math.random().toString(36).substring(2);
  const sessionId = `${timestamp}_${randomStr}`;
  
  // Đ<PERSON>m bảo không vượt quá 32 ký tự
  return sessionId.length > 32 ? sessionId.substring(0, 32) : sessionId;
}

// Test multiple session IDs
for (let i = 0; i < 5; i++) {
  const sessionId = generateSessionId();
  console.log(`Session ID ${i + 1}: ${sessionId} (Length: ${sessionId.length})`);
  
  if (sessionId.length > 32) {
    console.log(`❌ Session ID quá dài: ${sessionId.length} ký tự`);
  } else {
    console.log(`✅ Session ID hợp lệ: ${sessionId.length} ký tự`);
  }
}

console.log('\n--- Test 2: Redis Key Structure ---');

const REDIS_TOKEN_KEY = 'fpt_sms_access_token';
const TOKEN_EXPIRY_SECONDS = 20 * 60; // 20 phút

console.log(`Redis Key: ${REDIS_TOKEN_KEY}`);
console.log(`Token Expiry: ${TOKEN_EXPIRY_SECONDS} seconds (${TOKEN_EXPIRY_SECONDS / 60} minutes)`);

console.log('\n--- Test 3: Session ID Uniqueness ---');

const sessionIds = new Set();
const testCount = 100;

for (let i = 0; i < testCount; i++) {
  const sessionId = generateSessionId();
  sessionIds.add(sessionId);
}

console.log(`Generated ${testCount} session IDs`);
console.log(`Unique session IDs: ${sessionIds.size}`);
console.log(`Duplicates: ${testCount - sessionIds.size}`);

if (sessionIds.size === testCount) {
  console.log('✅ Tất cả session IDs đều unique');
} else {
  console.log('❌ Có session IDs bị trùng lặp');
}

console.log('\n--- Test 4: Session ID Format Validation ---');

const sampleSessionIds = Array.from(sessionIds).slice(0, 5);
sampleSessionIds.forEach((sessionId, index) => {
  console.log(`Sample ${index + 1}: ${sessionId}`);
  
  // Kiểm tra format: timestamp_randomstring
  const parts = sessionId.split('_');
  if (parts.length === 2) {
    console.log(`  ✅ Format đúng: timestamp_randomstring`);
    console.log(`  - Timestamp part: ${parts[0]} (${parts[0].length} chars)`);
    console.log(`  - Random part: ${parts[1]} (${parts[1].length} chars)`);
  } else {
    console.log(`  ❌ Format sai: ${parts.length} parts`);
  }
});

console.log('\n--- Test 5: Token Caching Strategy ---');

console.log('Token caching strategy:');
console.log('1. Kiểm tra token trong Redis trước');
console.log('2. Nếu có token cached -> sử dụng luôn');
console.log('3. Nếu không có -> gọi API lấy token mới');
console.log('4. Lưu token mới vào Redis với TTL 20 phút');
console.log('5. Mỗi request tạo session_id mới (unique)');

console.log('\n--- Test 6: Performance Estimation ---');

const avgApiCallTime = 500; // ms
const avgRedisGetTime = 5; // ms

console.log(`Estimated performance improvement:`);
console.log(`- API call time: ~${avgApiCallTime}ms`);
console.log(`- Redis get time: ~${avgRedisGetTime}ms`);
console.log(`- Performance gain: ~${((avgApiCallTime - avgRedisGetTime) / avgApiCallTime * 100).toFixed(1)}%`);

console.log('\n=== Test Completed ===');

// Test Redis connection simulation
console.log('\n--- Test 7: Redis Operations Simulation ---');

class MockRedis {
  constructor() {
    this.data = new Map();
    this.expiry = new Map();
  }

  async setex(key, seconds, value) {
    this.data.set(key, value);
    this.expiry.set(key, Date.now() + (seconds * 1000));
    console.log(`✅ SET ${key} with TTL ${seconds}s`);
  }

  async get(key) {
    const expiry = this.expiry.get(key);
    if (expiry && Date.now() > expiry) {
      this.data.delete(key);
      this.expiry.delete(key);
      console.log(`❌ Key ${key} expired`);
      return null;
    }
    const value = this.data.get(key);
    console.log(`✅ GET ${key}: ${value ? 'found' : 'not found'}`);
    return value || null;
  }

  async del(key) {
    const deleted = this.data.delete(key);
    this.expiry.delete(key);
    console.log(`✅ DEL ${key}: ${deleted ? 'deleted' : 'not found'}`);
  }
}

async function testRedisOperations() {
  const redis = new MockRedis();
  
  // Test set token
  await redis.setex(REDIS_TOKEN_KEY, TOKEN_EXPIRY_SECONDS, 'sample_access_token_123');
  
  // Test get token
  const token1 = await redis.get(REDIS_TOKEN_KEY);
  console.log(`Retrieved token: ${token1}`);
  
  // Test get non-existent key
  const token2 = await redis.get('non_existent_key');
  console.log(`Non-existent key: ${token2}`);
  
  // Test delete token
  await redis.del(REDIS_TOKEN_KEY);
  
  // Test get after delete
  const token3 = await redis.get(REDIS_TOKEN_KEY);
  console.log(`Token after delete: ${token3}`);
}

testRedisOperations().then(() => {
  console.log('\n✅ All tests completed successfully!');
}).catch(error => {
  console.error('❌ Test failed:', error);
});
