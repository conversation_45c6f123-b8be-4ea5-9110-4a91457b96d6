import { Injectable } from '@nestjs/common';
import { AgentUserMcpRepository } from '../../repositories';
import { McpConfig } from '../../interfaces/mcp-config.interface';


/**
 * Agent MCP Configuration Service
 * 
 * Handles MCP (Model Context Protocol) server configurations with encrypted headers.
 * Responsible for: "What tools can this agent access?"
 */
@Injectable()
export class AgentMcpConfigService {
  constructor(
    private readonly agentUserMcpRepository: AgentUserMcpRepository,
  ) {}

  /**
   * Get MCP configuration for an agent using JOIN query
   */
  async getMcpConfig(agentId: string): Promise<McpConfig> {
    // Use JOIN to get MCP configurations in one query
    const mcpConfigs = await this.agentUserMcpRepository.findMcpConfigsByAgentId(agentId);

    if (mcpConfigs.length === 0) {
      return {};
    }

    // Build MCP servers object with encrypted headers injected
    const mcpServers: McpConfig = {};

    for (const mcpConfig of mcpConfigs) {
      let config: any = mcpConfig.config;

      // Inject encrypted headers if they exist
      if (mcpConfig.encryptedHeaders) {
        config = {
          ...config,
          headers: mcpConfig.encryptedHeaders,
        };
      }

      mcpServers[mcpConfig.nameServer] = config;
    }
    
    return mcpServers;
  }

  /**
   * Build MCP clients configuration for LangGraph
   */
  async buildMcpClients(mcpServers: McpConfig): Promise<any> {
    const mcpClients = {};
    for (const [serverName, config] of Object.entries(mcpServers)) {
      mcpClients[serverName] = {
        serverName,
        config,
        // Add any additional MCP client configuration needed by LangGraph
      };
    }

    return mcpClients;
  }

  /**
   * Get MCP configuration using agentId (no database lookup for agentUser)
   * Use this when agentUser has already been fetched and validated
   */
  async getMcpConfigFromValidatedAgent(agentId: string): Promise<McpConfig> {
    // Use JOIN to get MCP configurations in one query
    const mcpConfigs = await this.agentUserMcpRepository.findMcpConfigsByAgentId(agentId);

    if (mcpConfigs.length === 0) {
      return {};
    }

    // Build MCP servers object with encrypted headers injected
    const mcpServers: McpConfig = {};

    for (const mcpConfig of mcpConfigs) {
      let config: any = mcpConfig.config;

      // Inject encrypted headers if they exist
      if (mcpConfig.encryptedHeaders) {
        config = {
          ...config,
          headers: mcpConfig.encryptedHeaders,
        };
      }

      mcpServers[mcpConfig.nameServer] = config;
    }

    return mcpServers;
  }
}
