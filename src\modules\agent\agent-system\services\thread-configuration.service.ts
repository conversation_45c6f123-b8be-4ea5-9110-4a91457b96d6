import { Injectable, Logger } from '@nestjs/common';
import { ThreadConfigurationService as IThreadConfigurationService } from '../interfaces/service.interface';
import { CustomConfigurableType, ThreadConfiguration } from '../schemas';
import { apiKeyEncryption } from '../../helpers/api-key-encryption.helper';
import { ValidationService } from './validation.service';
import { workflow } from '../core';
import { MemoryService } from './memory.service';
import { MessageContentService } from './message-content.service';

/**
 * Service responsible for building thread configuration and handling API key decryption
 * Extracted from AgentSystemService.processAgentThread method (Section 1: Configuration & Setup)
 */
@Injectable()
export class ThreadConfigurationService implements IThreadConfigurationService {
  private readonly logger = new Logger(ThreadConfigurationService.name);

  constructor(
    private readonly validationService: ValidationService,
    private readonly memoryService: MemoryService,
    private readonly messageContentService: MessageContentService,
  ) {}

  /**
   * Build complete thread configuration from run data
   * @param runData - Run data from database containing payload
   * @param threadId - Thread ID for the configuration
   * @param jwt - JWT token for authentication
   * @returns Complete thread configuration
   */
  async buildConfiguration(
    runData: any,
    threadId: string,
    jwt: string,
  ): Promise<ThreadConfiguration> {
    // Validate required JWT
    if (!jwt || jwt.trim() === '') {
      throw new Error('JWT token is required for thread configuration');
    }

    try {
      this.logger.debug(
        `Building thread configuration for thread ${threadId}`,
        {
          threadId,
          runId: runData.id,
          userId: runData.created_by,
          hasJwt: !!jwt,
          payloadSize: JSON.stringify(runData.payload).length,
        },
      );

      // Extract user ID from run data
      const userId = runData.created_by as number;

      const latestState = await workflow.getState({
        configurable: {
          thread_id: threadId,
        },
      });

      const checkpointId =
        latestState?.config?.configurable?.checkpoint_id || undefined;

      // Build custom configuration for LangGraph
      const customConfig = await this.buildCustomConfigurable(
        runData,
        threadId,
        jwt,
        checkpointId,
      );

      // Decrypt payload for message extraction
      const decryptedPayload = this.decryptApiKeysInPayload(
        runData.payload,
        userId,
      );

      // Validate custom configuration using Zod
      const customConfigValidation =
        this.validationService.validateCustomConfigurable(customConfig);
      if (!customConfigValidation.success) {
        const errorMessage = this.validationService.formatValidationErrors(
          customConfigValidation.issues || [],
        );
        this.logger.error(
          `Custom configuration validation failed for thread ${threadId}:`,
          {
            threadId,
            errors: customConfigValidation.issues,
          },
        );
        throw new Error(`Invalid custom configuration: ${errorMessage}`);
      }

      // Validate agent config map if present
      if (customConfig.agentConfigMap) {
        const agentConfigMapValidation =
          this.validationService.validateSystemAgentConfigMap(
            customConfig.agentConfigMap,
          );
        if (!agentConfigMapValidation.success) {
          const errorMessage = this.validationService.formatValidationErrors(
            agentConfigMapValidation.issues || [],
          );
          this.logger.error(
            `Agent configuration map validation failed for thread ${threadId}:`,
            {
              threadId,
              errors: agentConfigMapValidation.issues,
            },
          );
          throw new Error(`Invalid agent configuration map: ${errorMessage}`);
        }

        this.logger.debug(
          `✅ Agent configuration map validation passed for thread ${threadId}`,
          {
            threadId,
            agentCount: Object.keys(customConfig.agentConfigMap).length,
          },
        );
      }

      // ✅ NEW: Extract message data from payload
      const messageData = this.extractMessageData(decryptedPayload);

      const configuration: ThreadConfiguration = {
        customConfig,
        decryptedPayload,
        userId,
        threadId,
        runData,
        jwt,
        messageData, // ✅ NEW: Include extracted message data
      };

      // Validate the final thread configuration using Zod
      const configValidation =
        this.validationService.validateThreadConfiguration(configuration);
      if (!configValidation.success) {
        const errorMessage = this.validationService.formatValidationErrors(
          configValidation.issues || [],
        );
        throw new Error(`Invalid thread configuration: ${errorMessage}`);
      }

      this.logger.debug(
        `Successfully built thread configuration for thread ${threadId}`,
        {
          threadId,
          userId,
          agentConfigCount: customConfig.agentConfigMap
            ? Object.keys(customConfig.agentConfigMap).length
            : 0,
          supervisorAgentId: customConfig.supervisorAgentId,
          alwaysApproveToolCall: customConfig.alwaysApproveToolCall,
        },
      );

      return configuration;
    } catch (error) {
      this.logger.error(
        `Failed to build thread configuration for thread ${threadId}:`,
        {
          threadId,
          runId: runData.id,
          error: error.message,
          stack: error.stack,
        },
      );
      throw error;
    }
  }

  /**
   * Map payload data to CustomConfigurableType for LangGraph configuration
   * @param runData Run data from database containing payload
   * @param threadId Thread ID for the configuration
   * @param jwt JWT token for authenticated API calls
   * @param checkpointId
   * @returns CustomConfigurableType configuration object
   */
  private async buildCustomConfigurable(
    runData: any,
    threadId: string,
    jwt: string,
    checkpointId?: string,
  ): Promise<CustomConfigurableType> {
    // Validate required JWT
    if (!jwt || jwt.trim() === '') {
      throw new Error('JWT token is required for custom configurable');
    }

    try {
      // Decrypt API keys in payload before processing
      const userId = runData.created_by;
      const decryptedPayload = this.decryptApiKeysInPayload(
        runData.payload,
        userId,
      );

      // Extract configuration values from decrypted payload with fallbacks
      const alwaysApproveToolCall =
        decryptedPayload?.processing?.alwaysApproveToolCall || false;
      const agentConfigMap = decryptedPayload?.agentConfigMap || {};
      const supervisorAgentId = decryptedPayload?.primaryAgentId || '';

      // ✅ NEW: Load context data with comprehensive error handling
      const contextLoadingStartTime = Date.now();

      // ✅ NEW: Load user memories for context
      let userMemories: any[] = [];
      try {
        this.logger.debug(`Loading user memories for user ${userId}`);
        userMemories = await this.memoryService.retrieveUserMemories(userId);
        this.logger.debug(
          `Loaded ${userMemories.length} user memories for user ${userId}`,
        );
      } catch (error) {
        this.logger.error(`Failed to load user memories for user ${userId}:`, {
          userId,
          error: error.message,
          stack: error.stack,
        });
        userMemories = []; // Fallback to empty array
      }

      // ✅ NEW: Load reply context if replyToMessageId is present
      let replyToContext:
        | { messageId: string; originalMessageData: any }
        | undefined;
      const messageRequest = decryptedPayload?.message || {};
      const replyToMessageId = messageRequest.replyToMessageId;

      if (replyToMessageId) {
        try {
          this.logger.debug(
            `Loading reply context for message ${replyToMessageId}`,
          );
          // Use the new public method to get full message data
          const originalMessageData =
            await this.messageContentService.getMessageById(replyToMessageId);

          if (originalMessageData) {
            replyToContext = {
              messageId: replyToMessageId,
              originalMessageData,
            };
            this.logger.debug(
              `Loaded full reply context for message ${replyToMessageId}`,
            );
          } else {
            this.logger.warn(`Reply message ${replyToMessageId} not found`);
          }
        } catch (error) {
          this.logger.error(
            `Failed to load reply context for message ${replyToMessageId}:`,
            {
              replyToMessageId,
              error: error.message,
              stack: error.stack,
            },
          );
        }
      }

      // ✅ NEW: Extract attachment context from message data
      let attachmentContext: any[] = [];
      try {
        attachmentContext = messageRequest.attachmentContext || [];
        this.logger.debug(
          `Extracted ${attachmentContext.length} attachment context items`,
        );
      } catch (error) {
        this.logger.error(`Failed to extract attachment context:`, {
          error: error.message,
          stack: error.stack,
        });
        attachmentContext = []; // Fallback to empty array
      }

      // ✅ NEW: Log context loading performance
      const contextLoadingDuration = Date.now() - contextLoadingStartTime;
      this.logger.debug(
        `Context loading completed in ${contextLoadingDuration}ms`,
        {
          userMemoryCount: userMemories.length,
          hasReplyContext: !!replyToContext,
          attachmentContextCount: attachmentContext.length,
          duration: contextLoadingDuration,
        },
      );

      const config: CustomConfigurableType = {
        alwaysApproveToolCall,
        thread_id: threadId,
        checkpoint_id: checkpointId,
        userId: userId, // Add userId for memory tools
        jwt: jwt, // Add JWT for authenticated API calls
        agentConfigMap,
        supervisorAgentId,
        multiMcpClients: undefined, // Skip for now as requested

        // ✅ NEW: Memory service instances for dynamic loading in callModel()
        memoryService: this.memoryService,
        messageContentService: this.messageContentService,

        // ✅ NEW: Pre-loaded user memories (shared across all agents) - exclude createdAt for schema compliance
        userMemories: userMemories.map((memory) => ({
          id: memory.id,
          userId: memory.userId,
          structuredContent: memory.structuredContent,
          metadata: memory.metadata,
        })),

        // ✅ NEW: Reply context data for conversation continuity
        replyToContext,

        // ✅ NEW: Attachment context data for files and images
        attachmentContext,
      };

      this.logger.debug(
        `Built CustomConfigurableType for thread ${threadId}:`,
        {
          alwaysApproveToolCall,
          thread_id: threadId,
          userId: userId, // Now included in configurable
          hasJwt: !!jwt, // JWT included in configurable
          supervisorAgentId,
          agentConfigCount: Object.keys(agentConfigMap).length,
          hasMultiMcp: false,
          apiKeysDecrypted: true,
          userMemoryCount: userMemories.length, // ✅ NEW: Log user memory count
          hasMemoryService: !!this.memoryService, // ✅ NEW: Log memory service availability
          hasMessageContentService: !!this.messageContentService, // ✅ NEW: Log message content service availability
          hasReplyContext: !!replyToContext, // ✅ NEW: Log reply context availability
          replyToMessageId: replyToContext?.messageId, // ✅ NEW: Log reply message ID
          attachmentContextCount: attachmentContext.length, // ✅ NEW: Log attachment context count
        },
      );

      return config;
    } catch (error) {
      this.logger.error(
        `Failed to build CustomConfigurableType for thread ${threadId}:`,
        {
          threadId,
          error: error.message,
          stack: error.stack,
        },
      );

      // No fallback - JWT is required, so throw the error
      throw new Error(
        `Failed to build configuration for thread ${threadId}: ${error.message}`,
      );
    }
  }

  /**
   * Extract message data from decrypted payload
   * @param decryptedPayload Decrypted payload containing message data
   * @returns Extracted message data object
   */
  private extractMessageData(decryptedPayload: any): any {
    try {
      const messageRequest = decryptedPayload?.message || {};

      const messageData = {
        contentBlocks: messageRequest.contentBlocks || [],
        attachmentContext: messageRequest.attachmentContext || [],
        replyToMessageId: messageRequest.replyToMessageId,
        alwaysApproveToolCall: messageRequest.alwaysApproveToolCall || false,
      };

      this.logger.debug(`Extracted message data:`, {
        contentBlockCount: messageData.contentBlocks.length,
        attachmentCount: messageData.attachmentContext.length,
        // ✅ REMOVED: threadId logging - threadId is available from context, not from messageData
        alwaysApproveToolCall: messageData.alwaysApproveToolCall,
        contentBlockTypes: messageData.contentBlocks.map(
          (block: any) => block.type,
        ),
      });

      return messageData;
    } catch (error) {
      this.logger.error(`Failed to extract message data:`, {
        error: error.message,
        stack: error.stack,
      });

      // Return empty message data as fallback
      return {
        contentBlocks: [],
        attachmentContext: [],
        threadId: '',
        alwaysApproveToolCall: false,
      };
    }
  }

  /**
   * Decrypt API keys in the payload based on user type
   * @param payload The payload containing encrypted API keys
   * @param userId User ID for decryption (from created_by field)
   * @returns Payload with decrypted API keys
   */
  private decryptApiKeysInPayload(payload: any, userId: number): any {
    try {
      if (!payload) {
        return payload;
      }

      // Deep clone payload to avoid modifying original
      const decryptedPayload = JSON.parse(JSON.stringify(payload));

      // Decrypt API keys in agentConfigMap
      if (decryptedPayload.agentConfigMap) {
        for (const agentId in decryptedPayload.agentConfigMap) {
          const agentConfig = decryptedPayload.agentConfigMap[agentId];

          if (
            agentConfig?.model?.apiKeys &&
            Array.isArray(agentConfig.model.apiKeys)
          ) {
            this.logger.debug(
              `Decrypting ${agentConfig.model.apiKeys.length} API keys for agent ${agentId}`,
              { agentId, userId, keyCount: agentConfig.model.apiKeys.length },
            );

            agentConfig.model.apiKeys = agentConfig.model.apiKeys.map(
              (encryptedKey: string) => {
                try {
                  // Determine if this is admin or user key based on agent type
                  if (agentConfig.model.type === 'SYSTEM') {
                    // System agents use admin keys
                    return apiKeyEncryption.decryptAdminApiKey(encryptedKey);
                  } else {
                    // User agents use user-specific keys
                    return apiKeyEncryption.decryptUserApiKey(
                      encryptedKey,
                      userId,
                    );
                  }
                } catch (error) {
                  this.logger.error(
                    `Failed to decrypt API key for agent ${agentId}:`,
                    {
                      agentId,
                      userId,
                      error: error.message,
                    },
                  );
                  // Return original key if decryption fails (might already be decrypted)
                  return encryptedKey;
                }
              },
            );
          }
        }
      }

      this.logger.debug(
        `Successfully decrypted API keys in payload for user ${userId}`,
        {
          userId,
          hasAgentConfigMap: !!decryptedPayload.agentConfigMap,
          agentCount: decryptedPayload.agentConfigMap
            ? Object.keys(decryptedPayload.agentConfigMap).length
            : 0,
        },
      );

      return decryptedPayload;
    } catch (error) {
      this.logger.error(
        `Failed to decrypt API keys in payload for user ${userId}:`,
        {
          userId,
          error: error.message,
          stack: error.stack,
        },
      );
      // Return original payload if decryption fails
      return payload;
    }
  }
}
