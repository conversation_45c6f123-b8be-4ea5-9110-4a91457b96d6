import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName } from '../../../queue';
import { SmsJobName } from '../../../modules/sms_system/constants';
import { SmsMarketingJobDto } from './dto';
import { SmsCampaignType } from './constants';
import { SmsMarketingService, SmsTemplateService } from './services';
import { SmsProviderType } from '../interfaces/sms-provider-config.interface';
import { FptSmsBrandnameService } from '../../../shared/services/sms/fpt-sms-brandname.service';
import { SmsService } from '../../../shared/services/sms/sms.service';
import { SmsProviderFactory } from '../../../shared/services/sms/sms-provider-factory.service';
import { SmsEncryptionService } from '../../sms_system/services/sms-encryption.service';
import {
  SmsMarketingHistoryData,
  SmsSendResult,
} from './interfaces/sms-marketing-history.interface';

/**
 * Processor xử lý queue SMS marketing campaigns
 */
@Injectable()
@Processor(QueueName.SMS_MARKETING, { concurrency: 5 })
export class SmsCampaignProcessor extends WorkerHost {
  private readonly logger = new Logger(SmsCampaignProcessor.name);

  constructor(
    private readonly smsMarketingService: SmsMarketingService,
    private readonly smsTemplateService: SmsTemplateService,
    private readonly fprSmsBrandnameService: FptSmsBrandnameService,
    private readonly smsService: SmsService,
    private readonly smsProviderFactory: SmsProviderFactory,
    private readonly smsEncryptionService: SmsEncryptionService,
  ) {
    super();
  }

  /**
   * Xử lý job SMS marketing
   * @param job Job từ queue
   */
  async process(job: Job<SmsMarketingJobDto>): Promise<void> {
    // Xử lý job SMS marketing (user và admin)
    if (job.name !== SmsJobName.SMS_MARKETING && job.name !== SmsJobName.SMS_MARKETING_ADMIN) {
      return; // Bỏ qua job không phải SMS marketing
    }

    const jobData = job.data;

    this.logger.log(
      `Processing SMS marketing job: ${job.id} for campaign ${jobData.campaignId} with ${jobData.recipients.length} recipients`,
    );

    try {
      // Validate job data
      if (!this.validateJobData(jobData)) {
        throw new Error('Invalid job data');
      }

      // Cập nhật trạng thái campaign thành SENDING
      if (jobData.isAdminCampaign) {
        await this.smsMarketingService.updateAdminCampaignStatus(
          jobData.campaignId,
          'SENDING',
          { startedAt: Date.now() },
        );
      } else {
        await this.smsMarketingService.updateCampaignStatus(
          jobData.campaignId,
          'SENDING',
          { startedAt: Date.now() },
        );
      }

      // Sử dụng nội dung SMS đã được xử lý sẵn từ app
      const smsContent = jobData.content;

      if (!smsContent) {
        throw new Error(`SMS content is required for campaign ${jobData.campaignId}`);
      }

      this.logger.debug(
        `Using pre-processed content for campaign ${jobData.campaignId}`,
      );

      // Lấy cấu hình SMS server
      let serverConfig: any;

      if (jobData.smsServerConfig) {
        // Sử dụng config đã được pre-calculated từ app
        this.logger.log(`Using pre-calculated SMS server config for campaign ${jobData.campaignId}`);
        serverConfig = {
          providerName: jobData.smsServerConfig.provider || 'FPT_SMS',
          endpoint: jobData.smsServerConfig.apiUrl,
          additionalSettings: jobData.smsServerConfig.additionalSettings || {},
          integrationId: jobData.smsServerConfig.integrationId,
        };
      } else if (jobData.smsServerId) {
        // Fallback to old method for backward compatibility
        this.logger.log(`Using legacy smsServerId ${jobData.smsServerId} for campaign ${jobData.campaignId}`);
        serverConfig = await this.smsMarketingService.getServerConfigById(
          jobData.smsServerId,
        );
        if (!serverConfig) {
          throw new Error(
            `SMS server configuration not found: ${jobData.smsServerId}`,
          );
        }
      } else {
        throw new Error(
          `No SMS server configuration provided (neither smsServerConfig nor smsServerId)`,
        );
      }

      // Validate server config chỉ hỗ trợ FPT SMS
      const providerName = serverConfig.providerName || serverConfig.provider;
      if (providerName !== SmsProviderType.FPT_SMS) {
        throw new Error(
          `Unsupported SMS provider: ${providerName}. Only FPT_SMS is supported.`,
        );
      }

      let successCount = 0;
      let failedCount = 0;
      const smsHistories: SmsMarketingHistoryData[] = [];

      // Xử lý từng recipient
      for (let i = 0; i < jobData.recipients.length; i++) {
        const recipient = jobData.recipients[i];
        const sentAt = Date.now();

        try {
          // Validate nội dung SMS
          if (!this.smsTemplateService.validateSmsContent(smsContent)) {
            throw new Error('Invalid SMS content');
          }

          // Gửi SMS dựa trên loại campaign
          const sendResult = await this.sendSms(
            recipient.phone,
            smsContent, // Sử dụng content đã được xử lý sẵn
            jobData.campaignType,
            serverConfig,
            jobData.campaignName,
            jobData.providerOptions,
            jobData.scheduledAt,
          );

          if (sendResult.success) {
            successCount++;
            this.logger.debug(
              `SMS sent successfully to ${recipient.phone} for campaign ${jobData.campaignId}`,
            );
          } else {
            failedCount++;
            this.logger.error(
              `Failed to send SMS to ${recipient.phone}: ${sendResult.errorMessage}`,
            );
          }

          // Lưu lịch sử SMS marketing
          smsHistories.push({
            campaignId: jobData.campaignId,
            messageId: sendResult.messageId,
            phone: recipient.phone,
            brandName: sendResult.brandName,
            message: smsContent,
            partnerId: sendResult.partnerId,
            telco: sendResult.telco,
            campaignType: jobData.campaignType,
            status: sendResult.success ? 'SUCCESS' : 'FAILED',
            errorMessage: sendResult.errorMessage,
            errorCode: sendResult.errorCode,
            isVietnameseNumber: sendResult.isVietnameseNumber,
            sentAt,
            senderType: jobData.senderType,
            senderId: jobData.senderId,
          });
        } catch (error) {
          failedCount++;
          this.logger.error(
            `Error sending SMS to ${recipient.phone}: ${error.message}`,
          );

          // Lưu lịch sử lỗi
          smsHistories.push({
            campaignId: jobData.campaignId,
            phone: recipient.phone,
            message: smsContent, // Sử dụng nội dung gốc nếu xử lý template thất bại
            campaignType: jobData.campaignType,
            status: 'FAILED',
            errorMessage: error.message,
            isVietnameseNumber: this.isVietnamesePhoneNumber(recipient.phone),
            sentAt,
            senderType: jobData.senderType,
            senderId: jobData.senderId,
          });
        }

        // Update job progress
        const progress = Math.round(
          ((i + 1) / jobData.recipients.length) * 100,
        );
        await job.updateProgress(progress);
      }

      // Lưu lịch sử SMS marketing theo batch
      if (smsHistories.length > 0) {
        try {
          await this.smsMarketingService.batchSaveSmsMarketingHistory({
            histories: smsHistories,
            batchSize: 100,
          });
          this.logger.log(
            `Saved ${smsHistories.length} SMS marketing history records for campaign ${jobData.campaignId}`,
          );
        } catch (error) {
          this.logger.error(
            `Error saving SMS marketing history: ${error.message}`,
            error.stack,
          );
        }
      }

      // Cập nhật số lượng và trạng thái campaign
      await this.smsMarketingService.updateCampaignCounts(
        jobData.campaignId,
        successCount,
        failedCount,
      );

      const finalStatus =
        failedCount === 0 ? 'SENT' : successCount === 0 ? 'FAILED' : 'SENT';

      if (jobData.isAdminCampaign) {
        await this.smsMarketingService.updateAdminCampaignStatus(
          jobData.campaignId,
          finalStatus,
          { completedAt: Date.now() },
        );
      } else {
        await this.smsMarketingService.updateCampaignStatus(
          jobData.campaignId,
          finalStatus,
          { completedAt: Date.now() },
        );
      }

      this.logger.log(
        `SMS marketing job ${job.id} completed: ${successCount} sent, ${failedCount} failed`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing SMS marketing job ${job.id}: ${error.message}`,
        error.stack,
      );

      // Cập nhật trạng thái campaign thành FAILED
      if (jobData.isAdminCampaign) {
        await this.smsMarketingService.updateAdminCampaignStatus(
          jobData.campaignId,
          'FAILED',
          { completedAt: Date.now() },
        );
      } else {
        await this.smsMarketingService.updateCampaignStatus(
          jobData.campaignId,
          'FAILED',
          { completedAt: Date.now() },
        );
      }

      throw error; // Re-throw để Bull có thể retry
    }
  }

  /**
   * Gửi SMS dựa trên loại campaign
   * @param phone Số điện thoại
   * @param content Nội dung SMS
   * @param campaignType Loại campaign
   * @param serverConfig Cấu hình server
   * @param campaignName Tên campaign (cho brandname SMS)
   * @returns SmsSendResult với thông tin chi tiết
   */
  private async sendSms(
    phone: string,
    content: string,
    campaignType: SmsCampaignType,
    serverConfig: any,
    campaignName?: string,
    providerOptions?: any,
    scheduledAt?: number,
  ): Promise<SmsSendResult> {
    // Kiểm tra số điện thoại có phải Việt Nam hay quốc tế
    const isVietnameseNumber = this.isVietnamesePhoneNumber(phone);
    const brandName = serverConfig.additionalSettings?.brandName || 'REDAI';

    // Xác định provider type từ providerOptions hoặc serverConfig
    const providerType = providerOptions?.provider || (serverConfig.additionalSettings as any)?.provider || serverConfig.providerName;

    try {
      // Xử lý theo provider type
      if (providerType === 'FPT_SMS') {
        return await this.sendFptSms(phone, content, campaignType, serverConfig, campaignName, isVietnameseNumber, brandName, scheduledAt);
      } else if (providerType === 'TWILIO') {
        return await this.sendTwilioSms(phone, content, campaignType, serverConfig, isVietnameseNumber, brandName, providerOptions);
      } else {
        return await this.sendGenericProviderSms(phone, content, campaignType, serverConfig, isVietnameseNumber, brandName, providerOptions);
      }
      // Logic cũ đã được di chuyển vào các method riêng
    } catch (error) {
      this.logger.error(`Error sending SMS to ${phone}: ${error.message}`);
      return {
        success: false,
        errorMessage: error.message,
        isVietnameseNumber,
        brandName,
      };
    }
  }

  /**
   * Tính quota cho campaign dựa trên độ dài message và số lượng recipients
   * @param messageContent Nội dung tin nhắn
   * @param recipientCount Số lượng người nhận
   * @returns Quota cần thiết
   */
  private calculateQuota(messageContent: string, recipientCount: number): number {
    // Mỗi SMS có thể chứa tối đa 160 ký tự (cho ký tự Latin) hoặc 70 ký tự (cho Unicode)
    // Với Base64 encoding, cần tính toán lại
    const maxCharsPerSms = 160; // Giả sử sử dụng ký tự Latin
    const messageLength = messageContent.length;

    // Tính số SMS cần thiết cho 1 tin nhắn
    const smsCountPerMessage = Math.ceil(messageLength / maxCharsPerSms);

    // Tổng quota = số SMS per message × số người nhận
    const totalQuota = smsCountPerMessage * recipientCount;

    this.logger.debug(
      `Calculated quota: messageLength=${messageLength}, smsPerMessage=${smsCountPerMessage}, recipients=${recipientCount}, totalQuota=${totalQuota}`
    );

    return totalQuota;
  }

  /**
   * Validate job data
   * @param jobData Dữ liệu job
   * @returns True nếu valid
   */
  private validateJobData(jobData: SmsMarketingJobDto): boolean {
    if (!jobData.campaignId) {
      this.logger.error('Missing campaignId');
      return false;
    }

    // Validate content
    if (!jobData.content || jobData.content.trim() === '') {
      this.logger.error('Missing or empty content');
      return false;
    }

    if (
      !jobData.recipients ||
      !Array.isArray(jobData.recipients) ||
      jobData.recipients.length === 0
    ) {
      this.logger.error('Missing or empty recipients array');
      return false;
    }

    // Validate SMS server config - phải có ít nhất một trong hai
    if (!jobData.smsServerId && !jobData.smsServerConfig) {
      this.logger.error('Missing both smsServerId and smsServerConfig');
      return false;
    }

    // Nếu có smsServerConfig, validate structure
    if (jobData.smsServerConfig) {
      if (!jobData.smsServerConfig.provider && !jobData.smsServerConfig.providerName) {
        this.logger.error('Missing provider in smsServerConfig');
        return false;
      }
    }

    if (!jobData.campaignType) {
      this.logger.error('Missing campaignType');
      return false;
    }

    // Validate từng recipient
    for (const recipient of jobData.recipients) {
      if (!recipient.phone || !this.isValidPhone(recipient.phone)) {
        this.logger.error(`Invalid phone: ${recipient.phone}`);
        return false;
      }
    }

    return true;
  }

  /**
   * Kiểm tra số điện thoại có phải là số Việt Nam hay không
   * @param phone Số điện thoại cần kiểm tra
   * @returns true nếu là số Việt Nam, false nếu là số quốc tế
   */
  private isVietnamesePhoneNumber(phone: string): boolean {
    // Loại bỏ tất cả ký tự không phải số
    const cleanNumber = phone.replace(/\D/g, '');

    // Patterns cho số điện thoại Việt Nam
    const vietnamesePatterns = {
      // Số di động: 84 + (3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9]) + 7 số
      mobile: /^(84)(3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9])\d{7}$/,
      // Số di động bắt đầu bằng 0: 0 + (3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9]) + 7 số
      mobileWithZero: /^(0)(3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9])\d{7}$/,
      // Số cố định: 84 + 2[0-9] + 7-8 số (tổng 10-11 số)
      landline: /^(84)(2[0-9])\d{7,8}$/,
      // Số cố định bắt đầu bằng 0: 0 + 2[0-9] + 7-8 số (tổng 10-11 số)
      landlineWithZero: /^(0)(2[0-9])\d{7,8}$/,
    };

    return (
      vietnamesePatterns.mobile.test(cleanNumber) ||
      vietnamesePatterns.mobileWithZero.test(cleanNumber) ||
      vietnamesePatterns.landline.test(cleanNumber) ||
      vietnamesePatterns.landlineWithZero.test(cleanNumber)
    );
  }

  /**
   * Validate phone number format
   * @param phone Phone number cần validate
   * @returns True nếu phone hợp lệ
   */
  private isValidPhone(phone: string): boolean {
    // Regex cho số điện thoại Việt Nam
    const phoneRegex = /^(\+84|84|0)(3|5|7|8|9)[0-9]{8}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  }

  /**
   * Xử lý khi job failed
   * @param job Job bị failed
   * @param err Lỗi
   */
  async onFailed(job: Job<SmsMarketingJobDto>, err: Error): Promise<void> {
    if (job.name !== SmsJobName.SMS_MARKETING) {
      return;
    }

    this.logger.error(
      `SMS marketing job ${job.id} failed: ${err.message}`,
      err.stack,
    );

    const jobData = job.data;

    // Cập nhật trạng thái campaign thành FAILED
    try {
      if (jobData.isAdminCampaign) {
        await this.smsMarketingService.updateAdminCampaignStatus(
          jobData.campaignId,
          'FAILED',
          { completedAt: Date.now() },
        );
      } else {
        await this.smsMarketingService.updateCampaignStatus(
          jobData.campaignId,
          'FAILED',
          { completedAt: Date.now() },
        );
      }
    } catch (updateError) {
      this.logger.error(
        `Error updating campaign status on job failure: ${updateError.message}`,
      );
    }
  }

  /**
   * Xử lý khi job completed
   * @param job Job đã hoàn thành
   */
  async onCompleted(job: Job<SmsMarketingJobDto>): Promise<void> {
    if (job.name !== SmsJobName.SMS_MARKETING && job.name !== SmsJobName.SMS_MARKETING_ADMIN) {
      return;
    }

    const jobData = job.data;
    this.logger.debug(
      `SMS marketing job ${job.id} completed for campaign ${jobData.campaignId}`,
    );
  }

  /**
   * Xử lý khi job active
   * @param job Job đang xử lý
   */
  async onActive(job: Job<SmsMarketingJobDto>): Promise<void> {
    if (job.name !== SmsJobName.SMS_MARKETING && job.name !== SmsJobName.SMS_MARKETING_ADMIN) {
      return;
    }

    const jobData = job.data;
    this.logger.debug(
      `SMS marketing job ${job.id} started for campaign ${jobData.campaignId}`,
    );
  }

  /**
   * Gửi SMS qua FPT SMS provider
   */
  private async sendFptSms(
    phone: string,
    content: string,
    campaignType: SmsCampaignType,
    serverConfig: any,
    campaignName?: string,
    isVietnameseNumber: boolean = false,
    brandName: string = '',
    scheduledAt?: number,
  ): Promise<SmsSendResult> {
    if (campaignType === SmsCampaignType.OTP) {
      if (isVietnameseNumber) {
        // Kiểm tra và mã hóa Base64 nếu cần cho SMS OTP trong nước
        let messageContent = content;
        if (!this.smsEncryptionService.isBase64Encoded(content)) {
          messageContent = this.smsEncryptionService.encryptSmsContent(content);
          this.logger.debug(`Content encoded to Base64 for domestic OTP SMS to ${phone}`);
        } else {
          this.logger.debug(`Content already Base64 encoded for domestic OTP SMS to ${phone}`);
        }

        // Gửi SMS OTP trong nước
        const result = await this.fprSmsBrandnameService.sendOtp({
          BrandName: brandName,
          Phone: phone,
          Message: messageContent,
        });

        // Kiểm tra lỗi từ FPT SMS API
        if (result.error && result.error !== 0) {
          return {
            success: false,
            errorMessage: result.error_description || 'Unknown error',
            errorCode: result.error,
            isVietnameseNumber,
            brandName,
          };
        }

        this.logger.debug(`OTP SMS sent successfully: ${result.MessageId}`);
        return {
          success: true,
          messageId: result.MessageId?.toString(),
          brandName,
          partnerId: result.PartnerId,
          telco: result.Telco,
          isVietnameseNumber,
        };
      } else {
        // Gửi SMS OTP quốc tế
        const result = await this.fprSmsBrandnameService.sendInternationalSms(
          phone,
          content,
          {
            brandName,
            requestId: `sms_marketing_${Date.now()}`,
          },
        );

        if (!result.success) {
          return {
            success: false,
            errorMessage: result.errorMessage || 'Unknown error',
            isVietnameseNumber,
            brandName,
          };
        }

        this.logger.debug(
          `International OTP SMS sent successfully: ${result.messageId}`,
        );
        return {
          success: true,
          messageId: result.messageId?.toString(),
          brandName,
          partnerId: result.rawResponse?.PartnerId,
          telco: result.rawResponse?.Telco,
          isVietnameseNumber,
        };
      }
    } else {
      // Gửi SMS campaign ads (brandname) - chỉ hỗ trợ trong nước
      if (!isVietnameseNumber) {
        return {
          success: false,
          errorMessage: 'Campaign ads SMS chỉ hỗ trợ số điện thoại Việt Nam',
          isVietnameseNumber,
          brandName,
        };
      }

      // Kiểm tra và mã hóa Base64 nếu cần cho SMS Campaign ads
      let messageContent = content;
      if (!this.smsEncryptionService.isBase64Encoded(content)) {
        messageContent = this.smsEncryptionService.encryptSmsContent(content);
        this.logger.debug(`Content encoded to Base64 for campaign ads SMS to ${phone}`);
      } else {
        this.logger.debug(`Content already Base64 encoded for campaign ads SMS to ${phone}`);
      }

      // Bước 1: Tạo campaign
      // Đối với ADS campaign, scheduledAt là bắt buộc
      if (!scheduledAt) {
        throw new Error('ADS campaign requires scheduledAt');
      }

      // Tính quota dựa trên độ dài message và số lượng recipients
      const quota = this.calculateQuota(messageContent, 1); // 1 recipient cho mỗi job

      // Format ScheduleTime theo yêu cầu FPT: yyyy-mm-dd HH:ii
      const scheduleTime = new Date(scheduledAt * 1000)
        .toISOString()
        .slice(0, 16)
        .replace('T', ' ');

      const campaignPayload = {
        CampaignName: campaignName || `Campaign_${Date.now()}`,
        BrandName: brandName,
        Message: messageContent,
        ScheduleTime: scheduleTime,
        Quota: quota,
      };

      this.logger.debug(
        `Creating campaign with payload: ${JSON.stringify(campaignPayload)}`,
      );

      const campaignResult = await this.fprSmsBrandnameService.createCampaign(
        campaignPayload,
      );

      this.logger.debug(
        `Campaign created successfully: ${campaignResult.CampaignCode}`,
      );

      // Bước 2: Gửi ads
      const adsResult = await this.fprSmsBrandnameService.sendAds({
        CampaignCode: campaignResult.CampaignCode,
        PhoneList: phone,
      });

      // Kiểm tra kết quả gửi ads
      if (adsResult.FailureCount > 0) {
        const failedDetail = adsResult.Details?.find(
          (detail) => detail.Phone === phone,
        );
        return {
          success: false,
          errorMessage: failedDetail?.ErrorMessage || 'Unknown error',
          isVietnameseNumber,
          brandName,
        };
      }

      // Lấy thông tin chi tiết từ kết quả thành công
      const successDetail = adsResult.Details.find(
        (detail) => detail.Phone === phone,
      );

      this.logger.debug(
        `Brandname SMS sent successfully. Total sent: ${adsResult.TotalSent}, Success: ${adsResult.SuccessCount}`,
      );

      return {
        success: true,
        messageId: successDetail?.MessageId?.toString(),
        brandName,
        isVietnameseNumber,
      };
    }
  }

  /**
   * Gửi SMS qua Twilio provider
   */
  private async sendTwilioSms(
    phone: string,
    content: string,
    campaignType: SmsCampaignType,
    serverConfig: any,
    isVietnameseNumber: boolean = false,
    brandName: string = 'TWILIO',
    providerOptions?: any,
  ): Promise<SmsSendResult> {
    try {
      // Chuẩn bị Twilio options từ providerOptions hoặc server config
      const twilioOptions = providerOptions || this.prepareTwilioOptions(serverConfig);

      // Gửi SMS qua Twilio provider
      const result = await this.smsService.sendSms(phone, content, {
        providerType: 'TWILIO' as SmsProviderType,
        ...twilioOptions,
      });

      if (result.success) {
        this.logger.debug(`Twilio SMS sent successfully: ${result.messageId}`);
        return {
          success: true,
          messageId: result.messageId,
          brandName: brandName || 'TWILIO',
          isVietnameseNumber,
        };
      } else {
        return {
          success: false,
          errorMessage: result.errorMessage || 'Unknown Twilio error',
          isVietnameseNumber,
          brandName: brandName || 'TWILIO',
        };
      }
    } catch (error) {
      this.logger.error(`Error sending Twilio SMS to ${phone}: ${error.message}`);
      return {
        success: false,
        errorMessage: error.message,
        isVietnameseNumber,
        brandName: brandName || 'TWILIO',
      };
    }
  }

  /**
   * Gửi SMS qua generic provider (không phải FPT SMS hay Twilio)
   */
  private async sendGenericProviderSms(
    phone: string,
    content: string,
    campaignType: SmsCampaignType,
    serverConfig: any,
    isVietnameseNumber: boolean = false,
    brandName: string = 'GENERIC',
    providerOptions?: any,
  ): Promise<SmsSendResult> {
    try {
      // Xác định provider type từ config
      const providerType = (serverConfig.additionalSettings as any)?.provider || serverConfig.providerName;

      // Chuẩn bị options từ providerOptions hoặc server config
      const finalProviderOptions = providerOptions || this.prepareGenericProviderOptions(serverConfig);

      // Gửi SMS qua generic provider
      const result = await this.smsService.sendSms(phone, content, {
        providerType: providerType as SmsProviderType,
        ...finalProviderOptions,
      });

      if (result.success) {
        this.logger.debug(`${providerType} SMS sent successfully: ${result.messageId}`);
        return {
          success: true,
          messageId: result.messageId,
          brandName: brandName || providerType,
          isVietnameseNumber,
        };
      } else {
        return {
          success: false,
          errorMessage: result.errorMessage || `Unknown ${providerType} error`,
          isVietnameseNumber,
          brandName: brandName || providerType,
        };
      }
    } catch (error) {
      this.logger.error(`Error sending generic provider SMS to ${phone}: ${error.message}`);
      return {
        success: false,
        errorMessage: error.message,
        isVietnameseNumber,
        brandName: brandName || 'GENERIC',
      };
    }
  }

  /**
   * Chuẩn bị Twilio options từ server config
   */
  private prepareTwilioOptions(serverConfig: any): any {
    const settings = serverConfig.additionalSettings as any;

    const options: any = {
      accountSid: settings?.accountSid || serverConfig.apiKey,
      authToken: settings?.authToken,
    };

    // Thêm phone number hoặc messaging service SID
    if (settings?.messagingServiceSid) {
      options.messagingServiceSid = settings.messagingServiceSid;
    } else if (settings?.phoneNumber) {
      options.from = settings.phoneNumber;
    }

    // Thêm status callback URL nếu có
    if (settings?.statusCallbackUrl) {
      options.statusCallback = settings.statusCallbackUrl;
    }

    return options;
  }

  /**
   * Chuẩn bị generic provider options từ server config
   */
  private prepareGenericProviderOptions(serverConfig: any): any {
    const settings = serverConfig.additionalSettings as any;

    const options: any = {
      apiKey: serverConfig.apiKey,
      apiSecret: serverConfig.apiSecret,
    };

    // Thêm các settings bổ sung nếu có
    if (settings) {
      Object.keys(settings).forEach(key => {
        if (key !== 'provider') {
          options[key] = settings[key];
        }
      });
    }

    return options;
  }
}
