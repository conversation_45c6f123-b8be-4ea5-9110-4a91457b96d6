import { Processor, WorkerHost, OnWorkerEvent } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName, WebhookJobName } from '../../queue';

/**
 * Interface cho Zalo webhook job data - Updated for new queue system
 */
interface ZaloWebhookJobData {
  id: string;
  type: string; // ZaloQueueType
  jobName: string; // ZaloQueueJobName
  eventName: string;
  event: any; // ZaloWebhookEventUnion from SDK
  context: {
    oaId: string;
    userId?: string;
    integrationId?: string;
  };
  priority: number;
  retryCount: number;
  metadata: {
    oaId: string;
    userId?: string;
    integrationId?: string;
    timestamp: number;
    source: 'webhook' | 'retry' | 'manual';
    handlerName: string;
    originalEventId?: string;
    correlationId?: string;
  };
}

/**
 * Legacy interface for backward compatibility
 */
interface LegacyZaloWebhookJobData {
  zaloEvent: any;
  oaId: string;
  integrationId?: string;
  userId?: number;
  metadata?: {
    eventId?: string;
    timestamp: number;
  };
}

/**
 * Processor xử lý Zalo webhook events trong worker - Updated for new queue system
 */
@Processor(QueueName.WEBHOOK, {
  concurrency: 10, // Xử lý tối đa 10 jobs đồng thời
})
export class ZaloWebhookProcessor extends WorkerHost {
  private readonly logger = new Logger(ZaloWebhookProcessor.name);

  /**
   * Xử lý Zalo webhook job - Support both new and legacy formats
   */
  async process(job: Job<ZaloWebhookJobData | LegacyZaloWebhookJobData>): Promise<any> {
    // Check if this is a new format job
    if (this.isNewFormatJob(job.data)) {
      return this.processNewFormatJob(job as Job<ZaloWebhookJobData>);
    }

    // Handle legacy format
    if (job.name !== WebhookJobName.PROCESS_ZALO_WEBHOOK) {
      return; // Skip job này
    }

    return this.processLegacyFormatJob(job as Job<LegacyZaloWebhookJobData>);
  }

  /**
   * Check if job data is new format
   */
  private isNewFormatJob(data: any): data is ZaloWebhookJobData {
    return data && typeof data === 'object' && 'jobName' in data && 'eventName' in data && 'context' in data;
  }

  /**
   * Process new format job with queue system
   */
  private async processNewFormatJob(job: Job<ZaloWebhookJobData>): Promise<any> {
    const { event, context, metadata, jobName, eventName } = job.data;

    this.logger.log(
      `Processing NEW FORMAT Zalo webhook job ${job.id}: ${eventName} for OA ${context.oaId}, JobName: ${jobName}`
    );

    try {
      // Route to appropriate processor based on job name
      const result = await this.routeToProcessor(jobName, event, context);

      this.logger.log(
        `Successfully processed NEW FORMAT job ${job.id}: ${eventName}, Handler: ${metadata.handlerName}`
      );

      return {
        success: true,
        eventType: eventName,
        jobName,
        oaId: context.oaId,
        handlerName: metadata.handlerName,
        processingTime: Date.now() - metadata.timestamp,
        result,
      };
    } catch (error) {
      this.logger.error(
        `Failed to process NEW FORMAT job ${job.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Process legacy format job (backward compatibility)
   */
  private async processLegacyFormatJob(job: Job<LegacyZaloWebhookJobData>): Promise<any> {
    const { zaloEvent, oaId, integrationId, userId, metadata } = job.data;

    this.logger.debug(
      `Processing LEGACY FORMAT Zalo webhook job ${job.id}: ${zaloEvent.event_name} for OA ${oaId}`
    );

    try {
      // Xử lý theo loại event (legacy method)
      const result = await this.processZaloEvent(zaloEvent, oaId, integrationId, userId);

      this.logger.log(
        `Successfully processed LEGACY FORMAT job ${job.id}: ${zaloEvent.event_name}`
      );

      return {
        success: true,
        eventType: zaloEvent.event_name,
        oaId,
        processingTime: Date.now() - (metadata?.timestamp || Date.now()),
        result,
        format: 'legacy'
      };
    } catch (error) {
      this.logger.error(
        `Failed to process LEGACY FORMAT job ${job.id}: ${error.message}`,
        error.stack
      );

      throw error; // BullMQ sẽ retry job nếu có lỗi
    }
  }

  /**
   * Route to appropriate processor based on job name (NEW QUEUE SYSTEM)
   */
  private async routeToProcessor(jobName: string, event: any, context: any): Promise<any> {
    switch (jobName) {
      // Real-time processing
      case 'process-user-message':
        return this.processUserMessageJob(event, context);

      case 'process-user-interaction':
        return this.processUserInteractionJob(event, context);

      case 'process-follow-event':
        return this.processFollowEventJob(event, context);

      // Business logic processing
      case 'process-order':
        return this.processOrderJob(event, context);

      case 'process-user-info':
        return this.processUserInfoJob(event, context);

      case 'process-feedback':
        return this.processFeedbackJob(event, context);

      case 'process-call-event':
        return this.processCallEventJob(event, context);

      case 'process-consent':
        return this.processConsentJob(event, context);

      // Analytics processing
      case 'track-message-status':
        return this.trackMessageStatusJob(event, context);

      case 'track-oa-message':
        return this.trackOAMessageJob(event, context);

      case 'track-interaction':
        return this.trackInteractionJob(event, context);

      // Background processing
      case 'process-template-event':
        return this.processTemplateEventJob(event, context);

      case 'process-system-event':
        return this.processSystemEventJob(event, context);

      case 'process-group-management':
        return this.processGroupManagementJob(event, context);

      // Fallback to legacy processing
      default:
        this.logger.warn(`Unknown job name: ${jobName}, falling back to legacy processing`);
        return this.processZaloEvent(event, context.oaId, context.integrationId, context.userId);
    }
  }

  /**
   * Xử lý Zalo event theo loại
   */
  private async processZaloEvent(
    zaloEvent: any,
    oaId: string,
    integrationId?: string,
    userId?: number,
  ): Promise<any> {
    const eventType = zaloEvent.event_name;

    switch (eventType) {
      // User message events
      case 'user_send_text':
        return this.processUserSendText(zaloEvent, oaId);
      
      case 'user_send_image':
        return this.processUserSendImage(zaloEvent, oaId);
      
      case 'user_send_file':
        return this.processUserSendFile(zaloEvent, oaId);
      
      case 'user_send_sticker':
        return this.processUserSendSticker(zaloEvent, oaId);

      // Follow/Unfollow events
      case 'follow':
        return this.processUserFollow(zaloEvent, oaId);
      
      case 'unfollow':
        return this.processUserUnfollow(zaloEvent, oaId);

      // Message status events
      case 'user_received_message':
        return this.processUserReceivedMessage(zaloEvent, oaId);
      
      case 'user_seen_message':
        return this.processUserSeenMessage(zaloEvent, oaId);

      // Group events
      case 'create_group':
        return this.processCreateGroup(zaloEvent, oaId);
      
      case 'user_join_group':
        return this.processUserJoinGroup(zaloEvent, oaId);

      // ZNS events
      case 'user_received_zns_message':
        return this.processUserReceivedZNS(zaloEvent, oaId);
      
      case 'change_oa_daily_quota':
        return this.processQuotaChange(zaloEvent, oaId);

      // Default case
      default:
        this.logger.warn(`Unhandled Zalo event type: ${eventType}`);
        return this.processGenericEvent(zaloEvent, oaId);
    }
  }

  /**
   * Xử lý user send text event
   */
  private async processUserSendText(zaloEvent: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_send_text for OA ${oaId}`);

    // TODO: Implement business logic
    // - Lưu message vào database
    // - Trigger AI response nếu cần
    // - Update conversation state
    // - Send analytics event

    return {
      action: 'user_send_text_processed',
      messageId: zaloEvent.message?.msg_id,
      text: zaloEvent.message?.text,
      senderId: zaloEvent.sender?.id,
    };
  }

  /**
   * Xử lý user send image event
   */
  private async processUserSendImage(zaloEvent: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_send_image for OA ${oaId}`);

    // TODO: Implement business logic
    // - Download và lưu image
    // - Extract metadata
    // - Trigger image analysis nếu cần

    return {
      action: 'user_send_image_processed',
      messageId: zaloEvent.message?.msg_id,
      attachments: zaloEvent.message?.attachments,
      senderId: zaloEvent.sender?.id,
    };
  }

  /**
   * Xử lý user send file event
   */
  private async processUserSendFile(zaloEvent: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_send_file for OA ${oaId}`);

    // TODO: Implement business logic
    // - Download và lưu file
    // - Virus scan
    // - Extract metadata

    return {
      action: 'user_send_file_processed',
      messageId: zaloEvent.message?.msg_id,
      attachments: zaloEvent.message?.attachments,
      senderId: zaloEvent.sender?.id,
    };
  }

  /**
   * Xử lý user send sticker event
   */
  private async processUserSendSticker(zaloEvent: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_send_sticker for OA ${oaId}`);

    return {
      action: 'user_send_sticker_processed',
      messageId: zaloEvent.message?.msg_id,
      attachments: zaloEvent.message?.attachments,
      senderId: zaloEvent.sender?.id,
    };
  }

  /**
   * Xử lý user follow event
   */
  private async processUserFollow(zaloEvent: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing follow for OA ${oaId}`);

    // TODO: Implement business logic
    // - Lưu follower vào database
    // - Send welcome message
    // - Add to marketing segments
    // - Update analytics

    return {
      action: 'user_follow_processed',
      followerId: zaloEvent.follower?.id,
      userIdByApp: zaloEvent.user_id_by_app,
      source: zaloEvent.source,
    };
  }

  /**
   * Xử lý user unfollow event
   */
  private async processUserUnfollow(zaloEvent: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing unfollow for OA ${oaId}`);

    // TODO: Implement business logic
    // - Update follower status
    // - Remove from marketing segments
    // - Update analytics

    return {
      action: 'user_unfollow_processed',
      followerId: zaloEvent.follower?.id,
      userIdByApp: zaloEvent.user_id_by_app,
      source: zaloEvent.source,
    };
  }

  /**
   * Xử lý user received message event
   */
  private async processUserReceivedMessage(zaloEvent: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_received_message for OA ${oaId}`);

    // TODO: Update message delivery status

    return {
      action: 'user_received_message_processed',
      messageId: zaloEvent.message?.msg_id,
    };
  }

  /**
   * Xử lý user seen message event
   */
  private async processUserSeenMessage(zaloEvent: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_seen_message for OA ${oaId}`);

    // TODO: Update message read status

    return {
      action: 'user_seen_message_processed',
      messageIds: zaloEvent.message?.msg_ids,
    };
  }

  /**
   * Xử lý create group event
   */
  private async processCreateGroup(zaloEvent: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing create_group for OA ${oaId}`);

    // TODO: Lưu group info vào database

    return {
      action: 'create_group_processed',
      groupId: zaloEvent.group_id,
    };
  }

  /**
   * Xử lý user join group event
   */
  private async processUserJoinGroup(zaloEvent: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_join_group for OA ${oaId}`);

    // TODO: Update group membership

    return {
      action: 'user_join_group_processed',
      groupId: zaloEvent.group_id,
      users: zaloEvent.users,
    };
  }

  /**
   * Xử lý user received ZNS event
   */
  private async processUserReceivedZNS(zaloEvent: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_received_zns_message for OA ${oaId}`);

    // TODO: Update ZNS delivery status

    return {
      action: 'user_received_zns_processed',
      messageId: zaloEvent.message?.msg_id,
      deliveryTime: zaloEvent.message?.delivery_time,
      trackingId: zaloEvent.message?.tracking_id,
    };
  }

  /**
   * Xử lý quota change event
   */
  private async processQuotaChange(zaloEvent: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing change_oa_daily_quota for OA ${oaId}`);

    // TODO: Update quota info in database

    return {
      action: 'quota_change_processed',
      quota: zaloEvent.quota,
    };
  }

  /**
   * Xử lý generic event (fallback)
   */
  private async processGenericEvent(zaloEvent: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing generic event ${zaloEvent.event_name} for OA ${oaId}`);

    // TODO: Log event for analysis

    return {
      action: 'generic_event_processed',
      eventType: zaloEvent.event_name,
      data: zaloEvent,
    };
  }

  // =============================================================================
  // NEW QUEUE SYSTEM PROCESSORS
  // =============================================================================

  /**
   * Process user message jobs (Real-time priority)
   */
  private async processUserMessageJob(event: any, context: any): Promise<any> {
    const eventType = event.event_name;
    this.logger.log(`Processing user message job: ${eventType} for OA ${context.oaId}`);

    switch (eventType) {
      case 'user_send_text':
        return this.processUserSendText(event, context.oaId);
      case 'user_send_image':
        return this.processUserSendImage(event, context.oaId);
      case 'user_send_audio':
        return this.processUserSendAudio(event, context.oaId);
      case 'user_send_video':
        return this.processUserSendVideo(event, context.oaId);
      case 'user_send_file':
        return this.processUserSendFile(event, context.oaId);
      case 'user_send_sticker':
        return this.processUserSendSticker(event, context.oaId);
      case 'user_send_gif':
        return this.processUserSendGif(event, context.oaId);
      case 'user_send_location':
        return this.processUserSendLocation(event, context.oaId);
      case 'user_send_link':
        return this.processUserSendLink(event, context.oaId);
      case 'user_send_business_card':
        return this.processUserSendBusinessCard(event, context.oaId);
      default:
        return this.processGenericEvent(event, context.oaId);
    }
  }

  /**
   * Process user interaction jobs (Real-time priority)
   */
  private async processUserInteractionJob(event: any, context: any): Promise<any> {
    const eventType = event.event_name;
    this.logger.log(`Processing user interaction job: ${eventType} for OA ${context.oaId}`);

    switch (eventType) {
      case 'user_reacted_message':
        return this.processUserReactedMessage(event, context.oaId);
      case 'user_click_chatnow':
        return this.processUserClickChatNow(event, context.oaId);
      case 'oa_reacted_message':
        return this.processOAReactedMessage(event, context.oaId);
      default:
        return this.processGenericEvent(event, context.oaId);
    }
  }

  /**
   * Process follow event jobs (Real-time priority)
   */
  private async processFollowEventJob(event: any, context: any): Promise<any> {
    const eventType = event.event_name;
    this.logger.log(`Processing follow event job: ${eventType} for OA ${context.oaId}`);

    switch (eventType) {
      case 'follow':
        return this.processUserFollow(event, context.oaId);
      case 'unfollow':
        return this.processUserUnfollow(event, context.oaId);
      default:
        return this.processGenericEvent(event, context.oaId);
    }
  }

  /**
   * Process order jobs (Business logic priority)
   */
  private async processOrderJob(event: any, context: any): Promise<any> {
    this.logger.log(`Processing order job: ${event.event_name} for OA ${context.oaId}`);

    // TODO: Implement order processing logic
    // - Process order details
    // - Update inventory
    // - Send confirmations
    // - Update customer data

    return {
      action: 'order_job_processed',
      eventType: event.event_name,
      customerId: event.customer_id,
      oaId: context.oaId,
    };
  }

  /**
   * Process user info jobs (Business logic priority)
   */
  private async processUserInfoJob(event: any, context: any): Promise<any> {
    this.logger.log(`Processing user info job: ${event.event_name} for OA ${context.oaId}`);

    // TODO: Implement user info processing logic
    // - Update user profile
    // - Validate information
    // - Trigger workflows

    return {
      action: 'user_info_job_processed',
      eventType: event.event_name,
      userIdByApp: event.user_id_by_app,
      oaId: context.oaId,
    };
  }

  /**
   * Process feedback jobs (Business logic priority)
   */
  private async processFeedbackJob(event: any, context: any): Promise<any> {
    this.logger.log(`Processing feedback job: ${event.event_name} for OA ${context.oaId}`);

    // TODO: Implement feedback processing logic
    // - Save feedback
    // - Update satisfaction metrics
    // - Notify customer service

    return {
      action: 'feedback_job_processed',
      eventType: event.event_name,
      rate: event.rate,
      note: event.note,
      oaId: context.oaId,
    };
  }

  /**
   * Process call event jobs (Business logic priority)
   */
  private async processCallEventJob(event: any, context: any): Promise<any> {
    this.logger.log(`Processing call event job: ${event.event_name} for OA ${context.oaId}`);

    // TODO: Implement call event processing logic
    // - Log call details
    // - Update call analytics
    // - Route to agents

    return {
      action: 'call_event_job_processed',
      eventType: event.event_name,
      callId: event.call_id,
      phone: event.phone,
      oaId: context.oaId,
    };
  }

  /**
   * Process consent jobs (Business logic priority)
   */
  private async processConsentJob(event: any, context: any): Promise<any> {
    this.logger.log(`Processing consent job: ${event.event_name} for OA ${context.oaId}`);

    // TODO: Implement consent processing logic
    // - Update consent status
    // - Grant/revoke permissions
    // - Compliance tracking

    return {
      action: 'consent_job_processed',
      eventType: event.event_name,
      phone: event.phone,
      oaId: context.oaId,
    };
  }

  /**
   * Track message status jobs (Analytics priority)
   */
  private async trackMessageStatusJob(event: any, context: any): Promise<any> {
    this.logger.debug(`Tracking message status job: ${event.event_name} for OA ${context.oaId}`);

    // TODO: Implement message status tracking
    // - Update delivery status
    // - Calculate read rates
    // - Analytics

    return {
      action: 'message_status_tracked',
      eventType: event.event_name,
      messageId: event.message?.msg_id,
      oaId: context.oaId,
    };
  }

  /**
   * Track OA message jobs (Analytics priority)
   */
  private async trackOAMessageJob(event: any, context: any): Promise<any> {
    this.logger.debug(`Tracking OA message job: ${event.event_name} for OA ${context.oaId}`);

    // TODO: Implement OA message tracking
    // - Track outbound messages
    // - Update metrics
    // - Billing tracking

    return {
      action: 'oa_message_tracked',
      eventType: event.event_name,
      messageId: event.message?.msg_id,
      oaId: context.oaId,
    };
  }

  /**
   * Track interaction jobs (Analytics priority)
   */
  private async trackInteractionJob(event: any, context: any): Promise<any> {
    this.logger.debug(`Tracking interaction job: ${event.event_name} for OA ${context.oaId}`);

    // TODO: Implement interaction tracking
    // - Track user interactions
    // - Update engagement metrics
    // - Analytics

    return {
      action: 'interaction_tracked',
      eventType: event.event_name,
      oaId: context.oaId,
    };
  }

  /**
   * Process template event jobs (Background priority)
   */
  private async processTemplateEventJob(event: any, context: any): Promise<any> {
    this.logger.debug(`Processing template event job: ${event.event_name} for OA ${context.oaId}`);

    // TODO: Implement template event processing
    // - Update template status
    // - Sync with Zalo
    // - Notify administrators

    return {
      action: 'template_event_processed',
      eventType: event.event_name,
      oaId: context.oaId,
    };
  }

  /**
   * Process system event jobs (Background priority)
   */
  private async processSystemEventJob(event: any, context: any): Promise<any> {
    this.logger.debug(`Processing system event job: ${event.event_name} for OA ${context.oaId}`);

    // TODO: Implement system event processing
    // - Update system status
    // - Handle permissions
    // - Widget management

    return {
      action: 'system_event_processed',
      eventType: event.event_name,
      oaId: context.oaId,
    };
  }

  /**
   * Process group management jobs (Background priority)
   */
  private async processGroupManagementJob(event: any, context: any): Promise<any> {
    this.logger.debug(`Processing group management job: ${event.event_name} for OA ${context.oaId}`);

    // TODO: Implement group management processing
    // - Update group status
    // - Manage members
    // - Group analytics

    return {
      action: 'group_management_processed',
      eventType: event.event_name,
      groupId: event.group_id,
      oaId: context.oaId,
    };
  }

  // =============================================================================
  // NEW EVENT PROCESSORS (Missing from legacy)
  // =============================================================================

  private async processUserSendAudio(event: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_send_audio for OA ${oaId}`);

    // TODO: Implement audio processing
    // - Download và lưu audio
    // - Speech-to-text conversion
    // - Audio analysis

    return {
      action: 'user_send_audio_processed',
      messageId: event.message?.msg_id,
      attachments: event.message?.attachments,
      senderId: event.sender?.id,
    };
  }

  private async processUserSendVideo(event: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_send_video for OA ${oaId}`);

    // TODO: Implement video processing
    // - Download và lưu video
    // - Extract metadata
    // - Video processing/transcoding

    return {
      action: 'user_send_video_processed',
      messageId: event.message?.msg_id,
      attachments: event.message?.attachments,
      senderId: event.sender?.id,
    };
  }

  private async processUserSendGif(event: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_send_gif for OA ${oaId}`);

    return {
      action: 'user_send_gif_processed',
      messageId: event.message?.msg_id,
      attachments: event.message?.attachments,
      senderId: event.sender?.id,
    };
  }

  private async processUserSendLocation(event: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_send_location for OA ${oaId}`);

    return {
      action: 'user_send_location_processed',
      messageId: event.message?.msg_id,
      coordinates: event.message?.coordinates,
      senderId: event.sender?.id,
    };
  }

  private async processUserSendLink(event: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_send_link for OA ${oaId}`);

    return {
      action: 'user_send_link_processed',
      messageId: event.message?.msg_id,
      links: event.message?.links,
      senderId: event.sender?.id,
    };
  }

  private async processUserSendBusinessCard(event: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_send_business_card for OA ${oaId}`);

    return {
      action: 'user_send_business_card_processed',
      messageId: event.message?.msg_id,
      businessCard: event.message?.business_card,
      senderId: event.sender?.id,
    };
  }

  private async processUserReactedMessage(event: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_reacted_message for OA ${oaId}`);

    return {
      action: 'user_reacted_message_processed',
      messageId: event.message?.msg_id,
      reaction: event.message?.react_icon,
      senderId: event.sender?.id,
    };
  }

  private async processUserClickChatNow(event: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing user_click_chatnow for OA ${oaId}`);

    return {
      action: 'user_click_chatnow_processed',
      userIdByApp: event.user_id_by_app,
      oaId: event.oa_id,
    };
  }

  private async processOAReactedMessage(event: any, oaId: string): Promise<any> {
    this.logger.debug(`Processing oa_reacted_message for OA ${oaId}`);

    return {
      action: 'oa_reacted_message_processed',
      messageId: event.message?.msg_id,
      reaction: event.message?.react_icon,
      senderId: event.sender?.id,
    };
  }

  /**
   * Event handlers
   */
  @OnWorkerEvent('completed')
  onCompleted(job: Job<ZaloWebhookJobData | LegacyZaloWebhookJobData>) {
    this.logger.debug(`Zalo webhook job ${job.id} completed`);
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job<ZaloWebhookJobData | LegacyZaloWebhookJobData>, error: Error) {
    this.logger.error(
      `Zalo webhook job ${job.id} failed: ${error.message}`,
      error.stack
    );
  }

  @OnWorkerEvent('progress')
  onProgress(job: Job<ZaloWebhookJobData | LegacyZaloWebhookJobData>, progress: number) {
    this.logger.debug(`Zalo webhook job ${job.id} progress: ${progress}%`);
  }
}
