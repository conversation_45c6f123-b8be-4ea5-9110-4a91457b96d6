/**
 * @file Interface cho Switch node
 * 
 * Định nghĩa type-safe interface cho node Switch bao gồm:
 * - Parameters structure với validation
 * - Properties definition
 * - Input/Output data types
 * - Switch cases và routing logic
 */

import {
    IBaseNodeInput,
    IBaseNodeOutput,
    ITypedNodeExecution
} from '../../execute.interface';
import {
    EDataType,
    ICondition,
    operatorNeedsValue
} from '../shared/condition-evaluation.interface';

// =================================================================
// SECTION 1: ENUMS & TYPES
// Switch-specific enums (sử dụng shared logic từ condition-evaluation)
// =================================================================

/**
 * Switch modes - cách thức hoạt động của switch
 */
export enum ESwitchMode {
    /** So sánh điều kiện đơn gi<PERSON>n (sử dụng ICondition) */
    CONDITION = 'condition',

    /** Sử dụng expression để evaluate */
    EXPRESSION = 'expression',

    /** Route dựa trên data type của field value */
    DATA_TYPE = 'data_type'
}

// =================================================================
// SECTION 2: SWITCH CASE STRUCTURES
// Sử dụng shared ICondition để tránh duplicate logic
// =================================================================

/**
 * Single switch case - Simplified bằng cách sử dụng shared ICondition
 */
export interface ISwitchCase {
    /** ID duy nhất của case */
    id: string;

    /** Tên hiển thị của case */
    name: string;

    /** Condition để evaluate (cho CONDITION mode) - Sử dụng shared ICondition */
    condition?: ICondition;

    /** Expression để evaluate (cho EXPRESSION mode) */
    expression?: string;

    /** Data type để check (cho DATA_TYPE mode) */
    data_type?: EDataType;

    /** Output handle name cho case này */
    output_handle: string;

    /** Có enabled không */
    enabled?: boolean;

    /** Thứ tự ưu tiên (case nào được check trước) */
    priority?: number;
}

/**
 * Default case (fallback)
 */
export interface IDefaultCase {
    /** Có enabled default case không */
    enabled: boolean;

    /** Output handle name cho default case */
    output_handle: string;

    /** Có stop execution khi không match case nào không */
    stop_on_no_match?: boolean;
}

// =================================================================
// SECTION 3: PARAMETERS INTERFACE
// Định nghĩa cấu trúc parameters cho Switch node
// =================================================================

/**
 * Interface cho parameters của Switch node
 */
export interface ISwitchParameters {
    /** Switch mode */
    mode: ESwitchMode;

    /** Field name để lấy giá trị so sánh */
    field_name: string;

    /** Danh sách switch cases */
    cases: ISwitchCase[];

    /** Default case configuration */
    default_case: IDefaultCase;

    /** Có case sensitive không (cho string comparison) */
    case_sensitive?: boolean;

    /** Có continue checking các case khác sau khi match không */
    continue_after_match?: boolean;

    /** Custom variables có thể sử dụng trong expressions */
    variables?: Record<string, any>;
}

// =================================================================
// SECTION 4: INPUT/OUTPUT INTERFACES
// Định nghĩa cấu trúc dữ liệu đầu vào và đầu ra
// =================================================================

/**
 * Interface cho input data của Switch node
 */
export interface ISwitchInput extends IBaseNodeInput {
    /** Dữ liệu để evaluate switch */
    data: Record<string, any>;

    /** Context variables */
    variables?: Record<string, any>;

    /** Override field value */
    field_value?: any;
}

/**
 * Interface cho output data của Switch node
 */
export interface ISwitchOutput extends IBaseNodeOutput {
    /** Matched case information */
    matched_case?: {
        id: string;
        name: string;
        output_handle: string;
        matched_value: any;
    };

    /** Có match case nào không */
    has_match: boolean;

    /** Dữ liệu gốc được pass through */
    data: Record<string, any>;

    /** Chi tiết evaluation của từng case */
    evaluation_details: {
        field_name: string;
        field_value: any;
        cases_evaluated: Array<{
            case_id: string;
            case_name: string;
            result: boolean;
            evaluation_time: number;
        }>;
        total_evaluation_time: number;
    };

    /** Metadata */
    metadata: {
        switch_mode: ESwitchMode;
        timestamp: number;
    };
}

// =================================================================
// SECTION 5: PROPERTIES DEFINITION
// Định nghĩa properties cho dynamic UI generation
// =================================================================


/**
 * Type-safe node execution cho Switch
 */
export type ISwitchNodeExecution = ITypedNodeExecution<
    ISwitchInput,
    ISwitchOutput,
    ISwitchParameters
>;

// =================================================================
// SECTION 7: HELPER FUNCTIONS
// Helper functions cho switch logic
// =================================================================

/**
 * Helper function để generate output handles từ cases
 */
export function generateOutputHandles(cases: ISwitchCase[], defaultCase: IDefaultCase): string[] {
    const handles = cases
        .filter(c => c.enabled !== false)
        .map(c => c.output_handle);

    if (defaultCase.enabled) {
        handles.push(defaultCase.output_handle);
    }

    // Remove duplicates và sort
    return [...new Set(handles)].sort();
}

/**
 * Helper function để validate switch case - Simplified với shared logic
 */
export function validateSwitchCase(switchCase: ISwitchCase, mode: ESwitchMode): string[] {
    const errors: string[] = [];

    if (!switchCase.id) {
        errors.push('Case ID is required');
    }

    if (!switchCase.name) {
        errors.push('Case name is required');
    }

    if (!switchCase.output_handle) {
        errors.push('Output handle is required');
    }

    switch (mode) {
        case ESwitchMode.CONDITION:
            if (!switchCase.condition) {
                errors.push('Condition is required for condition mode');
            } else {
                // Validate condition using shared logic
                if (!switchCase.condition.field) {
                    errors.push('Condition field is required');
                }
                if (!switchCase.condition.operator) {
                    errors.push('Condition operator is required');
                }
                if (operatorNeedsValue(switchCase.condition.operator) &&
                    switchCase.condition.value === undefined &&
                    !switchCase.condition.values) {
                    errors.push('Condition value is required for this operator');
                }
            }
            break;

        case ESwitchMode.EXPRESSION:
            if (!switchCase.expression) {
                errors.push('Expression is required for expression mode');
            }
            break;

        case ESwitchMode.DATA_TYPE:
            if (!switchCase.data_type) {
                errors.push('Data type is required for data type mode');
            }
            break;
    }

    return errors;
}

/**
 * Helper function để sort cases theo priority
 */
export function sortCasesByPriority(cases: ISwitchCase[]): ISwitchCase[] {
    return [...cases].sort((a, b) => {
        const priorityA = a.priority || 999;
        const priorityB = b.priority || 999;
        return priorityA - priorityB;
    });
}

// =================================================================
// SECTION 8: FACTORY & VALIDATION
// Factory function và validation
// =================================================================

/**
 * Validation function cho Switch parameters
 */
export function validateSwitchParameters(params: Partial<ISwitchParameters>): {
    isValid: boolean;
    errors: string[];
} {
    const errors: string[] = [];

    if (!params.mode) {
        errors.push('Switch mode is required');
    }

    if (!params.field_name) {
        errors.push('Field name is required');
    }

    if (!params.cases || params.cases.length === 0) {
        errors.push('At least one switch case is required');
    } else {
        // Validate each case
        params.cases.forEach((switchCase, index) => {
            const caseErrors = validateSwitchCase(switchCase, params.mode!);
            caseErrors.forEach(error => {
                errors.push(`Case ${index + 1}: ${error}`);
            });
        });

        // Check for duplicate case IDs
        const caseIds = params.cases.map(c => c.id);
        const duplicateIds = caseIds.filter((id, index) => caseIds.indexOf(id) !== index);
        if (duplicateIds.length > 0) {
            errors.push(`Duplicate case IDs: ${duplicateIds.join(', ')}`);
        }

        // Check for duplicate output handles
        const outputHandles = params.cases.map(c => c.output_handle);
        const duplicateHandles = outputHandles.filter((handle, index) => outputHandles.indexOf(handle) !== index);
        if (duplicateHandles.length > 0) {
            errors.push(`Duplicate output handles: ${duplicateHandles.join(', ')}`);
        }
    }

    if (!params.default_case) {
        errors.push('Default case configuration is required');
    } else {
        if (params.default_case.enabled && !params.default_case.output_handle) {
            errors.push('Default case output handle is required when enabled');
        }
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}
