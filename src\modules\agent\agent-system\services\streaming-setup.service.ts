import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { HumanMessage, RemoveMessage } from '@langchain/core/messages';
import { DynamicStructuredTool } from '@langchain/core/tools';
import { StreamingSetupService as IStreamingSetupService } from '../interfaces/service.interface';
import { StreamingComponents, ThreadConfiguration } from '../schemas';
import { EmitEventCallback } from '../interfaces/event';
import { TokenUsageCollector } from '../token-usage-collector';
import { UserUsageQueries } from './user-usage.queries';
import { UserAgentRunsQueries } from './user-agent-runs.queries';
import { MessageContentService } from './message-content.service';
import { CdnService, RedisService } from '../../../../infra';
import { workflow } from '../core';
import { SystemAgentConfigMap } from '../interfaces/agent-system.interface';
import {
  Attachment<PERSON>ontext<PERSON>lock,
  ContentBlock,
  isImageContentBlock,
  isTextContentBlock,
} from '../interfaces/message.interface';
import { Command } from '@langchain/langgraph';
import { InterruptShapeInterface } from '../../interfaces';
import { TimeIntervalEnum } from '@common/dto/time-interval.enum';
import { HttpService } from '@nestjs/axios';
import { Readable } from 'stream';
import { firstValueFrom } from 'rxjs';
import { toBeDeleted } from '../../constants/special-message-id';
import { v4 } from 'uuid';
import base64 from 'base64-stream';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  createChooseImageFromContextTool,
  createSaveMemoryTool,
  createUpdateMemoryTool,
  createWebSearchTool,
} from '../../common-tools';
import { WebSearchService } from './websearch.service';

/**
 * Service responsible for setting up streaming components and dependencies
 * Handles basic HumanMessage creation with reply context and attachment processing.
 * Memory context is now handled by RichSystemMessageBuilder in react-agent-executor.ts.
 * Extracted from AgentSystemService.processAgentThread method (Section 2: Input Preparation & Section 3: Streaming Initialization)
 */
@Injectable()
export class StreamingSetupService implements IStreamingSetupService {
  private readonly logger = new Logger(StreamingSetupService.name);

  constructor(
    private readonly cdnService: CdnService,
    private readonly redisService: RedisService,
    private readonly userUsageQueries: UserUsageQueries,
    private readonly userAgentRunsQueries: UserAgentRunsQueries,
    private readonly httpService: HttpService,
    private readonly eventEmitter: EventEmitter2,
    private readonly webSearchService: WebSearchService,
  ) {}

  /**
   * Setup all streaming components required for agent thread processing with everything needed from start
   * @param config - Thread configuration containing all necessary data
   * @param emitEventCallback - The real emit event callback from AgentSystemService
   * @param abortController - Abort controller for cancellation support
   * @returns Complete streaming components ready for event processing
   */
  async setupComponents(
    config: ThreadConfiguration,
    emitEventCallback: EmitEventCallback,
    abortController: AbortController,
  ): Promise<StreamingComponents> {
    try {
      this.logger.debug(
        `Setting up streaming components for thread ${config.threadId}`,
        {
          threadId: config.threadId,
          runId: config.runData.id,
          userId: config.userId,
        },
      );

      // 1. Setup Redis producer client
      const producer = this.redisService.getRawClient();

      // 2. Prepare partial tokens array for accumulation
      const partialTokens: string[] = [];

      // 4. Create TokenUsageCollector with the real callback passed from AgentSystemService
      const tokenUsageCollector = new TokenUsageCollector(
        config.customConfig.agentConfigMap as SystemAgentConfigMap,
        config.userId,
        config.threadId,
        config.runData.id,
        this.userUsageQueries,
        this.userAgentRunsQueries,
        emitEventCallback, // Use the real callback passed as parameter
      );

      // 5. Prepare and validate streaming input
      const streamingInput = await this.prepareStreamingInput(config);

      // 6. Create streaming iterator with TokenUsageCollector and AbortController from start
      const streamingIterator = await this.createStreamingIterator(
        streamingInput,
        config,
        tokenUsageCollector,
        abortController,
      );

      // 7. Create abort listener function
      const abortListener = async () => {
        this.logger.log(`Thread ${config.threadId} processing was cancelled`);
        // Force-close the LLM stream generator
        await streamingIterator?.return?.();
      };

      // 8. Register abort listener
      abortController.signal.addEventListener('abort', abortListener);

      const components: StreamingComponents = {
        producer,
        tokenUsageCollector,
        emitEventCallback, // Real callback passed from AgentSystemService
        streamingInput,
        threadConfig: config, // Include thread config for abort signal recreation
        partialTokens,
        streamingIterator,
        abortListener,
      };

      this.logger.debug(
        `Successfully set up streaming components for thread ${config.threadId}`,
        {
          threadId: config.threadId,
          hasProducer: !!producer,
          hasTokenCollector: !!tokenUsageCollector,
          hasStreamingInput: !!streamingInput,
          hasStreamingIterator: !!streamingIterator,
          hasAbortListener: !!abortListener,
        },
      );

      return components;
    } catch (error) {
      this.logger.error(
        `Failed to setup streaming components for thread ${config.threadId}:`,
        {
          threadId: config.threadId,
          runId: config.runData.id,
          error: error.message,
          stack: error.stack,
        },
      );
      throw error;
    }
  }
  async streamUrlToBase64(url: string): Promise<Base64Result> {
    try {
      // 1. Lấy response, bao gồm stream và headers
      const response = await firstValueFrom(
        this.httpService.get(url, { responseType: 'stream' }),
      );

      const mimeType =
        response.headers['content-type'] || 'application/octet-stream';
      const fileStream = response.data as Readable;

      // 👇 THAY ĐỔI 2: Sử dụng Encoder từ thư viện thay vì class tự chế
      const base64Encoder = new base64.Base64Encode();

      let accumulatedBase64 = '';

      // 3. Nối stream nguồn (download) vào stream chuyển đổi (encoder)
      const encodedStream = fileStream.pipe(base64Encoder);

      // 4. Dùng vòng lặp 'for await...of' để tiêu thụ stream đã mã hóa
      for await (const chunk of encodedStream) {
        accumulatedBase64 += chunk;
      }

      // 5. Sau khi vòng lặp kết thúc, chuỗi đã hoàn chỉnh
      return {
        base64String: accumulatedBase64,
        mimeType: mimeType,
      };
    } catch (error) {
      this.logger.error(
        `Failed to stream or convert file from url: ${url}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        'Failed to process the file stream.',
      );
    }
  }

  /**
   * Prepare and validate streaming input from thread configuration
   * @param config - Thread configuration
   * @returns Validated streaming input for LangGraph
   */
  private async prepareStreamingInput(
    config: ThreadConfiguration,
  ): Promise<any> {
    try {
      // Extract message data
      const messageData = config.messageData;
      const contentBlocks: ContentBlock[] = messageData.contentBlocks || [];
      const attachmentContext: AttachmentContextBlock[] =
        messageData.attachmentContext || [];
      const replyToMessageId: string | undefined = messageData.replyToMessageId;

      // ✅ NEW: Build attachment image map for tool access
      const attachmentImageMap: Record<string, string> = {};
      if (attachmentContext && attachmentContext.length > 0) {
        this.logger.debug(
          `Processing ${attachmentContext.length} attachment context blocks for thread ${config.threadId}`,
          {
            threadId: config.threadId,
            attachmentCount: attachmentContext.length,
            attachmentTypes: attachmentContext.map(
              (block) => block.type || 'unknown',
            ),
          },
        );

        // Filter and process image attachments
        attachmentContext.filter(isImageContentBlock).forEach((imageBlock) => {
          if (imageBlock.fileId && imageBlock.path) {
            attachmentImageMap[imageBlock.fileId] = imageBlock.path;
            this.logger.debug(
              `Added image to attachment map: ${imageBlock.fileId} -> ${imageBlock.path}`,
              {
                threadId: config.threadId,
                imageId: imageBlock.fileId,
                s3Key: imageBlock.path,
                imageName: imageBlock.name,
              },
            );
          } else {
            this.logger.warn(
              `Skipping image attachment with missing fileId or path`,
              {
                threadId: config.threadId,
                hasFileId: !!imageBlock.fileId,
                hasPath: !!imageBlock.path,
                blockData: imageBlock,
              },
            );
          }
        });

        this.logger.debug(
          `Built attachment image map for thread ${config.threadId}`,
          {
            threadId: config.threadId,
            imageCount: Object.keys(attachmentImageMap).length,
            imageIds: Object.keys(attachmentImageMap),
          },
        );
      }

      // ✅ NEW: Store attachment image map in configurable for tool access
      config.customConfig.attachmentImageMap = attachmentImageMap;

      // ✅ NEW: Ensure userId is available in configurable for memory tools
      config.customConfig.userId = config.userId;

      // ✅ NEW: Ensure JWT is available in configurable for authenticated API calls
      if (!config.customConfig.jwt) {
        this.logger.error(
          `JWT token missing in custom config for thread ${config.threadId}`,
          {
            threadId: config.threadId,
            hasJwtInConfig: !!config.jwt,
            hasJwtInCustomConfig: !!config.customConfig.jwt,
            customConfigKeys: Object.keys(config.customConfig),
          },
        );
        throw new Error('JWT token is required in custom configuration');
      }

      // ✅ NEW: Dynamic tool registration based on attachment context
      let contextualTools: DynamicStructuredTool[] = [];
      if (Object.keys(attachmentImageMap).length > 0) {
        try {
          const chooseImageTool = createChooseImageFromContextTool(
            this.cdnService,
            this.streamUrlToBase64.bind(this),
          );
          contextualTools = [chooseImageTool];

          this.logger.debug(
            `Registered contextual tools for thread ${config.threadId}`,
            {
              threadId: config.threadId,
              toolCount: contextualTools.length,
              imageCount: Object.keys(attachmentImageMap).length,
              availableImages: Object.keys(attachmentImageMap),
            },
          );
        } catch (error) {
          this.logger.warn(
            `Failed to create contextual tools for thread ${config.threadId}:`,
            {
              threadId: config.threadId,
              error: error.message,
              imageCount: Object.keys(attachmentImageMap).length,
            },
          );
          // Continue without tools - don't break the flow
        }
      } else {
        this.logger.debug(
          `No images in attachment context for thread ${config.threadId}, skipping image tool registration`,
          {
            threadId: config.threadId,
            attachmentCount: attachmentContext?.length || 0,
          },
        );
      }

      // ✅ NEW: Always add save memory tool for long-term memory functionality
      try {
        // Verify userId is available for memory tools
        if (!config.userId) {
          this.logger.warn(
            `No userId available for memory tools in thread ${config.threadId}`,
            {
              threadId: config.threadId,
              hasUserId: !!config.userId,
              configKeys: Object.keys(config),
            },
          );
        }

        const saveMemoryTool = createSaveMemoryTool(this.eventEmitter);
        const updateMemoryTool = createUpdateMemoryTool(this.eventEmitter);
        const webSearchTool = createWebSearchTool(this.webSearchService);

        contextualTools.push(saveMemoryTool, updateMemoryTool, webSearchTool);

        this.logger.debug(
          `Registered memory and web search tools for thread ${config.threadId}`,
          {
            threadId: config.threadId,
            userId: config.userId,
            memoryToolsAdded: 2, // save_memory and update_memory tools
            webSearchToolAdded: 1, // web_search tool
            totalTools: contextualTools.length,
            userIdInConfigurable: config.customConfig.userId,
            hasJwtInConfigurable: !!config.customConfig.jwt,
          },
        );
      } catch (error) {
        this.logger.warn(
          `Failed to create save memory tool for thread ${config.threadId}:`,
          {
            threadId: config.threadId,
            error: error.message,
          },
        );
        // Continue without save memory tool - don't break the flow
      }

      // Store contextual tools in configurable for agent access
      config.customConfig.contextualTools = contextualTools;

      this.logger.debug(
        `Final contextual tools and configuration for thread ${config.threadId}`,
        {
          threadId: config.threadId,
          userId: config.customConfig.userId,
          hasJwt: !!config.customConfig.jwt,
          totalToolCount: contextualTools.length,
          toolNames: contextualTools.map((tool) => tool.name),
          hasImageTool: contextualTools.some(
            (tool) => tool.name === 'choose_image_from_attachment_context',
          ),
          hasMemoryTools: contextualTools.some(
            (tool) =>
              tool.name === 'save_memory' || tool.name === 'update_memory',
          ),
          hasWebSearchTool: contextualTools.some(
            (tool) => tool.name === 'web_search',
          ),
          configurableKeys: Object.keys(config.customConfig),
        },
      );

      // ✅ NEW: Validate content blocks instead of simple string
      if (!contentBlocks || contentBlocks.length === 0) {
        this.logger.error(
          `[prepareStreamingInput]: Empty content blocks for thread ${config.threadId}`,
          {
            threadId: config.threadId,
            hasPayload: !!config.decryptedPayload,
            hasMessage: !!config.decryptedPayload?.message,
            contentBlockCount: contentBlocks?.length || 0,
            attachmentCount: attachmentContext?.length || 0,
            payloadStructure: {
              keys: config.decryptedPayload
                ? Object.keys(config.decryptedPayload)
                : [],
              messageKeys: config.decryptedPayload?.message
                ? Object.keys(config.decryptedPayload.message)
                : [],
            },
            rawPayloadSample: JSON.stringify(config.decryptedPayload).substring(
              0,
              500,
            ),
          },
        );
        throw new Error('Content blocks are empty or invalid');
      }

      let input: any;

      this.logger.debug(
        `contentBlocks = ${JSON.stringify(contentBlocks, null, 2)}`,
      );

      if (contentBlocks[0].type === 'tool_call_decision') {
        this.logger.debug(`tool call decision is ${contentBlocks[0].decision}`);
        const toolCallDecision: InterruptShapeInterface = {
          choice: contentBlocks[0].decision,
        };
        input = new Command({ resume: toolCallDecision });
      } else {
        // Generate message content with reply context (memory loading removed)
        const messageContent = await this.generateMessageContentFromBlocks(
          contentBlocks,
          attachmentContext || [],
        );

        // ✅ NEW: Get message ID from run data for future use
        const messageId = config.runData?.payload?.messageId || v4();

        this.logger.debug(
          `[${this.prepareStreamingInput.name}]: messageId = ${messageId}`,
        );

        // Build input for multi-agent LangGraph workflow with message ID
        const humanMessage = new HumanMessage({
          id: messageId,
          content: [
            {
              type: 'text',
              text: messageContent,
            },
          ],
        });

        const imageHumanMessageContent = await Promise.all(
          contentBlocks
            .filter((block) => block.type === 'image')
            .map(async (block) => {
              const viewUrl = this.cdnService.generateUrlView(
                block.path,
                TimeIntervalEnum.FIVE_MINUTES,
              ) as string;
              // from file stream to base64 string
              const { base64String, mimeType } =
                await this.streamUrlToBase64(viewUrl);
              return {
                type: 'image_url',
                image_url: {
                  url: `data:${mimeType};base64,${base64String}`,
                },
              };
            }),
        );

        const imageHumanMessage = new HumanMessage({
          id: toBeDeleted,
          content: imageHumanMessageContent,
        });

        const deletedMessageIds =
          config.runData?.modificationDetails?.deletedMessageIds || [];

        const removeMessages = deletedMessageIds.map(
          (id: string) => new RemoveMessage({ id }),
        );

        input = {
          messages: [humanMessage, imageHumanMessage, ...removeMessages],
          activeAgent: config.customConfig.supervisorAgentId || 'supervisor',
        };

        this.logger.debug(
          `Prepared streaming input for thread ${config.threadId}:`,
          {
            threadId: config.threadId,
            activeAgent: input.activeAgent,
            contentBlockCount: contentBlocks.length,
            contentBlockTypes: contentBlocks.map((block: any) => block.type),
            attachmentCount: (attachmentContext || []).length,
            messageLength: messageContent.length,
            messagePreview: messageContent.substring(0, 100),
            alwaysApproveToolCall: config.customConfig.alwaysApproveToolCall,
            humanMessageType: humanMessage.constructor.name,
            hasValidMessage: !!humanMessage.content,
          },
        );
      }
      return input;
    } catch (error) {
      this.logger.error(
        `Failed to prepare streaming input for thread ${config.threadId}:`,
        {
          threadId: config.threadId,
          error: error.message,
          stack: error.stack,
        },
      );
      throw error;
    }
  }

  /**
   * Create streaming iterator with LangGraph workflow with everything needed from start
   * @param input - Streaming input for LangGraph
   * @param config - Thread configuration
   * @param tokenUsageCollector - Token usage collector instance
   * @param abortController - Abort controller for cancellation support
   * @returns Streaming iterator for event processing
   */
  private async createStreamingIterator(
    input: any,
    config: ThreadConfiguration,
    tokenUsageCollector: TokenUsageCollector,
    abortController: AbortController,
  ): Promise<AsyncIterableIterator<any>> {
    try {
      this.logger.debug(
        `Creating streaming iterator for thread ${config.threadId}`,
        {
          threadId: config.threadId,
          inputActiveAgent: input.activeAgent,
          hasTokenCollector: !!tokenUsageCollector,
          hasAbortController: !!abortController,
        },
      );

      const streaming = workflow.streamEvents(input, {
        configurable: config.customConfig,
        subgraphs: true,
        recursionLimit: 500,
        version: 'v2' as const,
        signal: abortController.signal,
        streamMode: ['values', 'updates'],
        callbacks: [tokenUsageCollector],
      });

      const streamingIterator = streaming[Symbol.asyncIterator]();

      this.logger.debug(
        `Successfully created streaming iterator for thread ${config.threadId}`,
        {
          threadId: config.threadId,
          hasIterator: !!streamingIterator,
        },
      );

      return streamingIterator;
    } catch (error) {
      this.logger.error(
        `Failed to create streaming iterator for thread ${config.threadId}:`,
        {
          threadId: config.threadId,
          error: error.message,
          stack: error.stack,
        },
      );
      throw error;
    }
  }

  /**
   * Generate message content from content blocks with reply context
   * @param contentBlocks Array of content blocks
   * @param attachmentContext Array of attachment context
   * @param replyToMessageId Optional message ID to reply to
   * @returns Generated message content string
   */
  private async generateMessageContentFromBlocks(
    contentBlocks: ContentBlock[],
    attachmentContext: AttachmentContextBlock[],
  ): Promise<string> {
    try {
      let messageContent = '';

      // Process only text content blocks - images and files are handled in system prompt attachment context
      if (contentBlocks && contentBlocks.length > 0) {
        for (const block of contentBlocks) {
          if (isTextContentBlock(block)) {
            messageContent += block.content; // Raw text content without XML wrapper
          }
          // Images and files are excluded from human message - they're in system prompt attachment context
        }
      }

      // Attachment context and reply context are now handled by RichSystemMessageBuilder in system prompt
      // Memory context is now handled by RichSystemMessageBuilder in react-agent-executor.ts

      const returnMessage = messageContent.trim() || 'No content provided';

      this.logger.debug(`Generated message content from blocks:`, {
        contentLength: returnMessage.length,
        contentPreview: returnMessage,
      });

      return returnMessage;
    } catch (error) {
      this.logger.error('Failed to generate message content from blocks:', {
        error: error.message,
        contentBlockCount: contentBlocks?.length || 0,
        attachmentCount: attachmentContext?.length || 0,
      });
      return '[Error processing message content]';
    }
  }
}

// Interface cho kết quả trả về
export interface Base64Result {
  base64String: string;
  mimeType: string;
}
