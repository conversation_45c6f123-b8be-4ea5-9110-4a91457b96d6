import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ZaloVideoUpload } from '../entities/zalo-video-upload.entity';

/**
 * Repository cho ZaloVideoUpload entity trong worker
 */
@Injectable()
export class ZaloVideoUploadRepository {
  constructor(
    @InjectRepository(ZaloVideoUpload)
    private readonly repository: Repository<ZaloVideoUpload>,
  ) {}

  /**
   * Tìm video upload theo token
   */
  async findByToken(token: string): Promise<ZaloVideoUpload | null> {
    return this.repository.findOne({
      where: { token },
    });
  }

  /**
   * Cập nhật trạng thái video theo token
   */
  async updateStatusByToken(
    token: string,
    status: number,
    statusMessage?: string,
    convertPercent?: number,
    videoId?: string,
  ): Promise<ZaloVideoUpload | null> {
    const updateData: Partial<ZaloVideoUpload> = { status };

    if (statusMessage !== undefined) {
      updateData.statusMessage = statusMessage;
    }
    if (convertPercent !== undefined) {
      updateData.convertPercent = convertPercent;
    }
    if (videoId !== undefined) {
      updateData.videoId = videoId;
    }

    await this.repository.update({ token }, updateData);
    return this.findByToken(token);
  }
}
