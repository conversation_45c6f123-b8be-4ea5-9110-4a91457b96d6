import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AgentUser } from '../entities/agent-user.entity';

@Injectable()
export class AgentUserRepository {
  constructor(
    @InjectRepository(AgentUser)
    private readonly repository: Repository<AgentUser>,
  ) {}

  async findByIdAndUser(id: string, userId: number): Promise<AgentUser | null> {
    return this.repository.findOne({
      where: {
        id,
        userId,
        active: true
      }
    });
  }
}
