import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng models trong cơ sở dữ liệu
 * Bảng thống nhất quản lý tất cả models (user và system)
 */
@Entity('models')
export class Models {
  /**
   * UUID định danh duy nhất cho mỗi model
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * ID định danh của model
   */
  @Column({
    name: 'model_id',
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'ID định danh của model'
  })
  modelId: string | null;

  /**
   * Liên kết đến bảng model_registry
   */
  @Column({
    name: 'model_registry_id',
    type: 'uuid',
    nullable: true,
    comment: 'Liên kết đến bảng model_registry'
  })
  modelRegistryId: string | null;

  /**
   * <PERSON><PERSON><PERSON> kết đến bảng model_detail
   */
  @Column({
    name: 'detail_id',
    type: 'uuid',
    nullable: true,
    comment: '<PERSON>ên kết đến bảng model_detail'
  })
  detailId: string | null;

  /**
   * Trạng thái hoạt động của model
   */
  @Column({
    name: 'active',
    type: 'boolean',
    default: false,
    comment: 'Trạng thái hoạt động của model'
  })
  active: boolean;

  /**
   * ID của user sở hữu model (nullable cho system models)
   * Tham chiếu đến bảng users
   */
  @Column({
    name: 'user_id',
    type: 'integer',
    nullable: true,
    comment: 'ID của user sở hữu model (null cho system models)'
  })
  userId: number | null;

  /**
   * Model có phải là model fine-tuned hay không
   */
  @Column({
    name: 'is_fine_tune',
    type: 'boolean',
    default: false,
    comment: 'Model có phải là model fine-tuned hay không'
  })
  isFineTune: boolean;
}
