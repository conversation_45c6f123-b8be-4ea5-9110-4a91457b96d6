import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule } from '@nestjs/config';
import { ZaloVideoTrackingProcessor } from './zalo-video-tracking.processor';
import { ZaloVideoTrackingService } from './zalo-video-tracking.service';
import { ZaloArticleService } from '../../../shared/services/zalo/zalo-article.service';
import { ZaloModule } from '../../../shared/services/zalo/zalo.module';
import { QueueName } from '../../../queue/queue-name.enum';
import { MarketingModule } from '../marketing.module';

/**
 * Module xử lý tracking video Zalo
 */
@Module({
  imports: [
    ConfigModule,
    ZaloModule,
    MarketingModule,
    BullModule.registerQueue({
      name: QueueName.ZALO_VIDEO_TRACKING,
    }),
  ],
  providers: [
    ZaloVideoTrackingProcessor,
    ZaloVideoTrackingService,
    ZaloArticleService,
  ],
  exports: [ZaloVideoTrackingService],
})
export class ZaloVideoTrackingModule {}
