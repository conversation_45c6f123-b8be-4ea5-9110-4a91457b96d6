import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Injectable, Logger } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';

/**
 * Interface cho Facebook Page Info
 */
export interface FacebookPageInfo {
  id: string;
  name: string;
  category?: string;
  about?: string;
  description?: string;
  picture?: any;
  cover?: any;
  fan_count?: number;
  followers_count?: number;
}

/**
 * Interface cho Facebook Page Post Request
 */
export interface FacebookPagePostRequest {
  message?: string;
  link?: string;
  picture?: string;
  name?: string;
  caption?: string;
  description?: string;
  published?: boolean;
  scheduled_publish_time?: number;
}

/**
 * Interface cho Facebook Page Post Response
 */
export interface FacebookPagePostResponse {
  id: string;
  post_id?: string;
}

/**
 * Service để xử lý các API liên quan đến Facebook Page
 */
@Injectable()
export class FacebookPageService {
  private readonly logger = new Logger(FacebookPageService.name);
  private readonly baseUrl = 'https://graph.facebook.com/v18.0';

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Set access token for requests
   */
  setAccessToken(accessToken: string): void {
    // Store access token for future requests
  }

  /**
   * Lấy thông tin page
   */
  async getPageInfo(pageId: string, accessToken: string): Promise<FacebookPageInfo> {
    try {
      const url = `${this.baseUrl}/${pageId}`;
      const params = {
        access_token: accessToken,
        fields: 'id,name,category,about,description,picture,cover,fan_count,followers_count',
      };

      const response = await firstValueFrom(
        this.httpService.get(url, { params }),
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Error getting page info: ${error.message}`);
      throw new Error(`Failed to get page info: ${error.message}`);
    }
  }

  /**
   * Tạo post trên page
   */
  async createPost(
    pageId: string,
    accessToken: string,
    postData: FacebookPagePostRequest,
  ): Promise<FacebookPagePostResponse> {
    try {
      const url = `${this.baseUrl}/${pageId}/feed`;
      
      const response = await firstValueFrom(
        this.httpService.post(url, {
          ...postData,
          access_token: accessToken,
        }),
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Error creating post: ${error.message}`);
      throw new Error(`Failed to create post: ${error.message}`);
    }
  }

  /**
   * Lấy danh sách posts của page
   */
  async getPosts(
    pageId: string,
    accessToken: string,
    limit: number = 25,
  ): Promise<any> {
    try {
      const url = `${this.baseUrl}/${pageId}/posts`;
      const params = {
        access_token: accessToken,
        limit: limit.toString(),
        fields: 'id,message,created_time,likes.summary(true),comments.summary(true),shares',
      };

      const response = await firstValueFrom(
        this.httpService.get(url, { params }),
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Error getting posts: ${error.message}`);
      throw new Error(`Failed to get posts: ${error.message}`);
    }
  }

  /**
   * Lấy insights của page
   */
  async getPageInsights(
    pageId: string,
    accessToken: string,
    metrics: string[] = ['page_fans', 'page_impressions'],
    period: string = 'day',
  ): Promise<any> {
    try {
      const url = `${this.baseUrl}/${pageId}/insights`;
      const params = {
        access_token: accessToken,
        metric: metrics.join(','),
        period,
      };

      const response = await firstValueFrom(
        this.httpService.get(url, { params }),
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Error getting insights: ${error.message}`);
      throw new Error(`Failed to get insights: ${error.message}`);
    }
  }

  /**
   * Lấy danh sách pages của user
   */
  async getUserPages(accessToken: string): Promise<FacebookPageInfo[]> {
    try {
      const url = `${this.baseUrl}/me/accounts`;
      const params = {
        access_token: accessToken,
        fields: 'id,name,category,access_token',
      };

      const response = await firstValueFrom(
        this.httpService.get(url, { params }),
      );

      return response.data.data || [];
    } catch (error) {
      this.logger.error(`Error getting user pages: ${error.message}`);
      throw new Error(`Failed to get user pages: ${error.message}`);
    }
  }

  /**
   * Xóa post
   */
  async deletePost(postId: string, accessToken: string): Promise<{ success: boolean }> {
    try {
      const url = `${this.baseUrl}/${postId}`;
      const params = {
        access_token: accessToken,
      };

      const response = await firstValueFrom(
        this.httpService.delete(url, { params }),
      );

      return { success: response.data.success || true };
    } catch (error) {
      this.logger.error(`Error deleting post: ${error.message}`);
      throw new Error(`Failed to delete post: ${error.message}`);
    }
  }

  /**
   * Cập nhật post
   */
  async updatePost(
    postId: string,
    accessToken: string,
    updateData: { message?: string; is_published?: boolean },
  ): Promise<{ success: boolean }> {
    try {
      const url = `${this.baseUrl}/${postId}`;
      
      const response = await firstValueFrom(
        this.httpService.post(url, {
          ...updateData,
          access_token: accessToken,
        }),
      );

      return { success: response.data.success || true };
    } catch (error) {
      this.logger.error(`Error updating post: ${error.message}`);
      throw new Error(`Failed to update post: ${error.message}`);
    }
  }

  /**
   * Trả lời comment
   */
  async replyToComment(
    commentId: string,
    accessToken: string,
    message: string,
  ): Promise<{ id: string }> {
    try {
      const url = `${this.baseUrl}/${commentId}/comments`;
      
      const response = await firstValueFrom(
        this.httpService.post(url, {
          message,
          access_token: accessToken,
        }),
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Error replying to comment: ${error.message}`);
      throw new Error(`Failed to reply to comment: ${error.message}`);
    }
  }
}
