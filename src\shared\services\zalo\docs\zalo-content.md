# Zalo Content Service Documentation

## Tổng quan

Zalo Content Service cung cấp các API để quản lý nội dung dạng bài viết của Zalo Official Account, bao gồm:

- Tạo nội dung dạng bài viết
- Upload video cho nội dung
- Kiểm tra tiến trình tạo nội dung
- Lấy chi tiết và danh sách nội dung
- Cập nhật và xóa nội dung
- Xuất bản và hủy xuất bản nội dung
- Tìm kiếm và thống kê nội dung

## Điều kiện sử dụng

Dựa trên tài liệu chính thức của Zalo:

### Tạo nội dung
- OA cần có quyền tạo nội dung bài viết
- Nội dung HTML hỗ trợ các thẻ cơ bản, không hỗ trợ JavaScript
- Mỗi OA có giới hạn số lượng nội dung tạo mỗi ngày

### Upload video
- Kích thước tối đa: 100MB
- <PERSON><PERSON><PERSON> dạng hỗ trợ: MP4, MOV, AVI
- Video sẽ được xử lý và có thể mất thời gian

### Ảnh đại diện
- Kích thước tối đa: 5MB
- Định dạng hỗ trợ: JPG, PNG

### Xuất bản
- Có thể lên lịch xuất bản hoặc xuất bản ngay
- Nội dung đã xuất bản có thể được hủy xuất bản

## API Endpoints

### 1. Tạo nội dung dạng bài viết

```typescript
async createContent(
  accessToken: string,
  request: ZaloContentRequest
): Promise<ZaloContentResponse>
```

**Ví dụ:**
```typescript
const request = {
  title: 'Khuyến mãi đặc biệt tháng 12',
  description: 'Chương trình khuyến mãi lớn nhất trong năm',
  content: '<p>Nội dung bài viết với <strong>định dạng HTML</strong></p>',
  cover_photo_url: 'https://example.com/cover-photo.jpg',
  status: 'draft',
  tags: ['khuyến mãi', 'sale', 'giảm giá']
};

const result = await zaloContentService.createContent(accessToken, request);
```

### 2. Upload video cho nội dung

```typescript
async uploadVideo(
  accessToken: string,
  request: ZaloVideoUploadRequest
): Promise<ZaloVideoUploadResponse>
```

**Ví dụ:**
```typescript
const request = {
  filename: 'promotional-video.mp4',
  file_size: 52428800, // 50MB
  mime_type: 'video/mp4',
  description: 'Video quảng cáo sản phẩm mới'
};

const uploadInfo = await zaloContentService.uploadVideo(accessToken, request);

// Sau đó upload file thực tế
const fileInfo = {
  filename: 'promotional-video.mp4',
  data: videoBuffer,
  mimetype: 'video/mp4',
  size: 52428800
};

const uploadResult = await zaloContentService.uploadVideoFile(
  uploadInfo.upload_url,
  uploadInfo.upload_token,
  fileInfo
);
```

### 3. Kiểm tra tiến trình tạo nội dung

```typescript
async checkContentProcess(
  accessToken: string,
  processId: string
): Promise<ZaloContentProcess>
```

**Ví dụ:**
```typescript
const process = await zaloContentService.checkContentProcess(accessToken, 'process_123456789');
console.log(`Progress: ${process.progress}%`);
console.log(`Status: ${process.status}`);
```

### 4. Lấy chi tiết nội dung

```typescript
async getContentDetail(
  accessToken: string,
  contentId: string
): Promise<ZaloContentDetail>
```

### 5. Lấy danh sách nội dung

```typescript
async getContentList(
  accessToken: string,
  options?: {
    status?: 'draft' | 'published' | 'deleted';
    limit?: number;
    offset?: number;
    from_time?: number;
    to_time?: number;
    tags?: string[];
  }
): Promise<ZaloContentList>
```

**Ví dụ:**
```typescript
const contentList = await zaloContentService.getContentList(accessToken, {
  status: 'published',
  limit: 20,
  offset: 0,
  tags: ['khuyến mãi']
});
```

### 6. Cập nhật nội dung

```typescript
async updateContent(
  accessToken: string,
  contentId: string,
  request: Partial<ZaloContentRequest>
): Promise<ZaloContentResponse>
```

### 7. Xóa nội dung

```typescript
async deleteContent(
  accessToken: string,
  contentId: string
): Promise<{ success: boolean; message?: string }>
```

### 8. Xuất bản nội dung

```typescript
async publishContent(
  accessToken: string,
  contentId: string,
  publishTime?: number
): Promise<ZaloContentResponse>
```

**Ví dụ:**
```typescript
// Xuất bản ngay
await zaloContentService.publishContent(accessToken, 'content_123456789');

// Lên lịch xuất bản
const publishTime = Math.floor(Date.now() / 1000) + 3600; // 1 giờ sau
await zaloContentService.publishContent(accessToken, 'content_123456789', publishTime);
```

### 9. Hủy xuất bản nội dung

```typescript
async unpublishContent(
  accessToken: string,
  contentId: string
): Promise<ZaloContentResponse>
```

### 10. Lấy thống kê nội dung

```typescript
async getContentStatistics(
  accessToken: string,
  contentId: string
): Promise<ContentStatistics>
```

### 11. Tìm kiếm nội dung

```typescript
async searchContent(
  accessToken: string,
  query: string,
  options?: SearchOptions
): Promise<ZaloContentList>
```

**Ví dụ:**
```typescript
const searchResults = await zaloContentService.searchContent(accessToken, 'khuyến mãi', {
  status: 'published',
  limit: 10,
  tags: ['sale']
});
```

### 12. Lấy danh sách tags

```typescript
async getTags(accessToken: string): Promise<{ tags: Array<{ name: string; count: number }> }>
```

### 13. Kiểm tra trạng thái video

```typescript
async getVideoStatus(
  accessToken: string,
  videoId: string
): Promise<ZaloVideoUploadResponse>
```

## Workflow tạo nội dung có video

```typescript
// 1. Tạo yêu cầu upload video
const videoUploadRequest = {
  filename: 'video.mp4',
  file_size: videoBuffer.length,
  mime_type: 'video/mp4'
};

const uploadInfo = await zaloContentService.uploadVideo(accessToken, videoUploadRequest);

// 2. Upload file video thực tế
const fileInfo = {
  filename: 'video.mp4',
  data: videoBuffer,
  mimetype: 'video/mp4',
  size: videoBuffer.length
};

const uploadResult = await zaloContentService.uploadVideoFile(
  uploadInfo.upload_url,
  uploadInfo.upload_token,
  fileInfo
);

// 3. Tạo nội dung với video
const contentRequest = {
  title: 'Bài viết có video',
  content: '<p>Nội dung bài viết</p>',
  video_url: uploadResult.video_url,
  status: 'draft'
};

const content = await zaloContentService.createContent(accessToken, contentRequest);

// 4. Xuất bản nội dung
await zaloContentService.publishContent(accessToken, content.content_id);
```

## Sử dụng trong Controller

```typescript
import { ZaloContentService } from '@shared/services/zalo';

@Controller('zalo/content')
export class ZaloContentController {
  constructor(private readonly zaloContentService: ZaloContentService) {}

  @Post()
  async createContent(@Body() request: ZaloContentRequestDto) {
    return this.zaloContentService.createContent(accessToken, request);
  }

  @Get(':id')
  async getContent(@Param('id') contentId: string) {
    return this.zaloContentService.getContentDetail(accessToken, contentId);
  }

  @Put(':id')
  async updateContent(
    @Param('id') contentId: string,
    @Body() request: ZaloContentUpdateRequestDto
  ) {
    return this.zaloContentService.updateContent(accessToken, contentId, request);
  }

  @Delete(':id')
  async deleteContent(@Param('id') contentId: string) {
    return this.zaloContentService.deleteContent(accessToken, contentId);
  }

  @Post(':id/publish')
  async publishContent(
    @Param('id') contentId: string,
    @Body() request: ZaloContentPublishRequestDto
  ) {
    return this.zaloContentService.publishContent(
      accessToken,
      contentId,
      request.publishTime
    );
  }
}
```

## Lưu ý quan trọng

1. **Access Token**: Cần access token hợp lệ của Official Account
2. **Quyền tạo nội dung**: OA phải có quyền tạo nội dung bài viết
3. **Giới hạn kích thước**: Video tối đa 100MB, ảnh tối đa 5MB
4. **Định dạng hỗ trợ**: Video (MP4, MOV, AVI), Ảnh (JPG, PNG)
5. **HTML Content**: Chỉ hỗ trợ các thẻ HTML cơ bản
6. **Giới hạn tạo**: Mỗi OA có giới hạn số lượng nội dung tạo mỗi ngày
7. **Xử lý video**: Video cần thời gian xử lý, kiểm tra trạng thái thường xuyên
8. **Error Handling**: Luôn xử lý exception khi gọi API

## Video Content APIs

### 1. Tạo nội dung dạng video

```typescript
async createVideoContent(
  accessToken: string,
  request: ZaloVideoContentRequest
): Promise<ZaloVideoContentResponse>
```

**Ví dụ:**
```typescript
const request = {
  title: 'Video giới thiệu sản phẩm mới',
  description: 'Video chi tiết về sản phẩm mới nhất',
  video_url: 'https://video.zalo.me/v1/video_123456789.mp4',
  thumbnail_url: 'https://example.com/thumbnail.jpg',
  status: 'draft',
  tags: ['sản phẩm', 'giới thiệu', 'video']
};

const result = await zaloContentService.createVideoContent(accessToken, request);
```

### 2. Lấy chi tiết nội dung video

```typescript
async getVideoContentDetail(
  accessToken: string,
  videoContentId: string
): Promise<ZaloVideoContentDetail>
```

### 3. Lấy danh sách nội dung video

```typescript
async getVideoContentList(
  accessToken: string,
  options?: VideoContentListOptions
): Promise<ZaloVideoContentList>
```

### 4. Cập nhật nội dung video

```typescript
async updateVideoContent(
  accessToken: string,
  videoContentId: string,
  request: Partial<ZaloVideoContentRequest>
): Promise<ZaloVideoContentResponse>
```

### 5. Xóa nội dung video

```typescript
async deleteVideoContent(
  accessToken: string,
  videoContentId: string
): Promise<{ success: boolean; message?: string }>
```

### 6. Xuất bản nội dung video

```typescript
async publishVideoContent(
  accessToken: string,
  videoContentId: string,
  publishTime?: number
): Promise<ZaloVideoContentResponse>
```

### 7. Tìm kiếm nội dung video

```typescript
async searchVideoContent(
  accessToken: string,
  query: string,
  options?: VideoSearchOptions
): Promise<ZaloVideoContentList>
```

**Ví dụ:**
```typescript
const searchResults = await zaloContentService.searchVideoContent(accessToken, 'sản phẩm', {
  status: 'published',
  limit: 10,
  tags: ['video'],
  duration_min: 30,
  duration_max: 300
});
```

### 8. Lấy thống kê nội dung video

```typescript
async getVideoContentStatistics(
  accessToken: string,
  videoContentId: string
): Promise<VideoContentStatistics>
```

## Workflow tạo và xuất bản video content

```typescript
// 1. Upload video trước (sử dụng uploadVideo)
const videoUploadInfo = await zaloContentService.uploadVideo(accessToken, videoRequest);
const uploadResult = await zaloContentService.uploadVideoFile(
  videoUploadInfo.upload_url,
  videoUploadInfo.upload_token,
  fileInfo
);

// 2. Tạo nội dung video
const videoContent = await zaloContentService.createVideoContent(accessToken, {
  title: 'Video mới',
  description: 'Mô tả video',
  video_url: uploadResult.video_url,
  thumbnail_url: 'https://example.com/thumbnail.jpg',
  status: 'draft'
});

// 3. Xuất bản video content
await zaloContentService.publishVideoContent(accessToken, videoContent.video_content_id);
```

## Tài liệu tham khảo

- [Zalo Official Account Content API - Tổng quan](https://developers.zalo.me/docs/official-account/noi-dung/tong-quan)
- [Nội dung dạng Bài viết](https://developers.zalo.me/docs/official-account/noi-dung/noi-dung-dang-bai-viet/)
- [Nội dung dạng Video](https://developers.zalo.me/docs/official-account/noi-dung/noi-dung-dang-video/)
- [Chỉnh sửa nội dung dạng video](https://developers.zalo.me/docs/official-account/noi-dung/noi-dung-dang-video/chinh-sua-noi-dung-dang-video)
- [Lấy chi tiết nội dung dạng video](https://developers.zalo.me/docs/official-account/noi-dung/noi-dung-dang-video/lay-chi-tiet-noi-dung-dang-video)
- [Lấy danh sách nội dung dạng video](https://developers.zalo.me/docs/official-account/noi-dung/noi-dung-dang-video/lay-danh-sach-noi-dung-dang-video)
