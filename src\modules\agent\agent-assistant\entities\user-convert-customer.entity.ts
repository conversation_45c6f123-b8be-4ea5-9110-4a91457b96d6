import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * User Convert Customer entity
 * Khách hàng được chuyển đổi từ nền tảng khác
 */
@Entity('user_convert_customers')
export class UserConvertCustomer {
  /**
   * ID khách hàng
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Ảnh đại diện
   */
  @Column({ name: 'avatar', type: 'varchar', length: 255, nullable: true })
  avatar?: string;

  /**
   * Tên khách hàng
   */
  @Column({ name: 'name', type: 'varchar', length: 255, nullable: true })
  name?: string;

  /**
   * <PERSON>ail khách hàng (dạng JSON)
   */
  @Column({ name: 'email', type: 'jsonb', nullable: true })
  email?: any;

  /**
   * Mã quốc gia
   */
  @Column({ name: 'country_code', type: 'int', nullable: true })
  countryCode?: number;

  /**
   * <PERSON>ố điện thoại khách hàng (không bao gồm mã quốc gia)
   */
  @Column({ name: 'phone', type: 'varchar', length: 20, nullable: true })
  phone?: string;

  /**
   * Nền tảng nguồn (Facebook, Web,...)
   */
  @Column({ name: 'platform', type: 'varchar', length: 50, nullable: true })
  platform?: string;

  /**
   * Múi giờ của khách hàng
   */
  @Column({ name: 'timezone', type: 'varchar', length: 50, nullable: true })
  timezone?: string;

  /**
   * Thời gian tạo
   */
  @Column({ 
    name: 'created_at', 
    type: 'bigint', 
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    nullable: false 
  })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @Column({ 
    name: 'updated_at', 
    type: 'bigint', 
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    nullable: false 
  })
  updatedAt: number;

  /**
   * Người dùng sở hữu khách hàng
   */
  @Column({ name: 'user_id', type: 'int', nullable: true })
  userId?: number;

  /**
   * ID agent hỗ trợ khách hàng
   */
  @Column({ name: 'agent_id', type: 'uuid', nullable: true })
  agentId?: string;

  /**
   * Trường tùy chỉnh
   */
  @Column({ name: 'metadata', type: 'jsonb', default: '[]', nullable: false })
  metadata: any[];

  /**
   * Facebook link
   */
  @Column({ name: 'facebook_link', type: 'varchar', length: 500, nullable: true })
  facebookLink?: string;

  /**
   * Twitter link
   */
  @Column({ name: 'twitter_link', type: 'varchar', length: 500, nullable: true })
  twitterLink?: string;

  /**
   * LinkedIn link
   */
  @Column({ name: 'linkedin_link', type: 'varchar', length: 500, nullable: true })
  linkedinLink?: string;

  /**
   * Zalo link
   */
  @Column({ name: 'zalo_link', type: 'varchar', length: 500, nullable: true })
  zaloLink?: string;

  /**
   * Website link
   */
  @Column({ name: 'website_link', type: 'varchar', length: 500, nullable: true })
  websiteLink?: string;

  /**
   * Địa chỉ khách hàng
   */
  @Column({ name: 'address', type: 'varchar', length: 500, nullable: true })
  address?: string;

  /**
   * Tags
   */
  @Column({ name: 'tags', type: 'jsonb', default: '[]', nullable: false })
  tags: any[];
}
