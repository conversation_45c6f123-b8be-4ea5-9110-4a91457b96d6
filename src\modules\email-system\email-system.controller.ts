import { <PERSON>, Post, Body, Get, Logger } from '@nestjs/common';
import { EmailSystemService } from './email-system.service';
import { EmailSystemJobDto } from './dto/email-system-job.dto';

/**
 * Controller để test email system
 */
@Controller('api/email-system')
export class EmailSystemController {
  private readonly logger = new Logger(EmailSystemController.name);

  constructor(private readonly emailSystemService: EmailSystemService) {}

  /**
   * Test thêm job email vào queue
   */
  @Post('test/add-job')
  async testAddJob(@Body() jobData?: Partial<EmailSystemJobDto>) {
    try {
      // Dữ liệu test mặc định
      const testJobData: EmailSystemJobDto = {
        category: 'test',
        data: {
          name: 'Test User',
          email: '<EMAIL>',
          message: 'This is a test email from worker app',
        },
        to: '<EMAIL>',
        ...jobData,
      };

      this.logger.log(
        `<PERSON><PERSON> thêm test job vào queue: ${JSON.stringify(testJobData)}`,
      );

      const jobId = await this.emailSystemService.addEmailJob(testJobData);

      return {
        success: true,
        message: 'Job đã được thêm vào queue thành công',
        jobId,
        data: testJobData,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi thêm job: ${error.message}`, error.stack);
      return {
        success: false,
        message: 'Lỗi khi thêm job vào queue',
        error: error.message,
      };
    }
  }

  /**
   * Lấy thông tin queue
   */
  @Get('queue/info')
  async getQueueInfo() {
    try {
      // Thông tin cơ bản về queue
      return {
        success: true,
        message: 'Email system queue đang hoạt động',
        queueName: 'EMAIL_SYSTEM',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy thông tin queue: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        message: 'Lỗi khi lấy thông tin queue',
        error: error.message,
      };
    }
  }
}
