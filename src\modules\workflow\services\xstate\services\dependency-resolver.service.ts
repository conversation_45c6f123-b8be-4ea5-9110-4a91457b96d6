import { Injectable, Logger } from '@nestjs/common';
import { Connection } from '../../../entities/connection.entity';
import { Node } from '../../../entities/node.entity';
import { DependencyGraph } from '../types';

/**
 * Dependency analysis result
 */
export interface DependencyAnalysisResult {
  /** Dependency graph */
  graph: DependencyGraph;
  
  /** Có circular dependencies không */
  hasCircularDependencies: boolean;
  
  /** Danh sách circular dependency paths */
  circularPaths: string[][];
  
  /** Execution order (topological sort) */
  executionOrder: string[];
  
  /** Nodes có thể chạy song song */
  parallelGroups: string[][];
  
  /** Orphaned nodes (không có connections) */
  orphanedNodes: string[];
  
  /** Analysis metadata */
  metadata: {
    totalNodes: number;
    totalConnections: number;
    maxDepth: number;
    analysisTime: number;
  };
}

/**
 * Service để analyze và resolve node dependencies
 */
@Injectable()
export class DependencyResolverService {
  private readonly logger = new Logger(DependencyResolverService.name);
  
  /**
   * Analyze workflow dependencies và build dependency graph
   */
  async analyzeDependencies(
    nodes: Node[], 
    connections: Connection[]
  ): Promise<DependencyAnalysisResult> {
    const startTime = Date.now();
    
    this.logger.debug(`Analyzing dependencies for ${nodes.length} nodes and ${connections.length} connections`);
    
    // Build dependency graph
    const graph = this.buildDependencyGraph(nodes, connections);
    
    // Detect circular dependencies
    const circularPaths = this.detectCircularDependencies(graph);
    const hasCircularDependencies = circularPaths.length > 0;
    
    // Calculate execution order (topological sort)
    const executionOrder = hasCircularDependencies 
      ? [] 
      : this.calculateExecutionOrder(graph);
    
    // Find parallel execution groups
    const parallelGroups = this.findParallelGroups(graph, executionOrder);
    
    // Find orphaned nodes
    const orphanedNodes = this.findOrphanedNodes(nodes, connections);
    
    // Calculate max depth
    const maxDepth = this.calculateMaxDepth(graph);
    
    const analysisTime = Date.now() - startTime;
    
    const result: DependencyAnalysisResult = {
      graph,
      hasCircularDependencies,
      circularPaths,
      executionOrder,
      parallelGroups,
      orphanedNodes,
      metadata: {
        totalNodes: nodes.length,
        totalConnections: connections.length,
        maxDepth,
        analysisTime,
      },
    };
    
    this.logger.debug(`Dependency analysis completed in ${analysisTime}ms`);
    
    if (hasCircularDependencies) {
      this.logger.warn(`Found ${circularPaths.length} circular dependency paths`);
    }
    
    return result;
  }
  
  /**
   * Get nodes ready for execution (dependencies satisfied)
   */
  getReadyNodes(
    graph: DependencyGraph, 
    completedNodes: Set<string>
  ): string[] {
    const readyNodes: string[] = [];
    
    for (const nodeId of graph.dependencies.keys()) {
      // Skip if already completed
      if (completedNodes.has(nodeId)) {
        continue;
      }
      
      // Check if all dependencies are completed
      const dependencies = graph.dependencies.get(nodeId) || [];
      const allDependenciesCompleted = dependencies.every(depId => 
        completedNodes.has(depId)
      );
      
      if (allDependenciesCompleted) {
        readyNodes.push(nodeId);
      }
    }
    
    return readyNodes;
  }
  
  /**
   * Get nodes waiting for dependencies
   */
  getWaitingNodes(
    graph: DependencyGraph, 
    completedNodes: Set<string>,
    runningNodes: Set<string>
  ): string[] {
    const waitingNodes: string[] = [];
    
    for (const nodeId of graph.dependencies.keys()) {
      // Skip if already completed or running
      if (completedNodes.has(nodeId) || runningNodes.has(nodeId)) {
        continue;
      }
      
      // Check if has pending dependencies
      const dependencies = graph.dependencies.get(nodeId) || [];
      const hasPendingDependencies = dependencies.some(depId => 
        !completedNodes.has(depId)
      );
      
      if (hasPendingDependencies) {
        waitingNodes.push(nodeId);
      }
    }
    
    return waitingNodes;
  }
  
  /**
   * Validate dependency graph
   */
  validateDependencyGraph(graph: DependencyGraph): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Check for circular dependencies
    const circularPaths = this.detectCircularDependencies(graph);
    if (circularPaths.length > 0) {
      errors.push(`Found ${circularPaths.length} circular dependency paths`);
      circularPaths.forEach((path, index) => {
        errors.push(`Circular path ${index + 1}: ${path.join(' -> ')}`);
      });
    }
    
    // Check for orphaned nodes
    const allNodes = new Set([
      ...graph.dependencies.keys(),
      ...graph.dependents.keys(),
    ]);
    
    const orphanedNodes = Array.from(allNodes).filter(nodeId => {
      const deps = graph.dependencies.get(nodeId) || [];
      const dependents = graph.dependents.get(nodeId) || [];
      return deps.length === 0 && dependents.length === 0;
    });
    
    if (orphanedNodes.length > 0) {
      warnings.push(`Found ${orphanedNodes.length} orphaned nodes: ${orphanedNodes.join(', ')}`);
    }
    
    // Check for missing dependencies
    for (const [nodeId, deps] of graph.dependencies.entries()) {
      for (const depId of deps) {
        if (!allNodes.has(depId)) {
          errors.push(`Node ${nodeId} depends on missing node ${depId}`);
        }
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }
  
  /**
   * Optimize execution order for parallel execution
   */
  optimizeExecutionOrder(
    graph: DependencyGraph,
    maxConcurrency: number = 5
  ): {
    optimizedGroups: string[][];
    estimatedTime: number;
    parallelismFactor: number;
  } {
    const groups: string[][] = [];
    const processed = new Set<string>();
    const remaining = new Set(graph.dependencies.keys());
    
    while (remaining.size > 0) {
      const currentGroup: string[] = [];
      
      // Find nodes that can run in parallel
      for (const nodeId of remaining) {
        if (processed.has(nodeId)) continue;
        
        // Check if all dependencies are processed
        const dependencies = graph.dependencies.get(nodeId) || [];
        const canRun = dependencies.every(depId => processed.has(depId));
        
        if (canRun && currentGroup.length < maxConcurrency) {
          currentGroup.push(nodeId);
        }
      }
      
      if (currentGroup.length === 0) {
        // Deadlock - should not happen with valid graph
        this.logger.error('Deadlock detected in execution order optimization');
        break;
      }
      
      groups.push(currentGroup);
      currentGroup.forEach(nodeId => {
        processed.add(nodeId);
        remaining.delete(nodeId);
      });
    }
    
    const totalNodes = graph.dependencies.size;
    const sequentialTime = totalNodes; // Assume 1 time unit per node
    const parallelTime = groups.length;
    const parallelismFactor = sequentialTime / parallelTime;
    
    return {
      optimizedGroups: groups,
      estimatedTime: parallelTime,
      parallelismFactor,
    };
  }
  
  // Private helper methods
  
  private buildDependencyGraph(nodes: Node[], connections: Connection[]): DependencyGraph {
    const dependencies = new Map<string, string[]>();
    const dependents = new Map<string, string[]>();
    
    // Initialize all nodes
    nodes.forEach(node => {
      dependencies.set(node.id, []);
      dependents.set(node.id, []);
    });
    
    // Build dependency relationships from connections
    connections.forEach(connection => {
      const sourceId = connection.sourceNodeId;
      const targetId = connection.targetNodeId;
      
      // Target depends on source
      const targetDeps = dependencies.get(targetId) || [];
      if (!targetDeps.includes(sourceId)) {
        targetDeps.push(sourceId);
        dependencies.set(targetId, targetDeps);
      }
      
      // Source has target as dependent
      const sourceDependents = dependents.get(sourceId) || [];
      if (!sourceDependents.includes(targetId)) {
        sourceDependents.push(targetId);
        dependents.set(sourceId, sourceDependents);
      }
    });
    
    // Find root and leaf nodes
    const rootNodes = Array.from(dependencies.entries())
      .filter(([_, deps]) => deps.length === 0)
      .map(([nodeId]) => nodeId);
    
    const leafNodes = Array.from(dependents.entries())
      .filter(([_, deps]) => deps.length === 0)
      .map(([nodeId]) => nodeId);
    
    return {
      dependencies,
      dependents,
      rootNodes,
      leafNodes,
    };
  }
  
  private detectCircularDependencies(graph: DependencyGraph): string[][] {
    const circularPaths: string[][] = [];
    const visited = new Set<string>();
    const recursionStack = new Set<string>();
    
    const dfs = (nodeId: string, path: string[]): void => {
      if (recursionStack.has(nodeId)) {
        // Found circular dependency
        const circularStart = path.indexOf(nodeId);
        const circularPath = path.slice(circularStart).concat([nodeId]);
        circularPaths.push(circularPath);
        return;
      }
      
      if (visited.has(nodeId)) {
        return;
      }
      
      visited.add(nodeId);
      recursionStack.add(nodeId);
      path.push(nodeId);
      
      const dependencies = graph.dependencies.get(nodeId) || [];
      for (const depId of dependencies) {
        dfs(depId, [...path]);
      }
      
      recursionStack.delete(nodeId);
    };
    
    for (const nodeId of graph.dependencies.keys()) {
      if (!visited.has(nodeId)) {
        dfs(nodeId, []);
      }
    }
    
    return circularPaths;
  }
  
  private calculateExecutionOrder(graph: DependencyGraph): string[] {
    const order: string[] = [];
    const inDegree = new Map<string, number>();
    const queue: string[] = [];
    
    // Calculate in-degree for each node
    for (const nodeId of graph.dependencies.keys()) {
      inDegree.set(nodeId, (graph.dependencies.get(nodeId) || []).length);
    }
    
    // Find nodes with no dependencies
    for (const [nodeId, degree] of inDegree.entries()) {
      if (degree === 0) {
        queue.push(nodeId);
      }
    }
    
    // Process nodes in topological order
    while (queue.length > 0) {
      const nodeId = queue.shift()!;
      order.push(nodeId);
      
      // Reduce in-degree of dependent nodes
      const dependents = graph.dependents.get(nodeId) || [];
      for (const depId of dependents) {
        const newDegree = (inDegree.get(depId) || 0) - 1;
        inDegree.set(depId, newDegree);
        
        if (newDegree === 0) {
          queue.push(depId);
        }
      }
    }
    
    return order;
  }
  
  private findParallelGroups(graph: DependencyGraph, executionOrder: string[]): string[][] {
    const groups: string[][] = [];
    const processed = new Set<string>();
    
    for (const nodeId of executionOrder) {
      if (processed.has(nodeId)) continue;
      
      const currentGroup = [nodeId];
      processed.add(nodeId);
      
      // Find other nodes that can run in parallel
      for (const otherId of executionOrder) {
        if (processed.has(otherId)) continue;
        
        // Check if nodes can run in parallel (no dependency relationship)
        const deps1 = graph.dependencies.get(nodeId) || [];
        const deps2 = graph.dependencies.get(otherId) || [];
        
        const canRunInParallel = !deps1.includes(otherId) && 
                                !deps2.includes(nodeId) &&
                                !this.hasTransitiveDependency(graph, nodeId, otherId) &&
                                !this.hasTransitiveDependency(graph, otherId, nodeId);
        
        if (canRunInParallel) {
          currentGroup.push(otherId);
          processed.add(otherId);
        }
      }
      
      groups.push(currentGroup);
    }
    
    return groups;
  }
  
  private findOrphanedNodes(nodes: Node[], connections: Connection[]): string[] {
    const connectedNodes = new Set<string>();
    
    connections.forEach(connection => {
      connectedNodes.add(connection.sourceNodeId);
      connectedNodes.add(connection.targetNodeId);
    });
    
    return nodes
      .filter(node => !connectedNodes.has(node.id))
      .map(node => node.id);
  }
  
  private calculateMaxDepth(graph: DependencyGraph): number {
    let maxDepth = 0;
    
    const calculateDepth = (nodeId: string, visited: Set<string>): number => {
      if (visited.has(nodeId)) {
        return 0; // Avoid infinite recursion
      }
      
      visited.add(nodeId);
      
      const dependencies = graph.dependencies.get(nodeId) || [];
      if (dependencies.length === 0) {
        return 1;
      }
      
      let maxDepthFromDeps = 0;
      for (const depId of dependencies) {
        const depth = calculateDepth(depId, new Set(visited));
        maxDepthFromDeps = Math.max(maxDepthFromDeps, depth);
      }
      
      return maxDepthFromDeps + 1;
    };
    
    for (const nodeId of graph.dependencies.keys()) {
      const depth = calculateDepth(nodeId, new Set());
      maxDepth = Math.max(maxDepth, depth);
    }
    
    return maxDepth;
  }
  
  private hasTransitiveDependency(
    graph: DependencyGraph, 
    fromId: string, 
    toId: string,
    visited: Set<string> = new Set()
  ): boolean {
    if (visited.has(fromId)) {
      return false;
    }
    
    visited.add(fromId);
    
    const dependencies = graph.dependencies.get(fromId) || [];
    
    if (dependencies.includes(toId)) {
      return true;
    }
    
    for (const depId of dependencies) {
      if (this.hasTransitiveDependency(graph, depId, toId, visited)) {
        return true;
      }
    }
    
    return false;
  }
}
