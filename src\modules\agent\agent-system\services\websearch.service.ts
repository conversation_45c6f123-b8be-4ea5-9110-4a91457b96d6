import { Injectable, Logger } from '@nestjs/common';
import { backOff } from 'exponential-backoff';
import OpenAI from 'openai';
import { ChatDatabaseService } from '../database.service';
import { webSearchKeyEncryption } from '../../helpers/websearch-key-encryption.helper';

export interface WebSearchKeyRecord {
  id: number;
  key: string;
  provider: string;
  created_at: number;
  updated_at: number;
  active: boolean;
}

export interface WebSearchOptions {
  search_context_size?: 'low' | 'medium' | 'high';
  user_location?: {
    type: 'approximate';
    approximate: {
      country?: string; // Two-letter ISO country code (e.g., "US", "GB")
      city?: string; // Free text string (e.g., "Minneapolis")
      region?: string; // Free text string (e.g., "Minnesota")
      timezone?: string; // IANA timezone (e.g., "America/Chicago")
    };
  };
}

export interface WebSearchResponse {
  content: string;
  annotations: Array<{
    type: 'url_citation';
    url_citation: {
      end_index: number;
      start_index: number;
      title: string;
      url: string;
    };
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface WebSearchError {
  keyId: number;
  provider: string;
  error: string;
  statusCode?: number;
  retryAfter?: number;
  attemptNumber: number;
}

/**
 * Web Search Service
 *
 * This service handles API key management and web search operations using OpenAI SDK
 * with exponential backoff across multiple API keys, caching mechanisms, and error handling.
 */
@Injectable()
export class WebSearchService {
  private readonly logger = new Logger(WebSearchService.name);

  // Cache for decrypted keys and OpenAI clients
  private keyCache = new Map<number, string>();
  private clientCache = new Map<number, OpenAI>();
  private cacheExpiry = new Map<number, number>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor(private readonly databaseService: ChatDatabaseService) {
    setInterval(() => this.clearExpiredCache(), 60 * 1000);
  }

  /**
   * Clear expired cache entries
   */
  private clearExpiredCache(): void {
    const now = Date.now();
    for (const [keyId, expiry] of this.cacheExpiry.entries()) {
      if (now > expiry) {
        this.keyCache.delete(keyId);
        this.clientCache.delete(keyId);
        this.cacheExpiry.delete(keyId);
      }
    }
  }

  /**
   * Perform web search with exponential backoff across multiple API keys
   */
  async performWebSearch(
    query: string,
    options?: WebSearchOptions,
    model: string = 'gpt-4o-search-preview',
  ): Promise<WebSearchResponse> {
    const startTime = Date.now();

    this.logger.debug(`Performing web search`, {
      query: query.substring(0, 100), // Log first 100 chars for privacy
      model,
      hasLocationOptions: !!options?.user_location,
      searchContext: options?.search_context_size,
      locationDetails: options?.user_location
        ? {
            hasCountry: !!options.user_location.approximate?.country,
            hasCity: !!options.user_location.approximate?.city,
            hasRegion: !!options.user_location.approximate?.region,
            hasTimezone: !!options.user_location.approximate?.timezone,
          }
        : null,
    });

    // Get active keys for OpenAI provider
    const keys = await this.getActiveWebSearchKeysByProvider('openai');

    if (keys.length === 0) {
      throw new Error('No active web search keys found for provider: openai');
    }

    const errors: WebSearchError[] = [];
    let lastError: Error | null = null;

    // Try each key with exponential backoff
    for (let keyIndex = 0; keyIndex < keys.length; keyIndex++) {
      const key = keys[keyIndex];

      try {
        const result = await backOff(
          () => this.callWebSearchAPI(query, options, model, key),
          {
            numOfAttempts: 3,
            startingDelay: 1000, // 1 second
            timeMultiple: 2, // Double the delay each time
            maxDelay: 30000, // Max 30 seconds
            jitter: 'full', // Add jitter to prevent thundering herd
            retry: (error: any, attemptNumber: number) => {
              const webSearchError: WebSearchError = {
                keyId: key.id,
                provider: key.provider,
                error: error.message,
                statusCode: error.status || error.response?.status,
                retryAfter: error.headers?.['retry-after'],
                attemptNumber,
              };

              errors.push(webSearchError);

              this.logger.warn(`Web search API call failed for key ${key.id}`, {
                keyId: key.id,
                keyIndex,
                attemptNumber,
                error: error.message,
                statusCode: error.status || error.response?.status,
                errorType: error.type || error.code,
                retryAfter: error.headers?.['retry-after'],
                willRetry: attemptNumber < 3,
              });

              return this.shouldRetryError(error);
            },
          },
        );

        const duration = Date.now() - startTime;

        this.logger.debug(`Web search completed successfully`, {
          keyId: key.id,
          duration,
          model,
          hasAnnotations: result.annotations && result.annotations.length > 0,
          annotationCount: result.annotations?.length || 0,
          responseLength: result.content?.length || 0,
        });

        return result;
      } catch (error) {
        lastError = error;
        this.logger.warn(`Web search failed for key ${key.id}`, {
          keyId: key.id,
          keyIndex,
          totalKeys: keys.length,
          error: error.message,
          willTryNextKey: keyIndex < keys.length - 1,
        });
      }
    }

    // All keys failed
    const duration = Date.now() - startTime;
    this.logger.error(`All web search keys failed`, {
      totalKeys: keys.length,
      totalErrors: errors.length,
      duration,
      lastError: lastError?.message,
      query: query.substring(0, 50), // Truncated for privacy
    });

    throw new Error(
      `Web search failed after trying ${keys.length} API keys. Last error: ${lastError?.message}`,
    );
  }

  /**
   * Call the OpenAI web search API
   */
  private async callWebSearchAPI(
    query: string,
    options: WebSearchOptions | undefined,
    model: string,
    key: WebSearchKeyRecord,
  ): Promise<WebSearchResponse> {
    const client = await this.getOrCreateClient(key);

    const requestOptions: any = {
      model,
      messages: [
        {
          role: 'user',
          content: query,
        },
      ],
    };

    // Add web search options if provided
    if (options) {
      requestOptions.web_search_options = {};

      if (options.search_context_size) {
        requestOptions.web_search_options.search_context_size =
          options.search_context_size;
      }

      if (options.user_location) {
        requestOptions.web_search_options.user_location = options.user_location;
      }
    } else {
      // Default empty web_search_options to enable web search
      requestOptions.web_search_options = {};
    }

    this.logger.debug(`Making web search API call`, {
      keyId: key.id,
      model,
      hasSearchOptions: !!options,
      searchContext: options?.search_context_size,
      hasUserLocation: !!options?.user_location,
    });

    const completion = await client.chat.completions.create(requestOptions);

    const choice = completion.choices[0];
    if (!choice) {
      throw new Error('No response choice returned from OpenAI API');
    }

    return {
      content: choice.message.content || '',
      annotations: choice.message.annotations || [],
      usage: {
        prompt_tokens: completion.usage?.prompt_tokens || 0,
        completion_tokens: completion.usage?.completion_tokens || 0,
        total_tokens: completion.usage?.total_tokens || 0,
      },
    };
  }

  /**
   * Get active web search keys for a specific provider
   */
  private async getActiveWebSearchKeysByProvider(
    provider: string,
  ): Promise<WebSearchKeyRecord[]> {
    const selectQuery = `
      SELECT id, key, provider, created_at, updated_at, active
      FROM system_config_websearch_key
      WHERE active = true AND provider = $1
      ORDER BY id ASC
    `;

    try {
      const result = await this.databaseService.query(selectQuery, [provider]);

      this.logger.debug(
        `Retrieved ${result.length} active web search keys for provider ${provider}`,
        {
          provider,
          keyCount: result.length,
        },
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to retrieve active web search keys for provider ${provider}`,
        {
          provider,
          error: error.message,
          stack: error.stack,
        },
      );
      throw error;
    }
  }

  /**
   * Get or create OpenAI client for a specific key
   */
  private async getOrCreateClient(key: WebSearchKeyRecord): Promise<OpenAI> {
    // Check if client is cached and not expired
    const cachedClient = this.clientCache.get(key.id);
    const expiry = this.cacheExpiry.get(key.id);

    if (cachedClient && expiry && Date.now() < expiry) {
      return cachedClient;
    }

    // Get decrypted API key
    const apiKey = await this.getDecryptedKey(key);

    // Create new OpenAI client
    const client = new OpenAI({
      apiKey,
      timeout: 60000, // 60 seconds timeout
      maxRetries: 0, // We handle retries ourselves
    });

    // Cache the client
    this.clientCache.set(key.id, client);
    this.cacheExpiry.set(key.id, Date.now() + this.CACHE_TTL);

    this.logger.debug(`Created and cached OpenAI client for key ${key.id}`, {
      keyId: key.id,
      provider: key.provider,
    });

    return client;
  }

  /**
   * Get decrypted API key for a specific key record
   */
  private async getDecryptedKey(
    keyRecord: WebSearchKeyRecord,
  ): Promise<string> {
    // Check if key is cached and not expired
    const cachedKey = this.keyCache.get(keyRecord.id);
    const expiry = this.cacheExpiry.get(keyRecord.id);

    if (cachedKey && expiry && Date.now() < expiry) {
      return cachedKey;
    }

    // Decrypt and cache the key
    try {
      const decryptedKey = webSearchKeyEncryption.decryptWebSearchApiKey(
        keyRecord.key,
      );
      this.keyCache.set(keyRecord.id, decryptedKey);
      // Note: We don't update cacheExpiry here since it's shared with client cache

      this.logger.debug(`Decrypted and cached web search key`, {
        keyId: keyRecord.id,
        provider: keyRecord.provider,
      });

      return decryptedKey;
    } catch (error) {
      this.logger.error(`Failed to decrypt web search key ${keyRecord.id}`, {
        keyId: keyRecord.id,
        provider: keyRecord.provider,
        error: error.message,
      });
      throw new Error(`Failed to decrypt web search key: ${error.message}`);
    }
  }

  /**
   * Determine if an error should be retried
   */
  private shouldRetryError(error: any): boolean {
    const statusCode = error.status || error.response?.status;
    const errorType = this.getErrorType(error);

    // Retry on server errors (5xx), rate limits (429), and network errors
    if (statusCode >= 500 || statusCode === 429) {
      return true;
    }

    // Retry on network/connection errors
    if (errorType === 'network' || errorType === 'timeout') {
      return true;
    }

    // Don't retry on client errors (4xx except 429)
    return false;
  }

  /**
   * Get error type for logging and retry logic
   */
  private getErrorType(error: any): string {
    if (
      error.code === 'ECONNRESET' ||
      error.code === 'ENOTFOUND' ||
      error.code === 'ECONNREFUSED'
    ) {
      return 'network';
    }
    if (error.code === 'ETIMEDOUT' || error.type === 'timeout') {
      return 'timeout';
    }
    if (error.status || error.response?.status) {
      return 'http';
    }
    return 'unknown';
  }
}
