// Re-export types from Zod schemas (single source of truth)
export type {
  <PERSON><PERSON><PERSON>,
  AssistantModelConfig,
  AgentAssistantConfig,
  AgentAssistantConfigMap,
  AgentAssistantCustomConfigurableType,
} from '../schemas/agent-assistant.schema';

// Re-export schemas for validation
export {
  GenderEnumSchema,
  PaymentMethodEnumSchema,
  ProviderShipmentTypeEnumSchema,
  ProfileAgentSchema,
  TrimmingTypeSchema,
  AssistantModelConfigSchema,
  AgentAssistantConfigSchema,
  AgentAssistantConfigMapSchema,
  AgentAssistantCustomConfigurableTypeSchema,
} from '../schemas/agent-assistant.schema';

import type {
  AgentAssistantConfig,
  AgentAssistantConfigMap,
  AssistantModelConfig,
} from '../schemas/agent-assistant.schema';

/**
 * Type guard to check if an agent config is a user agent
 */
export function isUserAgentConfig(config: any): config is AgentAssistantConfig {
  return config?.model?.type === 'USER';
}

/**
 * Type guard to check if an agent config is a fine-tuned agent
 */
export function isFineTunedAgentConfig(config: any): config is AgentAssistantConfig {
  return config?.model?.type === 'FINE_TUNE';
}

/**
 * Type guard to check if an agent config has payment capabilities
 */
export function hasPaymentCapabilities(config: any): config is AgentAssistantConfig {
  return config?.paymentGatewayId !== null && config?.paymentGatewayId !== undefined;
}

/**
 * Type guard to check if an agent config has shipment capabilities
 */
export function hasShipmentCapabilities(config: any): config is AgentAssistantConfig {
  return config?.userProviderShipmentId !== null && config?.userProviderShipmentId !== undefined;
}
