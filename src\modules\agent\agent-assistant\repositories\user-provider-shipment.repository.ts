import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserProviderShipment } from '../entities/user-provider-shipment.entity';

@Injectable()
export class UserProviderShipmentRepository {
  constructor(
    @InjectRepository(UserProviderShipment)
    private readonly repository: Repository<UserProviderShipment>,
  ) {}

  async findById(id: string): Promise<UserProviderShipment | null> {
    return this.repository.findOne({ 
      where: { id } 
    });
  }
}
