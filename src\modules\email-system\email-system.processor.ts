import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName } from '../../queue';
import { EmailSystemService } from './email-system.service';
import { EmailService } from '../../shared/services/email.service';
import { EmailSystemJobDto } from './dto/email-system-job.dto';

/**
 * Processor xử lý queue gửi email
 */
@Injectable()
@Processor(QueueName.EMAIL_SYSTEM)
export class EmailSystemProcessor extends WorkerHost {
  private readonly logger = new Logger(EmailSystemProcessor.name);

  constructor(
    private readonly emailSystemService: EmailSystemService,
    private readonly emailService: EmailService,
  ) {
    super();
  }

  /**
   * Xử lý job gửi email
   * @param job Job từ queue
   */
  async process(job: Job<EmailSystemJobDto>): Promise<any> {
    try {
      this.logger.log(
        `Đang xử lý job email ${job.id} với category: ${job.data.category}`,
      );

      const { category, data, to } = job.data;

      // Lấy template email từ database
      const template =
        await this.emailSystemService.getTemplateByCategory(category);
      if (!template) {
        throw new Error(
          `Không tìm thấy template email với category: ${category}`,
        );
      }

      // Thay thế các placeholder trong tiêu đề và nội dung
      const subject = this.emailSystemService.replacePlaceholders(
        template.subject,
        data,
      );

      const content = this.emailSystemService.replacePlaceholders(
        template.content,
        data,
      );

      // Gửi email
      const result = await this.emailService.sendEmail(to, subject, content);

      if (result) {
        this.logger.log(
          `Đã gửi email thành công đến ${to} với category: ${category}`,
        );
        return { success: true, message: 'Email đã được gửi thành công' };
      } else {
        throw new Error('Không thể gửi email');
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý job email: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
