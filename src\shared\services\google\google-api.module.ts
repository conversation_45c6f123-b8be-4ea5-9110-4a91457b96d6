import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { GoogleOAuthService } from './auth/google-oauth.service';
// import { GoogleStorageService } from './google-storage.service';
// import { GoogleVisionService } from './google-vision.service';
// import { GoogleTranslateService } from './google-translate.service';
// import {
//   GoogleAdsService,
//   GoogleAdsCampaignService,
//   GoogleAdsKeywordService,
//   GoogleAdsReportService
// } from './ads';
// import { GoogleGmailApiService } from './gmail/google-gmail-api.service';
import { GoogleGmailOAuthService } from './auth/google-gmail-oauth.service';
// import { GoogleGmailEmailService } from './gmail/google-gmail-email.service';
import { GoogleSheetsService } from './sheets/google-sheets.service';
// import { GoogleDriveService } from './drive/google-drive.service';
// import { GoogleDocsService } from './docs/google-docs.service';
// import { GoogleCalendarService } from './calendar/google-calendar.service';
// import { GoogleAnalyticsService } from './analytics/google-analytics.service';

@Module({
  imports: [ConfigModule],
  providers: [
    GoogleOAuthService,
    // GoogleStorageService,
    // GoogleVisionService,
    // GoogleTranslateService,
    // GoogleAdsService,
    // GoogleAdsCampaignService,
    // GoogleAdsKeywordService,
    // GoogleAdsReportService,
    // GoogleGmailApiService,
    GoogleGmailOAuthService,
    // GoogleGmailEmailService,
    GoogleSheetsService,
    // GoogleDriveService,
    // GoogleDocsService,
    // GoogleCalendarService,
    // GoogleAnalyticsService,
  ],
  exports: [
    GoogleOAuthService,
    // GoogleStorageService,
    // GoogleVisionService,
    // GoogleTranslateService,
    // GoogleAdsService,
    // GoogleAdsCampaignService,
    // GoogleAdsKeywordService,
    // GoogleAdsReportService,
    // GoogleGmailApiService,
    GoogleGmailOAuthService,
    // GoogleGmailEmailService,
    GoogleSheetsService,
    // GoogleDriveService,
    // GoogleDocsService,
    // GoogleCalendarService,
    // GoogleAnalyticsService,
  ],
})
export class GoogleApiModule {}
