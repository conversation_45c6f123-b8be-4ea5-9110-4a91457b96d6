import { Injectable, Logger } from '@nestjs/common';
import { Node } from '../../../entities/node.entity';
import { NodeDefinition } from '../../../entities/node-definition.entity';
import { NodeGroupEnum } from '../../../enums/node-group.enum';

/**
 * Agent node detection result
 */
export interface AgentNodeDetectionResult {
  /** Có phải agent node không */
  isAgentNode: boolean;
  
  /** Agent ID nếu có */
  agentId?: string;
  
  /** Confidence score (0-1) */
  confidence: number;
  
  /** Detection method used */
  detectionMethod: 'agentId' | 'nodeType' | 'parameters' | 'heuristic';
  
  /** Agent configuration extracted */
  agentConfig?: {
    model?: {
      name?: string;
      temperature?: number;
      maxTokens?: number;
    };
    tools?: string[];
    systemPrompt?: string;
    enableCheckpoints?: boolean;
    streaming?: boolean;
  };
  
  /** Validation results */
  validation: {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    suggestions: string[];
  };
}

/**
 * Agent node patterns để detection
 */
interface AgentNodePattern {
  /** Pattern name */
  name: string;
  
  /** Node type patterns */
  nodeTypes?: string[];
  
  /** Parameter patterns */
  parameterPatterns?: {
    required?: string[];
    optional?: string[];
    agentIdFields?: string[];
  };
  
  /** Node group */
  nodeGroup?: NodeGroupEnum;
  
  /** Confidence score nếu match */
  confidence: number;
}

/**
 * Service để detect và validate agent nodes
 */
@Injectable()
export class AgentNodeDetectorService {
  private readonly logger = new Logger(AgentNodeDetectorService.name);
  
  // Predefined patterns để detect agent nodes
  private readonly agentNodePatterns: AgentNodePattern[] = [
    {
      name: 'Direct Agent ID',
      confidence: 1.0,
      parameterPatterns: {
        agentIdFields: ['agentId', 'agent_id'],
      },
    },
    {
      name: 'AI Node Group',
      nodeGroup: NodeGroupEnum.AI,
      nodeTypes: [
        'ai-agent',
        'chat-agent', 
        'assistant-agent',
        'langgraph-agent',
        'openai-agent',
        'claude-agent',
      ],
      confidence: 0.9,
    },
    {
      name: 'LangGraph Node Types',
      nodeTypes: [
        'langgraph-workflow',
        'langgraph-chain',
        'langgraph-graph',
        'multi-agent',
        'react-agent',
      ],
      confidence: 0.95,
    },
    {
      name: 'Agent Parameters',
      parameterPatterns: {
        required: ['model'],
        optional: ['tools', 'systemPrompt', 'temperature'],
      },
      confidence: 0.7,
    },
    {
      name: 'Conversation Patterns',
      parameterPatterns: {
        optional: ['messages', 'threadId', 'conversationId', 'chatHistory'],
      },
      confidence: 0.6,
    },
  ];
  
  /**
   * Detect xem node có phải agent node không
   */
  async detectAgentNode(
    node: Node, 
    nodeDefinition?: NodeDefinition
  ): Promise<AgentNodeDetectionResult> {
    this.logger.debug(`Detecting agent node for: ${node.id} (${node.name})`);
    
    const detectionResults: Array<{
      pattern: AgentNodePattern;
      matches: boolean;
      confidence: number;
      method: AgentNodeDetectionResult['detectionMethod'];
    }> = [];
    
    // 1. Check direct agentId field
    if (node.agentId && node.agentId.trim() !== '') {
      detectionResults.push({
        pattern: this.agentNodePatterns[0],
        matches: true,
        confidence: 1.0,
        method: 'agentId',
      });
    }
    
    // 2. Check node type patterns
    if (nodeDefinition) {
      for (const pattern of this.agentNodePatterns) {
        if (pattern.nodeTypes) {
          const matches = pattern.nodeTypes.some(type => 
            nodeDefinition.typeName.toLowerCase().includes(type.toLowerCase())
          );
          
          if (matches) {
            detectionResults.push({
              pattern,
              matches: true,
              confidence: pattern.confidence,
              method: 'nodeType',
            });
          }
        }
        
        if (pattern.nodeGroup && nodeDefinition.groupName === pattern.nodeGroup) {
          detectionResults.push({
            pattern,
            matches: true,
            confidence: pattern.confidence * 0.8, // Slightly lower confidence
            method: 'nodeType',
          });
        }
      }
    }
    
    // 3. Check parameter patterns
    const parameterAnalysis = this.analyzeNodeParameters(node);
    for (const pattern of this.agentNodePatterns) {
      if (pattern.parameterPatterns) {
        const matches = this.matchParameterPattern(parameterAnalysis, pattern.parameterPatterns);
        
        if (matches.score > 0) {
          detectionResults.push({
            pattern,
            matches: true,
            confidence: pattern.confidence * matches.score,
            method: 'parameters',
          });
        }
      }
    }
    
    // 4. Heuristic detection
    const heuristicResult = this.performHeuristicDetection(node, nodeDefinition);
    if (heuristicResult.confidence > 0.3) {
      detectionResults.push({
        pattern: { name: 'Heuristic', confidence: heuristicResult.confidence },
        matches: true,
        confidence: heuristicResult.confidence,
        method: 'heuristic',
      });
    }
    
    // Calculate final result
    const finalResult = this.calculateFinalDetectionResult(
      node, 
      nodeDefinition, 
      detectionResults,
      parameterAnalysis
    );
    
    this.logger.debug(`Agent detection result for ${node.id}: ${finalResult.isAgentNode} (confidence: ${finalResult.confidence})`);
    
    return finalResult;
  }
  
  /**
   * Batch detect agent nodes
   */
  async detectAgentNodes(
    nodes: Node[], 
    nodeDefinitions?: Map<string, NodeDefinition>
  ): Promise<Map<string, AgentNodeDetectionResult>> {
    const results = new Map<string, AgentNodeDetectionResult>();
    
    for (const node of nodes) {
      const nodeDefinition = nodeDefinitions?.get(node.nodeDefinitionId || '');
      const result = await this.detectAgentNode(node, nodeDefinition);
      results.set(node.id, result);
    }
    
    return results;
  }
  
  /**
   * Validate agent node configuration
   */
  async validateAgentNodeConfiguration(
    node: Node,
    detectionResult: AgentNodeDetectionResult
  ): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
    suggestions: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];
    
    if (!detectionResult.isAgentNode) {
      return {
        isValid: true,
        errors,
        warnings,
        suggestions,
      };
    }
    
    // Validate agent ID
    if (!detectionResult.agentId) {
      errors.push('Agent node must have a valid agentId');
      suggestions.push('Set the agentId field in the node configuration');
    }
    
    // Validate agent configuration
    if (detectionResult.agentConfig) {
      const config = detectionResult.agentConfig;
      
      // Model validation
      if (config.model) {
        if (!config.model.name) {
          warnings.push('Model name not specified, will use default');
          suggestions.push('Specify model name for better control');
        }
        
        if (config.model.temperature !== undefined) {
          if (config.model.temperature < 0 || config.model.temperature > 2) {
            warnings.push('Temperature should be between 0 and 2');
            suggestions.push('Adjust temperature to be within valid range');
          }
        }
        
        if (config.model.maxTokens !== undefined) {
          if (config.model.maxTokens > 4096) {
            warnings.push('MaxTokens is very high, may cause performance issues');
            suggestions.push('Consider reducing maxTokens for better performance');
          }
          
          if (config.model.maxTokens < 10) {
            warnings.push('MaxTokens is very low, may truncate responses');
            suggestions.push('Increase maxTokens for complete responses');
          }
        }
      }
      
      // Tools validation
      if (config.tools && config.tools.length > 10) {
        warnings.push('Many tools configured, may impact performance');
        suggestions.push('Consider reducing number of tools for better performance');
      }
      
      // System prompt validation
      if (config.systemPrompt && config.systemPrompt.length > 2000) {
        warnings.push('System prompt is very long');
        suggestions.push('Consider shortening system prompt for better performance');
      }
    }
    
    // Check for common misconfigurations
    const params = node.parameters as any;
    if (params) {
      // Check for conflicting configurations
      if (params.streaming && params.enableCheckpoints) {
        warnings.push('Streaming and checkpoints may conflict');
        suggestions.push('Consider disabling one of streaming or checkpoints');
      }
      
      // Check for missing required fields
      if (!params.model && !params.defaultModel) {
        warnings.push('No model configuration found');
        suggestions.push('Configure model settings for predictable behavior');
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
    };
  }
  
  /**
   * Get agent node statistics
   */
  getAgentNodeStatistics(detectionResults: Map<string, AgentNodeDetectionResult>): {
    totalNodes: number;
    agentNodes: number;
    nonAgentNodes: number;
    highConfidenceAgents: number;
    lowConfidenceAgents: number;
    detectionMethods: Record<string, number>;
    validationSummary: {
      valid: number;
      invalid: number;
      withWarnings: number;
    };
  } {
    const stats = {
      totalNodes: detectionResults.size,
      agentNodes: 0,
      nonAgentNodes: 0,
      highConfidenceAgents: 0,
      lowConfidenceAgents: 0,
      detectionMethods: {} as Record<string, number>,
      validationSummary: {
        valid: 0,
        invalid: 0,
        withWarnings: 0,
      },
    };
    
    for (const result of detectionResults.values()) {
      if (result.isAgentNode) {
        stats.agentNodes++;
        
        if (result.confidence >= 0.8) {
          stats.highConfidenceAgents++;
        } else {
          stats.lowConfidenceAgents++;
        }
        
        // Count detection methods
        const method = result.detectionMethod;
        stats.detectionMethods[method] = (stats.detectionMethods[method] || 0) + 1;
        
        // Validation summary
        if (result.validation.isValid) {
          if (result.validation.warnings.length > 0) {
            stats.validationSummary.withWarnings++;
          } else {
            stats.validationSummary.valid++;
          }
        } else {
          stats.validationSummary.invalid++;
        }
      } else {
        stats.nonAgentNodes++;
      }
    }
    
    return stats;
  }
  
  // Private helper methods
  
  private analyzeNodeParameters(node: Node): {
    hasAgentId: boolean;
    hasModelConfig: boolean;
    hasTools: boolean;
    hasConversationFields: boolean;
    parameterKeys: string[];
  } {
    const params = (node.parameters as any) || {};
    const keys = Object.keys(params);
    
    return {
      hasAgentId: !!(node.agentId || params.agentId || params.agent_id),
      hasModelConfig: !!(params.model || params.modelName || params.modelConfig),
      hasTools: !!(params.tools && Array.isArray(params.tools)),
      hasConversationFields: keys.some(key => 
        ['messages', 'threadId', 'conversationId', 'chatHistory'].includes(key)
      ),
      parameterKeys: keys,
    };
  }
  
  private matchParameterPattern(
    analysis: ReturnType<typeof this.analyzeNodeParameters>,
    pattern: NonNullable<AgentNodePattern['parameterPatterns']>
  ): { score: number; matches: string[] } {
    let score = 0;
    const matches: string[] = [];
    
    // Check required parameters
    if (pattern.required) {
      const requiredMatches = pattern.required.filter(req => 
        analysis.parameterKeys.includes(req)
      );
      
      if (requiredMatches.length === pattern.required.length) {
        score += 0.8;
        matches.push(...requiredMatches);
      } else if (requiredMatches.length > 0) {
        score += 0.4 * (requiredMatches.length / pattern.required.length);
        matches.push(...requiredMatches);
      }
    }
    
    // Check optional parameters
    if (pattern.optional) {
      const optionalMatches = pattern.optional.filter(opt => 
        analysis.parameterKeys.includes(opt)
      );
      
      if (optionalMatches.length > 0) {
        score += 0.3 * (optionalMatches.length / pattern.optional.length);
        matches.push(...optionalMatches);
      }
    }
    
    // Check agent ID fields
    if (pattern.agentIdFields && analysis.hasAgentId) {
      score += 0.9;
      matches.push('agentId');
    }
    
    return { score: Math.min(score, 1.0), matches };
  }
  
  private performHeuristicDetection(
    node: Node, 
    nodeDefinition?: NodeDefinition
  ): { confidence: number; reasons: string[] } {
    let confidence = 0;
    const reasons: string[] = [];
    
    // Check node name for agent-related keywords
    const agentKeywords = ['agent', 'chat', 'assistant', 'ai', 'bot', 'llm', 'gpt'];
    const nodeName = node.name.toLowerCase();
    
    for (const keyword of agentKeywords) {
      if (nodeName.includes(keyword)) {
        confidence += 0.1;
        reasons.push(`Node name contains '${keyword}'`);
      }
    }
    
    // Check node definition description
    if (nodeDefinition?.description) {
      const description = nodeDefinition.description.toLowerCase();
      for (const keyword of agentKeywords) {
        if (description.includes(keyword)) {
          confidence += 0.05;
          reasons.push(`Description contains '${keyword}'`);
        }
      }
    }
    
    // Check for AI-related parameters
    const params = (node.parameters as any) || {};
    const aiParams = ['temperature', 'topP', 'maxTokens', 'systemPrompt', 'prompt'];
    
    for (const param of aiParams) {
      if (params[param] !== undefined) {
        confidence += 0.05;
        reasons.push(`Has AI parameter '${param}'`);
      }
    }
    
    return { 
      confidence: Math.min(confidence, 0.8), // Cap at 0.8 for heuristics
      reasons 
    };
  }
  
  private calculateFinalDetectionResult(
    node: Node,
    nodeDefinition: NodeDefinition | undefined,
    detectionResults: Array<{
      pattern: AgentNodePattern;
      matches: boolean;
      confidence: number;
      method: AgentNodeDetectionResult['detectionMethod'];
    }>,
    parameterAnalysis: ReturnType<typeof this.analyzeNodeParameters>
  ): AgentNodeDetectionResult {
    // Calculate weighted confidence
    let totalConfidence = 0;
    let totalWeight = 0;
    let bestMethod: AgentNodeDetectionResult['detectionMethod'] = 'heuristic';
    
    for (const result of detectionResults) {
      if (result.matches) {
        const weight = this.getMethodWeight(result.method);
        totalConfidence += result.confidence * weight;
        totalWeight += weight;
        
        if (result.confidence > 0.8) {
          bestMethod = result.method;
        }
      }
    }
    
    const finalConfidence = totalWeight > 0 ? totalConfidence / totalWeight : 0;
    const isAgentNode = finalConfidence >= 0.5;
    
    // Extract agent configuration
    const agentConfig = this.extractAgentConfiguration(node);
    
    // Perform validation
    const validation = {
      isValid: true,
      errors: [] as string[],
      warnings: [] as string[],
      suggestions: [] as string[],
    };
    
    if (isAgentNode && !node.agentId) {
      validation.isValid = false;
      validation.errors.push('Agent node detected but no agentId specified');
    }
    
    return {
      isAgentNode,
      agentId: node.agentId || undefined,
      confidence: finalConfidence,
      detectionMethod: bestMethod,
      agentConfig: isAgentNode ? agentConfig : undefined,
      validation,
    };
  }
  
  private getMethodWeight(method: AgentNodeDetectionResult['detectionMethod']): number {
    switch (method) {
      case 'agentId': return 1.0;
      case 'nodeType': return 0.8;
      case 'parameters': return 0.6;
      case 'heuristic': return 0.3;
      default: return 0.1;
    }
  }
  
  private extractAgentConfiguration(node: Node): AgentNodeDetectionResult['agentConfig'] {
    const params = (node.parameters as any) || {};
    
    return {
      model: params.model ? {
        name: params.model.name || params.modelName,
        temperature: params.model.temperature || params.temperature,
        maxTokens: params.model.maxTokens || params.maxTokens,
      } : undefined,
      tools: params.tools,
      systemPrompt: params.systemPrompt || params.prompt,
      enableCheckpoints: params.enableCheckpoints,
      streaming: params.streaming,
    };
  }
}
