/**
 * Interface cho payload encryption
 */
export interface PayloadEncryption {
  /**
   * Encrypt payload data
   */
  encrypt(data: any): Promise<string>;

  /**
   * Decrypt payload data
   */
  decrypt(encryptedData: string): Promise<any>;

  /**
   * Validate encrypted payload
   */
  validate(encryptedData: string): Promise<boolean>;
}

/**
 * Encryption configuration
 */
export interface EncryptionConfig {
  algorithm: string;
  keySize: number;
  ivSize: number;
}

/**
 * Encrypted payload structure
 */
export interface EncryptedPayload {
  data: string;
  iv: string;
  algorithm: string;
  timestamp: number;
}
