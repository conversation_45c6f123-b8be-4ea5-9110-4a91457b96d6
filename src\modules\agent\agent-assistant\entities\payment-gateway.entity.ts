import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * PaymentGateway entity
 * Linked bank accounts
 */
@Entity('payment_gateway')
export class PaymentGateway {
  /**
   * Primary key - UUID
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Bank account code on sepay
   */
  @Column({ name: 'account_id', type: 'varchar', length: 30, nullable: false })
  accountId: string;

  /**
   * Company ID reference to user_company_in_sepay
   */
  @Column({ name: 'company_id', type: 'int', nullable: true })
  companyId?: number;

  /**
   * Bank code
   */
  @Column({ name: 'bank_code', type: 'varchar', length: 20, nullable: true })
  bankCode?: string;

  /**
   * Account number
   */
  @Column({ name: 'account_number', type: 'varchar', length: 30, nullable: true })
  accountNumber?: string;

  /**
   * Citizen identification number
   */
  @Column({ name: 'identification_number', type: 'varchar', length: 30, nullable: true })
  identificationNumber?: string;

  /**
   * Phone number registered with bank account
   */
  @Column({ name: 'phone_number', type: 'varchar', length: 20, nullable: true })
  phoneNumber?: string;

  /**
   * Label
   */
  @Column({ name: 'label', type: 'varchar', length: 50, nullable: true })
  label?: string;

  /**
   * Status
   */
  @Column({ name: 'status', type: 'varchar', length: 30, nullable: true })
  status?: string;

  /**
   * Link code
   */
  @Column({ name: 'request_id', type: 'varchar', length: 100, nullable: true })
  requestId?: string;

  /**
   * Bank account holder name
   */
  @Column({ name: 'account_holder_name', type: 'varchar', length: 50, nullable: true })
  accountHolderName?: string;

  /**
   * Merchant address
   */
  @Column({ name: 'merchant_address', type: 'varchar', length: 1000, nullable: true })
  merchantAddress?: string;

  /**
   * Merchant name
   */
  @Column({ name: 'merchant_name', type: 'varchar', length: 500, nullable: true })
  merchantName?: string;

  /**
   * Whether it's a VA account
   */
  @Column({ name: 'is_va', type: 'boolean', nullable: true })
  isVa?: boolean;

  /**
   * If it's a VA account, this is the main account code
   */
  @Column({ name: 'main_id', type: 'int', nullable: true })
  mainId?: number;

  /**
   * VA code
   */
  @Column({ name: 'va_id', type: 'varchar', length: 30, nullable: true })
  vaId?: string;

  /**
   * Whether this account can create VA accounts
   */
  @Column({ name: 'can_create_va', type: 'boolean', default: false, nullable: true })
  canCreateVa?: boolean;

  /**
   * Record creation time
   */
  @Column({ name: 'created_at', type: 'timestamp', default: () => 'now()', nullable: true })
  createdAt?: Date;

  /**
   * Record update time
   */
  @Column({ name: 'updated_at', type: 'timestamp', default: () => 'now()', nullable: true })
  updatedAt?: Date;

  /**
   * Record deletion time (soft delete)
   */
  @Column({ name: 'deleted_at', type: 'timestamp', nullable: true })
  deletedAt?: Date;
}
