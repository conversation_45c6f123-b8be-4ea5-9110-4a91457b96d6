export enum QueueName {
  EMAIL_SYSTEM = 'email-system',
  EMAIL_MARKETING = 'email-marketing',
  SMS = 'sms',
  SMS_MARKETING = 'sms-marketing',
  AFFILIATE_CLICK = 'affiliate-click',
  FINE_TUNE = 'fine-tune',
  ZALO_WEBHOOK = 'zalo-webhook',
  ZALO_AI_RESPONSE = 'zalo-ai-response',
  ZALO_ZNS = 'zalo-zns',
  ZALO_VIDEO_TRACKING = 'zalo-video-tracking',
  INTEGRATION = 'integration',
  DATA_PROCESS = 'data-process',
  ZALO_AUDIENCE_SYNC = 'zalo-audience-sync',

  // Workflow queues - only real execution
  WORKFLOW_EXECUTION = 'workflow-execution',
  WEBHOOK = 'webhook',
}

/**
 * Enum định nghĩa các tên job trong queue Zalo Audience Sync
 */
export enum ZaloAudienceSyncJobName {
  /**
   * Job đồng bộ người dùng Zalo vào audience
   */
  SYNC_ZALO_USERS_TO_AUDIENCE = 'sync-zalo-users-to-audience',
}

/**
 * Enum định nghĩa các tên job trong queue Workflow Execution
 */
export enum WorkflowExecutionJobName {
  /**
   * Job thực thi workflow từ trigger
   */
  EXECUTE_WORKFLOW = 'execute-workflow',

  /**
   * Job thực thi node đơn lẻ trong workflow
   */
  EXECUTE_NODE = 'execute-node',

  /**
   * Job retry failed workflow execution
   */
  RETRY_WORKFLOW = 'retry-workflow',

  /**
   * Job cleanup workflow execution data
   */
  CLEANUP_EXECUTION = 'cleanup-execution',
}

// Node Test job names removed - focusing on real execution only

/**
 * Enum định nghĩa các tên job trong queue Zalo Video Tracking
 */
export enum ZaloVideoTrackingJobName {
  /**
   * Job kiểm tra trạng thái video upload
   */
  CHECK_VIDEO_STATUS = 'check-video-status',
}
