import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { QueueName } from '../../../queue';
import { FineTuneJobName } from '../constants/fine-tune-job-name.enum';
import { MonitoringJobData } from '../dto/fine-tune-job.dto';
import { ProviderFineTuneEnum } from '../constants/provider.enum';

/**
 * Service để quản lý fine-tune queue operations
 */
@Injectable()
export class FineTuneQueueService {
  private readonly logger = new Logger(FineTuneQueueService.name);

  constructor(
    @InjectQueue(QueueName.FINE_TUNE) private readonly fineTuneQueue: Queue,
  ) {}

  /**
   * Thêm monitoring job vào queue
   * Được gọi từ BE app sau khi tạo fine-tune job với provider
   */
  async addMonitoringJob(
    monitoringJobData: {
      historyId: string;
      providerJobId: string;
      provider: ProviderFineTuneEnum;
      userId?: number;
    },
    options?: {
      delay?: number;
      attempts?: number;
      backoff?: {
        type: 'exponential' | 'fixed';
        delay: number;
      };
    },
  ): Promise<string> {
    try {
      const jobData: MonitoringJobData = {
        ...monitoringJobData,
        timestamp: Date.now(),
      };

      const defaultOptions = {
        delay: 30000, // 30 giây delay mặc định
        attempts: 1, // Mỗi job chỉ thử 1 lần, logic retry được handle trong processor
        removeOnComplete: 10, // Giữ lại 10 job hoàn thành gần nhất
        removeOnFail: 50, // Giữ lại 50 job thất bại gần nhất
        backoff: {
          type: 'exponential' as const,
          delay: 5000,
        },
        ...options,
      };

      const job = await this.fineTuneQueue.add(
        FineTuneJobName.FINE_TUNE_MONITORING,
        jobData,
        defaultOptions,
      );

      this.logger.log('Added monitoring job to queue', {
        jobId: job.id,
        historyId: monitoringJobData.historyId,
        providerJobId: monitoringJobData.providerJobId,
        provider: monitoringJobData.provider,
        userId: monitoringJobData.userId,
        delay: defaultOptions.delay,
      });

      return job.id || 'unknown';
    } catch (error) {
      this.logger.error('Error adding monitoring job to queue', {
        error: error.message,
        stack: error.stack,
        monitoringJobData,
      });
      throw error;
    }
  }

  /**
   * Lấy thông tin job từ queue
   */
  async getJob(jobId: string): Promise<any> {
    try {
      const job = await this.fineTuneQueue.getJob(jobId);
      
      if (!job) {
        this.logger.warn(`Job not found: ${jobId}`);
        return null;
      }

      return {
        id: job.id,
        name: job.name,
        data: job.data,
        opts: job.opts,
        progress: job.progress,
        attemptsMade: job.attemptsMade,
        processedOn: job.processedOn,
        finishedOn: job.finishedOn,
        failedReason: job.failedReason,
      };
    } catch (error) {
      this.logger.error('Error getting job from queue', {
        error: error.message,
        jobId,
      });
      return null;
    }
  }

  /**
   * Hủy job đang chờ xử lý
   */
  async cancelJob(jobId: string): Promise<boolean> {
    try {
      const job = await this.fineTuneQueue.getJob(jobId);
      
      if (!job) {
        this.logger.warn(`Job not found for cancellation: ${jobId}`);
        return false;
      }

      await job.remove();
      
      this.logger.log('Job cancelled successfully', {
        jobId,
        jobName: job.name,
      });

      return true;
    } catch (error) {
      this.logger.error('Error cancelling job', {
        error: error.message,
        jobId,
      });
      return false;
    }
  }

  /**
   * Lấy thống kê queue
   */
  async getQueueStats(): Promise<any> {
    try {
      const waiting = await this.fineTuneQueue.getWaiting();
      const active = await this.fineTuneQueue.getActive();
      const completed = await this.fineTuneQueue.getCompleted();
      const failed = await this.fineTuneQueue.getFailed();
      const delayed = await this.fineTuneQueue.getDelayed();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
        total: waiting.length + active.length + completed.length + failed.length + delayed.length,
      };
    } catch (error) {
      this.logger.error('Error getting queue stats', {
        error: error.message,
      });
      return null;
    }
  }

  /**
   * Dọn dẹp các job cũ
   */
  async cleanOldJobs(
    maxAge: number = 24 * 60 * 60 * 1000, // 24 giờ
    maxCount: number = 100,
  ): Promise<void> {
    try {
      // Dọn dẹp completed jobs
      await this.fineTuneQueue.clean(maxAge, maxCount, 'completed');
      
      // Dọn dẹp failed jobs
      await this.fineTuneQueue.clean(maxAge, maxCount, 'failed');

      this.logger.log('Cleaned old jobs from queue', {
        maxAge,
        maxCount,
      });
    } catch (error) {
      this.logger.error('Error cleaning old jobs', {
        error: error.message,
        maxAge,
        maxCount,
      });
    }
  }

  /**
   * Kiểm tra xem có job monitoring nào đang chạy cho model không
   */
  async hasActiveMonitoringJob(historyId: string): Promise<boolean> {
    try {
      // Kiểm tra waiting jobs
      const waitingJobs = await this.fineTuneQueue.getWaiting();
      const activeJobs = await this.fineTuneQueue.getActive();
      const delayedJobs = await this.fineTuneQueue.getDelayed();

      const allActiveJobs = [...waitingJobs, ...activeJobs, ...delayedJobs];

      for (const job of allActiveJobs) {
        if (
          job.name === FineTuneJobName.FINE_TUNE_MONITORING &&
          job.data?.historyId === historyId
        ) {
          return true;
        }
      }

      return false;
    } catch (error) {
      this.logger.error('Error checking for active monitoring job', {
        error: error.message,
        historyId,
      });
      return false;
    }
  }
}
