import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Integration } from '../entities/integration.entity';
import { IntegrationProvider } from '../entities/integration-provider.entity';
import { EncryptionService } from './encryption/encryption.service';
import { ProviderEnum } from '../enums/provider.enum';
import { PayloadEncryption } from '../interfaces/payload-encryption.interface';

/**
 * Service quản lý integrations và authentication cho workflow nodes
 */
@Injectable()
export class IntegrationService {
  private readonly logger = new Logger(IntegrationService.name);

  constructor(
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
    @InjectRepository(IntegrationProvider)
    private readonly integrationProviderRepository: Repository<IntegrationProvider>,
    private readonly encryptionService: EncryptionService,
  ) {}

  /**
   * Lấy credentials đã gi<PERSON>i mã cho một provider
   */
  async getDecryptedCredentials<T extends PayloadEncryption>(
    userId: number,
    providerType: ProviderEnum,
  ): Promise<T> {
    try {
      // 1. Tìm integration provider
      const provider = await this.integrationProviderRepository.findOne({
        where: { type: providerType },
      });

      if (!provider) {
        throw new Error(`Integration provider not found: ${providerType}`);
      }

      // 2. Tìm integration của user
      const integration = await this.integrationRepository.findOne({
        where: {
          userId,
          typeId: provider.id,
        },
      });

      if (!integration) {
        throw new Error(
          `Integration not found for user ${userId} and provider ${providerType}. Please configure the integration first.`
        );
      }

      // 3. Kiểm tra có encrypted config
      if (!integration.encryptedConfig || !integration.secretKey) {
        throw new Error('Integration credentials not configured properly');
      }

      // 4. Giải mã credentials
      const decryptedCredentials = this.encryptionService.decrypt<T>(
        integration.secretKey,
        process.env.ENCRYPTION_PRIVATE_KEY || 'default-private-key',
        integration.encryptedConfig,
      );

      this.logger.debug(`Successfully decrypted credentials for ${providerType}`);
      return decryptedCredentials;

    } catch (error) {
      this.logger.error(`Error getting credentials for ${providerType}:`, error);
      throw error;
    }
  }

  /**
   * Lấy Google access token
   */
  async getGoogleAccessToken(userId: number): Promise<string> {
    const credentials = await this.getDecryptedCredentials(userId, ProviderEnum.GMAIL);
    
    if (!credentials || !('accessToken' in credentials)) {
      throw new Error('Google access token not found in credentials');
    }

    return (credentials as any).accessToken;
  }

  /**
   * Lấy Facebook Page access token
   */
  async getFacebookPageAccessToken(userId: number): Promise<string> {
    const credentials = await this.getDecryptedCredentials(userId, ProviderEnum.FACEBOOK_PAGE);
    
    if (!credentials || !('accessToken' in credentials)) {
      throw new Error('Facebook Page access token not found in credentials');
    }

    return (credentials as any).accessToken;
  }

  /**
   * Lấy Zalo OA access token
   */
  async getZaloOAAccessToken(userId: number): Promise<string> {
    const credentials = await this.getDecryptedCredentials(userId, ProviderEnum.ZALO_OA);
    
    if (!credentials || !('accessToken' in credentials)) {
      throw new Error('Zalo OA access token not found in credentials');
    }

    return (credentials as any).accessToken;
  }

  /**
   * Kiểm tra integration có tồn tại không
   */
  async hasIntegration(userId: number, providerType: ProviderEnum): Promise<boolean> {
    try {
      const provider = await this.integrationProviderRepository.findOne({
        where: { type: providerType },
      });

      if (!provider) {
        return false;
      }

      const integration = await this.integrationRepository.findOne({
        where: {
          userId,
          typeId: provider.id,
        },
      });

      return !!integration;
    } catch (error) {
      this.logger.error(`Error checking integration for ${providerType}:`, error);
      return false;
    }
  }
}
