import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AgentStrategy } from '../entities/agent-strategy.entity';

@Injectable()
export class AgentStrategyRepository {
  constructor(
    @InjectRepository(AgentStrategy)
    private readonly repository: Repository<AgentStrategy>,
  ) {}

  /**
   * Find agent strategy by ID
   * @param id Strategy ID (same as agent ID)
   * @returns AgentStrategy or null if not found
   */
  async findById(id: string): Promise<AgentStrategy | null> {
    return this.repository.findOne({
      where: {
        id,
        using: true, // Only return strategies that are being used
      }
    });
  }

  /**
   * Find agent strategy by ID without using filter
   * @param id Strategy ID (same as agent ID)
   * @returns AgentStrategy or null if not found
   */
  async findByIdWithoutUsingFilter(id: string): Promise<AgentStrategy | null> {
    return this.repository.findOne({
      where: {
        id,
      }
    });
  }

  /**
   * Find agent strategy by system model ID
   * @param systemModelId System model ID
   * @returns AgentStrategy or null if not found
   */
  async findBySystemModelId(systemModelId: string): Promise<AgentStrategy | null> {
    return this.repository.findOne({
      where: {
        systemModelId,
        using: true,
      }
    });
  }
}
