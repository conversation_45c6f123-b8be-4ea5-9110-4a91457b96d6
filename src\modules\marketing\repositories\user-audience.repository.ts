import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { UserAudience } from '../entities/user-audience.entity';

/**
 * Repository cho UserAudience entity
 */
@Injectable()
export class UserAudienceRepository extends Repository<UserAudience> {
  private readonly logger = new Logger(UserAudienceRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserAudience, dataSource.createEntityManager());
  }

  /**
   * Tìm audience theo Zalo Social ID
   */
  async findByZaloSocialId(
    zaloSocialId: string,
    userId: number,
  ): Promise<UserAudience | null> {
    try {
      return await this.findOne({
        where: {
          zaloSocialId,
          userId,
        },
      });
    } catch (error) {
      this.logger.error(`Error finding audience by Zalo Social ID: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Tìm audience theo số điện thoại
   */
  async findByPhoneNumber(
    countryCode: string,
    phoneNumber: string,
    userId: number,
  ): Promise<UserAudience | null> {
    try {
      return await this.findOne({
        where: {
          countryCode: parseInt(countryCode),
          phoneNumber,
          userId,
        },
      });
    } catch (error) {
      this.logger.error(`Error finding audience by phone: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Tạo audience mới
   */
  async createAudience(data: Partial<UserAudience>): Promise<UserAudience> {
    try {
      const audience = super.create(data);
      return await this.save(audience);
    } catch (error) {
      this.logger.error(`Error creating audience: ${error.message}`, error.stack);
      throw error;
    }
  }
}
