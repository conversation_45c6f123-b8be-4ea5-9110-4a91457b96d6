import {
  BaseMessage,
  isAIMessage,
  SystemMessage,
  ToolMessage,
} from '@langchain/core/messages';
import { RunnableConfig } from '@langchain/core/runnables';
import {
  BaseCheckpointSaver,
  BaseStore,
} from '@langchain/langgraph-checkpoint';
import {
  Annotation,
  AnnotationRoot,
  Command,
  CompiledStateGraph,
  END,
  interrupt,
  LangGraphRunnableConfig,
  Messages,
  messagesStateReducer,
  START,
  StateGraph,
} from '@langchain/langgraph';
import { InterruptShapeInterface, InterruptValue } from '../../interfaces';
import { SystemAgentConfig, SystemAgentConfigMap } from '../interfaces';
import { ModelProviderEnum, InputModality, ModelFeature } from '../../enums';
import { DynamicStructuredTool } from '@langchain/core/dist/tools';
import { ToolNode } from '@langchain/langgraph/prebuilt';
import { MemoryService } from '../services/memory.service';
import { MessageContentService } from '../services/message-content.service';
import { UserMemoryRecord } from '../../interfaces';
import { AttachmentContextBlock } from '../interfaces/message.interface';
import { UserMessageData } from '../services/user-messages.queries';
import { Logger } from '@nestjs/common';
import { getHandoffTool } from './helpers';
import {
  SUPERVISOR_TAG,
  SUPERVISOR_TOOL_CALL_TAG,
  WORKER_TAG,
  WORKER_TOOL_CALL_TAG,
} from './constants';
import { initChatModel } from 'langchain/chat_models/universal';
import { backOff } from 'exponential-backoff';
import { MultiServerMCPClient } from '@langchain/mcp-adapters';

const logger = new Logger('ReactAgentExecutor');

/**
 * Rich SystemMessage Builder for dynamic message generation with memory context
 *
 * @description Builds SystemMessages with rich context including user memories,
 * agent memories, reply context, and attachment context using ultra-compact XML structure.
 */
class RichSystemMessageBuilder {
  private readonly logger = new Logger('RichSystemMessageBuilder');

  constructor(
    private readonly agentConfig: any,
    private readonly config?: CustomRunnableConfig,
  ) {}

  /**
   * Build a rich SystemMessage with context data
   * @returns SystemMessage with enhanced prompt content
   */
  async buildSystemMessage(): Promise<SystemMessage> {
    try {
      // Validate agent config
      if (!this.agentConfig || !this.agentConfig.instruction) {
        this.logger.warn(
          `Invalid agent config provided, using fallback instruction`,
          {
            agentId: this.agentConfig?.id || 'unknown',
            hasInstruction: !!this.agentConfig?.instruction,
          },
        );
        return new SystemMessage(
          '<instruction>You are a helpful AI assistant.</instruction>',
        );
      }

      const startTime = Date.now();

      // Start with the base agent instruction wrapped in instruction tag
      let promptContent = `<instruction>${this.agentConfig.instruction}</instruction>`;

      // Add profile context if available (for user agents)
      if (this.agentConfig.profile) {
        try {
          const profileContext = this.buildProfileContext(
            this.agentConfig.profile,
          );
          if (profileContext) {
            promptContent += '\n\n' + profileContext;
          }
        } catch (profileError) {
          this.logger.warn(
            `Failed to build profile context, continuing without profile:`,
            {
              agentId: this.agentConfig.id,
              error: profileError.message,
            },
          );
        }
      }

      // Add rich context if available
      if (this.config?.configurable) {
        try {
          const richContext = await this.buildRichSystemPrompt(
            this.config.configurable,
          );
          if (richContext) {
            promptContent += '\n\n' + richContext;
          }
        } catch (contextError) {
          this.logger.warn(
            `Failed to build rich context, continuing with base instruction:`,
            {
              agentId: this.agentConfig.id,
              error: contextError.message,
            },
          );
          // Continue with base instruction only
        }
      }

      const duration = Date.now() - startTime;
      this.logger.debug(`Built rich SystemMessage in ${duration}ms`, {
        agentId: this.agentConfig.id,
        hasContext: !!this.config?.configurable,
        hasProfile: !!this.agentConfig.profile,
        promptLength: promptContent.length,
        duration,
      });

      return new SystemMessage(promptContent);
    } catch (error) {
      this.logger.error(`Critical failure building SystemMessage:`, {
        agentId: this.agentConfig?.id || 'unknown',
        error: error.message,
        stack: error.stack,
      });

      // Ultimate fallback to ensure system never fails
      const fallbackInstruction =
        this.agentConfig?.instruction || 'You are a helpful AI assistant.';
      return new SystemMessage(
        `<instruction>${fallbackInstruction}</instruction>`,
      );
    }
  }

  /**
   * Build rich system prompt content with context data
   * @param configurable CustomConfigurableType data
   * @returns Rich prompt content or null
   */
  private async buildRichSystemPrompt(
    configurable: CustomConfigurableType,
  ): Promise<string | null> {
    try {
      const contextSections: string[] = [];

      // Add user memories context if available
      if (configurable.userMemories && configurable.userMemories.length > 0) {
        const userMemoryContext = this.buildUserMemoryContext(
          configurable.userMemories,
        );
        if (userMemoryContext) {
          contextSections.push(userMemoryContext);
        }
      }

      // Add agent-specific memories if memory service is available
      if (configurable.memoryService && this.agentConfig.id) {
        const agentMemoryContext = await this.buildAgentMemoryContext(
          configurable.memoryService,
          this.agentConfig.id,
        );
        if (agentMemoryContext) {
          contextSections.push(agentMemoryContext);
        }
      }

      // Add reply context if available
      if (configurable.replyToContext) {
        const replyContext = this.buildReplyContext(
          configurable.replyToContext,
        );
        if (replyContext) {
          contextSections.push(replyContext);
        }
      }

      // Add attachment context if available
      if (
        configurable.attachmentContext &&
        configurable.attachmentContext.length > 0
      ) {
        const attachmentContext = this.buildAttachmentContext(
          configurable.attachmentContext,
        );
        if (attachmentContext) {
          contextSections.push(attachmentContext);
        }
      }

      // Add worker context if this is a supervisor agent
      if (this.agentConfig.isSupervisor && configurable.agentConfigMap) {
        const workerContext = this.buildWorkerContext(
          configurable.agentConfigMap,
          configurable.supervisorAgentId,
        );
        if (workerContext) {
          contextSections.push(workerContext);
        }
      }

      // Return combined context or null if no context available
      if (contextSections.length === 0) {
        return null;
      }

      const richPrompt = `\n\n${contextSections.join('\n\n')}`;

      this.logger.debug(
        `Built rich system prompt with ${contextSections.length} context sections`,
        {
          agentId: this.agentConfig.id,
          isSupervisor: this.agentConfig.isSupervisor,
          sectionCount: contextSections.length,
          promptLength: richPrompt.length,
          hasWorkerContext:
            this.agentConfig.isSupervisor && !!configurable.agentConfigMap,
        },
      );

      return richPrompt;
    } catch (error) {
      this.logger.error(`Failed to build rich system prompt:`, {
        agentId: this.agentConfig.id,
        error: error.message,
        stack: error.stack,
      });
      return null;
    }
  }

  /**
   * Build agent-specific memory context section
   * @param memoryService MemoryService instance
   * @param agentId Agent UUID for memory retrieval
   * @returns Formatted agent memory context string
   */
  private async buildAgentMemoryContext(
    memoryService: any,
    agentId: string,
  ): Promise<string | null> {
    try {
      this.logger.debug(`Loading agent memories for agent ${agentId}`);

      // Load agent-specific memories
      const agentMemories = await memoryService.retrieveAgentMemories(agentId);

      if (!agentMemories || agentMemories.length === 0) {
        this.logger.debug(`No agent memories found for agent ${agentId}`);
        return null;
      }

      const memoryItems = agentMemories.map((memory: any) => {
        const title = memory.structuredContent?.title || 'Untitled Memory';
        const content = memory.structuredContent?.content || 'No content';
        const reason = memory.structuredContent?.reason || 'No reason';

        return `<memory id="${memory.id}" title="${title}" reason="${reason}">${content}</memory>`;
      });

      this.logger.debug(
        `Loaded ${agentMemories.length} agent memories for agent ${agentId}`,
      );

      return `<agent-memories count="${memoryItems.length}">\n${memoryItems.join('\n')}\n</agent-memories>`;
    } catch (error) {
      this.logger.error(`Failed to load agent memories for agent ${agentId}:`, {
        agentId,
        error: error.message,
        stack: error.stack,
        memoryServiceAvailable: !!memoryService,
      });
      return null;
    }
  }

  /**
   * Build user memory context section
   * @param userMemories Array of user memory records
   * @returns Formatted user memory context string
   */
  private buildUserMemoryContext(userMemories: any[]): string | null {
    try {
      if (!userMemories || userMemories.length === 0) {
        return null;
      }

      const memoryItems = userMemories.map((memory) => {
        const title = memory.structuredContent?.title || 'Untitled Memory';
        const content = memory.structuredContent?.content || 'No content';
        const reason = memory.structuredContent?.reason || 'No reason';

        return `<memory id="${memory.id}" title="${title}" reason="${reason}">${content}</memory>`;
      });

      return `<user-memories count="${memoryItems.length}">\n${memoryItems.join('\n')}\n</user-memories>`;
    } catch (error) {
      this.logger.warn(`Failed to build user memory context:`, error);
      return null;
    }
  }

  /**
   * Build reply context section
   * @param replyToContext Reply context data
   * @returns Formatted reply context string
   */
  private buildReplyContext(replyToContext: any): string | null {
    try {
      if (!replyToContext || !replyToContext.originalMessageData) {
        return null;
      }

      const originalMessage = replyToContext.originalMessageData;

      // Extract content with proper XML formatting for images and files
      let contentText = 'No content available';
      if (originalMessage.content?.contentBlocks?.length > 0) {
        const textParts: string[] = [];
        for (const block of originalMessage.content.contentBlocks) {
          if (block.type === 'text' && block.content) {
            textParts.push(block.content);
          } else if (block.type === 'image') {
            const tags = block.tags ? block.tags.join(', ') : '';
            const desc = block.desc || 'No description available';
            textParts.push(
              `<image file-id="${block.fileId || 'unknown'}" name="${block.name || 'image'}" tags="${tags}" desc="${desc}" />`,
            );
          } else if (block.type === 'file') {
            const tags = block.tags ? block.tags.join(', ') : '';
            const desc = block.desc || 'No description available';
            textParts.push(
              `<file file-id="${block.fileId || 'unknown'}" name="${block.name || 'file'}" tags="${tags}" desc="${desc}" />`,
            );
          }
        }
        contentText = textParts.join(' ').trim() || 'No readable content';
      }

      return `<reply-to>${contentText}</reply-to>`;
    } catch (error) {
      this.logger.warn(`Failed to build reply context:`, error);
      return null;
    }
  }

  /**
   * Build attachment context section
   * @param attachmentContext Array of attachment context blocks
   * @returns Formatted attachment context string
   */
  private buildAttachmentContext(attachmentContext: any[]): string | null {
    try {
      if (!attachmentContext || attachmentContext.length === 0) {
        return null;
      }

      const attachmentItems = attachmentContext.map((attachment) => {
        const type = attachment.type || 'unknown';
        const fileName = attachment.name || 'unnamed';
        const fileId = attachment.fileId || 'unknown';
        const tags = attachment.tags ? attachment.tags.join(', ') : '';
        const desc = attachment.desc || 'No description available';

        return `<${type} file-id="${fileId}" name="${fileName}" tags="${tags}" desc="${desc}" />`;
      });

      return `<attachments count="${attachmentItems.length}">\n${attachmentItems.join('\n')}\n</attachments>`;
    } catch (error) {
      this.logger.warn(`Failed to build attachment context:`, error);
      return null;
    }
  }

  /**
   * Build profile context section for user agents
   * @param profile ProfileAgent data
   * @returns Formatted profile context string
   */
  private buildProfileContext(profile: any): string | null {
    try {
      if (!profile) {
        return null;
      }

      const profileParts: string[] = [];

      // Add basic information
      if (profile.gender) {
        profileParts.push(`<gender>${profile.gender}</gender>`);
      }

      if (profile.dateOfBirth) {
        const birthDate =
          typeof profile.dateOfBirth === 'string'
            ? profile.dateOfBirth
            : profile.dateOfBirth.toISOString().split('T')[0];
        profileParts.push(`<date-of-birth>${birthDate}</date-of-birth>`);
      }

      if (profile.position) {
        profileParts.push(`<position>${profile.position}</position>`);
      }

      if (profile.education) {
        profileParts.push(`<education>${profile.education}</education>`);
      }

      if (profile.nations) {
        profileParts.push(`<nationality>${profile.nations}</nationality>`);
      }

      // Add array-based information
      if (profile.skills && profile.skills.length > 0) {
        const skillsList = profile.skills.join(', ');
        profileParts.push(`<skills>${skillsList}</skills>`);
      }

      if (profile.personality && profile.personality.length > 0) {
        const personalityList = profile.personality.join(', ');
        profileParts.push(`<personality>${personalityList}</personality>`);
      }

      if (profile.languages && profile.languages.length > 0) {
        const languagesList = profile.languages.join(', ');
        profileParts.push(`<languages>${languagesList}</languages>`);
      }

      if (profileParts.length === 0) {
        return null;
      }

      const profileContext = `<agent-profile>\n${profileParts.join('\n')}\n</agent-profile>`;

      this.logger.debug(
        `Built profile context for agent ${this.agentConfig.id}`,
        {
          agentId: this.agentConfig.id,
          profileFieldCount: profileParts.length,
          hasGender: !!profile.gender,
          hasPosition: !!profile.position,
          hasSkills: !!(profile.skills && profile.skills.length > 0),
          hasPersonality: !!(
            profile.personality && profile.personality.length > 0
          ),
        },
      );

      return profileContext;
    } catch (error) {
      this.logger.warn(`Failed to build profile context:`, {
        agentId: this.agentConfig.id,
        error: error.message,
        profileKeys: profile ? Object.keys(profile) : [],
      });
      return null;
    }
  }

  /**
   * Build worker context section for supervisor agents
   * @param agentConfigMap Map of all agent configurations
   * @param supervisorAgentId ID of the supervisor agent
   * @returns Formatted worker context string
   */
  private buildWorkerContext(
    agentConfigMap: any,
    supervisorAgentId?: string,
  ): string | null {
    try {
      if (!agentConfigMap || !supervisorAgentId) {
        return null;
      }

      // Get all worker agents (exclude the supervisor)
      const workerAgents = Object.values(agentConfigMap).filter(
        (agent: any) => agent.id !== supervisorAgentId,
      );

      if (workerAgents.length === 0) {
        return null;
      }

      const workerItems = workerAgents.map((worker: any) => {
        const workerId = worker.id;
        const workerName = worker.name || 'Unnamed Worker';
        const workerDescription =
          worker.description || 'No description available';
        const workerType = worker.model?.type || 'UNKNOWN';
        const isUserAgent = workerType === 'USER' || workerType === 'FINE_TUNE';
        const agentTypeLabel = isUserAgent ? 'User Agent' : 'System Agent';

        // Build worker capabilities info
        const capabilities: string[] = [];
        if (worker.model?.features) {
          capabilities.push(`Features: ${worker.model.features.join(', ')}`);
        }
        if (worker.model?.inputModalities) {
          capabilities.push(
            `Input: ${worker.model.inputModalities.join(', ')}`,
          );
        }

        const capabilitiesText =
          capabilities.length > 0 ? ` (${capabilities.join('; ')})` : '';

        return `<worker id="${workerId}" name="${workerName}" type="${agentTypeLabel}"${capabilitiesText}>${workerDescription}</worker>`;
      });

      const workerContext = `<available-workers count="${workerItems.length}">\n${workerItems.join('\n')}\n</available-workers>`;

      this.logger.debug(
        `Built worker context for supervisor ${supervisorAgentId}`,
        {
          supervisorId: supervisorAgentId,
          workerCount: workerItems.length,
          userWorkers: workerAgents.filter(
            (w: any) =>
              w.model?.type === 'USER' || w.model?.type === 'FINE_TUNE',
          ).length,
          systemWorkers: workerAgents.filter(
            (w: any) => w.model?.type === 'SYSTEM',
          ).length,
        },
      );

      return workerContext;
    } catch (error) {
      this.logger.warn(`Failed to build worker context:`, {
        supervisorId: supervisorAgentId,
        error: error.message,
        agentConfigMapKeys: agentConfigMap ? Object.keys(agentConfigMap) : [],
      });
      return null;
    }
  }
}

const CHAT_MODEL_WITHOUT_PARALLEL_TOOL_CALL_PARAM = new Set([
  'o1',
  'o1-2024-12-17',
  'o1-preview-2024-09-12',
  'o1-pro',
  'o1-pro-2025-03-19',
  'o3',
  'o3-2025-04-16',
  'o4-mini',
  'o4-mini-2025-04-16',
  'o3-mini',
  'o3-mini-2025-01-31',
  'o1-mini',
  'o1-mini-2024-09-12',
]);

export type N = typeof START | 'agent' | 'humanReview' | 'tools' | typeof END;

export function createReactAgentAnnotation() {
  return Annotation.Root({
    messages: Annotation<BaseMessage[], Messages>({
      reducer: messagesStateReducer,
      default: () => [],
    }),
    activeAgent: Annotation<string>({
      reducer: (x, y) => {
        logger.log(`transferring from ${x} to ${y}`);
        return y;
      },
      default: () => 'supervisor',
    }),
    metadata: Annotation<Record<string, any>>,
  });
}

export const GraphState = createReactAgentAnnotation();

export type AgentState = (typeof GraphState)['State'];

/**
 * Custom configurable type for LangGraph runtime configuration
 */
export type CustomConfigurableType = {
  /** Whether to automatically approve tool calls without user confirmation */
  alwaysApproveToolCall?: boolean;
  /** Thread ID for the current conversation */
  thread_id?: string;
  /** User ID for the current session (required for memory tools) */
  userId?: number;
  /** JWT token for authenticated API calls (required) */
  jwt?: string;
  /** Map of agent configurations by agent ID */
  agentConfigMap?: SystemAgentConfigMap;
  /** ID of the supervisor agent */
  supervisorAgentId?: string;
  /**
   * Map of image IDs to S3 keys for efficient lookup during tool execution.
   * Used by chooseImageFromContext tool to retrieve and process images from attachment context.
   */
  attachmentImageMap?: Record<string, string>;
  /**
   * Dynamically registered tools based on context availability.
   * Tools are only included when relevant context (e.g., image attachments) is present.
   * Used for efficient tool registration without unnecessary overhead.
   */
  contextualTools?: DynamicStructuredTool[];

  // ✅ NEW: Memory service instances for dynamic memory loading
  /** Memory service instance for loading agent-specific memories in callModel() */
  memoryService?: MemoryService;
  /** Message content service instance for loading reply context data */
  messageContentService?: MessageContentService;

  // ✅ NEW: Context data fields for rich SystemMessage generation
  /** Pre-loaded user memories (shared across all agents) */
  userMemories?: UserMemoryRecord[];
  /** Reply context data with original message information */
  replyToContext?: {
    messageId: string;
    originalMessageData: UserMessageData;
  };
  /** Attachment context data for files and images */
  attachmentContext?: AttachmentContextBlock[];
};

// Define custom runnable config with our configurable type
export type CustomRunnableConfig = RunnableConfig<CustomConfigurableType>;

export type CreateReactAgentParams = {
  checkpointSaver?: BaseCheckpointSaver;
  store?: BaseStore;
  defaultTools?: DynamicStructuredTool[];
};

export function createReactAgent<
  A extends AnnotationRoot<any> = ReturnType<typeof createReactAgentAnnotation>,
>(
  params: CreateReactAgentParams,
): CompiledStateGraph<A['State'], A['Update'], any, A['spec'], A['spec']> {
  const { checkpointSaver, store } = params;

  const shouldContinue = (
    state: (typeof GraphState)['State'],
    config?: CustomRunnableConfig,
  ): N => {
    const { messages } = state;
    const lastMessage = messages[messages.length - 1];

    if (isAIMessage(lastMessage)) {
      const aiMessage = lastMessage;
      if (!aiMessage.tool_calls || aiMessage.tool_calls.length === 0) {
        return END;
      }

      const alwaysApproveToolCall =
        config?.configurable?.alwaysApproveToolCall || false;
      return alwaysApproveToolCall ? 'tools' : 'humanReview';
    }

    return END;
  };

  const humanReviewNode = (state: AgentState, config: CustomRunnableConfig) => {
    const { messages } = state;
    const lastMessage = messages[messages.length - 1];

    if (!lastMessage || !isAIMessage(lastMessage)) {
      throw new Error('Last message is not an AI message');
    }

    const aiMessage = lastMessage;

    // Check for errored tool calls (missing name or id)
    const erroredToolCalls =
      aiMessage.tool_calls?.filter(
        (toolCall) =>
          !toolCall.name ||
          toolCall.name.trim() === '' ||
          !toolCall.id ||
          toolCall.id.trim() === '',
      ) || [];

    if (erroredToolCalls.length > 0) {
      throw new Error(
        `Found ${erroredToolCalls.length} errored tool calls: ${JSON.stringify(erroredToolCalls)}. Tool calls must have both name and id.`,
      );
    }

    // Note: If alwaysApproveToolCall is true, we wouldn't reach this node
    // as shouldContinue would have routed directly to tools

    // Display the tool calls to the user and ask for approval
    const toolCalls =
      aiMessage.tool_calls
        ?.map((toolCall, index) => {
          return `Tool ${index + 1}: ${toolCall.name}\nArguments: ${JSON.stringify(toolCall.args, null, 2)}`;
        })
        .join('\n\n') || '';
    const name =
      state.activeAgent === config.configurable?.supervisorAgentId
        ? 'supervisor'
        : 'worker';
    const { choice } = interrupt<InterruptValue, InterruptShapeInterface>({
      role: name,
      prompt: `The AI wants to use the following tools:\n\n${toolCalls}\n\nDo you approve these tool calls? (yes/no/always)`,
      prompter:
        config?.configurable?.agentConfigMap?.[state.activeAgent]?.name || '',
      prompterId: state.activeAgent,
    });

    // Handle user response
    if (choice === 'always' || choice === 'yes') {
      // When user selects 'always', we just route to tools
      // The frontend should capture this response and set alwaysApproveToolCall=true in the configurable for future calls
      // We can't set configurable in the Command object
      return new Command({
        goto: 'tools',
      });
    } else {
      // User rejected the tool calls, create rejection tool messages
      const rejectionMessages =
        aiMessage.tool_calls?.map((toolCall) => {
          return new ToolMessage({
            content: 'Tool call was rejected by the user.',
            tool_call_id: toolCall.id ?? '',
            name: toolCall.name,
          });
        }) || [];

      // Return to the worker with the rejection messages
      return new Command({
        update: {
          messages: rejectionMessages,
        },
        goto: 'agent',
      });
    }
  };

  const callModel = async (
    state: typeof GraphState.State,
    config?: CustomRunnableConfig,
  ) => {
    // Get the active worker ID from state
    const activeAgentId = state.activeAgent;
    // Get the worker configuration from the config
    const agentConfig = config?.configurable?.agentConfigMap?.[activeAgentId];
    if (!agentConfig) {
      throw new Error(`No configuration found for agent: ${activeAgentId}`);
    }
    // Create a model instance based on the worker config
    const modelConfig = agentConfig.model;
    const apiKeys = modelConfig.apiKeys;
    // This is our state, scoped to this single `callModel` execution.
    let currentKeyIndex = 0;

    const apiCallTask = async () => {
      const apiKey = apiKeys[currentKeyIndex];
      const keyIdentifier = `...${apiKey.slice(-4)}`;
      logger.debug(
        `Attempting API call for agent ${activeAgentId} with key #${currentKeyIndex} (${keyIdentifier})`,
      );
      const dynamicLLM = await initChatModel(
        `${modelConfig.provider.toLowerCase()}:${modelConfig.name}`,
        {
          configurableFields: [...modelConfig.samplingParameters, 'apiKey'],
          ...modelConfig.parameters,
          apiKey,
        },
      );
      // Configure MCP servers if available (user agents have null mcpConfig)
      if (agentConfig.mcpConfig) {
        for (const mcp of Object.values(agentConfig.mcpConfig)) {
          (mcp as any).headers = {
            Authorization: `Bearer ${config.configurable?.jwt}`,
          };
          (mcp as any).reconnect = {
            enabled: true,
            maxAttempts: 5,
            delayMs: 2000,
          };
        }
      }

      logger.debug(`
        ___________________________________________________________
        mcpConfig: ${JSON.stringify(agentConfig.mcpConfig, null, 2)}
        ____________________________________________________________
        `);

      let multipleMcpClient: MultiServerMCPClient | undefined;
      try {
        let dynamicTools: any[] = [];

        // Only create MCP client if there are actual MCP servers configured
        if (
          agentConfig.mcpConfig &&
          Object.keys(agentConfig.mcpConfig).length > 0
        ) {
          multipleMcpClient = new MultiServerMCPClient({
            throwOnLoadError: true,
            additionalToolNamePrefix: '',
            useStandardContentBlocks: true,
            mcpServers: agentConfig.mcpConfig,
          });
          dynamicTools = (await multipleMcpClient?.getTools()) || [];
        } else {
          logger.debug(
            `No MCP servers configured for agent ${agentConfig.id}, skipping MCP client creation`,
          );
        }
        let toolInstances = [
          ...dynamicTools,
          ...(params.defaultTools || []),
          ...(config?.configurable?.contextualTools || []),
        ];
        const handoffTool = getHandoffTool(config);
        if (handoffTool) {
          toolInstances.push(handoffTool);
        }

        // ✅ Filter image tools if agent doesn't support image input
        if (!agentConfig.model.inputModalities.includes(InputModality.IMAGE)) {
          toolInstances = toolInstances.filter(
            (tool) => tool.name !== 'choose_image_from_context',
          );
        }
        // Bind tools to the model
        // Check if the model is OpenAI to conditionally set parallel_tool_calls
        const condition =
          modelConfig.provider !== ModelProviderEnum.OPENAI ||
          CHAT_MODEL_WITHOUT_PARALLEL_TOOL_CALL_PARAM.has(modelConfig.name);

        let modelWithTools;

        if (modelConfig.features.includes(ModelFeature.FORCED_TOOL_CALL)) {
          modelWithTools = condition
            ? dynamicLLM.bindTools(toolInstances, { tool_choice: 'web_search' })
            : dynamicLLM.bindTools(toolInstances, {
                parallel_tool_calls: false,
                tool_choice: 'web_search',
              });
        } else {
          modelWithTools = condition
            ? dynamicLLM.bindTools(toolInstances)
            : dynamicLLM.bindTools(toolInstances, {
                parallel_tool_calls: false,
              });
        }

        // Create a rich SystemMessage with context data
        const systemMessageBuilder = new RichSystemMessageBuilder(
          agentConfig,
          config,
        );
        const systemMessage = await systemMessageBuilder.buildSystemMessage();

        // Filter messages to only include those from the current worker
        const inputMessages = state.messages;

        const input = [systemMessage, ...inputMessages];

        const tag =
          agentConfig.id === config?.configurable?.supervisorAgentId
            ? SUPERVISOR_TAG
            : WORKER_TAG;
        // Invoke the model
        console.log(
          `---------------------------\nagent_id:${state.activeAgent}\n--------------------------`,
        );
        const result = await modelWithTools.invoke(input, {
          ...config,
          tags: [tag, `agent_id:${state.activeAgent}`],
        });
        return result;
      } catch (e) {
        logger.error(`Error calling model: ${e.message}`, e.stack);
        throw e;
      } finally {
        await multipleMcpClient?.close();
      }
    };

    const response = await backOff(apiCallTask, {
      // We have exactly as many attempts as we have keys.
      numOfAttempts: apiKeys.length,
      startingDelay: 200, // A small delay between key swaps
      timeMultiple: 1.5, // You can adjust the backoff strategy

      // This function now controls the key swapping logic.
      retry: (e: any, attemptNumber: number) => {
        // if e is abort error we stop retrying
        if (e instanceof Error && e.name === 'AbortError') {
          logger.log('LangGraph stream aborted cleanly.');
          return false;
        }
        const keyIdentifier = `...${apiKeys[currentKeyIndex].slice(-4)}`;
        logger.warn(
          `Attempt #${attemptNumber} with key ${keyIdentifier} failed. Error: ${e.message}`,
        );

        // Advance to the next key for the next attempt.
        ++currentKeyIndex;

        // Check if we've run out of keys.
        if (currentKeyIndex >= apiKeys.length) {
          logger.error('All API keys have been tried and failed.');
          return false; // Stop retrying
        }

        logger.log(`Swapping to next key (index: ${currentKeyIndex}).`);
        return true; // Yes, please retry (the task will now use the new key)
      },
    });

    // Add worker name to the response
    response.response_metadata.invoker = activeAgentId;
    if (response.lc_kwargs) {
      response.lc_kwargs.name = `${activeAgentId}-${agentConfig.name}`;
    }

    return {
      messages: [response],
    };
  };

  const wrappedToolNode = async (
    state: AgentState,
    config?: LangGraphRunnableConfig,
  ) => {
    const { messages: prevMessages, activeAgent } = state;
    const lastMessage = prevMessages.at(-1);
    if (!lastMessage) {
      throw new Error('No messages found');
    }
    if (isAIMessage(lastMessage)) {
      const aiMessage = lastMessage;
      if (aiMessage.tool_calls && aiMessage.tool_calls.length > 0) {
        const erroredToolCalls = aiMessage.tool_calls.filter(
          (toolCall) =>
            !toolCall.name ||
            toolCall.name.trim() === '' ||
            !toolCall.id ||
            toolCall.id.trim() === '',
        );
        if (erroredToolCalls.length > 0) {
          throw new Error(
            `Found ${erroredToolCalls.length} errored tool calls: ${JSON.stringify(erroredToolCalls)}. Tool calls must have both name and id.`,
          );
        }
      }
    }

    const agentConfig = config?.configurable?.agentConfigMap?.[activeAgent];
    if (!agentConfig) {
      throw new Error(`No configuration found for agent: ${activeAgent}`);
    }
    const realAgentConfig = agentConfig as SystemAgentConfig;

    // Configure MCP servers if available (user agents have null mcpConfig)
    if (realAgentConfig.mcpConfig) {
      for (const mcp of Object.values(realAgentConfig.mcpConfig)) {
        (mcp as any).headers = {
          Authorization: `Bearer ${config.configurable?.jwt}`,
        };
        (mcp as any).reconnect = {
          enabled: true,
          maxAttempts: 5,
          delayMs: 2000,
        };
      }
    }

    let multipleMcpClient: MultiServerMCPClient | undefined;
    try {
      let dynamicTools: any[] = [];

      // Only create MCP client if there are actual MCP servers configured
      if (
        realAgentConfig.mcpConfig &&
        Object.keys(realAgentConfig.mcpConfig).length > 0
      ) {
        multipleMcpClient = new MultiServerMCPClient({
          throwOnLoadError: true,
          additionalToolNamePrefix: '',
          useStandardContentBlocks: true,
          mcpServers: realAgentConfig.mcpConfig,
        });
        dynamicTools = (await multipleMcpClient?.getTools()) || [];
      } else {
        logger.debug(
          `No MCP servers configured for agent ${realAgentConfig.id}, skipping MCP client creation in tool node`,
        );
      }
      let toolInstances = [
        ...dynamicTools,
        ...(params.defaultTools || []),
        ...(config?.configurable?.contextualTools || []),
      ];
      const handoffTool = getHandoffTool(config);
      if (handoffTool) {
        toolInstances.push(handoffTool);
      }

      // ✅ Filter image tools if agent doesn't support image input
      if (
        !realAgentConfig.model.inputModalities.includes(InputModality.IMAGE)
      ) {
        toolInstances = toolInstances.filter(
          (tool) => tool.name !== 'choose_image_from_attachment_context',
        );
      }

      const tag =
        realAgentConfig.id === config?.configurable?.supervisorAgentId
          ? SUPERVISOR_TOOL_CALL_TAG
          : WORKER_TOOL_CALL_TAG;
      // Create a dynamic tool node
      const dynamicToolNode = new ToolNode(toolInstances, {
        handleToolErrors: true,
      });
      const raw = await dynamicToolNode.invoke(state, {
        ...config,
        tags: [tag],
      });

      // Flatten raw into a single output array of either Commands or BaseMessage[]
      const output: any[] = [];
      if (Array.isArray(raw)) {
        logger.log('raw array');
        output.push(...raw);
      } else if (raw instanceof Command) {
        logger.log('raw command');
        output.push(raw);
      } else if (Array.isArray(raw.messages)) {
        logger.log('raw messages');
        output.push(...raw.messages);
      } else {
        logger.error('bad raw');
        throw new Error('wrappedToolNode: unexpected return shape');
      }
      const hasCommand = output.some((item) => item instanceof Command);
      if (hasCommand) {
        return output;
      }
      // Tag every BaseMessage with invoker
      const messages: BaseMessage[] = [];
      for (const item of output) {
        // item is either a BaseMessage or an object with `.messages`
        if (item instanceof BaseMessage) {
          item.response_metadata = {
            ...item.response_metadata,
            invoker: state.activeAgent,
          };
          messages.push(item);
        }
      }
      return { messages };
    } catch (e) {
      logger.error(`Error calling tool: ${e.message}`, e.stack);
      throw e;
    } finally {
      await (multipleMcpClient as any)?.close();
    }
  };

  const workflow = new StateGraph(GraphState)
    .addNode('agent', callModel)
    .addNode('humanReview', humanReviewNode, { ends: ['tools', 'agent'] })
    .addNode('tools', wrappedToolNode)
    .addEdge(START, 'agent')
    .addConditionalEdges('agent', shouldContinue, {
      humanReview: 'humanReview',
      tools: 'tools',
      [END]: END,
    })
    .addEdge('tools', 'agent');
  return workflow.compile({
    checkpointer: checkpointSaver,
    store,
  });
}
