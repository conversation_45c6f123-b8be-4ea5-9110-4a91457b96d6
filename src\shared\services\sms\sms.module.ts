import { Module, Global } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule, ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

import { SmsService } from './sms.service';
import { SmsProviderFactory } from './sms-provider-factory.service';
import { SpeedSmsProvider } from './speed-sms-provider.service';
import { TwilioProvider } from './twilio-provider.service';
import { VonageProvider } from './vonage-provider.service';
import { FptSmsProvider } from './fpt-sms-provider.service';
import { FptSmsBrandnameService, FptSmsConfig as FptSmsConfig } from './fpt-sms-brandname.service';
import { env } from 'src/config';

@Global()
@Module({
  imports: [HttpModule, ConfigModule],
  providers: [
    SmsService,
    SmsProviderFactory,
    SpeedSmsProvider,
    TwilioProvider,
    VonageProvider,
    FptSmsProvider,
    FptSmsBrandnameService,
    {
      provide: Redis,
      useValue: new Redis(env.external.REDIS_URL),
    },
  ],
  exports: [
    SmsService,
    SmsProviderFactory,
    SpeedSmsProvider,
    TwilioProvider,
    VonageProvider,
    FptSmsProvider,
    FptSmsBrandnameService,
    Redis,
  ],
})
export class SmsModule {}
