import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName, WorkflowExecutionJobName } from '../../../queue/queue-name.enum';
import { WorkflowExecutionJobData, WorkflowNodeExecutionJobData } from '../../../queue/queue.types';
import { UserWorkflowService } from '../service/user-workflow.service';

/**
 * Processor xử lý workflow jobs cho user
 * Xử lý các job từ queue WORKFLOW_EXECUTION cho regular users
 */
@Injectable()
@Processor(QueueName.WORKFLOW_EXECUTION, {
  concurrency: 5, // Xử lý tối đa 5 job đồng thời cho user (nhiều hơn admin)
  stalledInterval: 30 * 1000, // 30 giây
  maxStalledCount: 1,
})
export class UserWorkflowProcessor extends WorkerHost {
  private readonly logger = new Logger(UserWorkflowProcessor.name);

  constructor(
    private readonly userWorkflowService: UserWorkflowService,
  ) {
    super();
  }

  /**
   * Xử lý job từ queue workflow execution
   * @param job Job chứa dữ liệu workflow execution
   */
  async process(job: Job<WorkflowExecutionJobData | WorkflowNodeExecutionJobData, any, string>): Promise<void> {
    this.logger.log(
      `[USER] Bắt đầu xử lý workflow job: ${job.id} - Type: ${job.name}`,
    );

    try {
      switch (job.name) {
        case WorkflowExecutionJobName.EXECUTE_WORKFLOW:
          await this.handleExecuteWorkflow(job as Job<WorkflowExecutionJobData>);
          break;

        case WorkflowExecutionJobName.EXECUTE_NODE:
          await this.handleExecuteNode(job as Job<WorkflowNodeExecutionJobData>);
          break;

        case WorkflowExecutionJobName.RETRY_WORKFLOW:
          await this.handleRetryWorkflow(job as Job<WorkflowExecutionJobData>);
          break;

        case WorkflowExecutionJobName.CLEANUP_EXECUTION:
          await this.handleCleanupExecution(job);
          break;

        default:
          this.logger.warn(`[USER] Không hỗ trợ job type: ${job.name}`);
          throw new Error(`Unsupported job type: ${job.name}`);
      }

      this.logger.log(
        `[USER] Hoàn thành xử lý workflow job: ${job.id} - Type: ${job.name}`,
      );
    } catch (error) {
      this.logger.error(
        `[USER] Lỗi khi xử lý workflow job: ${job.id} - Type: ${job.name}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xử lý job thực thi workflow hoàn chỉnh
   * @param job Job chứa dữ liệu workflow execution
   */
  private async handleExecuteWorkflow(job: Job<WorkflowExecutionJobData>): Promise<void> {
    const { executionId, workflowId, userId, triggerData, triggerType, metadata, options } = job.data;

    this.logger.log(
      `[USER] Thực thi workflow: ${workflowId} cho user: ${userId} - Execution: ${executionId}`,
    );

    // TODO: Implement workflow execution logic
    // 1. Validate user permissions
    // 2. Check user quota/limits
    // 3. Load workflow definition
    // 4. Validate workflow structure
    // 5. Create execution record
    // 6. Execute workflow nodes in sequence
    // 7. Handle node dependencies and conditions
    // 8. Update execution status
    // 9. Send SSE events if enabled
    // 10. Handle errors and retries
    // 11. Update user usage metrics

    this.logger.log(
      `[USER] Workflow execution completed: ${executionId}`,
    );
  }

  /**
   * Xử lý job thực thi node đơn lẻ
   * @param job Job chứa dữ liệu node execution
   */
  private async handleExecuteNode(job: Job<WorkflowNodeExecutionJobData>): Promise<void> {
    const { executionId, nodeId, nodeType, nodeConfig, inputData, executionContext, options } = job.data;

    this.logger.log(
      `[USER] Thực thi node: ${nodeId} (${nodeType}) - Execution: ${executionId}`,
    );

    // TODO: Implement node execution logic
    // 1. Validate user permissions for node type
    // 2. Check node usage limits
    // 3. Load node definition
    // 4. Validate node configuration
    // 5. Prepare execution context
    // 6. Execute node with input data
    // 7. Validate output data
    // 8. Update execution node data
    // 9. Send SSE events if enabled
    // 10. Handle node-specific errors
    // 11. Update node usage metrics

    this.logger.log(
      `[USER] Node execution completed: ${nodeId}`,
    );
  }

  /**
   * Xử lý job retry workflow execution
   * @param job Job chứa dữ liệu retry workflow
   */
  private async handleRetryWorkflow(job: Job<WorkflowExecutionJobData>): Promise<void> {
    const { executionId, workflowId, userId } = job.data;

    this.logger.log(
      `[USER] Retry workflow execution: ${executionId} - Workflow: ${workflowId}`,
    );

    // TODO: Implement retry logic
    // 1. Validate retry permissions
    // 2. Check retry limits
    // 3. Load failed execution
    // 4. Identify failed nodes
    // 5. Reset execution state
    // 6. Resume from failed point
    // 7. Update retry count
    // 8. Handle max retry limits
    // 9. Update retry metrics

    this.logger.log(
      `[USER] Workflow retry completed: ${executionId}`,
    );
  }

  /**
   * Xử lý job cleanup execution data
   * @param job Job chứa dữ liệu cleanup
   */
  private async handleCleanupExecution(job: Job<any>): Promise<void> {
    const { executionId } = job.data;

    this.logger.log(
      `[USER] Cleanup execution data: ${executionId}`,
    );

    // TODO: Implement cleanup logic
    // 1. Check cleanup permissions
    // 2. Remove old execution logs
    // 3. Clean temporary files
    // 4. Archive execution data
    // 5. Update cleanup metrics

    this.logger.log(
      `[USER] Execution cleanup completed: ${executionId}`,
    );
  }

  /**
   * Event handler khi job hoàn thành
   */
  async onCompleted(job: Job, result: any): Promise<void> {
    this.logger.log(
      `[USER] Job completed successfully: ${job.id} - Type: ${job.name}`,
    );
  }

  /**
   * Event handler khi job thất bại
   */
  async onFailed(job: Job, error: Error): Promise<void> {
    this.logger.error(
      `[USER] Job failed: ${job.id} - Type: ${job.name} - Error: ${error.message}`,
      error.stack,
    );
  }

  /**
   * Event handler khi job bị stalled
   */
  async onStalled(job: Job): Promise<void> {
    this.logger.warn(
      `[USER] Job stalled: ${job.id} - Type: ${job.name}`,
    );
  }
}
