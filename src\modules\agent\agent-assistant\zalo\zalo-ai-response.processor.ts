import { OnQueueEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName } from '../../../../queue';
import {
  ZaloContextLoadingService,
  AgentConfigBuilderService,
} from '../services';
import { ZaloAiMessageRepository } from '../repositories/zalo-ai-message.repository';
import { MessageDirectionEnum, MessageTypeEnum } from '../enums/message.enum';
import {
  ZaloWebhookDto,
  ZaloEventName,
} from '../../../../shared/dto/zalo-webhook-v2.dto';
import { ZaloOaService } from '../services/zalo-oa.service';
import { UserAgentRunRepository } from '../repositories/user-agent-run.repository';
import { RunStatusEnum, PlatformEnum } from '../enums/agent-run.enum';
import { PlatformContextEnum } from '../enums/platform-context.enum';
import { ZaloOfficialAccount } from '../entities';
import { HumanMessage } from '@langchain/core/messages';
import { ZaloConsultationService } from '../../../../shared/services/zalo/zalo-consultation.service';
import { ZaloTokenUtilsService } from '../../../../shared/services/zalo/zalo-token-utils.service';
import {
  AgentAssistantCustomConfigurableType,
  MessageIngestResult,
} from '../interfaces';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { Readable } from 'stream';
import base64 from 'base64-stream';
import { AssistantGraphFactory } from '../core/assistant-graph.factory';
import { env } from 'src/config';
import {
  deleteActiveThread,
  getActiveThread,
  setActiveThread,
} from './active-thread-map';

interface Base64Result {
  base64String: string;
  mimeType: string;
}

/**
 * 🚀 REAL ZALO AI RESPONSE PROCESSOR
 *
 * Handles Zalo webhook events and creates user_agent_runs records.
 * This processor loads context and creates proper database records for AI processing.
 *
 * ⚡ Performance Configuration:
 * - No concurrency limit (maximum system utilization)
 * - 10-second stalled job detection (aggressive monitoring)
 * - Real context loading and database operations
 */
@Injectable()
@Processor(QueueName.ZALO_AI_RESPONSE, {
  concurrency: 1000,
})
export class ZaloAiResponseProcessor extends WorkerHost {
  private readonly logger = new Logger(ZaloAiResponseProcessor.name);

  constructor(
    private readonly contextLoadingService: ZaloContextLoadingService,
    private readonly agentConfigBuilderService: AgentConfigBuilderService,
    private readonly zaloOaService: ZaloOaService,
    private readonly userAgentRunRepository: UserAgentRunRepository,
    private readonly zaloConsultationService: ZaloConsultationService,
    private readonly zaloTokenUtils: ZaloTokenUtilsService,
    private readonly httpService: HttpService,
    private readonly zaloAiMessageRepository: ZaloAiMessageRepository,
    private readonly assistantGraphFactory: AssistantGraphFactory,
  ) {
    super();

    // Log the real processor activation
    this.logger.warn(
      '🚀 REAL ZALO AI RESPONSE PROCESSOR RUNNING AT MAXIMUM CONCURRENCY! 🚀',
    );
    this.logger.warn(
      '📋 Creating real user_agent_runs records with Platform.ZALO',
    );
  }

  /*
    step 1: filter trigger auto response
    step 2: load oa by oaid
    step 3: load context
    step 4: build agent config
    step 5: build langgraph configurable
    step 6: create user_agent_runs record
    step 7: build langgraph input
    step 8: cancel previous thread via this.activeThread.get(threadId)?.abort()
    step 9: invoke langgraph  
  */
  async process(job: Job<ZaloWebhookDto>): Promise<any> {
    const { name, data: webhookData, id: jobId } = job;
    const startTime = Date.now();

    this.logger.log(`🔥 Processing REAL AI job: ${name} (ID: ${job.id})`);
    this.logger.log(
      `📋 Event: ${webhookData.event_name} | OA: ${webhookData.recipient?.id} | Zalo User: ${webhookData.sender?.id}`,
    );

    // FAIL FAST: Early filtering - only process supported message types
    if (!this.triggersAutoResponse(webhookData.event_name)) {
      this.logger.debug(
        `Event ${webhookData.event_name} does not trigger auto-response, skipping job ${job.id}`,
      );
      return {
        success: true,
        skipped: true,
        reason: `Event ${webhookData.event_name} not supported`,
      };
    }
    let context: MessageIngestResult | null = null;
    try {
      // 1. Load OA by oaId first - exit early if not found
      this.logger.debug('🔄 Loading OA for Zalo message processing...');

      const nullableOa = await this.zaloOaService.getOaByOaId(
        webhookData.recipient?.id || '',
      );

      if (!nullableOa) {
        this.logger.warn(
          `No active OA ${webhookData.recipient?.id} found - job completed`,
        );
      }

      const oa: ZaloOfficialAccount = nullableOa as ZaloOfficialAccount;

      if (!oa.agentId) {
        this.logger.warn(
          `No agent configured for OA ${webhookData.recipient?.id} - job completed`,
        );
        return { success: true, skipped: true, reason: 'No agent configured' };
      }

      if (!oa.userId) {
        this.logger.warn(
          `No user ID found for OA ${webhookData.recipient?.id} - job completed`,
        );
        return { success: true, skipped: true, reason: 'No user ID' };
      }

      // 2. Check if access token is valid
      if (!this.zaloOaService.isTokenValid(oa)) {
        this.logger.warn(
          `Invalid or expired token for OA ${webhookData.recipient?.id} - job completed`,
        );
        return { success: true, skipped: true, reason: 'Invalid token' };
      }

      // 3. Extract Zalo user ID from webhook data
      const zaloUserId = webhookData.sender?.id;

      if (!zaloUserId) {
        this.logger.error('No Zalo user ID found in job data or webhook data');
        throw new Error('No Zalo user ID found in job data or webhook data');
      }

      // 4. Now load complete context using the OA object
      this.logger.debug('🔄 Loading context for Zalo message processing...');
      /**
       * Load message by quote_message_id from DB
       * Load the customer identified with webhookData.sender.id
       *
       */
      context = await this.contextLoadingService.loadContext(
        zaloUserId,
        webhookData,
        oa,
      );

      if (!context) {
        throw new Error('Failed to load context for Zalo message processing');
      }

      this.logger.log(
        `✅ Context loaded: Thread ${context.threadId}, Customer ${context.customer.id}`,
      );

      // 2. Build agent configuration for LangGraph
      this.logger.debug('🔧 Building agent configuration...');

      const platformInput = {
        zaloOfficialAccount: oa, // Pass the full OA object
        zaloCustomerId: context.customer.id,
        platform: PlatformContextEnum.ZALO,
        replyToMessageId: this.extractQuoteMessageId(webhookData),
      };
      const latestState =
        await this.assistantGraphFactory.assistantGraph.getState({
          configurable: {
            thread_id: context.threadId,
          },
        });
      const checkpointId =
        latestState?.config?.configurable?.checkpoint_id || undefined;
      const agentConfig = await this.agentConfigBuilderService.buildAgentConfig(
        context,
        platformInput,
        checkpointId,
      );

      this.logger.log(`🚀 Agent Config Built Successfully!`);

      // 3. Create user_agent_runs record with Platform.ZALO
      this.logger.debug('💾 Creating user_agent_runs record...');

      const agentRunData = {
        payload: {
          threadId: context.threadId,
          messageId: context.messageId,
          customerId: context.customer.id,
          oaId: oa.oaId,
          agentId: oa.agentId,
        },
        status: RunStatusEnum.CREATED,
        createdAt: Date.now(),
        createdBy: oa.userId,
        threadId: null,
        platform: PlatformEnum.ZALO,
        platformThreadId: context.threadId.split(':')[2],
        platformMessageId: context.messageId, // Zalo message ID
        jobId: job.id?.toString(),
        startedAt: startTime,
        metadata: {
          oaId: webhookData.recipient?.id,
          eventName: webhookData.event_name,
          zaloUserId: webhookData.sender?.id,
          messageType: this.extractMessageType(webhookData),
        },
      };

      const agentRun =
        await this.userAgentRunRepository.createRun(agentRunData);

      const processingTime = Date.now() - startTime;
      this.logger.log(`✅ REAL Job ${job.id} completed in ${processingTime}ms`);
      this.logger.log(
        `🎯 Created user_agent_run: ${agentRun.id} with Platform.ZALO`,
      );

      this.logger.log(`🤖 invoking langgraph`);

      // 7. Build LangGraph input (convert webhook to HumanMessage)
      const messageContent = await this.extractMessageContent(webhookData);
      const input = {
        messages: [
          new HumanMessage({
            content: messageContent,
          }),
        ],
      };

      this.logger.log(`🤖 LangGraph input built successfully!`);
      const previousAbortController = getActiveThread(context.threadId);

      if (previousAbortController) {
        this.logger.log(`🤖 Cancelling previous thread ${context.threadId}`);
        previousAbortController.abort();
      }

      // 8. Add abort controller for cancellation support
      setActiveThread(context.threadId, context.abortController);


      // 9. Invoke LangGraph with streaming
      const stream = this.assistantGraphFactory.assistantGraph.streamEvents(
        input,
        {
          configurable: agentConfig,
          version: 'v2' as const,
          signal: context.abortController.signal,
          streamMode: ['messages', 'updates'],
        },
      );

      // 10. Handle streaming events and send messages to Zalo
      for await (const event of stream) {
        if (event.event === 'on_chat_model_end') {
          this.logger.log(`💬 AI Response: ${JSON.stringify(event.data)}`);
          const messageContent = event.data.output.content;
          if (messageContent && agentConfig.zaloOfficialAccount) {
            this.logger.log(
              `📤 Sending message to Zalo: ${messageContent.substring(0, 100)}...`,
            );

            // Send message to Zalo and save to database
            await this.sendMessageToZalo(
              messageContent,
              agentConfig.zaloOfficialAccount,
              zaloUserId,
              context.threadId,
            );

            this.logger.log(
              `✅ Message sent to Zalo and saved to database successfully`,
            );
          }
        }
      }

      return {
        success: true,
        real: true,
        result: {
          agentRunId: agentRun.id,
          threadId: context.threadId,
          customerId: context.customer.id,
          messageId: context.messageId,
          processingTime,
          platform: PlatformEnum.ZALO,
        },
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(
        `❌ Job ${job.id} failed after ${processingTime}ms:`,
        error.message,
      );
      // if error is from abort controller then do something that does not throw error
      if (error.name === 'AbortError') {
        this.logger.log(`❌ Job ${job.id} was cancelled`);
        return {
          success: true,
          real: true,
          reason: 'Job was cancelled',
        };
      }
      throw error; // Let BullMQ handle retries
    } finally {
      // Clean up abort controller on error (if context was loaded)
      if (context?.threadId) {
        deleteActiveThread(context?.threadId)
          ? this.logger.log(
              `✅ Deleted abort controller for threadId ${context?.threadId}`,
            )
          : this.logger.warn(
              `⚠️ No abort controller found for threadId ${context?.threadId}`,
            );
      }
    }
  }

  /**
   * Send message to Zalo user and save AI response to database
   */
  private async sendMessageToZalo(
    messageContent: string,
    zaloOfficialAccount: AgentAssistantCustomConfigurableType['zaloOfficialAccount'],
    zaloUserId: string,
    threadId: string,
  ): Promise<void> {
    try {
      if (!zaloOfficialAccount) {
        throw new Error('Zalo Official Account configuration is missing');
      }

      // // Send message to Zalo using token utils with retry mechanism
      const result = await this.zaloTokenUtils.executeWithTokenRetry(
        async (accessToken) => {
          return await this.zaloConsultationService.sendConsultationTextMessage(
            accessToken,
            zaloUserId,
            messageContent,
          );
        },
        zaloOfficialAccount.oaId,
        3, // maxRetries
      );

      this.logger.log(
        `✅ Message sent to Zalo user ${zaloUserId} via OA ${zaloOfficialAccount.oaId}, message ID: ${result.message_id}`,
      );

      // Save AI response to database
      await this.saveAiResponseToDatabase(
        threadId,
        messageContent,
        result.message_id,
      );
    } catch (error) {
      this.logger.error(
        `❌ Failed to send message to Zalo user ${zaloUserId}:`,
        error.message,
      );
      throw error;
    }
  }

  /**
   * Save AI response to database
   */
  private async saveAiResponseToDatabase(
    threadId: string,
    messageContent: string,
    zaloMessageId: string,
  ): Promise<void> {
    try {
      // Extract thread UUID from LangGraph format "zalo:userId:uuid"
      const threadUuid = this.extractThreadUuid(threadId);

      // Create AI message record
      const messageData = {
        threadId: threadUuid,
        messageId: zaloMessageId, // Use Zalo's message ID
        direction: MessageDirectionEnum.OUTGOING,
        messageType: MessageTypeEnum.TEXT,
        rawWebhookData: null, // No webhook data for outgoing messages
        content: messageContent,
        mediaIds: [], // No media for text responses
        createdAt: Date.now(),
      };

      const savedMessage =
        await this.zaloAiMessageRepository.saveZaloMessage(messageData);

      this.logger.debug(
        `💾 Saved AI response ${savedMessage.id} for thread ${threadUuid}`,
      );
    } catch (error) {
      this.logger.error(
        `❌ Failed to save AI response to database:`,
        error.message,
      );
      // Don't throw - message was already sent to user
    }
  }

  /**
   * Extract thread UUID from LangGraph thread ID format
   */
  private extractThreadUuid(threadId: string): string {
    const parts = threadId.split(':');
    if (parts.length !== 3 || parts[0] !== 'zalo') {
      throw new Error(
        `Invalid thread ID format: ${threadId}. Expected format: zalo:userId:uuid`,
      );
    }
    return parts[2]; // Extract UUID from thread ID
  }

  /**
   * Convert URL stream to base64 string with MIME type detection
   */
  private async streamUrlToBase64(url: string): Promise<Base64Result> {
    try {
      // 1. Get response with stream and headers
      const response = await firstValueFrom(
        this.httpService.get(url, { responseType: 'stream' }),
      );

      const mimeType = response.headers['content-type'] || 'image/jpeg';
      const fileStream = response.data as Readable;

      // 2. Use Base64Encode from base64-stream library
      const base64Encoder = new base64.Base64Encode();

      let accumulatedBase64 = '';

      // 3. Pipe source stream (download) to conversion stream (encoder)
      const encodedStream = fileStream.pipe(base64Encoder);

      // 4. Use 'for await...of' loop to consume encoded stream
      for await (const chunk of encodedStream) {
        accumulatedBase64 += chunk;
      }

      // 5. After loop ends, string is complete
      return {
        base64String: accumulatedBase64,
        mimeType: mimeType,
      };
    } catch (error) {
      this.logger.error(
        `Failed to stream or convert file from url: ${url}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Extract message content from webhook data for LangGraph input
   * Handles mixed content (text + attachments) in the same message
   */
  private async extractMessageContent(
    webhookData: ZaloWebhookDto,
  ): Promise<any[]> {
    const content: any[] = [];

    // 1. Always check for text content first (regardless of event type)
    if ('text' in webhookData.message && webhookData.message.text) {
      content.push({
        type: 'text',
        text: webhookData.message.text,
      });
    }

    // 2. Always check for attachments (regardless of event type)
    if (
      'attachments' in webhookData.message &&
      webhookData.message.attachments
    ) {
      for (const attachment of webhookData.message.attachments) {
        switch (attachment.type) {
          case 'image':
            const imageUrl = attachment.payload?.url;
            if (imageUrl) {
              try {
                const { base64String, mimeType } =
                  await this.streamUrlToBase64(imageUrl);
                content.push({
                  type: 'image_url',
                  image_url: {
                    url: `data:${mimeType};base64,${base64String}`,
                  },
                });
              } catch (error) {
                this.logger.error(
                  `Failed to process image: ${imageUrl}`,
                  error,
                );
                content.push({
                  type: 'text',
                  text: `[User sent an image, but failed to process: ${imageUrl}]`,
                });
              }
            }
            break;

          case 'sticker':
            content.push({
              type: 'text',
              text: `[User sent a sticker: ${attachment.payload?.id || 'sticker'}]`,
            });
            break;

          case 'link':
            const linkUrl = attachment.payload?.url;
            const description = attachment.payload?.description;
            content.push({
              type: 'text',
              text: `[User shared a link: ${linkUrl}${description ? ` - ${description}` : ''}]`,
            });
            break;

          default:
            content.push({
              type: 'text',
              text: `[User sent ${attachment.type} attachment]`,
            });
        }
      }
    }

    // 3. Fallback if no content was extracted
    if (content.length === 0) {
      content.push({
        type: 'text',
        text: `[User sent ${webhookData.event_name}]`,
      });
    }

    return content;
  }

  /**
   * Check if webhook event triggers auto response
   */
  private triggersAutoResponse(eventName: ZaloEventName): boolean {
    return [
      ZaloEventName.UserSendText,
      ZaloEventName.UserSendImage,
      ZaloEventName.UserSendSticker,
      ZaloEventName.UserSendLink,
    ].includes(eventName);
  }

  /**
   * Extract message type from webhook data using official structure
   */
  private extractMessageType(webhookData: ZaloWebhookDto): string {
    switch (webhookData.event_name as string) {
      case ZaloEventName.UserSendText:
        return 'text';
      case ZaloEventName.UserSendImage:
        return 'image';
      case ZaloEventName.UserSendSticker:
        return 'sticker';
      case ZaloEventName.UserSendLocation:
        return 'location';
      case ZaloEventName.UserSendLink:
        return 'link';
      case ZaloEventName.UserSendAudio:
        return 'audio';
      case ZaloEventName.UserSendVideo:
        return 'video';
      case ZaloEventName.UserSendFile:
        return 'file';
      case ZaloEventName.UserSendGif:
        return 'gif';
      case ZaloEventName.UserSendBusinessCard:
        return 'business_card';
      default:
        return 'unknown';
    }
  }

  /**
   * Extract quote message ID from webhook data for reply functionality
   * @param webhookData Webhook data from Zalo
   * @returns Quote message ID if present, undefined otherwise
   */
  private extractQuoteMessageId(
    webhookData: ZaloWebhookDto,
  ): string | undefined {
    // Check if the message has a quote_msg_id field
    const message = webhookData.message as any;
    if (message?.quote_msg_id) {
      this.logger.debug(`Found quote message ID: ${message.quote_msg_id}`);
      return message.quote_msg_id;
    }

    this.logger.debug('No quote message ID found in webhook data');
    return undefined;
  }
}
