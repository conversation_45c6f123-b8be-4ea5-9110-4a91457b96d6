import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { ZaloOfficialAccount } from '../entities/zalo-official-account.entity';

/**
 * Repository cho ZaloOfficialAccount entity
 */
@Injectable()
export class ZaloOfficialAccountRepository extends Repository<ZaloOfficialAccount> {
  private readonly logger = new Logger(ZaloOfficialAccountRepository.name);

  constructor(private dataSource: DataSource) {
    super(ZaloOfficialAccount, dataSource.createEntityManager());
  }

  /**
   * Tìm OA theo OA ID và user ID
   */
  async findByOaIdAndUserId(
    oaId: string,
    userId: number,
  ): Promise<ZaloOfficialAccount | null> {
    try {
      return await this.findOne({
        where: {
          oaId,
          userId,
          status: 'active',
        },
      });
    } catch (error) {
      this.logger.error(`Error finding OA: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Tìm OA theo ID
   */
  async findById(id: number): Promise<ZaloOfficialAccount | null> {
    try {
      return await this.findOne({
        where: {
          id,
          status: 'active',
        },
      });
    } catch (error) {
      this.logger.error(`Error finding OA by ID: ${error.message}`, error.stack);
      return null;
    }
  }
}
