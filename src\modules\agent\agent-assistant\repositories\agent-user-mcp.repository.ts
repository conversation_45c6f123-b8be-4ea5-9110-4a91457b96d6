import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AgentUserMcp } from '../entities/agent-user-mcp.entity';
import { UserMcp } from '../entities/user-mcp.entity';

@Injectable()
export class AgentUserMcpRepository {
  constructor(
    @InjectRepository(AgentUserMcp)
    private readonly repository: Repository<AgentUserMcp>,
  ) {}

  async findByAgentId(agentId: string): Promise<AgentUserMcp[]> {
    return this.repository.find({
      where: { agentId }
    });
  }

  async findMcpConfigsByAgentId(agentId: string): Promise<UserMcp[]> {
    return this.repository
      .createQueryBuilder('aum')
      .innerJoin('user_mcp', 'um', 'aum.mcp_id = um.id')
      .select([
        'um.id as "id"',
        'um.name_server as "nameServer"',
        'um.description as "description"',
        'um.config as "config"',
        'um.encrypted_headers as "encryptedHeaders"',
        'um.created_at as "createdAt"',
        'um.updated_at as "updatedAt"',
        'um.user_id as "userId"'
      ])
      .where('aum.agent_id = :agentId', { agentId })
      .andWhere('um.deleted_at IS NULL')
      .getRawMany();
  }
}
