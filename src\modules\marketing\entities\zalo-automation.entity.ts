import { Column, <PERSON>tity, PrimaryGeneratedColumn } from 'typeorm';
import {
  ZaloAutomationActionDto,
  ZaloAutomationStatus,
  ZaloAutomationTriggerDto,
} from '../dto/zalo';

/**
 * Entity cho tự động hóa Zalo
 */
@Entity('zalo_automations')
export class ZaloAutomation {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'oa_id' })
  oaId: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ type: 'json' })
  trigger: ZaloAutomationTriggerDto;

  @Column({ type: 'json' })
  actions: ZaloAutomationActionDto[];

  @Column({
    type: 'enum',
    enum: ZaloAutomationStatus,
    default: ZaloAutomationStatus.ACTIVE,
  })
  status: ZaloAutomationStatus;

  @Column({ name: 'trigger_count', default: 0 })
  triggerCount: number;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
