import { Injectable, Logger } from '@nestjs/common';
import { ChatDatabaseService } from '../database.service';
import { AssistantSpendingRecord } from '../interfaces/spending-history.interface';

@Injectable()
export class UserUsageQueries {
  private readonly logger = new Logger(UserUsageQueries.name);

  constructor(private readonly databaseService: ChatDatabaseService) {}

  async getPointBalanceByUserId(userId: number): Promise<number> {
    const query = `SELECT points_balance
                   from users
                   where id = $1;`;
    const values = [userId];
    try {
      const result = await this.databaseService.query(query, values);
      if (!result?.length) {
        this.logger.error(`No user found with id = ${userId}`);
        throw new Error('No user found');
      }
      return result[0].point_balance;
    } catch (error) {
      this.logger.error(error?.stack | error.message);
      throw error;
    }
  }

  async updatePointBalanceByUserId(
    usage: number,
    userId: number,
  ): Promise<number> {
    const query = `UPDATE users
                   SET points_balance = GREATEST(0, points_balance - $1)
                   WHERE id = $2
                   RETURNING points_balance;`;
    const values = [usage, userId];
    try {
      const result = await this.databaseService.query(query, values);
      this.logger.debug(JSON.stringify(result, null, 2));
      if (!result?.length) {
        this.logger.error(`No user found with id = ${userId}`);
        throw new Error('No user found');
      }
      return result[0][0].points_balance;
    } catch (error) {
      this.logger.error(error?.stack | error.message);
      throw error;
    }
  }

  /**
   * Create a spending history record for assistant usage
   * @param record Spending record data
   * @returns Promise<void>
   */
  async createSpendingRecord(record: AssistantSpendingRecord): Promise<void> {
    const query = `
      INSERT INTO assistant_spending_history (agent_id, user_id, point, model_id, type)
      VALUES ($1, $2, $3, $4, $5)
    `;
    const values = [
      record.agent_id,
      record.user_id,
      record.point,
      record.model_id,
      record.type,
    ];

    try {
      await this.databaseService.query(query, values);
      this.logger.debug(
        `💾 Created spending record for user ${record.user_id}`,
        {
          agentId: record.agent_id,
          userId: record.user_id,
          points: record.point,
          modelId: record.model_id,
          type: record.type,
        },
      );
    } catch (error) {
      this.logger.error(`Failed to create spending record:`, {
        agentId: record.agent_id,
        userId: record.user_id,
        points: record.point,
        modelId: record.model_id,
        type: record.type,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }
}
