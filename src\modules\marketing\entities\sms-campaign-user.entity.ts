import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Enum trạng thái SMS campaign
 */
export enum SmsCampaignStatus {
  DRAFT = 'DRAFT',
  SCHEDULED = 'SCHEDULED',
  SENDING = 'SENDING',
  SENT = 'SENT',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  PAUSED = 'PAUSED',
}

/**
 * Entity đại diện cho bảng sms_campaign_user trong cơ sở dữ liệu
 * Lưu trữ các chiến dịch SMS marketing của user
 */
@Entity('sms_campaign_user')
export class SmsCampaignUser {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của user tạo campaign
   */
  @Column({ name: 'user_id', type: 'bigint' })
  userId: number;

  /**
   * Tên campaign
   */
  @Column({ name: 'name', type: 'varchar', length: 255 })
  name: string;

  /**
   * <PERSON><PERSON> tả campaign
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  /**
   * Nội dung SMS
   */
  @Column({ name: 'content', type: 'text' })
  content: string;

  /**
   * ID của SMS server configuration
   */
  @Column({ name: 'sms_server_id', type: 'integer' })
  smsServerId: number;

  /**
   * ID của segment (nếu gửi cho segment)
   */
  @Column({ name: 'segment_id', type: 'integer', nullable: true })
  segmentId: number | null;

  /**
   * Danh sách audience IDs (nếu gửi cho audience cụ thể)
   */
  @Column({ name: 'audience_ids', type: 'jsonb', nullable: true })
  audienceIds: number[] | null;

  /**
   * ID template SMS (nếu sử dụng template)
   */
  @Column({ name: 'template_id', type: 'integer', nullable: true })
  templateId: number | null;

  /**
   * Template variables
   */
  @Column({ name: 'template_variables', type: 'jsonb', nullable: true })
  templateVariables: Record<string, any> | null;

  /**
   * Trạng thái campaign
   */
  @Column({
    name: 'status',
    type: 'varchar',
    length: 50,
    default: SmsCampaignStatus.DRAFT,
  })
  status: SmsCampaignStatus;

  /**
   * Thời gian lên lịch gửi (Unix timestamp)
   */
  @Column({ name: 'scheduled_at', type: 'bigint', nullable: true })
  scheduledAt: number | null;

  /**
   * Thời gian bắt đầu gửi (Unix timestamp)
   */
  @Column({ name: 'started_at', type: 'bigint', nullable: true })
  startedAt: number | null;

  /**
   * Thời gian hoàn thành (Unix timestamp)
   */
  @Column({ name: 'completed_at', type: 'bigint', nullable: true })
  completedAt: number | null;

  /**
   * Tổng số người nhận
   */
  @Column({ name: 'total_recipients', type: 'integer', default: 0 })
  totalRecipients: number;

  /**
   * Số SMS đã gửi thành công
   */
  @Column({ name: 'sent_count', type: 'integer', default: 0 })
  sentCount: number;

  /**
   * Số SMS gửi thất bại
   */
  @Column({ name: 'failed_count', type: 'integer', default: 0 })
  failedCount: number;

  /**
   * Danh sách job IDs trong queue
   */
  @Column({ name: 'job_ids', type: 'jsonb', nullable: true })
  jobIds: string[] | null;

  /**
   * Thời gian tạo
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
