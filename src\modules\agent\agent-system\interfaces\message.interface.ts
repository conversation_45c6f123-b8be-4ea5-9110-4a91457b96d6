import { EmitEventCallback } from './event';

/**
 * ✅ Message interfaces synced from backend DTOs
 * These interfaces mirror the backend DTOs to ensure type consistency
 * between backend and worker processing
 */

// ✅ Content Block Interfaces (synced from backend DTOs)

export interface TextContentBlock {
  type: 'text';
  content: string;
  edited?: boolean;
  editedAt?: number;
}

export interface ImageContentBlock {
  type: 'image';
  fileId: string;
  name: string;
  path: string;
  tags?: string[];
  desc?: string;
}

export interface FileContentBlock {
  type: 'file';
  fileId: string;
  name: string;
  tags?: string[];
  desc?: string;
}

export interface ToolCallDecisionBlock {
  type: 'tool_call_decision';
  decision: 'yes' | 'no' | 'always';
}

export interface ToolCallInterruptBlock {
  type: 'tool_call_interrupt';
}

// ✅ CLEAN: Union type for all content blocks (flat reply design - no ReplyToBlock needed)
export type ContentBlock =
  | TextContentBlock
  | ImageContentBlock
  | FileContentBlock
  | ToolCallDecisionBlock
  | ToolCallInterruptBlock;

// ✅ Attachment Context Interfaces (synced from backend DTOs)

// ✅ Union type for attachment contexts
export type AttachmentContextBlock = ImageContentBlock | FileContentBlock;

// ✅ Message Request Interface (flat reply design)
export interface MessageRequest {
  contentBlocks: ContentBlock[];
  replyToMessageId?: string; // Optional message ID to reply to
  alwaysApproveToolCall?: boolean;
  attachmentContext?: AttachmentContextBlock[];
}

// ✅ Message Data Interface (extracted from payload)
export interface MessageData {
  contentBlocks: ContentBlock[];
  attachmentContext: AttachmentContextBlock[];
  replyToMessageId?: string; // Optional message ID to reply to
  alwaysApproveToolCall: boolean;
}

/**
 * Configuration for message persistence operations
 */
export interface MessagePersistenceConfig {
  /** Thread ID */
  threadId: string;
  /** Message content */
  content: string;
  /** User ID */
  userId: number;
  /** Message role */
  role: 'user' | 'assistant';
  /** Whether message is partial */
  partial?: boolean;
  /** Whether message was cancelled */
  cancelled?: boolean;
  /** Whether message is complete */
  complete?: boolean;
  /** Token usage data */
  tokenUsage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
    pointCost: number;
  };
}

/**
 * Configuration for saving complete messages
 */
export interface MessageSaveConfig {
  /** Thread ID */
  threadId: string;
  /** Run data from database */
  runData: any;
  /** Message content */
  content: string;
  /** Message role (assistant, user, etc.) */
  role: 'user' | 'assistant';
  /** User ID */
  userId: number;
  /** Optional callback for event emission */
  emitEventCallback?: EmitEventCallback;
}

/**
 * Configuration for saving partial responses
 */
export interface PartialResponseConfig {
  /** Thread ID */
  threadId: string;
  /** Run ID */
  runId: string;
  /** Partial response text */
  partialText: string;
  /** Optional callback for event emission */
  emitEventCallback?: EmitEventCallback;
}

// ✅ Type Guards for Content Blocks

export function isTextContentBlock(
  block: ContentBlock,
): block is TextContentBlock {
  return block.type === 'text';
}

export function isImageContentBlock(
  block: ContentBlock,
): block is ImageContentBlock {
  return block.type === 'image';
}

export function isFileContentBlock(
  block: ContentBlock,
): block is FileContentBlock {
  return block.type === 'file';
}

export function isToolCallDecisionBlock(
  block: ContentBlock,
): block is ToolCallDecisionBlock {
  return block.type === 'tool_call_decision';
}

export function textContentBlockToXml(block: TextContentBlock): string {
  return `<text-block>
    ${block.content}
  </text-block>\n`;
}

export function imageContentBlockToXml(block: ImageContentBlock): string {
  let messageContent: string = '';
  const fileName = block.name;
  const description = block.desc || 'No description available';
  messageContent += `    <file-attachment file-id="${block.fileId}" name="${fileName}" tags="${(block.tags || []).join(', ')}">\n`;
  messageContent += `      <description>${description}</description>\n`;
  messageContent += `    </file-attachment>\n`;
  return messageContent;
}

export function fileContentBlockToXml(block: FileContentBlock): string {
  let messageContent: string = '';
  const fileName = block.name;
  const description = block.desc || 'No description available';
  messageContent += `    <image-attachment image-id="${block.fileId}" name="${fileName}" tags="${(block.tags || []).join(', ')}">\n`;
  messageContent += `      <description>${description}</description>\n`;
  messageContent += `    </image-attachment>\n`;
  return messageContent;
}

// ✅ Helper Functions

export function extractTextFromContentBlocks(
  contentBlocks: ContentBlock[],
): string[] {
  return contentBlocks.filter(isTextContentBlock).map((block) => block.content);
}

export function extractFileIdsFromContentBlocks(
  contentBlocks: ContentBlock[],
): string[] {
  return contentBlocks
    .filter((block) => isImageContentBlock(block) || isFileContentBlock(block))
    .map((block) => block.fileId);
}
