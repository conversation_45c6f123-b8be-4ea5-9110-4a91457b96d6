import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserModelFineTune } from '../entities/user-model-fine-tune.entity';

@Injectable()
export class UserModelFineTuneRepository {
  constructor(
    @InjectRepository(UserModelFineTune)
    private readonly repository: Repository<UserModelFineTune>,
  ) {}

  async findById(id: string): Promise<UserModelFineTune | null> {
    return this.repository.findOne({ 
      where: { id, isSuccess: true }
    });
  }
}
