import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RedisService } from '../../../../infra';
import { UserCampaignHistory } from '../../entities/user-campaign-history.entity';
import { EmailTrackingDto } from '../dto';
import { CampaignAudience } from '../../types/campaign.types';

/**
 * Service xử lý tracking email
 */
@Injectable()
export class EmailTrackingService {
  private readonly logger = new Logger(EmailTrackingService.name);
  private readonly REDIS_TRACKING_KEY = 'email_tracking';
  private readonly BATCH_SIZE = 100;
  private readonly BATCH_INTERVAL = 30000; // 30 seconds

  constructor(
    @InjectRepository(UserCampaignHistory)
    private readonly campaignHistoryRepository: Repository<UserCampaignHistory>,
    private readonly redisService: RedisService,
  ) {
    // Khởi động batch processor
    this.startBatchProcessor();
  }

  /**
   * Lưu tracking event vào Redis
   * @param trackingData Dữ liệu tracking
   */
  async saveTrackingToRedis(trackingData: EmailTrackingDto): Promise<void> {
    try {
      const redisKey = `${this.REDIS_TRACKING_KEY}:${Date.now()}:${Math.random()}`;
      const data = JSON.stringify(trackingData);

      await this.redisService.getRawClient().setex(redisKey, 3600, data); // TTL 1 hour

      this.logger.debug(
        `Tracking data saved to Redis: ${trackingData.trackingId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error saving tracking to Redis: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Tạo tracking ID duy nhất
   * @param campaignId ID campaign
   * @param email Email audience
   * @returns Tracking ID
   */
  generateTrackingId(campaignId: number, email: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    const emailHash = Buffer.from(email).toString('base64').substring(0, 8);
    return `${campaignId}_${emailHash}_${timestamp}_${random}`;
  }

  /**
   * Xử lý tracking event khi email được gửi
   * @param campaignId ID campaign
   * @param audience Thông tin audience đầy đủ
   * @param trackingId ID tracking
   */
  async trackEmailSent(
    campaignId: number,
    audience: CampaignAudience,
    trackingId: string,
  ): Promise<void> {
    const trackingData: EmailTrackingDto = {
      trackingId,
      campaignId,
      audienceId: 0, // Legacy field, không sử dụng nữa
      email: audience.email,
      eventType: 'sent',
      timestamp: Date.now(),
      metadata: {
        audienceName: audience.name,
      },
    };

    await this.saveTrackingToRedis(trackingData);
  }

  /**
   * Xử lý tracking event khi email được gửi (legacy method với audienceId)
   * @param campaignId ID campaign
   * @param audienceId ID audience
   * @param email Email người nhận
   * @param trackingId ID tracking
   * @deprecated Use trackEmailSent(campaignId, audience, trackingId) instead
   */
  async trackEmailSentLegacy(
    campaignId: number,
    audienceId: number,
    email: string,
    trackingId: string,
  ): Promise<void> {
    const trackingData: EmailTrackingDto = {
      trackingId,
      campaignId,
      audienceId,
      email,
      eventType: 'sent',
      timestamp: Date.now(),
    };

    await this.saveTrackingToRedis(trackingData);
  }

  /**
   * Xử lý tracking event khi email được mở (pixel tracking)
   * @param trackingId ID tracking
   * @param metadata Thông tin bổ sung (IP, User-Agent, etc.)
   */
  async trackEmailOpened(trackingId: string, metadata?: any): Promise<void> {
    try {
      // Lấy thông tin campaign và audience từ tracking ID
      const trackingInfo = this.parseTrackingId(trackingId);
      if (!trackingInfo) {
        this.logger.warn(`Invalid tracking ID: ${trackingId}`);
        return;
      }

      const trackingData: EmailTrackingDto = {
        trackingId,
        campaignId: trackingInfo.campaignId,
        audienceId: trackingInfo.audienceId,
        email: '', // Sẽ được lấy từ database nếu cần
        eventType: 'opened',
        timestamp: Date.now(),
        metadata,
      };

      await this.saveTrackingToRedis(trackingData);
    } catch (error) {
      this.logger.error(
        `Error tracking email opened: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Xử lý tracking event khi email gửi thất bại
   * @param campaignId ID campaign
   * @param audienceId ID audience
   * @param email Email người nhận
   * @param trackingId ID tracking
   * @param error Thông tin lỗi
   */
  async trackEmailFailed(
    campaignId: number,
    audienceId: number,
    email: string,
    trackingId: string,
    error?: any,
  ): Promise<void> {
    const trackingData: EmailTrackingDto = {
      trackingId,
      campaignId,
      audienceId,
      email,
      eventType: 'failed',
      timestamp: Date.now(),
      metadata: { error: error?.message || 'Unknown error' },
    };

    await this.saveTrackingToRedis(trackingData);
  }

  /**
   * Xử lý tracking event khi email được delivered thành công
   * @param trackingId ID tracking
   * @param metadata Thông tin bổ sung (message ID, delivery time, etc.)
   */
  async trackEmailDelivered(trackingId: string, metadata?: any): Promise<void> {
    try {
      // Lấy thông tin campaign và audience từ tracking ID
      const trackingInfo = this.parseTrackingId(trackingId);
      if (!trackingInfo) {
        this.logger.warn(`Invalid tracking ID: ${trackingId}`);
        return;
      }

      const trackingData: EmailTrackingDto = {
        trackingId,
        campaignId: trackingInfo.campaignId,
        audienceId: trackingInfo.audienceId,
        email: '', // Sẽ được lấy từ database nếu cần
        eventType: 'delivered',
        timestamp: Date.now(),
        metadata,
      };

      await this.saveTrackingToRedis(trackingData);
    } catch (error) {
      this.logger.error(
        `Error tracking email delivered: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Xử lý tracking event khi email bị bounced
   * @param trackingId ID tracking
   * @param metadata Thông tin bổ sung (bounce reason, bounce type, etc.)
   */
  async trackEmailBounced(trackingId: string, metadata?: any): Promise<void> {
    try {
      // Lấy thông tin campaign và audience từ tracking ID
      const trackingInfo = this.parseTrackingId(trackingId);
      if (!trackingInfo) {
        this.logger.warn(`Invalid tracking ID: ${trackingId}`);
        return;
      }

      const trackingData: EmailTrackingDto = {
        trackingId,
        campaignId: trackingInfo.campaignId,
        audienceId: trackingInfo.audienceId,
        email: '', // Sẽ được lấy từ database nếu cần
        eventType: 'bounced',
        timestamp: Date.now(),
        metadata,
      };

      await this.saveTrackingToRedis(trackingData);
    } catch (error) {
      this.logger.error(
        `Error tracking email bounced: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Xử lý tracking event khi link trong email được click
   * @param trackingId ID tracking
   * @param clickedUrl URL được click
   * @param metadata Thông tin bổ sung (IP, User-Agent, etc.)
   */
  async trackEmailClicked(
    trackingId: string,
    clickedUrl: string,
    metadata?: any,
  ): Promise<void> {
    try {
      // Lấy thông tin campaign và audience từ tracking ID
      const trackingInfo = this.parseTrackingId(trackingId);
      if (!trackingInfo) {
        this.logger.warn(`Invalid tracking ID: ${trackingId}`);
        return;
      }

      // Validate URL để tránh malicious redirects
      if (!this.isValidClickUrl(clickedUrl)) {
        this.logger.warn(
          `Invalid click URL: ${clickedUrl} for tracking: ${trackingId}`,
        );
        return;
      }

      // Check for duplicate clicks (rate limiting)
      const isDuplicate = await this.isDuplicateClick(
        trackingId,
        clickedUrl,
        metadata?.ip,
      );
      if (isDuplicate) {
        this.logger.debug(
          `Duplicate click detected: ${trackingId} -> ${clickedUrl}`,
        );
        // Still allow redirect but don't count as new click
        return;
      }

      const trackingData: EmailTrackingDto = {
        trackingId,
        campaignId: trackingInfo.campaignId,
        audienceId: trackingInfo.audienceId,
        email: '', // Sẽ được lấy từ database nếu cần
        eventType: 'clicked',
        timestamp: Date.now(),
        metadata: {
          ...metadata,
          clickedUrl,
          urlDomain: this.extractDomain(clickedUrl),
          isFirstClick: !isDuplicate,
        },
      };

      await this.saveTrackingToRedis(trackingData);

      // Cache click để detect duplicates
      await this.cacheClickEvent(trackingId, clickedUrl, metadata?.ip);

      this.logger.debug(`Email click tracked: ${trackingId} -> ${clickedUrl}`);
    } catch (error) {
      this.logger.error(
        `Error tracking email clicked: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Validate URL để đảm bảo an toàn
   * @param url URL cần validate
   * @returns True nếu URL hợp lệ
   */
  private isValidClickUrl(url: string): boolean {
    try {
      const parsedUrl = new URL(url);

      // Chỉ cho phép HTTP/HTTPS
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        return false;
      }

      // Blacklist một số domains nguy hiểm
      const blacklistedDomains = [
        'localhost',
        '127.0.0.1',
        '0.0.0.0',
        'file://',
        'ftp://',
      ];

      const hostname = parsedUrl.hostname.toLowerCase();
      return !blacklistedDomains.some((domain) => hostname.includes(domain));
    } catch {
      return false;
    }
  }

  /**
   * Extract domain từ URL
   * @param url URL
   * @returns Domain name
   */
  private extractDomain(url: string): string {
    try {
      const parsedUrl = new URL(url);
      return parsedUrl.hostname;
    } catch {
      return 'unknown';
    }
  }

  /**
   * Check xem có phải duplicate click không
   * @param trackingId Tracking ID
   * @param url Clicked URL
   * @param ip IP address
   * @returns True nếu là duplicate
   */
  private async isDuplicateClick(
    trackingId: string,
    url: string,
    ip?: string,
  ): Promise<boolean> {
    try {
      const cacheKey = `click_cache:${trackingId}:${url}:${ip || 'unknown'}`;
      const cached = await this.redisService.getRawClient().get(cacheKey);
      return cached !== null;
    } catch (error) {
      this.logger.error(`Error checking duplicate click: ${error.message}`);
      return false;
    }
  }

  /**
   * Cache click event để detect duplicates
   * @param trackingId Tracking ID
   * @param url Clicked URL
   * @param ip IP address
   */
  private async cacheClickEvent(
    trackingId: string,
    url: string,
    ip?: string,
  ): Promise<void> {
    try {
      const cacheKey = `click_cache:${trackingId}:${url}:${ip || 'unknown'}`;
      // Cache trong 1 giờ để detect duplicate clicks
      await this.redisService
        .getRawClient()
        .setex(cacheKey, 3600, Date.now().toString());
    } catch (error) {
      this.logger.error(`Error caching click event: ${error.message}`);
    }
  }

  /**
   * Lấy click analytics cho campaign
   * @param campaignId Campaign ID
   * @returns Click analytics data
   */
  async getClickAnalytics(campaignId: number): Promise<{
    totalClicks: number;
    uniqueClicks: number;
    clicksByUrl: Record<string, number>;
    clicksByDomain: Record<string, number>;
    clickRate: number;
    topClickedUrls: Array<{ url: string; clicks: number; domain: string }>;
  }> {
    try {
      // Lấy tất cả click events cho campaign
      const clickEvents = await this.campaignHistoryRepository.find({
        where: {
          campaignId,
          status: 'clicked',
        },
      });

      const totalClicks = clickEvents.length;
      const uniqueClicks = new Set(clickEvents.map((e) => e.audience?.email)).size;

      // Phân tích clicks theo URL và domain
      const clicksByUrl: Record<string, number> = {};
      const clicksByDomain: Record<string, number> = {};

      for (const event of clickEvents) {
        // Parse metadata để lấy clicked URL
        try {
          const metadata =
            typeof event.sentAt === 'string'
              ? JSON.parse(event.sentAt)
              : event.sentAt;
          const clickedUrl = metadata?.clickedUrl || 'unknown';
          const domain = metadata?.urlDomain || this.extractDomain(clickedUrl);

          clicksByUrl[clickedUrl] = (clicksByUrl[clickedUrl] || 0) + 1;
          clicksByDomain[domain] = (clicksByDomain[domain] || 0) + 1;
        } catch {
          // Skip invalid metadata
        }
      }

      // Tính click rate
      const totalSent = await this.campaignHistoryRepository.count({
        where: {
          campaignId,
          status: 'sent',
        },
      });

      const clickRate = totalSent > 0 ? (uniqueClicks / totalSent) * 100 : 0;

      // Top clicked URLs
      const topClickedUrls = Object.entries(clicksByUrl)
        .map(([url, clicks]) => ({
          url,
          clicks,
          domain: this.extractDomain(url),
        }))
        .sort((a, b) => b.clicks - a.clicks)
        .slice(0, 10);

      return {
        totalClicks,
        uniqueClicks,
        clicksByUrl,
        clicksByDomain,
        clickRate,
        topClickedUrls,
      };
    } catch (error) {
      this.logger.error(
        `Error getting click analytics: ${error.message}`,
        error.stack,
      );
      return {
        totalClicks: 0,
        uniqueClicks: 0,
        clicksByUrl: {},
        clicksByDomain: {},
        clickRate: 0,
        topClickedUrls: [],
      };
    }
  }

  /**
   * Lấy click heatmap data cho campaign
   * @param campaignId Campaign ID
   * @returns Click heatmap data by time
   */
  async getClickHeatmap(campaignId: number): Promise<{
    hourlyClicks: Record<string, number>;
    dailyClicks: Record<string, number>;
    clickTimeline: Array<{ timestamp: number; clicks: number }>;
  }> {
    try {
      const clickEvents = await this.campaignHistoryRepository.find({
        where: {
          campaignId,
          status: 'clicked',
        },
        order: {
          sentAt: 'ASC',
        },
      });

      const hourlyClicks: Record<string, number> = {};
      const dailyClicks: Record<string, number> = {};
      const clickTimeline: Array<{ timestamp: number; clicks: number }> = [];

      for (const event of clickEvents) {
        const timestamp = event.sentAt;
        const date = new Date(timestamp);

        const hour = date.getHours().toString().padStart(2, '0');
        const day = date.toISOString().split('T')[0];

        hourlyClicks[hour] = (hourlyClicks[hour] || 0) + 1;
        dailyClicks[day] = (dailyClicks[day] || 0) + 1;

        clickTimeline.push({
          timestamp,
          clicks: 1,
        });
      }

      return {
        hourlyClicks,
        dailyClicks,
        clickTimeline,
      };
    } catch (error) {
      this.logger.error(
        `Error getting click heatmap: ${error.message}`,
        error.stack,
      );
      return {
        hourlyClicks: {},
        dailyClicks: {},
        clickTimeline: [],
      };
    }
  }

  /**
   * Parse tracking ID để lấy thông tin campaign và audience
   * @param trackingId ID tracking
   * @returns Thông tin parsed hoặc null
   */
  public parseTrackingId(
    trackingId: string,
  ): { campaignId: number; audienceId: number } | null {
    try {
      const parts = trackingId.split('_');
      if (parts.length >= 2) {
        return {
          campaignId: parseInt(parts[0]),
          audienceId: parseInt(parts[1]),
        };
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Khởi động batch processor để lưu dữ liệu từ Redis vào database
   */
  private startBatchProcessor(): void {
    setInterval(async () => {
      await this.processBatchTracking();
    }, this.BATCH_INTERVAL);

    this.logger.log('Email tracking batch processor started');
  }

  /**
   * Xử lý batch tracking data từ Redis vào database
   */
  private async processBatchTracking(): Promise<void> {
    try {
      const redis = this.redisService.getRawClient();
      const pattern = `${this.REDIS_TRACKING_KEY}:*`;
      const keys = await redis.keys(pattern);

      if (keys.length === 0) {
        return;
      }

      // Lấy dữ liệu từ Redis
      const trackingEvents: EmailTrackingDto[] = [];
      const pipeline = redis.pipeline();

      for (const key of keys.slice(0, this.BATCH_SIZE)) {
        pipeline.get(key);
        pipeline.del(key); // Xóa key sau khi lấy
      }

      const results = await pipeline.exec();

      // Parse dữ liệu
      if (results && results.length > 0) {
        for (let i = 0; i < results.length; i += 2) {
          const [getError, data] = results[i];
          if (!getError && data) {
            try {
              const trackingEvent = JSON.parse(data as string);
              trackingEvents.push(trackingEvent);
            } catch (parseError) {
              this.logger.error(
                `Error parsing tracking data: ${parseError.message}`,
              );
            }
          }
        }
      }

      if (trackingEvents.length > 0) {
        await this.saveBatchToDatabase(trackingEvents);
        this.logger.debug(`Processed ${trackingEvents.length} tracking events`);
      }
    } catch (error) {
      this.logger.error(
        `Error processing batch tracking: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Lưu batch tracking events vào database
   * @param events Danh sách tracking events
   */
  private async saveBatchToDatabase(events: EmailTrackingDto[]): Promise<void> {
    try {
      const historyEntries = events.map((event) => {
        const history = new UserCampaignHistory();
        history.campaignId = event.campaignId;
        // Sử dụng cấu trúc mới với audience object
        history.audience = {
          name: event.metadata?.audienceName || '',
          email: event.email,
        };
        history.status = event.eventType;
        history.sentAt = event.timestamp;
        history.createdAt = Date.now();
        return history;
      });

      await this.campaignHistoryRepository.save(historyEntries);
      this.logger.debug(
        `Saved ${historyEntries.length} tracking events to database`,
      );
    } catch (error) {
      this.logger.error(
        `Error saving batch to database: ${error.message}`,
        error.stack,
      );
    }
  }
}
