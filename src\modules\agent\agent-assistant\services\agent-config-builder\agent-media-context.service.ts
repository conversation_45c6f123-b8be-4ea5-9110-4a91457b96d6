import { Injectable } from '@nestjs/common';
import { ZaloThreadMediaContextRepository } from '../../repositories';
import { MediaWithContextData } from '../../interfaces/media-context-data.interface';
import { AttachmentContext } from '../../interfaces/attachment-context.interface';


/**
 * Media context interface
 */
export interface MediaContext {
  attachmentImageMap: Record<string, string>;  // mediaId → s3Key mapping
  attachmentContext: AttachmentContext[];      // structured metadata array
}

/**
 * Agent Media Context Service
 * 
 * Handles media context building for LangGraph's image processing workflow.
 * Responsible for: "What images/media are in this conversation?"
 */
@Injectable()
export class AgentMediaContextService {
  constructor(
    private readonly contextRepository: ZaloThreadMediaContextRepository,
  ) {}

  /**
   * Get media context for a conversation thread using JOIN query
   */
  async getMediaContext(threadId: string): Promise<MediaContext> {
    // Use JOIN to get media with context data in one query
    const mediaWithContextData = await this.contextRepository.findMediaWithContextByThreadId(threadId);

    if (mediaWithContextData.length === 0) {
      return {
        attachmentImageMap: {},
        attachmentContext: [],
      };
    }

    return {
      attachmentImageMap: this.buildAttachmentImageMapFromJoin(mediaWithContextData),
      attachmentContext: this.buildAttachmentContextFromJoin(mediaWithContextData),
    };
  }

  /**
   * Build attachment image map from JOIN data (mediaId → s3Key) for LangGraph references
   */
  private buildAttachmentImageMapFromJoin(mediaData: MediaWithContextData[]): Record<string, string> {
    const imageMap: Record<string, string> = {};

    for (const data of mediaData) {
      if (data.mediaId && data.s3Key) {
        imageMap[data.mediaId] = data.s3Key;
      }
    }

    return imageMap;
  }

  /**
   * Build attachment context array from JOIN data for system prompt
   */
  private buildAttachmentContextFromJoin(mediaData: MediaWithContextData[]): AttachmentContext[] {
    return mediaData
      .filter(data => data.mediaId) // Only include records where media exists
      .map(data => ({
        id: data.mediaId!,
        name: data.fileName || 'Unknown',
        description: data.description || data.humanNotes || '',
        tag: data.contextType,
        type: this.getAttachmentTypeFromMimeType(data.mimeType), // Add type field
        metadata: {
          mimeType: data.mimeType,
          tags: data.tags || [],
        },
      }));
  }

  /**
   * Determine attachment type from MIME type
   * @param mimeType MIME type string (e.g., 'image/jpg', 'application/pdf')
   * @returns Attachment type string
   */
  private getAttachmentTypeFromMimeType(mimeType: string | null | undefined): string {
    if (!mimeType) {
      return 'unknown';
    }

    const lowerMimeType = mimeType.toLowerCase();

    // Image types
    if (lowerMimeType.startsWith('image/')) {
      return 'image';
    }

    // Video types
    if (lowerMimeType.startsWith('video/')) {
      return 'video';
    }

    // Audio types
    if (lowerMimeType.startsWith('audio/')) {
      return 'audio';
    }

    // Document/file types
    if (lowerMimeType.includes('pdf') ||
        lowerMimeType.includes('document') ||
        lowerMimeType.includes('text') ||
        lowerMimeType.includes('application/')) {
      return 'file';
    }

    return 'file'; // Default to file for unknown types
  }
}
