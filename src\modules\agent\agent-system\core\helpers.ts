import {
  AIMessage,
  BaseMessage,
  RemoveMessage,
} from '@langchain/core/messages';
import { ToolMessage } from '@langchain/core/messages/tool';
import { createHandoffTool } from './handoff-tool';
import { CustomRunnableConfig } from './react-agent-executor';
import { Logger } from '@nestjs/common';

const logger = new Logger('TrimmerHelper');

/**
 * Scan only the first block and return RemoveMessage(...) for any orphaned
 * AI→Tool sequence or stray ToolMessages. Otherwise return [].
 */
export function dropLeadingOrphanAsRemovals(
  messages: BaseMessage[],
): RemoveMessage[] {
  if (messages.length === 0) return [];

  const [first, ...rest] = messages;

  // Case A: orphaned AIMessage with N tool_calls
  if (first instanceof AIMessage && first.tool_calls?.length) {
    const expectedIds = first.tool_calls.map((tc) => tc.id);
    // grab the next N messages
    const nextN = rest.slice(0, expectedIds.length);

    // are they an exact match, in order?
    const allMatch =
      nextN.length === expectedIds.length &&
      nextN.every(
        (msg, i) =>
          msg instanceof ToolMessage && msg.tool_call_id === expectedIds[i],
      );

    if (!allMatch) {
      // drop the AI + whatever tools DID match
      const toDelete = [
        first,
        ...nextN.filter(
          (msg): msg is ToolMessage =>
            msg instanceof ToolMessage &&
            expectedIds.includes(msg.tool_call_id),
        ),
      ];
      return toDelete
        .filter((m) => !!m.id)
        .map((m) => new RemoveMessage({ id: m.id! }));
    }
    return [];
  }

  // Case B: stray ToolMessages at front
  if (first instanceof ToolMessage) {
    // collect only LEADING ToolMessages (consecutive from start)
    const orphans: BaseMessage[] = [];
    for (const msg of messages) {
      if (msg instanceof ToolMessage) {
        orphans.push(msg);
      } else {
        break; // Stop at first non-ToolMessage
      }
    }
    return orphans
      .filter((m) => !!m.id)
      .map((m) => new RemoveMessage({ id: m.id! }));
  }

  // nothing to drop
  return [];
}

/** Helper for tail-end orphan block removal */
export function dropTrailingOrphanBlock(msgs: BaseMessage[]): BaseMessage[] {
  if (msgs.length === 0) return msgs;

  let end = msgs.length;

  // 1) Strip trailing ToolMessage(s)
  while (end > 0 && msgs[end - 1] instanceof ToolMessage) {
    end--;
  }

  // 2) If we now end on an AIMessage with tool_calls, drop it too (orphaned AI call)
  if (end > 0) {
    const last = msgs[end - 1];
    if (
      last instanceof AIMessage &&
      Array.isArray(last.tool_calls) &&
      last.tool_calls.length > 0
    ) {
      end--;
    }
  }

  return msgs.slice(0, end);
}

export function getHandoffTool(config: CustomRunnableConfig) {
  if (Object.keys(config?.configurable?.agentConfigMap || {})?.length) {
    return createHandoffTool(
      Object.keys(config?.configurable?.agentConfigMap || {}),
    );
  } else {
    return null;
  }
}
