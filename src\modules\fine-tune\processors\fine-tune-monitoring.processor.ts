import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { Job, Queue } from 'bullmq';
import { InjectQueue } from '@nestjs/bullmq';
import { QueueName } from '../../../queue';
import { FineTuneJobName } from '../constants/fine-tune-job-name.enum';
import { MonitoringJobData } from '../dto/fine-tune-job.dto';
import { FineTuneMonitoringService } from '../services/fine-tune-monitoring.service';
import { FineTuneLoggingService } from '../services/fine-tune-logging.service';

/**
 * Processor để xử lý fine-tune monitoring jobs từ BE app
 */
@Injectable()
@Processor(QueueName.FINE_TUNE)
export class FineTuneMonitoringProcessor extends WorkerHost {
  private readonly logger = new Logger(FineTuneMonitoringProcessor.name);
  private readonly POLLING_INTERVAL = 5 * 60 * 1000; // 5 phút
  private readonly MAX_POLLING_ATTEMPTS = 288; // 24 giờ với interval 5 phút

  constructor(
    private readonly monitoringService: FineTuneMonitoringService,
    private readonly loggingService: FineTuneLoggingService,
    @InjectQueue(QueueName.FINE_TUNE) private readonly fineTuneQueue: Queue,
  ) {
    super();
  }

  /**
   * Xử lý monitoring job
   */
  async process(job: Job<MonitoringJobData, any, string>): Promise<void> {
    // Chỉ xử lý job monitoring
    if (job.name !== FineTuneJobName.FINE_TUNE_MONITORING) {
      return;
    }

    const { historyId, providerJobId, provider, userId } = job.data;

    this.logger.log('Processing fine-tune monitoring job', {
      jobId: job.id,
      historyId,
      providerJobId,
      provider,
      userId,
      attempt: job.attemptsMade + 1,
      maxAttempts: this.MAX_POLLING_ATTEMPTS,
    });

    try {
      // Kiểm tra xem job có cần tiếp tục monitoring không
      const shouldContinue = await this.monitoringService.shouldContinueMonitoring(historyId);
      
      if (!shouldContinue) {
        this.logger.log('Job no longer needs monitoring', {
          historyId,
          providerJobId,
        });
        return;
      }

      // Thực hiện monitoring
      const result = await this.monitoringService.monitorJob(job.data);

      this.logger.debug('Monitoring result', {
        historyId,
        providerJobId,
        status: result.status,
        shouldContinuePolling: result.shouldContinuePolling,
        fineTunedModelId: result.fineTunedModelId,
        error: result.error,
      });

      // Nếu cần tiếp tục polling, schedule job tiếp theo
      if (result.shouldContinuePolling && job.attemptsMade < this.MAX_POLLING_ATTEMPTS) {
        await this.scheduleNextMonitoring(job.data);
        
        this.logger.debug('Scheduled next monitoring job', {
          historyId,
          providerJobId,
          nextAttempt: job.attemptsMade + 2,
        });
      } else if (job.attemptsMade >= this.MAX_POLLING_ATTEMPTS) {
        this.logger.warn('Max monitoring attempts reached', {
          historyId,
          providerJobId,
          attempts: job.attemptsMade + 1,
        });
        
        // Cập nhật status thành timeout
        await this.monitoringService.monitorJob({
          ...job.data,
          providerJobId: 'timeout', // Đánh dấu timeout
        });
      } else {
        this.logger.log('Monitoring completed', {
          historyId,
          providerJobId,
          status: result.status,
          fineTunedModelId: result.fineTunedModelId,
        });
      }

    } catch (error) {
      this.logger.error('Error processing fine-tune monitoring job', {
        error: error.message,
        stack: error.stack,
        jobData: job.data,
        attempt: job.attemptsMade + 1,
      });

      // Nếu chưa đạt max attempts, tiếp tục thử
      if (job.attemptsMade < this.MAX_POLLING_ATTEMPTS) {
        await this.scheduleNextMonitoring(job.data);
        
        this.logger.debug('Scheduled retry monitoring job', {
          historyId,
          providerJobId,
          nextAttempt: job.attemptsMade + 2,
          error: error.message,
        });
      } else {
        this.logger.error('Max monitoring attempts reached after errors', {
          historyId,
          providerJobId,
          attempts: job.attemptsMade + 1,
          finalError: error.message,
        });
      }
    }
  }

  /**
   * Schedule job monitoring tiếp theo
   */
  private async scheduleNextMonitoring(jobData: MonitoringJobData): Promise<void> {
    try {
      await this.fineTuneQueue.add(
        FineTuneJobName.FINE_TUNE_MONITORING,
        jobData,
        {
          delay: this.POLLING_INTERVAL,
          attempts: 1, // Mỗi job chỉ thử 1 lần, logic retry được handle trong processor
          removeOnComplete: 10, // Giữ lại 10 job hoàn thành gần nhất
          removeOnFail: 50, // Giữ lại 50 job thất bại gần nhất
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
        },
      );

      this.logger.debug('Next monitoring job scheduled', {
        historyId: jobData.historyId,
        providerJobId: jobData.providerJobId,
        delayMs: this.POLLING_INTERVAL,
      });
    } catch (error) {
      this.logger.error('Error scheduling next monitoring job', {
        error: error.message,
        jobData,
      });
    }
  }

  /**
   * Handle job completion
   */
  async onCompleted(job: Job<MonitoringJobData>): Promise<void> {
    if (job.name !== FineTuneJobName.FINE_TUNE_MONITORING) {
      return;
    }

    this.logger.debug('Monitoring job completed', {
      jobId: job.id,
      historyId: job.data.historyId,
      providerJobId: job.data.providerJobId,
      attempts: job.attemptsMade + 1,
    });
  }

  /**
   * Handle job failure
   */
  async onFailed(job: Job<MonitoringJobData>, error: Error): Promise<void> {
    if (job.name !== FineTuneJobName.FINE_TUNE_MONITORING) {
      return;
    }

    this.logger.error('Monitoring job failed', {
      jobId: job.id,
      historyId: job.data.historyId,
      providerJobId: job.data.providerJobId,
      error: error.message,
      attempts: job.attemptsMade + 1,
    });
  }

  /**
   * Handle job stalled
   */
  async onStalled(job: Job<MonitoringJobData>): Promise<void> {
    if (job.name !== FineTuneJobName.FINE_TUNE_MONITORING) {
      return;
    }

    this.logger.warn('Monitoring job stalled', {
      jobId: job.id,
      historyId: job.data.historyId,
      providerJobId: job.data.providerJobId,
      attempts: job.attemptsMade + 1,
    });
  }
}
