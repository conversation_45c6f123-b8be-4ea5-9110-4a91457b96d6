/**
 * Model pricing configuration interface
 * Defines input and output token rates for AI models
 */
export interface ModelPricingInterface {
  /**
   * Input token rate (cost per 1000 input tokens in USD)
   */
  inputRate: number;

  /**
   * Output token rate (cost per 1000 output tokens in USD)
   */
  outputRate: number;
}

/**
 * Default pricing configuration for models
 */
export const DEFAULT_MODEL_PRICING: ModelPricingInterface = {
  inputRate: 1,
  outputRate: 1,
};
