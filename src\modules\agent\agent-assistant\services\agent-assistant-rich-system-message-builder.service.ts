import { Injectable, Logger } from '@nestjs/common';
import {
  AgentAssistantCustomConfigurableType,
  ConvertCustomerContext,
  ProfileAgent,
  AgentMemory,
  CustomerPlatformData,
  AssistantModelConfig,
} from '../schemas/agent-assistant.schema';
import { CleanZaloOfficialAccount } from '../interfaces/platform-context.interface';

/**
 * Service for building rich system messages with customer context for agent-assistant
 * Takes configurable data and transforms it into XML-formatted prompts
 */
@Injectable()
export class AgentAssistantRichSystemMessageBuilderService {
  private readonly logger = new Logger(
    AgentAssistantRichSystemMessageBuilderService.name,
  );

  /**
   * Build rich system prompt content with customer context data
   * @param configurable AgentAssistantCustomConfigurableType data
   * @param agentConfig Optional agent configuration for profile context
   * @returns Rich prompt content string or null if no context available
   */
  // TODO: add system prompt in ZALO context
  async buildRichSystemPrompt(
    configurable: AgentAssistantCustomConfigurableType,
  ): Promise<string | null> {
    try {
      const contextSections: string[] = [
        `<instruction>${configurable.mainAgent.instruction}</instruction>`,
      ];
      const agentConfig = configurable.mainAgent;

      // Add agent profile context if available (foundation context)
      if (agentConfig?.profile) {
        const profileContext = this.buildProfileContext(agentConfig.profile);
        if (profileContext) {
          contextSections.push(profileContext);
        }
      }

      // Add agent model context if available (foundation context)
      if (agentConfig?.model) {
        const modelContext = this.buildAgentModelContext(agentConfig.model);
        if (modelContext) {
          contextSections.push(modelContext);
        }
      }

      // Add agent memories context if available (foundation context)
      if (agentConfig?.agentMemories && agentConfig.agentMemories.length > 0) {
        const agentMemoryContext = this.buildAgentMemoryContext(
          agentConfig.agentMemories,
        );
        if (agentMemoryContext) {
          contextSections.push(agentMemoryContext);
        }
      }

      // Add customer context if available (new context)
      if (configurable.convertCustomerContext) {
        const customerContext = this.buildConvertCustomerContext(
          configurable.convertCustomerContext,
        );
        if (customerContext) {
          contextSections.push(customerContext);
        }
      }

      // Add Zalo Official Account context if available
      if (configurable.zaloOfficialAccount) {
        const oaContext = this.buildZaloOfficialAccountContext(
          configurable.zaloOfficialAccount,
        );
        if (oaContext) {
          contextSections.push(oaContext);
        }
      }

      // Add reply context if available (foundation context)
      if (configurable.replyToContext) {
        const replyContext = this.buildReplyContext(
          configurable.replyToContext,
        );
        if (replyContext) {
          contextSections.push(replyContext);
        }
      }

      // Add attachment context if available (foundation context)
      if (
        configurable.attachmentContext &&
        configurable.attachmentContext.length > 0
      ) {
        const attachmentContext = this.buildAttachmentContext(
          configurable.attachmentContext,
        );
        if (attachmentContext) {
          contextSections.push(attachmentContext);
        }
      }

      // Return combined context or null if no context available
      if (contextSections.length === 0) {
        this.logger.debug(
          'No context sections available for rich system prompt',
        );
        return null;
      }

      const richPrompt = contextSections.join('\n\n');
      this.logger.debug(
        `Built rich system prompt with ${contextSections.length} context sections`,
      );

      return richPrompt;
    } catch (error) {
      this.logger.error('Failed to build rich system prompt:', error);
      return null;
    }
  }

  /**
   * Build agent profile context section (foundation context)
   * @param profile ProfileAgent data from agent config
   * @returns Formatted profile context string
   */
  private buildProfileContext(profile: ProfileAgent): string | null {
    try {
      if (!profile) {
        return null;
      }

      const profileParts: string[] = [];

      // Add basic information
      if (profile.gender) {
        profileParts.push(`<gender>${profile.gender}</gender>`);
      }

      if (profile.dateOfBirth) {
        const birthDate =
          typeof profile.dateOfBirth === 'string'
            ? profile.dateOfBirth
            : profile.dateOfBirth.toISOString().split('T')[0];
        profileParts.push(`<date-of-birth>${birthDate}</date-of-birth>`);
      }

      if (profile.position) {
        profileParts.push(`<position>${profile.position}</position>`);
      }

      if (profile.education) {
        profileParts.push(`<education>${profile.education}</education>`);
      }

      if (profile.nations) {
        profileParts.push(`<nationality>${profile.nations}</nationality>`);
      }

      // Add array-based information
      if (profile.skills && profile.skills.length > 0) {
        const skillsList = profile.skills.join(', ');
        profileParts.push(`<skills>${skillsList}</skills>`);
      }

      if (profile.personality && profile.personality.length > 0) {
        const personalityList = profile.personality.join(', ');
        profileParts.push(`<personality>${personalityList}</personality>`);
      }

      if (profile.languages && profile.languages.length > 0) {
        const languagesList = profile.languages.join(', ');
        profileParts.push(`<languages>${languagesList}</languages>`);
      }

      if (profileParts.length === 0) {
        return null;
      }

      return `<agent-profile>\n${profileParts.join('\n')}\n</agent-profile>`;
    } catch (error) {
      this.logger.warn('Failed to build profile context:', error);
      return null;
    }
  }

  /**
   * Build agent model context section (foundation context)
   * @param model AssistantModelConfig data from agent config
   * @returns Formatted model context string
   */
  private buildAgentModelContext(model: AssistantModelConfig): string | null {
    try {
      if (!model) {
        return null;
      }

      const modelParts: string[] = [];

      // Add model name
      if (model.name) {
        modelParts.push(`<name>${model.name}</name>`);
      }

      // add input modalities
      if (model.inputModalities && model.inputModalities.length > 0) {
        const inputModalitiesList = model.inputModalities.join(', ');
        modelParts.push(`<input-modalities>${inputModalitiesList}</input-modalities>`);
      }

      if (modelParts.length === 0) {
        return null;
      }

      return `<agent-model>\n${modelParts.join('\n')}\n</agent-model>`;
    } catch (error) {
      this.logger.warn('Failed to build agent model context:', error);
      return null;
    }
  }

  /**
   * Build agent memory context section (foundation context)
   * @param agentMemories Array of agent memory records
   * @returns Formatted agent memory context string
   */
  private buildAgentMemoryContext(agentMemories: AgentMemory[]): string | null {
    try {
      if (!agentMemories || agentMemories.length === 0) {
        return null;
      }

      const memoryItems = agentMemories.map((memory) => {
        const title = memory.title || 'Untitled Memory';
        const content = memory.content || 'No content';
        const reason = memory.reason || 'No reason';

        return `<memory id="${memory.id}" title="${title}" reason="${reason}">${content}</memory>`;
      });

      return `<agent-memories count="${memoryItems.length}">\n${memoryItems.join('\n')}\n</agent-memories>`;
    } catch (error) {
      this.logger.warn('Failed to build agent memory context:', error);
      return null;
    }
  }

  /**
   * Build comprehensive customer context including profile, platform data, and memories
   * @param convertCustomerContext Customer context data
   * @returns Formatted customer context string
   */
  private buildConvertCustomerContext(
    convertCustomerContext: ConvertCustomerContext,
  ): string | null {
    try {
      const contextParts: string[] = [];

      // Customer Profile Context
      if (convertCustomerContext.customerProfile) {
        const profile = convertCustomerContext.customerProfile;
        const profileParts: string[] = [];

        if (profile.name) profileParts.push(`name="${profile.name}"`);
        if (profile.phone) profileParts.push(`phone="${profile.phone}"`);
        if (profile.email)
          profileParts.push(`email="${JSON.stringify(profile.email)}"`);
        if (profile.address) profileParts.push(`address="${profile.address}"`);
        if (profile.tags && profile.tags.length > 0) {
          profileParts.push(`tags="${profile.tags.join(', ')}"`);
        }

        if (profileParts.length > 0) {
          contextParts.push(`<customer_profile ${profileParts.join(' ')} />`);
        }
      }

      // Customer Platform Data Context
      if (convertCustomerContext.customerPlatformData) {
        const platformData = convertCustomerContext.customerPlatformData;
        const platformParts: string[] = [];

        if (platformData.displayName) {
          platformParts.push(`display_name="${platformData.displayName}"`);
        }
        if (platformData.userAlias) {
          platformParts.push(`alias="${platformData.userAlias}"`);
        }
        if (platformData.userIsFollower !== undefined) {
          platformParts.push(`is_follower="${platformData.userIsFollower}"`);
        }
        if (platformData.interactionCount) {
          platformParts.push(
            `interaction_count="${platformData.interactionCount}"`,
          );
        }

        // Calculate engagement level
        const engagementLevel = this.calculateEngagementLevel(platformData);
        if (engagementLevel) {
          platformParts.push(`engagement_level="${engagementLevel}"`);
        }

        if (platformParts.length > 0) {
          contextParts.push(
            `<customer_platform_data ${platformParts.join(' ')} />`,
          );
        }
      }

      // Customer Memories Context
      if (
        convertCustomerContext.customerMemories &&
        convertCustomerContext.customerMemories.length > 0
      ) {
        const memoriesXml = convertCustomerContext.customerMemories
          .map(
            (memory) =>
              `<memory id="${memory.id}" title="${memory.title}" reason="${memory.reason}">${memory.content}</memory>`,
          )
          .join('\n');

        contextParts.push(
          `<customer_memories>\n${memoriesXml}\n</customer_memories>`,
        );
      }

      if (contextParts.length === 0) {
        return null;
      }

      return `<customer_context>\n${contextParts.join('\n')}\n</customer_context>`;
    } catch (error) {
      this.logger.error('Failed to build customer context:', error);
      return null;
    }
  }

  /**
   * Calculate engagement level based on platform interaction data
   * @param platformData Customer platform data
   * @returns Engagement level string
   */
  private calculateEngagementLevel(
    platformData: CustomerPlatformData,
  ): string | null {
    try {
      const interactionCount = platformData.interactionCount || 0;
      const isFollower = platformData.userIsFollower || false;
      const hasAlias = !!platformData.userAlias;

      // Simple engagement calculation
      let score = 0;
      if (interactionCount > 10) score += 3;
      else if (interactionCount > 5) score += 2;
      else if (interactionCount > 1) score += 1;

      if (isFollower) score += 2;
      if (hasAlias) score += 1;

      if (score >= 5) return 'high';
      if (score >= 3) return 'medium';
      if (score >= 1) return 'low';
      return 'new';
    } catch (error) {
      this.logger.warn('Failed to calculate engagement level:', error);
      return null;
    }
  }

  /**
   * Build Zalo Official Account context with business insights
   * @param zaloOfficialAccount OA data (clean version without redundant fields)
   * @returns Formatted OA context string with business context
   */
  private buildZaloOfficialAccountContext(
    zaloOfficialAccount: CleanZaloOfficialAccount,
  ): string | null {
    try {
      const oaParts: string[] = [];

      if (zaloOfficialAccount.name) {
        oaParts.push(`name="${zaloOfficialAccount.name}"`);
      }

      // Business context
      if (zaloOfficialAccount.description) {
        oaParts.push(`description="${zaloOfficialAccount.description}"`);
      }

      if (oaParts.length === 0) {
        return null;
      }

      return `<zalo_official_account ${oaParts.join(' ')} />`;
    } catch (error) {
      this.logger.error('Failed to build Zalo OA context:', error);
      return null;
    }
  }

  /**
   * Calculate account age for business context
   * @param createdAt Account creation timestamp
   * @returns Account age description
   */
  private calculateAccountAge(createdAt: number): string | null {
    try {
      const now = Date.now();
      const ageInDays = Math.floor((now - createdAt) / (1000 * 60 * 60 * 24));

      if (ageInDays < 30) return 'new';
      if (ageInDays < 90) return 'recent';
      if (ageInDays < 365) return 'established';
      return 'mature';
    } catch (error) {
      this.logger.warn('Failed to calculate account age:', error);
      return null;
    }
  }

  /**
   * Build reply context from reply-to message data
   * @param replyToContext Reply context data
   * @returns Formatted reply context string
   */
  private buildReplyContext(replyToContext: {
    messageId: string;
    originalMessageData?: any;
  }): string | null {
    try {
      if (!replyToContext.messageId || !replyToContext.originalMessageData) {
        return null;
      }

      const messageContent =
        replyToContext.originalMessageData.content || 'No content';

      return `<reply_context message_id="${replyToContext.messageId}">${messageContent}</reply_context>`;
    } catch (error) {
      this.logger.error('Failed to build reply context:', error);
      return null;
    }
  }

  /**
   * Build attachment context from attachment data
   * @param attachmentContext Attachment context array
   * @returns Formatted attachment context string
   */
  private buildAttachmentContext(attachmentContext: any[]): string | null {
    try {
      if (!attachmentContext || attachmentContext.length === 0) {
        return null;
      }

      const attachmentXml = attachmentContext
        .map((attachment) => {
          if (attachment.type === 'image') {
            return `<attachment type="image" url="${attachment.url || ''}" />`;
          } else if (attachment.type === 'file') {
            return `<attachment type="file" name="${attachment.name || ''}" />`;
          }
          return `<attachment type="${attachment.type || 'unknown'}" />`;
        })
        .join('\n');

      return `<attachments>\n${attachmentXml}\n</attachments>`;
    } catch (error) {
      this.logger.error('Failed to build attachment context:', error);
      return null;
    }
  }
}
