import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SmsCampaignUser } from '../../entities/sms-campaign-user.entity';
import { UserTemplateSms } from '../../entities/user-template-sms.entity';
import { SmsServerConfiguration } from '../../entities/sms-server-configuration.entity';
import { UserAudienceCustomField } from '../../entities/user-audience-custom-field.entity';
import { SmsMarketingHistory } from '../../entities/sms-marketing-history.entity';
import { SmsCampaignAdmin } from '../../entities/sms-campaign-admin.entity';
import {
  SmsMarketingHistoryData,
  SmsMarketingHistoryBatch,
} from '../interfaces/sms-marketing-history.interface';

/**
 * Service xử lý SMS marketing
 */
@Injectable()
export class SmsMarketingService {
  private readonly logger = new Logger(SmsMarketingService.name);

  constructor(
    @InjectRepository(SmsCampaignUser)
    private readonly campaignRepository: Repository<SmsCampaignUser>,
    @InjectRepository(SmsCampaignAdmin)
    private readonly adminCampaignRepository: Repository<SmsCampaignAdmin>,
    @InjectRepository(UserTemplateSms)
    private readonly templateRepository: Repository<UserTemplateSms>,
    @InjectRepository(SmsServerConfiguration)
    private readonly serverConfigRepository: Repository<SmsServerConfiguration>,
    @InjectRepository(UserAudienceCustomField)
    private readonly customFieldRepository: Repository<UserAudienceCustomField>,
    @InjectRepository(SmsMarketingHistory)
    private readonly historyRepository: Repository<SmsMarketingHistory>,
  ) {}

  /**
   * Lấy campaign theo ID
   * @param campaignId ID của campaign
   * @returns Campaign hoặc null nếu không tìm thấy
   */
  async getCampaignById(campaignId: number): Promise<SmsCampaignUser | null> {
    try {
      const campaign = await this.campaignRepository.findOne({
        where: { id: campaignId },
      });

      if (!campaign) {
        this.logger.warn(`Campaign not found: ${campaignId}`);
        return null;
      }

      return campaign;
    } catch (error) {
      this.logger.error(
        `Error getting campaign ${campaignId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy template SMS theo ID
   * @param templateId ID của template
   * @returns Template SMS hoặc null nếu không tìm thấy
   */
  async getTemplateById(templateId: number): Promise<UserTemplateSms | null> {
    try {
      const template = await this.templateRepository.findOne({
        where: { id: templateId },
      });

      if (!template) {
        this.logger.warn(`Template not found: ${templateId}`);
        return null;
      }

      return template;
    } catch (error) {
      this.logger.error(
        `Error getting template ${templateId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy cấu hình SMS server theo ID
   * @param serverId ID của server configuration
   * @returns Server configuration hoặc null nếu không tìm thấy
   */
  async getServerConfigById(
    serverId: number,
  ): Promise<SmsServerConfiguration | null> {
    try {
      const serverConfig = await this.serverConfigRepository.findOne({
        where: { id: serverId },
      });

      if (!serverConfig) {
        this.logger.warn(`SMS server configuration not found: ${serverId}`);
        return null;
      }

      return serverConfig;
    } catch (error) {
      this.logger.error(
        `Error getting SMS server config ${serverId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy custom fields cho một audience
   * @param audienceId ID của audience
   * @returns Object chứa custom fields
   */
  async getAudienceCustomFields(
    audienceId: number,
  ): Promise<Record<string, any>> {
    try {
      const customFields = await this.customFieldRepository.find({
        where: { audienceId },
      });

      const result: Record<string, any> = {};

      for (const field of customFields) {
        if (field.fieldName) {
          result[field.fieldName] = field.fieldValue;
        }
      }

      return result;
    } catch (error) {
      this.logger.error(
        `Error getting custom fields for audience ${audienceId}: ${error.message}`,
      );
      return {};
    }
  }

  /**
   * Cập nhật trạng thái campaign
   * @param campaignId ID của campaign
   * @param status Trạng thái mới
   * @param additionalData Dữ liệu bổ sung để cập nhật
   */
  async updateCampaignStatus(
    campaignId: number,
    status: string,
    additionalData?: Partial<SmsCampaignUser>,
  ): Promise<void> {
    try {
      const updateData: Partial<SmsCampaignUser> = {
        status: status as any,
        updatedAt: Date.now(),
        ...additionalData,
      };

      await this.campaignRepository.update(campaignId, updateData);

      this.logger.debug(`Updated campaign ${campaignId} status to ${status}`);
    } catch (error) {
      this.logger.error(
        `Error updating campaign ${campaignId} status: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật trạng thái admin campaign
   * @param campaignId ID của admin campaign
   * @param status Trạng thái mới
   * @param additionalData Dữ liệu bổ sung để cập nhật
   */
  async updateAdminCampaignStatus(
    campaignId: number,
    status: string,
    additionalData?: Partial<SmsCampaignAdmin>,
  ): Promise<void> {
    try {
      const updateData: Partial<SmsCampaignAdmin> = {
        status: status as any,
        updatedAt: Date.now(), // Unix timestamp
        ...additionalData,
      };

      await this.adminCampaignRepository.update(campaignId, updateData);

      this.logger.debug(`Updated admin campaign ${campaignId} status to ${status}`);
    } catch (error) {
      this.logger.error(
        `Error updating admin campaign ${campaignId} status: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật số lượng SMS đã gửi và thất bại
   * @param campaignId ID của campaign
   * @param sentCount Số SMS đã gửi thành công
   * @param failedCount Số SMS gửi thất bại
   */
  async updateCampaignCounts(
    campaignId: number,
    sentCount: number,
    failedCount: number,
  ): Promise<void> {
    try {
      await this.campaignRepository.update(campaignId, {
        sentCount,
        failedCount,
        updatedAt: Date.now(),
      });

      this.logger.debug(
        `Updated campaign ${campaignId} counts: sent=${sentCount}, failed=${failedCount}`,
      );
    } catch (error) {
      this.logger.error(
        `Error updating campaign ${campaignId} counts: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lưu lịch sử SMS marketing theo batch để tối ưu hiệu suất
   * @param batch Dữ liệu batch lịch sử SMS marketing
   * @returns Promise<void>
   */
  async batchSaveSmsMarketingHistory(
    batch: SmsMarketingHistoryBatch,
  ): Promise<void> {
    const { histories, batchSize = 100 } = batch;

    if (histories.length === 0) {
      this.logger.debug('No SMS marketing history to save');
      return;
    }

    try {
      this.logger.debug(
        `Batch saving ${histories.length} SMS marketing history records`,
      );

      // Chia thành các batch nhỏ hơn để tránh quá tải database
      for (let i = 0; i < histories.length; i += batchSize) {
        const batchData = histories.slice(i, i + batchSize);

        // Chuyển đổi dữ liệu thành entity format
        const entities = batchData.map((history) => {
          const entity = new SmsMarketingHistory();
          entity.campaignId = history.campaignId;
          entity.messageId = history.messageId || null;
          entity.phone = history.phone;
          entity.brandName = history.brandName || null;
          entity.message = history.message;
          entity.partnerId = history.partnerId || null;
          entity.telco = history.telco || null;
          entity.campaignType = history.campaignType;
          entity.status = history.status;
          entity.errorMessage = history.errorMessage || null;
          entity.errorCode = history.errorCode || null;
          entity.isVietnameseNumber = history.isVietnameseNumber;
          entity.sentAt = history.sentAt;
          entity.createdAt = Date.now();
          // Thêm thông tin người gửi
          entity.senderType = history.senderType || null;
          entity.senderId = history.senderId || null;
          return entity;
        });

        // Bulk insert
        await this.historyRepository.save(entities);

        this.logger.debug(
          `Saved batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(histories.length / batchSize)} with ${batchData.length} records`,
        );
      }

      this.logger.log(
        `Successfully saved ${histories.length} SMS marketing history records`,
      );
    } catch (error) {
      this.logger.error(
        `Error batch saving SMS marketing history: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
