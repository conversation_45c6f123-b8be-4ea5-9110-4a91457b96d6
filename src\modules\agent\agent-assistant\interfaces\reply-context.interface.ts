/**
 * Reply context interface
 * Defines the structure for message reply context data
 */
export interface ReplyContext {
  /**
   * ID of the message being replied to
   */
  messageId: string;

  /**
   * Original message data for context
   */
  originalMessageData: {
    /**
     * Content of the original message
     */
    content: string;

    /**
     * ID of the original message
     */
    messageId: string;

    /**
     * Timestamp when the original message was created
     */
    createdAt: number;

    /**
     * Our internal media IDs associated with the original message
     */
    mediaIds: string[];
  };
}
