export enum ProviderLlmEnum {
  OPENAI = 'OPENAI',
  ANTHROPIC = 'ANTHROPIC',
  GEMINI = 'GEMINI',
  DEEPSEEK = 'DEEPSEEK',
  XAI = 'XAI',
}

export enum ProviderFineTuneEnum {
  OPENAI = 'OPENAI',
  GEMINI = 'GEMINI',
}

/**
 * Object tiện ích để làm việc với ProviderEnum
 */
export const ProviderLlmUtil = {
  /**
   * Lấy giá trị chuỗi của một loại file
   * @param type Loại file
   * @returns Giá trị MIME tương ứng
   */
  getValue(type: ProviderLlmEnum): string {
    return type;
  },

  /**
   * Lấy enum FileTypeEnum từ tên loại file hoặc giá trị MIME type
   * @param type Tên loại file (key của enum) hoặc giá trị MIME type (ví dụ: 'application/pdf')
   * @returns Giá trị enum FileTypeEnum tương ứng
   * @throws AppException nếu loại file không tồn tại
   */
  getProviderType(type: string): ProviderLlmEnum {
    // Kiểm tra nếu là key của enum (ví dụ: 'PDF')
    const typeFromKey = ProviderLlmEnum[type as keyof typeof ProviderLlmEnum];
    if (typeFromKey) {
      return typeFromKey;
    }

    // Kiểm tra nếu là giá trị MIME type (ví dụ: 'application/pdf')
    const entries = Object.entries(ProviderLlmEnum);
    const entry = entries.find(([_, value]) => value === type);

    if (entry) {
      return ProviderLlmEnum[entry[0] as keyof typeof ProviderLlmEnum];
    } 

    throw new Error(`Loại nhà cung cấp AI '${type}' không được hỗ trợ`);

  },
};


/**
 * Object tiện ích để làm việc với ProviderFineTuneEnum
 */
export const ProviderFineTuneUtil = {
  /**
   * Lấy giá trị chuỗi của một loại file
   * @param type Loại file
   * @returns Giá trị MIME tương ứng
   */
  getValue(type: ProviderFineTuneEnum): string {
    return type;
  },

  /**
   * Lấy enum FileTypeEnum từ tên loại file hoặc giá trị MIME type
   * @param type Tên loại file (key của enum) hoặc giá trị MIME type (ví dụ: 'application/pdf')
   * @returns Giá trị enum FileTypeEnum tương ứng
   * @throws AppException nếu loại file không tồn tại
   */
  getProviderType(type: string): ProviderFineTuneEnum {
    // Kiểm tra nếu là key của enum (ví dụ: 'PDF')
    const typeFromKey = ProviderFineTuneEnum[type as keyof typeof ProviderFineTuneEnum];
    if (typeFromKey) {
      return typeFromKey;
    }

    // Kiểm tra nếu là giá trị MIME type (ví dụ: 'application/pdf')
    const entries = Object.entries(ProviderFineTuneEnum);
    const entry = entries.find(([_, value]) => value === type);

    if (entry) {
      return ProviderFineTuneEnum[entry[0] as keyof typeof ProviderFineTuneEnum];
    }

    // Nếu không tìm thấy, ném exception
    throw new Error(`Loại nhà cung cấp AI '${type}' không được hỗ trợ`);
  },
};