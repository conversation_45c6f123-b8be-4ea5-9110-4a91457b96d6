import { Injectable, Logger } from '@nestjs/common';
import { WorkflowContext, NodeExecutionState } from '../types';
import { ExecutionRepository } from '../../../repositories/execution.repository';
import { ExecutionNodeDataRepository } from '../../../repositories/execution-node-data.repository';

/**
 * Workflow state snapshot để persistence
 */
export interface WorkflowStateSnapshot {
  /** ID của execution */
  executionId: string;
  
  /** ID của workflow */
  workflowId: string;
  
  /** Timestamp của snapshot */
  timestamp: number;
  
  /** Version của snapshot format */
  version: string;
  
  /** Serialized workflow context */
  context: SerializedWorkflowContext;
  
  /** Current state của XState machine */
  machineState: any;
  
  /** Checkpoint metadata */
  metadata: {
    reason: 'manual' | 'auto' | 'error' | 'pause';
    triggeredBy?: string;
    description?: string;
    tags?: string[];
  };
}

/**
 * Serialized version của WorkflowContext (JSON-safe)
 */
export interface SerializedWorkflowContext {
  workflowId: string;
  executionId: string;
  nodes: Record<string, NodeExecutionState>;
  connections: any[];
  currentNode?: string;
  runningNodes: string[];
  executionData: Record<string, any>;
  errors: Record<string, string>; // Error messages only
  triggerData: any;
  metadata: any;
  dependencyGraph: {
    dependencies: Record<string, string[]>;
    dependents: Record<string, string[]>;
    rootNodes: string[];
    leafNodes: string[];
  };
  readyNodes: string[];
  waitingNodes: string[];
  options?: any;
  workflowSettings?: any;
}

/**
 * State recovery options
 */
export interface StateRecoveryOptions {
  /** Có restore running nodes về pending không */
  resetRunningNodes?: boolean;
  
  /** Có clear errors không */
  clearErrors?: boolean;
  
  /** Có validate state sau khi restore không */
  validateAfterRestore?: boolean;
  
  /** Timeout cho recovery process (ms) */
  recoveryTimeout?: number;
  
  /** Có force recovery ngay cả khi có conflicts không */
  forceRecovery?: boolean;
}

/**
 * Service để manage workflow state persistence và recovery
 */
@Injectable()
export class WorkflowStateManagerService {
  private readonly logger = new Logger(WorkflowStateManagerService.name);
  private readonly SNAPSHOT_VERSION = '1.0.0';
  
  constructor(
    private readonly executionRepository: ExecutionRepository,
    private readonly executionNodeDataRepository: ExecutionNodeDataRepository,
  ) {}
  
  /**
   * Save workflow state snapshot
   */
  async saveStateSnapshot(
    context: WorkflowContext,
    machineState: any,
    reason: 'manual' | 'auto' | 'error' | 'pause' = 'auto',
    metadata?: Partial<WorkflowStateSnapshot['metadata']>
  ): Promise<string> {
    try {
      const snapshot: WorkflowStateSnapshot = {
        executionId: context.executionId,
        workflowId: context.workflowId,
        timestamp: Date.now(),
        version: this.SNAPSHOT_VERSION,
        context: this.serializeContext(context),
        machineState: this.serializeMachineState(machineState),
        metadata: {
          reason,
          ...metadata,
        },
      };
      
      // Save to database
      const snapshotId = await this.persistSnapshot(snapshot);
      
      this.logger.debug(`Saved state snapshot ${snapshotId} for execution ${context.executionId}`);
      
      return snapshotId;
      
    } catch (error) {
      this.logger.error(`Failed to save state snapshot for execution ${context.executionId}:`, error);
      throw error;
    }
  }
  
  /**
   * Load workflow state snapshot
   */
  async loadStateSnapshot(
    executionId: string,
    snapshotId?: string
  ): Promise<WorkflowStateSnapshot | null> {
    try {
      const snapshot = await this.retrieveSnapshot(executionId, snapshotId);
      
      if (!snapshot) {
        this.logger.warn(`No snapshot found for execution ${executionId}`);
        return null;
      }
      
      // Validate snapshot version
      if (snapshot.version !== this.SNAPSHOT_VERSION) {
        this.logger.warn(`Snapshot version mismatch: ${snapshot.version} vs ${this.SNAPSHOT_VERSION}`);
        // Could implement migration logic here
      }
      
      this.logger.debug(`Loaded state snapshot for execution ${executionId}`);
      
      return snapshot;
      
    } catch (error) {
      this.logger.error(`Failed to load state snapshot for execution ${executionId}:`, error);
      throw error;
    }
  }
  
  /**
   * Restore workflow context từ snapshot
   */
  async restoreWorkflowContext(
    snapshot: WorkflowStateSnapshot,
    options: StateRecoveryOptions = {}
  ): Promise<WorkflowContext> {
    try {
      const context = this.deserializeContext(snapshot.context);
      
      // Apply recovery options
      if (options.resetRunningNodes) {
        context.runningNodes = [];
        // Reset running nodes to pending
        for (const [nodeId, nodeState] of context.nodes.entries()) {
          if (nodeState.status === 'running') {
            nodeState.status = 'pending';
            nodeState.startTime = undefined;
          }
        }
      }
      
      if (options.clearErrors) {
        context.errors.clear();
        // Clear error status from nodes
        for (const [nodeId, nodeState] of context.nodes.entries()) {
          if (nodeState.status === 'failed') {
            nodeState.status = 'pending';
            nodeState.error = undefined;
          }
        }
      }
      
      // Validate restored context
      if (options.validateAfterRestore) {
        const validation = await this.validateRestoredContext(context);
        if (!validation.isValid) {
          throw new Error(`Context validation failed: ${validation.errors.join(', ')}`);
        }
      }
      
      this.logger.log(`Restored workflow context for execution ${context.executionId}`);
      
      return context;
      
    } catch (error) {
      this.logger.error(`Failed to restore workflow context:`, error);
      throw error;
    }
  }
  
  /**
   * Create checkpoint tự động
   */
  async createAutoCheckpoint(
    context: WorkflowContext,
    machineState: any,
    trigger: string
  ): Promise<string> {
    return this.saveStateSnapshot(context, machineState, 'auto', {
      triggeredBy: trigger,
      description: `Auto checkpoint triggered by ${trigger}`,
      tags: ['auto', trigger],
    });
  }
  
  /**
   * Create checkpoint manual
   */
  async createManualCheckpoint(
    context: WorkflowContext,
    machineState: any,
    description?: string,
    tags?: string[]
  ): Promise<string> {
    return this.saveStateSnapshot(context, machineState, 'manual', {
      description: description || 'Manual checkpoint',
      tags: ['manual', ...(tags || [])],
    });
  }
  
  /**
   * List tất cả snapshots cho execution
   */
  async listSnapshots(executionId: string): Promise<Array<{
    id: string;
    timestamp: number;
    reason: string;
    description?: string;
    tags?: string[];
  }>> {
    try {
      return await this.getSnapshotList(executionId);
    } catch (error) {
      this.logger.error(`Failed to list snapshots for execution ${executionId}:`, error);
      throw error;
    }
  }
  
  /**
   * Delete old snapshots (cleanup)
   */
  async cleanupOldSnapshots(
    executionId: string,
    keepCount: number = 10,
    maxAge: number = 7 * 24 * 60 * 60 * 1000 // 7 days
  ): Promise<number> {
    try {
      const deletedCount = await this.deleteOldSnapshots(executionId, keepCount, maxAge);
      
      this.logger.debug(`Cleaned up ${deletedCount} old snapshots for execution ${executionId}`);
      
      return deletedCount;
      
    } catch (error) {
      this.logger.error(`Failed to cleanup snapshots for execution ${executionId}:`, error);
      throw error;
    }
  }
  
  /**
   * Validate restored context
   */
  private async validateRestoredContext(context: WorkflowContext): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Validate basic structure
    if (!context.workflowId) {
      errors.push('Missing workflowId');
    }
    
    if (!context.executionId) {
      errors.push('Missing executionId');
    }
    
    if (!context.nodes || context.nodes.size === 0) {
      errors.push('No nodes found in context');
    }
    
    // Validate node states
    for (const [nodeId, nodeState] of context.nodes.entries()) {
      if (!nodeState.node) {
        errors.push(`Missing node definition for ${nodeId}`);
      }
      
      if (!['pending', 'running', 'completed', 'failed', 'skipped'].includes(nodeState.status)) {
        errors.push(`Invalid status for node ${nodeId}: ${nodeState.status}`);
      }
    }
    
    // Validate dependency graph
    if (!context.dependencyGraph) {
      errors.push('Missing dependency graph');
    } else {
      // Check if all nodes in dependency graph exist
      for (const nodeId of context.dependencyGraph.dependencies.keys()) {
        if (!context.nodes.has(nodeId)) {
          warnings.push(`Node ${nodeId} in dependency graph but not in nodes map`);
        }
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }
  
  /**
   * Serialize workflow context to JSON-safe format
   */
  private serializeContext(context: WorkflowContext): SerializedWorkflowContext {
    return {
      workflowId: context.workflowId,
      executionId: context.executionId,
      nodes: Object.fromEntries(context.nodes.entries()),
      connections: context.connections,
      currentNode: context.currentNode,
      runningNodes: context.runningNodes,
      executionData: Object.fromEntries(context.executionData.entries()),
      errors: Object.fromEntries(
        Array.from(context.errors.entries()).map(([key, error]) => [key, error.message])
      ),
      triggerData: context.triggerData,
      metadata: context.metadata,
      dependencyGraph: {
        dependencies: Object.fromEntries(context.dependencyGraph.dependencies.entries()),
        dependents: Object.fromEntries(context.dependencyGraph.dependents.entries()),
        rootNodes: context.dependencyGraph.rootNodes,
        leafNodes: context.dependencyGraph.leafNodes,
      },
      readyNodes: context.readyNodes,
      waitingNodes: context.waitingNodes,
      options: context.options,
      workflowSettings: context.workflowSettings,
    };
  }
  
  /**
   * Deserialize context từ JSON format
   */
  private deserializeContext(serialized: SerializedWorkflowContext): WorkflowContext {
    return {
      workflowId: serialized.workflowId,
      executionId: serialized.executionId,
      nodes: new Map(Object.entries(serialized.nodes)),
      connections: serialized.connections,
      currentNode: serialized.currentNode,
      runningNodes: serialized.runningNodes,
      executionData: new Map(Object.entries(serialized.executionData)),
      errors: new Map(
        Object.entries(serialized.errors).map(([key, message]) => [key, new Error(message)])
      ),
      triggerData: serialized.triggerData,
      metadata: serialized.metadata,
      dependencyGraph: {
        dependencies: new Map(Object.entries(serialized.dependencyGraph.dependencies)),
        dependents: new Map(Object.entries(serialized.dependencyGraph.dependents)),
        rootNodes: serialized.dependencyGraph.rootNodes,
        leafNodes: serialized.dependencyGraph.leafNodes,
      },
      readyNodes: serialized.readyNodes,
      waitingNodes: serialized.waitingNodes,
      options: serialized.options,
      workflowSettings: serialized.workflowSettings,
    };
  }
  
  /**
   * Serialize XState machine state
   */
  private serializeMachineState(machineState: any): any {
    // XState machine state should be JSON serializable
    return JSON.parse(JSON.stringify(machineState));
  }
  
  // Database operations - implement based on your database schema
  
  private async persistSnapshot(snapshot: WorkflowStateSnapshot): Promise<string> {
    // TODO: Implement database persistence
    // This could save to a dedicated snapshots table or use execution metadata
    const snapshotId = `snapshot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // For now, we'll store in execution metadata
    // In production, you might want a dedicated snapshots table
    
    return snapshotId;
  }
  
  private async retrieveSnapshot(
    executionId: string, 
    snapshotId?: string
  ): Promise<WorkflowStateSnapshot | null> {
    // TODO: Implement database retrieval
    // If snapshotId is provided, get specific snapshot
    // Otherwise, get latest snapshot for execution
    
    return null;
  }
  
  private async getSnapshotList(executionId: string): Promise<Array<{
    id: string;
    timestamp: number;
    reason: string;
    description?: string;
    tags?: string[];
  }>> {
    // TODO: Implement database query
    return [];
  }
  
  private async deleteOldSnapshots(
    executionId: string,
    keepCount: number,
    maxAge: number
  ): Promise<number> {
    // TODO: Implement cleanup logic
    return 0;
  }
}
