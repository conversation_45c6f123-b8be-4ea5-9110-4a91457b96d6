import { <PERSON>alo<PERSON>ust<PERSON>, ZaloOfficialAccount } from '../entities';

/**
 * Message ingestion result interface
 * Defines the result of ingesting a Zalo message into the system
 */
export interface MessageIngestResult {
  /**
   * Thread ID for LangGraph processing (format: "zalo:userId:uuid")
   */
  threadId: string;

  /**
   * Zalo customer information
   */
  customer: ZaloCustomer;

  /**
   * Message ID from the webhook
   */
  messageId: string;

  /**
   * Zalo Official Account information (contains agentId)
   */
  oa: ZaloOfficialAccount;

  /**
   * Abort controller for cancelling the processing
   */
  abortController: AbortController;
}
