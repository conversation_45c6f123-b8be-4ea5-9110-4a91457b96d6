import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserConvertCustomer } from '../entities/user-convert-customer.entity';

@Injectable()
export class UserConvertCustomerRepository {
  constructor(
    @InjectRepository(UserConvertCustomer)
    private readonly repository: Repository<UserConvertCustomer>,
  ) {}

  /**
   * Create new convert customer record
   * @param customerData Customer data to create
   * @returns Created customer record
   */
  async createCustomer(customerData: Partial<UserConvertCustomer>): Promise<UserConvertCustomer> {
    const customer = this.repository.create(customerData);
    return this.repository.save(customer);
  }

  /**
   * Find customer by ID
   * @param customerId Customer ID
   * @returns Customer record or null
   */
  async findById(customerId: string): Promise<UserConvertCustomer | null> {
    return this.repository.findOne({
      where: { id: customerId },
    });
  }

  /**
   * Find customers by user ID
   * @param userId User ID
   * @returns Array of customer records
   */
  async findByUserId(userId: number): Promise<UserConvertCustomer[]> {
    return this.repository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Find customers by agent ID
   * @param agentId Agent ID
   * @returns Array of customer records
   */
  async findByAgentId(agentId: string): Promise<UserConvertCustomer[]> {
    return this.repository.find({
      where: { agentId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Update customer record
   * @param customerId Customer ID
   * @param updateData Data to update
   * @returns Updated customer record
   */
  async updateCustomer(
    customerId: string, 
    updateData: Partial<UserConvertCustomer>
  ): Promise<UserConvertCustomer | null> {
    await this.repository.update(customerId, {
      ...updateData,
      updatedAt: Date.now(),
    });
    
    return this.findById(customerId);
  }

  /**
   * Delete customer record
   * @param customerId Customer ID
   * @returns Success status
   */
  async deleteCustomer(customerId: string): Promise<boolean> {
    const result = await this.repository.delete(customerId);
    if (result?.affected === undefined || result?.affected === 0) {
      return false;
    }
    return true;
  }

  /**
   * Find customers by platform
   * @param platform Platform name
   * @param userId User ID (optional filter)
   * @returns Array of customer records
   */
  async findByPlatform(platform: string, userId?: number): Promise<UserConvertCustomer[]> {
    const where: any = { platform };
    if (userId) {
      where.userId = userId;
    }

    return this.repository.find({
      where,
      order: { createdAt: 'DESC' },
    });
  }
}
