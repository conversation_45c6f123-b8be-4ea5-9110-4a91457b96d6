/**
 * Redis Stream Key Patterns for Agent Mo<PERSON>le (Worker)
 *
 * IMPORTANT: These constants must be kept identical to the ones in redai-v201-be-app
 * to maintain consistency across separate codebases.
 *
 * These constants define the stream key patterns used for Redis Streams
 * in the Agent module to avoid collisions with other modules.
 */

/**
 * Stream key patterns for Chat module Redis Streams
 * Must match CHAT_STREAM_KEYS in redai-v201-be-app
 */
export const CHAT_STREAM_KEYS = {
  /**
   * Agent stream for chat processing
   * Pattern: chat:agent_stream:{threadId}:{runId}
   * @param threadId Chat thread ID
   * @param runId Chat run ID
   * @returns Namespaced stream key
   */
  AGENT_STREAM: (threadId: string, runId: string): string =>
    `chat:agent_stream:${threadId}:${runId}`,

  /**
   * Status stream for run status updates
   * Pattern: chat:status_stream:{runId}
   * @param runId Chat run ID
   * @returns Namespaced status stream key
   */
  STATUS_STREAM: (runId: string): string => `chat:status_stream:${runId}`,

  /**
   * Error stream for error reporting
   * Pattern: chat:error_stream:{threadId}
   * @param threadId Chat thread ID
   * @returns Namespaced error stream key
   */
  ERROR_STREAM: (threadId: string): string => `chat:error_stream:${threadId}`,
} as const;

/**
 * Stream key validation patterns
 */
export const CHAT_STREAM_PATTERNS = {
  AGENT_STREAM_PATTERN: /^chat:agent_stream:[a-f0-9-]{36}:[a-f0-9-]{36}$/,
  STATUS_STREAM_PATTERN: /^chat:status_stream:[a-f0-9-]{36}$/,
  ERROR_STREAM_PATTERN: /^chat:error_stream:[a-f0-9-]{36}$/,
} as const;
