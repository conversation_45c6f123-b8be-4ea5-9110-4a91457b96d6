/**
 * Enum for supported platforms in the agent system
 * 
 * IMPORTANT: This enum must be kept identical in both redai-v201-be-app and redai-v201-be-worker
 * to maintain consistency across separate codebases.
 */
export enum Platform {
  /**
   * Internal chat system (default)
   */
  CHAT = 'chat',

  /**
   * Zalo Official Account platform
   */
  ZALO = 'zalo',

  /**
   * Facebook Messenger platform
   */
  MESSENGER = 'messenger',

  /**
   * Telegram platform
   */
  TELEGRAM = 'telegram'
}

/**
 * Get all platform values as array
 * @returns Array of platform values
 */
export function getAllPlatforms(): Platform[] {
  return Object.values(Platform);
}

/**
 * Check if a string is a valid platform
 * @param value String to check
 * @returns True if valid platform, false otherwise
 */
export function isValidPlatform(value: string): value is Platform {
  return Object.values(Platform).includes(value as Platform);
}

/**
 * Get platform display name
 * @param platform Platform enum value
 * @returns Human-readable platform name
 */
export function getPlatformDisplayName(platform: Platform): string {
  const displayNames: Record<Platform, string> = {
    [Platform.CHAT]: 'Internal Chat',
    [Platform.ZALO]: 'Zalo Official Account',
    [Platform.MESSENGER]: 'Facebook Messenger',
    [Platform.TELEGRAM]: 'Telegram'
  };
  
  return displayNames[platform] || platform;
}
