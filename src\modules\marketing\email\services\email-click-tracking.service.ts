import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RedisService } from '../../../../infra/redis/redis.service';
import { UserCampaignHistory } from '../../entities/user-campaign-history.entity';
import { EmailTrackingService } from './email-tracking.service';

/**
 * Service chuyên biệt cho Click Tracking
 * Xử lý tất cả logic liên quan đến tracking clicks trong email
 */
@Injectable()
export class EmailClickTrackingService {
  private readonly logger = new Logger(EmailClickTrackingService.name);
  private readonly CLICK_CACHE_PREFIX = 'email_click_cache';
  private readonly CLICK_RATE_LIMIT_PREFIX = 'email_click_rate_limit';

  constructor(
    private readonly redisService: RedisService,
    private readonly emailTrackingService: EmailTrackingService,
    @InjectRepository(UserCampaignHistory)
    private readonly campaignHistoryRepository: Repository<UserCampaignHistory>,
  ) {}

  /**
   * Process click event với advanced features
   * @param trackingId Tracking ID
   * @param clickedUrl Clicked URL
   * @param metadata Click metadata
   * @returns Click processing result
   */
  async processClick(
    trackingId: string,
    clickedUrl: string,
    metadata: any,
  ): Promise<{
    success: boolean;
    shouldRedirect: boolean;
    isFirstClick: boolean;
    isRateLimited: boolean;
    error?: string;
  }> {
    try {
      // Validate tracking ID
      const trackingInfo =
        this.emailTrackingService.parseTrackingId(trackingId);
      if (!trackingInfo) {
        return {
          success: false,
          shouldRedirect: false,
          isFirstClick: false,
          isRateLimited: false,
          error: 'Invalid tracking ID',
        };
      }

      // Validate URL
      if (!this.isValidClickUrl(clickedUrl)) {
        return {
          success: false,
          shouldRedirect: false,
          isFirstClick: false,
          isRateLimited: false,
          error: 'Invalid URL',
        };
      }

      // Check rate limiting
      const isRateLimited = await this.checkRateLimit(trackingId, metadata.ip);
      if (isRateLimited) {
        this.logger.warn(
          `Rate limit exceeded for ${trackingId} from ${metadata.ip}`,
        );
        return {
          success: false,
          shouldRedirect: true, // Still redirect but don't track
          isFirstClick: false,
          isRateLimited: true,
        };
      }

      // Check if first click
      const isFirstClick = await this.isFirstClick(
        trackingId,
        clickedUrl,
        metadata.ip,
      );

      // Track the click
      await this.emailTrackingService.trackEmailClicked(
        trackingId,
        clickedUrl,
        {
          ...metadata,
          isFirstClick,
          clickSource: 'email_link',
        },
      );

      // Update rate limit counter
      await this.updateRateLimit(trackingId, metadata.ip);

      // Cache click for duplicate detection
      await this.cacheClick(trackingId, clickedUrl, metadata.ip);

      return {
        success: true,
        shouldRedirect: true,
        isFirstClick,
        isRateLimited: false,
      };
    } catch (error) {
      this.logger.error(
        `Error processing click: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        shouldRedirect: true, // Still redirect on error
        isFirstClick: false,
        isRateLimited: false,
        error: error.message,
      };
    }
  }

  /**
   * Validate click URL
   * @param url URL to validate
   * @returns True if valid
   */
  private isValidClickUrl(url: string): boolean {
    try {
      const parsedUrl = new URL(url);

      // Only allow HTTP/HTTPS
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        return false;
      }

      // Blacklist dangerous domains
      const blacklistedDomains = [
        'localhost',
        '127.0.0.1',
        '0.0.0.0',
        '10.',
        '192.168.',
        '172.16.',
      ];

      const hostname = parsedUrl.hostname.toLowerCase();
      return !blacklistedDomains.some((domain) => hostname.includes(domain));
    } catch {
      return false;
    }
  }

  /**
   * Check if this is the first click
   * @param trackingId Tracking ID
   * @param url Clicked URL
   * @param ip IP address
   * @returns True if first click
   */
  private async isFirstClick(
    trackingId: string,
    url: string,
    ip: string,
  ): Promise<boolean> {
    try {
      const cacheKey = `${this.CLICK_CACHE_PREFIX}:${trackingId}:${url}:${ip}`;
      const cached = await this.redisService.getRawClient().get(cacheKey);
      return cached === null;
    } catch (error) {
      this.logger.error(`Error checking first click: ${error.message}`);
      return true; // Default to first click on error
    }
  }

  /**
   * Cache click for duplicate detection
   * @param trackingId Tracking ID
   * @param url Clicked URL
   * @param ip IP address
   */
  private async cacheClick(
    trackingId: string,
    url: string,
    ip: string,
  ): Promise<void> {
    try {
      const cacheKey = `${this.CLICK_CACHE_PREFIX}:${trackingId}:${url}:${ip}`;
      // Cache for 24 hours
      await this.redisService
        .getRawClient()
        .setex(cacheKey, 86400, Date.now().toString());
    } catch (error) {
      this.logger.error(`Error caching click: ${error.message}`);
    }
  }

  /**
   * Check rate limiting
   * @param trackingId Tracking ID
   * @param ip IP address
   * @returns True if rate limited
   */
  private async checkRateLimit(
    trackingId: string,
    ip: string,
  ): Promise<boolean> {
    try {
      const rateLimitKey = `${this.CLICK_RATE_LIMIT_PREFIX}:${ip}`;
      const current = await this.redisService.getRawClient().get(rateLimitKey);

      // Allow max 10 clicks per minute per IP
      const maxClicks = 10;
      const currentCount = current ? parseInt(current) : 0;

      return currentCount >= maxClicks;
    } catch (error) {
      this.logger.error(`Error checking rate limit: ${error.message}`);
      return false; // Don't rate limit on error
    }
  }

  /**
   * Update rate limit counter
   * @param trackingId Tracking ID
   * @param ip IP address
   */
  private async updateRateLimit(trackingId: string, ip: string): Promise<void> {
    try {
      const rateLimitKey = `${this.CLICK_RATE_LIMIT_PREFIX}:${ip}`;
      const current = await this.redisService.getRawClient().get(rateLimitKey);

      if (current) {
        await this.redisService.getRawClient().incr(rateLimitKey);
      } else {
        // Set with 60 second expiry
        await this.redisService.getRawClient().setex(rateLimitKey, 60, '1');
      }
    } catch (error) {
      this.logger.error(`Error updating rate limit: ${error.message}`);
    }
  }

  /**
   * Get click statistics for campaign
   * @param campaignId Campaign ID
   * @returns Click statistics
   */
  async getClickStatistics(campaignId: number): Promise<{
    totalClicks: number;
    uniqueClicks: number;
    clickRate: number;
    topUrls: Array<{ url: string; clicks: number }>;
    clicksByHour: Record<string, number>;
    clicksByDevice: Record<string, number>;
  }> {
    try {
      const clickEvents = await this.campaignHistoryRepository.find({
        where: {
          campaignId,
          status: 'clicked',
        },
      });

      const totalClicks = clickEvents.length;
      const uniqueClicks = new Set(clickEvents.map((e) => e.audience?.email)).size;

      // Calculate click rate
      const totalSent = await this.campaignHistoryRepository.count({
        where: {
          campaignId,
          status: 'sent',
        },
      });

      const clickRate = totalSent > 0 ? (uniqueClicks / totalSent) * 100 : 0;

      // Analyze clicks by URL
      const urlClicks: Record<string, number> = {};
      const hourlyClicks: Record<string, number> = {};
      const deviceClicks: Record<string, number> = {};

      for (const event of clickEvents) {
        try {
          // Parse metadata
          const metadata =
            typeof event.sentAt === 'string'
              ? JSON.parse(event.sentAt)
              : event.sentAt;
          const url = metadata?.clickedUrl || 'unknown';
          const timestamp = metadata?.timestamp || event.sentAt;
          const userAgent = metadata?.userAgent || 'unknown';

          // Count by URL
          urlClicks[url] = (urlClicks[url] || 0) + 1;

          // Count by hour
          const hour = new Date(timestamp)
            .getHours()
            .toString()
            .padStart(2, '0');
          hourlyClicks[hour] = (hourlyClicks[hour] || 0) + 1;

          // Count by device type
          const deviceType = this.getDeviceType(userAgent);
          deviceClicks[deviceType] = (deviceClicks[deviceType] || 0) + 1;
        } catch {
          // Skip invalid metadata
        }
      }

      // Top URLs
      const topUrls = Object.entries(urlClicks)
        .map(([url, clicks]) => ({ url, clicks }))
        .sort((a, b) => b.clicks - a.clicks)
        .slice(0, 10);

      return {
        totalClicks,
        uniqueClicks,
        clickRate,
        topUrls,
        clicksByHour: hourlyClicks,
        clicksByDevice: deviceClicks,
      };
    } catch (error) {
      this.logger.error(
        `Error getting click statistics: ${error.message}`,
        error.stack,
      );
      return {
        totalClicks: 0,
        uniqueClicks: 0,
        clickRate: 0,
        topUrls: [],
        clicksByHour: {},
        clicksByDevice: {},
      };
    }
  }

  /**
   * Detect device type from user agent
   * @param userAgent User agent string
   * @returns Device type
   */
  private getDeviceType(userAgent: string): string {
    if (!userAgent) return 'unknown';

    const ua = userAgent.toLowerCase();

    if (
      ua.includes('mobile') ||
      ua.includes('android') ||
      ua.includes('iphone')
    ) {
      return 'mobile';
    } else if (ua.includes('tablet') || ua.includes('ipad')) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  }

  /**
   * Clean up old click cache data
   */
  async cleanupClickCache(): Promise<void> {
    try {
      const pattern = `${this.CLICK_CACHE_PREFIX}:*`;
      const keys = await this.redisService.getRawClient().keys(pattern);

      if (keys.length > 0) {
        // Delete keys older than 7 days
        const cutoffTime = Date.now() - 7 * 24 * 60 * 60 * 1000;
        const keysToDelete: string[] = [];

        for (const key of keys) {
          try {
            const value = await this.redisService.getRawClient().get(key);
            if (value && parseInt(value) < cutoffTime) {
              keysToDelete.push(key);
            }
          } catch {
            // Skip invalid keys
          }
        }

        if (keysToDelete.length > 0) {
          await this.redisService.getRawClient().del(...keysToDelete);
          this.logger.log(
            `Cleaned up ${keysToDelete.length} old click cache entries`,
          );
        }
      }
    } catch (error) {
      this.logger.error(`Error cleaning up click cache: ${error.message}`);
    }
  }
}
