# Workflow Processors

Thư mục này chứa các processor xử lý workflow jobs từ queue `WORKFLOW_EXECUTION`.

## 📁 Cấu Trúc

```
processors/
├── admin-workflow.processor.ts    # Processor cho admin workflow jobs
├── user-workflow.processor.ts     # Processor cho user workflow jobs
├── index.ts                       # Export tất cả processors
└── README.md                      # File này
```

## 🎯 Mục Đích

### AdminWorkflowProcessor
- **Concurrency**: 3 jobs đồng thời (ít hơn user để ưu tiên admin)
- **Stalled Interval**: 60 giây
- **Max Stalled Count**: 2
- **Chức năng**: Xử lý workflow jobs cho admin users với quyền cao hơn

### UserWorkflowProcessor  
- **Concurrency**: 5 jobs đồng thời (nhiều hơn admin)
- **Stalled Interval**: 30 giây
- **Max Stalled Count**: 1
- **<PERSON><PERSON><PERSON> năng**: Xử lý workflow jobs cho regular users với validation và limits

## 🔄 Job Types Được Hỗ Trợ

Cả 2 processor đều xử lý các job types sau từ `WorkflowExecutionJobName`:

### 1. EXECUTE_WORKFLOW
- **Mục đích**: Thực thi workflow hoàn chỉnh từ trigger
- **Input**: `WorkflowExecutionJobData`
- **Chức năng**:
  - Load workflow definition
  - Validate workflow structure
  - Create execution record
  - Execute workflow nodes in sequence
  - Handle node dependencies và conditions
  - Update execution status
  - Send SSE events (nếu enabled)
  - Handle errors và retries

### 2. EXECUTE_NODE
- **Mục đích**: Thực thi node đơn lẻ trong workflow
- **Input**: `WorkflowNodeExecutionJobData`
- **Chức năng**:
  - Load node definition
  - Validate node configuration
  - Prepare execution context
  - Execute node với input data
  - Validate output data
  - Update execution node data
  - Send SSE events (nếu enabled)
  - Handle node-specific errors

### 3. RETRY_WORKFLOW
- **Mục đích**: Retry failed workflow execution
- **Input**: `WorkflowExecutionJobData`
- **Chức năng**:
  - Load failed execution
  - Identify failed nodes
  - Reset execution state
  - Resume từ failed point
  - Update retry count
  - Handle max retry limits

### 4. CLEANUP_EXECUTION
- **Mục đích**: Cleanup execution data
- **Input**: Job với `executionId`
- **Chức năng**:
  - Remove old execution logs
  - Clean temporary files
  - Archive execution data
  - Update cleanup metrics

## 🚀 Cách Sử Dụng

### 1. Queue Job Creation
```typescript
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { QueueName, WorkflowExecutionJobName } from '../../../queue';

@Injectable()
export class WorkflowService {
  constructor(
    @InjectQueue(QueueName.WORKFLOW_EXECUTION) 
    private workflowQueue: Queue
  ) {}

  async executeWorkflow(data: WorkflowExecutionJobData) {
    await this.workflowQueue.add(
      WorkflowExecutionJobName.EXECUTE_WORKFLOW,
      data,
      {
        priority: 1,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      }
    );
  }
}
```

### 2. Job Data Types
```typescript
// Workflow execution job
interface WorkflowExecutionJobData {
  executionId: string;
  workflowId: string;
  userId: number;
  triggerData: any;
  triggerType: 'manual' | 'webhook' | 'schedule';
  metadata?: {
    source?: string;
    webhookId?: string;
    scheduleId?: string;
    priority?: number;
  };
  options?: {
    enableSSE?: boolean;
    timeout?: number;
    retryOnFailure?: boolean;
  };
}

// Node execution job
interface WorkflowNodeExecutionJobData {
  executionId: string;
  nodeId: string;
  nodeType: string;
  nodeConfig: Record<string, any>;
  inputData: Record<string, any>;
  executionContext: Record<string, any>;
  options?: {
    enableSSE?: boolean;
    timeout?: number;
    skipValidation?: boolean;
  };
}
```

## 📊 Event Handlers

Cả 2 processor đều có các event handlers:

- **onCompleted**: Log khi job hoàn thành thành công
- **onFailed**: Log và handle khi job thất bại
- **onStalled**: Log cảnh báo khi job bị stalled

## 🔧 Configuration

Processors được đăng ký trong `WorkflowModule`:

```typescript
@Module({
  imports: [
    BullModule.registerQueue({
      name: QueueName.WORKFLOW_EXECUTION,
    }),
  ],
  providers: [
    AdminWorkflowProcessor,
    UserWorkflowProcessor,
  ],
})
export class WorkflowModule {}
```

## 📝 TODO Implementation

Các processor hiện tại chỉ có skeleton code. Cần implement:

1. **Workflow Execution Logic**
2. **Node Execution Logic** 
3. **Error Handling và Retry Logic**
4. **SSE Event Streaming**
5. **User Permission Validation**
6. **Usage Metrics và Limits**
7. **Database Operations**
8. **Integration với External Services**

## 🔍 Monitoring

Sử dụng Bull Board để monitor queue:
- URL: `/queues`
- Username: `admin`
- Password: `redai@123`
