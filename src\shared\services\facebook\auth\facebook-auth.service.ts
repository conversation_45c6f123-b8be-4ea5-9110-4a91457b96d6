import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Injectable, Logger } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import {
  FacebookAuthResponse,
  FacebookLongLivedTokenResponse,
} from '../interfaces/facebook.interface';

/**
 * Service để xử lý xác thực Facebook
 */
@Injectable()
export class FacebookAuthService {
  private readonly logger = new Logger(FacebookAuthService.name);
  private readonly baseUrl = 'https://graph.facebook.com/v18.0';

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Tạo URL xác thực Facebook
   */
  generateAuthUrl(
    appId: string,
    redirectUri: string,
    scopes: string[] = ['public_profile', 'email'],
    state?: string,
  ): string {
    const baseUrl = 'https://www.facebook.com/v18.0/dialog/oauth';
    const params = new URLSearchParams({
      client_id: appId,
      redirect_uri: redirectUri,
      scope: scopes.join(','),
      response_type: 'code',
      ...(state && { state }),
    });

    return `${baseUrl}?${params.toString()}`;
  }

  /**
   * Đổi authorization code thành access token
   */
  async exchangeCodeForToken(
    appId: string,
    appSecret: string,
    code: string,
    redirectUri: string,
  ): Promise<FacebookAuthResponse> {
    try {
      const url = `${this.baseUrl}/oauth/access_token`;
      const params = {
        client_id: appId,
        client_secret: appSecret,
        redirect_uri: redirectUri,
        code,
      };

      const response = await firstValueFrom(
        this.httpService.get(url, { params }),
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Error exchanging code for token: ${error.message}`);
      throw new Error(`Failed to exchange code for token: ${error.message}`);
    }
  }

  /**
   * Lấy long-lived access token
   */
  async getLongLivedToken(
    appId: string,
    appSecret: string,
    shortLivedToken: string,
  ): Promise<FacebookLongLivedTokenResponse> {
    try {
      const url = `${this.baseUrl}/oauth/access_token`;
      const params = {
        grant_type: 'fb_exchange_token',
        client_id: appId,
        client_secret: appSecret,
        fb_exchange_token: shortLivedToken,
      };

      const response = await firstValueFrom(
        this.httpService.get(url, { params }),
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Error getting long-lived token: ${error.message}`);
      throw new Error(`Failed to get long-lived token: ${error.message}`);
    }
  }

  /**
   * Verify access token
   */
  async verifyToken(accessToken: string): Promise<any> {
    try {
      const url = `${this.baseUrl}/me`;
      const params = {
        access_token: accessToken,
        fields: 'id,name,email',
      };

      const response = await firstValueFrom(
        this.httpService.get(url, { params }),
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Error verifying token: ${error.message}`);
      throw new Error(`Failed to verify token: ${error.message}`);
    }
  }
}
