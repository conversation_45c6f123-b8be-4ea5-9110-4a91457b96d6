import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { GoogleFineTuneJobResponse, PollingResult } from '../interfaces';

/**
 * Service để tương tác với Google Vertex AI Fine-tuning API
 */
@Injectable()
export class GoogleFineTuneService {
  private readonly logger = new Logger(GoogleFineTuneService.name);
  private readonly baseURL = 'https://aiplatform.googleapis.com/v1';

  /**
   * Tạo HTTP client với API key
   */
  private createHttpClient(apiKey: string): AxiosInstance {
    return axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000,
    });
  }

  /**
   * Lấy trạng thái của fine-tune job từ Google Vertex AI
   * @param jobId - ID của fine-tune job (format: projects/{project}/locations/{location}/customJobs/{job})
   * @param apiKey - API key đã decrypt
   * @returns Thông tin trạng thái job
   */
  async getFineTuneJobStatus(jobId: string, apiKey: string): Promise<PollingResult> {
    try {
      this.logger.debug(`Getting Google fine-tune job status for job: ${jobId}`);

      const httpClient = this.createHttpClient(apiKey);
      
      // jobId có thể là full path hoặc chỉ là job name
      const jobPath = jobId.startsWith('projects/') ? jobId : this.constructJobPath(jobId);
      
      const response = await httpClient.get<GoogleFineTuneJobResponse>(
        `/${jobPath}`
      );

      const jobData = response.data;
      
      this.logger.debug(`Google job status: ${jobData.state}`, {
        jobId,
        state: jobData.state,
        name: jobData.name,
      });

      return this.mapGoogleStatusToResult(jobData);
    } catch (error) {
      this.logger.error(`Failed to get Google fine-tune job status for job: ${jobId}`, {
        error: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });

      // Nếu job không tồn tại (404), coi như failed
      if (error.response?.status === 404) {
        return {
          success: false,
          status: 'failed',
          error: 'Job not found',
          shouldContinuePolling: false,
        };
      }

      // Nếu là lỗi authentication (401, 403), coi như failed
      if (error.response?.status === 401 || error.response?.status === 403) {
        return {
          success: false,
          status: 'failed',
          error: 'Authentication failed',
          shouldContinuePolling: false,
        };
      }

      // Các lỗi khác, tiếp tục polling
      return {
        success: false,
        status: 'error',
        error: error.message,
        shouldContinuePolling: true,
      };
    }
  }

  /**
   * Map Google status thành PollingResult
   */
  private mapGoogleStatusToResult(jobData: GoogleFineTuneJobResponse): PollingResult {
    const { state, error, name } = jobData;

    switch (state) {
      case 'JOB_STATE_SUCCEEDED':
        return {
          success: true,
          status: 'succeeded',
          modelId: this.extractModelIdFromJobName(name),
          shouldContinuePolling: false,
        };

      case 'JOB_STATE_FAILED':
      case 'JOB_STATE_CANCELLED':
        return {
          success: false,
          status: state.toLowerCase(),
          error: error?.message || `Job ${state}`,
          shouldContinuePolling: false,
        };

      case 'JOB_STATE_QUEUED':
      case 'JOB_STATE_RUNNING':
        return {
          success: false,
          status: state.toLowerCase(),
          shouldContinuePolling: true,
        };

      case 'JOB_STATE_UNSPECIFIED':
      default:
        this.logger.warn(`Unknown Google status: ${state}`);
        return {
          success: false,
          status: state?.toLowerCase() || 'unknown',
          shouldContinuePolling: true,
        };
    }
  }

  /**
   * Construct job path từ job ID
   */
  private constructJobPath(jobId: string): string {
    // Nếu jobId đã là full path, return as is
    if (jobId.startsWith('projects/')) {
      return jobId;
    }

    // Mặc định sử dụng project và location từ environment hoặc default
    const project = process.env.GOOGLE_CLOUD_PROJECT || 'default-project';
    const location = process.env.GOOGLE_CLOUD_LOCATION || 'us-central1';
    
    return `projects/${project}/locations/${location}/customJobs/${jobId}`;
  }

  /**
   * Extract model ID từ job name
   */
  private extractModelIdFromJobName(jobName: string): string {
    // Google Vertex AI thường trả về job name, có thể cần extract model ID
    // Tùy thuộc vào cách Google trả về, có thể cần adjust logic này
    if (jobName && jobName.includes('/')) {
      const parts = jobName.split('/');
      return parts[parts.length - 1]; // Lấy phần cuối
    }
    return jobName;
  }

  /**
   * Kiểm tra API key có hợp lệ không
   */
  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const httpClient = this.createHttpClient(apiKey);
      
      // Gọi API đơn giản để kiểm tra key
      const project = process.env.GOOGLE_CLOUD_PROJECT || 'default-project';
      const location = process.env.GOOGLE_CLOUD_LOCATION || 'us-central1';
      
      await httpClient.get(`/projects/${project}/locations/${location}/customJobs`, {
        params: { pageSize: 1 },
      });

      return true;
    } catch (error) {
      this.logger.warn('Google API key validation failed', {
        status: error.response?.status,
        error: error.message,
      });
      return false;
    }
  }

  /**
   * Lấy danh sách fine-tune jobs
   */
  async listFineTuneJobs(apiKey: string, pageSize: number = 20): Promise<GoogleFineTuneJobResponse[]> {
    try {
      const httpClient = this.createHttpClient(apiKey);
      
      const project = process.env.GOOGLE_CLOUD_PROJECT || 'default-project';
      const location = process.env.GOOGLE_CLOUD_LOCATION || 'us-central1';
      
      const response = await httpClient.get(`/projects/${project}/locations/${location}/customJobs`, {
        params: { pageSize },
      });

      return response.data.customJobs || [];
    } catch (error) {
      this.logger.error('Failed to list Google fine-tune jobs', error);
      throw error;
    }
  }

  /**
   * Hủy fine-tune job
   */
  async cancelFineTuneJob(jobId: string, apiKey: string): Promise<boolean> {
    try {
      const httpClient = this.createHttpClient(apiKey);
      
      const jobPath = jobId.startsWith('projects/') ? jobId : this.constructJobPath(jobId);
      
      await httpClient.post(`/${jobPath}:cancel`);
      
      this.logger.log(`Successfully cancelled Google fine-tune job: ${jobId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to cancel Google fine-tune job: ${jobId}`, error);
      return false;
    }
  }

  /**
   * Lấy logs của fine-tune job
   */
  async getFineTuneJobLogs(jobId: string, apiKey: string): Promise<any[]> {
    try {
      const httpClient = this.createHttpClient(apiKey);
      
      const jobPath = jobId.startsWith('projects/') ? jobId : this.constructJobPath(jobId);
      
      // Google Vertex AI có thể có endpoint khác để lấy logs
      // Cần check documentation để implement chính xác
      const response = await httpClient.get(`/${jobPath}/logs`);
      
      return response.data.logs || [];
    } catch (error) {
      this.logger.error(`Failed to get Google fine-tune job logs: ${jobId}`, error);
      return [];
    }
  }
}
