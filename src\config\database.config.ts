import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { env } from './env';

// <PERSON><PERSON><PERSON> hình kết nối cơ sở dữ liệu sử dụng TypeORM
export const databaseConfig: TypeOrmModuleOptions = {
  type: 'postgres',
  host: env.database.DB_HOST,
  port: Number(env.database.DB_PORT),
  username: env.database.DB_USERNAME,
  password: env.database.DB_PASSWORD,
  database: env.database.DB_DATABASE,
  ssl: env.database.DB_SSL ? { rejectUnauthorized: false } : false,
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  migrations: [__dirname + '/../modules/database/migrations/*{.ts,.js}'],
  synchronize: false, // Tắt synchronize để tránh lỗi quyền trên production database
  logging: env.misc.NODE_ENV !== 'production',
  autoLoadEntities: true,
  migrationsRun: env.misc.NODE_ENV === 'production', // Tự động chạy migrations trong môi trường production
};
