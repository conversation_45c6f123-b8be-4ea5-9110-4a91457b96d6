import { Injectable, Logger } from '@nestjs/common';
import { BaseNodeExecutor } from '../../base/base-node.executor';
import { 
  ExecutorContext, 
  ValidationResult,
} from '../../base/node-executor.interface';
import { DetailedNodeExecutionResult, NodeExecutionConfig } from '../../../types';
import { NodeGroupEnum } from '../../../../../enums/node-group.enum';
import { ENodeType } from '../../../../../interfaces/node-manager.interface';
import {
  IIfConditionParameters,
  IIfConditionInput,
  IIfConditionOutput,
  validateIfConditionParameters
} from '../../../../../interfaces';
import { ConditionEvaluatorService } from '../../shared/condition-evaluator.service';
import { ValidationUtils } from '../../shared/validation.utils';

/**
 * Executor for IF_CONDITION node type
 * Handles conditional logic with complex condition evaluation
 */
@Injectable()
export class IfConditionExecutor extends BaseNodeExecutor {
  readonly nodeGroup = NodeGroupEnum.LOGIC;
  readonly supportedNodeTypes = [ENodeType.IF_CONDITION];
  readonly executorName = 'IfConditionExecutor';
  readonly version = '1.0.0';

  constructor(
    private readonly conditionEvaluatorService: ConditionEvaluatorService
  ) {
    super();
  }

  /**
   * Execute IF condition node
   */
  protected async executeNode(
    context: ExecutorContext,
    config: NodeExecutionConfig
  ): Promise<DetailedNodeExecutionResult> {
    const startTime = Date.now();

    // Parse and validate parameters
    const params = context.node.parameters as IIfConditionParameters;
    const input = context.inputData as IIfConditionInput;

    try {
      
      this.logger.debug(`Executing IF condition with ${params.condition_groups.length} condition groups`);
      
      // Evaluate conditions using shared service
      const evaluationResult = await this.conditionEvaluatorService.evaluateConditionGroups(
        params.condition_groups,
        params.groups_logical_operator,
        input.data
      );
      
      const executionTime = Date.now() - startTime;
      
      const output: IIfConditionOutput = {
        result: evaluationResult.finalResult,
        data: input.data,
        evaluation_details: {
          condition_groups: evaluationResult.details || [],
          final_result: evaluationResult.finalResult
        },
        metadata: {
          evaluation_time: evaluationResult.evaluationTime,
          timestamp: Date.now()
        }
      };
      
      const totalConditions = params.condition_groups.reduce((sum, group) => sum + group.conditions.length, 0);
      const branchTaken = evaluationResult.finalResult ? 'true_path' : 'false_path';

      return {
        nodeType: 'IF_CONDITION',
        success: true,
        outputData: output,
        metadata: {
          executionTime,
          conditionsEvaluated: totalConditions,
          evaluationTime: evaluationResult.evaluationTime,
          branchTaken,
          totalConditionGroups: params.condition_groups.length,
          conditionResult: evaluationResult.finalResult,
          customMetrics: {
            conditionResult: evaluationResult.finalResult,
            totalConditions,
            evaluationTime: evaluationResult.evaluationTime,
            groupsEvaluated: params.condition_groups.length,
          },
          logs: [
            `IF condition evaluation: ${evaluationResult.finalResult}`,
            `Total conditions: ${totalConditions}`,
            `Evaluation time: ${evaluationResult.evaluationTime}ms`,
            `Next path: ${branchTaken}`,
            `Execution time: ${executionTime}ms`,
          ],
        },
      };
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        nodeType: 'IF_CONDITION',
        success: false,
        error,
        metadata: {
          executionTime,
          conditionsEvaluated: 0,
          evaluationTime: 0,
          branchTaken: 'false_path',
          totalConditionGroups: params.condition_groups?.length || 0,
          conditionResult: false,
          logs: [
            `IF condition evaluation failed: ${error.message}`,
            `Execution time: ${executionTime}ms`,
          ],
        },
      };
    }
  }

  /**
   * Validate IF condition node input
   */
  protected async validateNodeSpecificInput(
    context: ExecutorContext,
    result: ValidationResult
  ): Promise<void> {
    const params = context.node.parameters as IIfConditionParameters;
    
    // Use existing validation function from interface
    const interfaceValidation = validateIfConditionParameters(params);
    if (!interfaceValidation.isValid) {
      for (const error of interfaceValidation.errors) {
        ValidationUtils.addError(
          result,
          'INTERFACE_VALIDATION_ERROR',
          error,
          'parameters'
        );
      }
    }

    // Additional custom validations
    this.validateIfConditionSpecific(params, result);
  }

  /**
   * IF condition specific validations
   */
  private validateIfConditionSpecific(
    params: IIfConditionParameters,
    result: ValidationResult
  ): void {
    // Validate condition groups
    ValidationUtils.validateArray(
      result,
      params.condition_groups,
      'Condition groups',
      1,
      10,
      'condition_groups'
    );

    // Validate each condition group
    for (let i = 0; i < params.condition_groups.length; i++) {
      const group = params.condition_groups[i];
      
      ValidationUtils.validateArray(
        result,
        group.conditions,
        `Condition group ${i + 1} conditions`,
        1,
        20,
        `condition_groups[${i}].conditions`
      );
      
      // Validate each condition in the group
      for (let j = 0; j < group.conditions.length; j++) {
        const condition = group.conditions[j];
        
        ValidationUtils.validateFieldPath(
          result,
          condition.field,
          `Condition ${i + 1}.${j + 1} field`,
          `condition_groups[${i}].conditions[${j}].field`
        );
        
        ValidationUtils.validateRequired(
          result,
          condition.operator,
          `Condition ${i + 1}.${j + 1} operator`,
          `condition_groups[${i}].conditions[${j}].operator`
        );
        
        ValidationUtils.validateRequired(
          result,
          condition.data_type,
          `Condition ${i + 1}.${j + 1} data type`,
          `condition_groups[${i}].conditions[${j}].data_type`
        );
      }
    }

    // Note: true_path và false_path không tồn tại trong IIfConditionParameters
    // Flow control được handle bởi workflow engine dựa trên condition result

    // Performance warnings
    const totalConditions = params.condition_groups.reduce((sum, group) => sum + group.conditions.length, 0);
    if (totalConditions > 50) {
      ValidationUtils.addWarning(
        result,
        'HIGH_CONDITION_COUNT',
        `High number of conditions (${totalConditions}) may impact performance`,
        'condition_groups',
        'Consider simplifying condition logic'
      );
    }

    if (params.condition_groups.length > 5) {
      ValidationUtils.addWarning(
        result,
        'HIGH_GROUP_COUNT',
        `High number of condition groups (${params.condition_groups.length}) may be complex to maintain`,
        'condition_groups',
        'Consider consolidating condition groups'
      );
    }
  }


}
