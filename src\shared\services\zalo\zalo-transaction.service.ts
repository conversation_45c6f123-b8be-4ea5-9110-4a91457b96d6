import { Injectable, Logger } from '@nestjs/common';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloService } from './zalo.service';
import { ZaloTemplateConfigService } from './zalo-template-config.service';

/**
 * Service xử lý các API tin nhắn giao dịch của Zalo Official Account
 *
 * ĐIỀU KIỆN GỬI TIN GIAO DỊCH:
 *
 * 1. THỜI GIAN GỬI:
 *    - Chỉ được gửi trong vòng 24 giờ kể từ khi người dùng tương tác cuối cùng với OA
 *    - Tương tác bao gồm: g<PERSON><PERSON> tin nhắn, nhấn button, gọi đi<PERSON>n, truy cập website từ OA
 *
 * 2. NỘI DUNG TIN NHẮN:
 *    - Phải liên quan trực tiếp đến giao dịch thực tế
 *    - Bao gồm: xác nhận đơn hàng, thông báo thanh toán, cập nhật trạng thái giao hàng
 *    - <PERSON>h<PERSON>ng được chứa nội dung quảng cáo, khuyến mãi
 *
 * 3. TẦN SUẤT GỬI:
 *    - Tối đa 3 tin nhắn giao dịch/ngày cho mỗi người dùng
 *    - Phải có khoảng cách ít nhất 1 giờ giữa các tin nhắn
 *
 * 4. ĐỊNH DẠNG:
 *    - Phải sử dụng template được Zalo phê duyệt trước
 *    - Template phải tuân thủ format chuẩn của tin giao dịch
 *
 * 5. NGƯỜI DÙNG:
 *    - Người dùng phải đã follow OA
 *    - Người dùng không được block OA
 *    - Người dùng phải có tương tác gần đây với OA
 *
 * 6. OFFICIAL ACCOUNT:
 *    - OA phải được xác minh (verified)
 *    - OA phải có quyền gửi tin giao dịch được Zalo cấp phép
 *    - OA không được vi phạm chính sách của Zalo
 *
 * LỖI THƯỜNG GẶP:
 * - 1004: Người dùng chưa follow OA hoặc đã unfollow
 * - 1005: Vượt quá thời gian 24 giờ từ lần tương tác cuối
 * - 1006: Vượt quá giới hạn 3 tin/ngày
 * - 1007: Template chưa được phê duyệt hoặc không hợp lệ
 * - 1008: Nội dung tin nhắn vi phạm chính sách
 */
@Injectable()
export class ZaloTransactionService {
  private readonly logger = new Logger(ZaloTransactionService.name);
  private readonly transactionApiUrl =
    'https://openapi.zalo.me/v2.0/oa/message/transaction';

  constructor(
    private readonly zaloService: ZaloService,
    private readonly templateConfigService: ZaloTemplateConfigService,
  ) {}

  /**
   * Gửi tin nhắn giao dịch sử dụng template
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param templateId ID của template giao dịch đã được phê duyệt
   * @param templateData Dữ liệu để điền vào template
   * @param mode Chế độ gửi tin nhắn (development/production)
   * @returns ID của tin nhắn
   */
  async sendTransactionMessage(
    accessToken: string,
    userId: string,
    templateId: string,
    templateData: Record<string, string>,
    mode: 'development' | 'production' = 'production',
  ): Promise<{ message_id: string }> {
    try {
      // Validate template data
      this.validateTemplateData(templateData);

      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'template',
            payload: {
              template_type: 'transaction',
              template_id: templateId,
              template_data: templateData,
              mode: mode,
            },
          },
        },
      };

      this.logger.debug(
        `Sending transaction message to user ${userId} with template ${templateId}`,
      );
      this.logger.debug(
        `Template data:`,
        JSON.stringify(templateData, null, 2),
      );

      const result = await this.zaloService.post<{ message_id: string }>(
        this.transactionApiUrl,
        accessToken,
        data,
      );

      this.logger.log(
        `Transaction message sent successfully. Message ID: ${result.message_id}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Error sending transaction message: ${error.message}`,
        error.stack,
      );

      // Handle specific Zalo API errors
      if (error.response?.data?.error) {
        const zaloError = error.response.data.error;
        switch (zaloError) {
          case 1004:
            throw new AppException(
              ErrorCode.VALIDATION_ERROR,
              'Người dùng chưa follow Official Account hoặc đã unfollow',
            );
          case 1005:
            throw new AppException(
              ErrorCode.VALIDATION_ERROR,
              'Vượt quá thời gian 24 giờ từ lần tương tác cuối cùng',
            );
          case 1006:
            throw new AppException(
              ErrorCode.VALIDATION_ERROR,
              'Vượt quá giới hạn 3 tin nhắn giao dịch/ngày',
            );
          case 1007:
            throw new AppException(
              ErrorCode.VALIDATION_ERROR,
              'Template chưa được phê duyệt hoặc không hợp lệ',
            );
          case 1008:
            throw new AppException(
              ErrorCode.VALIDATION_ERROR,
              'Nội dung tin nhắn vi phạm chính sách Zalo',
            );
          default:
            throw new AppException(
              ErrorCode.EXTERNAL_SERVICE_ERROR,
              `Lỗi từ Zalo API: ${error.response.data.message || 'Unknown error'}`,
            );
        }
      }

      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn giao dịch',
      );
    }
  }

  /**
   * Gửi tin nhắn xác nhận đơn hàng
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param orderData Thông tin đơn hàng
   * @returns ID của tin nhắn
   */
  async sendOrderConfirmation(
    accessToken: string,
    userId: string,
    orderData: {
      orderId: string;
      orderDate: string;
      customerName: string;
      totalAmount: string;
      paymentMethod: string;
      deliveryAddress?: string;
      estimatedDelivery?: string;
    },
  ): Promise<{ message_id: string }> {
    try {
      // Template ID cho xác nhận đơn hàng (lấy từ database)
      const templateId =
        await this.templateConfigService.getOrderConfirmationTemplateId();

      if (!templateId) {
        throw new AppException(
          ErrorCode.CONFIGURATION_ERROR,
          'Template ID cho xác nhận đơn hàng chưa được cấu hình',
        );
      }

      const templateData = {
        order_id: orderData.orderId,
        order_date: orderData.orderDate,
        customer_name: orderData.customerName,
        total_amount: orderData.totalAmount,
        payment_method: orderData.paymentMethod,
        ...(orderData.deliveryAddress && {
          delivery_address: orderData.deliveryAddress,
        }),
        ...(orderData.estimatedDelivery && {
          estimated_delivery: orderData.estimatedDelivery,
        }),
      };

      return await this.sendTransactionMessage(
        accessToken,
        userId,
        templateId,
        templateData,
      );
    } catch (error) {
      this.logger.error(
        `Error sending order confirmation: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn xác nhận đơn hàng',
      );
    }
  }

  /**
   * Gửi tin nhắn cập nhật trạng thái giao hàng
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param deliveryData Thông tin giao hàng
   * @returns ID của tin nhắn
   */
  async sendDeliveryUpdate(
    accessToken: string,
    userId: string,
    deliveryData: {
      orderId: string;
      status: string;
      trackingNumber?: string;
      estimatedDelivery?: string;
      deliveryNote?: string;
    },
  ): Promise<{ message_id: string }> {
    try {
      // Template ID cho cập nhật giao hàng (lấy từ database)
      const templateId =
        await this.templateConfigService.getDeliveryUpdateTemplateId();

      if (!templateId) {
        throw new AppException(
          ErrorCode.CONFIGURATION_ERROR,
          'Template ID cho cập nhật giao hàng chưa được cấu hình',
        );
      }

      const templateData = {
        order_id: deliveryData.orderId,
        status: deliveryData.status,
        ...(deliveryData.trackingNumber && {
          tracking_number: deliveryData.trackingNumber,
        }),
        ...(deliveryData.estimatedDelivery && {
          estimated_delivery: deliveryData.estimatedDelivery,
        }),
        ...(deliveryData.deliveryNote && {
          delivery_note: deliveryData.deliveryNote,
        }),
      };

      return await this.sendTransactionMessage(
        accessToken,
        userId,
        templateId,
        templateData,
      );
    } catch (error) {
      this.logger.error(
        `Error sending delivery update: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn cập nhật giao hàng',
      );
    }
  }

  /**
   * Gửi tin nhắn thông báo thanh toán
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param paymentData Thông tin thanh toán
   * @returns ID của tin nhắn
   */
  async sendPaymentNotification(
    accessToken: string,
    userId: string,
    paymentData: {
      orderId: string;
      amount: string;
      paymentMethod: string;
      transactionId?: string;
      paymentDate: string;
      status: 'success' | 'failed' | 'pending';
    },
  ): Promise<{ message_id: string }> {
    try {
      // Template ID cho thông báo thanh toán (lấy từ database)
      const templateId =
        await this.templateConfigService.getPaymentNotificationTemplateId();

      if (!templateId) {
        throw new AppException(
          ErrorCode.CONFIGURATION_ERROR,
          'Template ID cho thông báo thanh toán chưa được cấu hình',
        );
      }

      const templateData = {
        order_id: paymentData.orderId,
        amount: paymentData.amount,
        payment_method: paymentData.paymentMethod,
        payment_date: paymentData.paymentDate,
        status: paymentData.status,
        ...(paymentData.transactionId && {
          transaction_id: paymentData.transactionId,
        }),
      };

      return await this.sendTransactionMessage(
        accessToken,
        userId,
        templateId,
        templateData,
      );
    } catch (error) {
      this.logger.error(
        `Error sending payment notification: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn thông báo thanh toán',
      );
    }
  }

  /**
   * Validate template data để đảm bảo tuân thủ quy định
   * @param templateData Dữ liệu template cần validate
   */
  private validateTemplateData(templateData: Record<string, string>): void {
    // Kiểm tra các trường bắt buộc
    if (!templateData || Object.keys(templateData).length === 0) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Dữ liệu template không được để trống',
      );
    }

    // Kiểm tra độ dài của các giá trị
    for (const [key, value] of Object.entries(templateData)) {
      if (typeof value !== 'string') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Giá trị của trường ${key} phải là chuỗi`,
        );
      }

      if (value.length > 500) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Giá trị của trường ${key} không được vượt quá 500 ký tự`,
        );
      }
    }

    // Kiểm tra nội dung không chứa từ khóa quảng cáo
    const advertisingKeywords = [
      'khuyến mãi',
      'giảm giá',
      'sale',
      'promotion',
      'discount',
      'miễn phí',
      'free',
      'quà tặng',
      'gift',
      'voucher',
    ];

    const allText = Object.values(templateData).join(' ').toLowerCase();
    const hasAdvertising = advertisingKeywords.some((keyword) =>
      allText.includes(keyword.toLowerCase()),
    );

    if (hasAdvertising) {
      this.logger.warn(
        'Template data contains advertising keywords, this may cause rejection',
      );
    }
  }
}
