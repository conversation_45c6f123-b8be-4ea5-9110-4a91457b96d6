import { Injectable, Logger } from '@nestjs/common';
import { BaseNodeExecutor } from '../base/base-node.executor';
import { 
  ExecutorContext, 
  ValidationResult,
  DetailedNodeExecutionResult 
} from '../base/node-executor.interface';
import { NodeExecutionConfig } from '../../types';
import { NodeGroupEnum } from '../../../../enums/node-group.enum';

/**
 * Logic operation types
 */
type LogicOperationType = 
  | 'if-else'
  | 'switch'
  | 'loop'
  | 'while'
  | 'for-each'
  | 'filter'
  | 'map'
  | 'reduce'
  | 'find'
  | 'some'
  | 'every'
  | 'compare'
  | 'boolean-logic'
  | 'conditional';

/**
 * Condition configuration
 */
interface ConditionConfig {
  /** Left operand */
  left: any;
  
  /** Comparison operator */
  operator: '==' | '!=' | '>' | '<' | '>=' | '<=' | 'contains' | 'startsWith' | 'endsWith' | 'regex' | 'in' | 'notIn';
  
  /** Right operand */
  right: any;
  
  /** Case sensitive for string operations */
  caseSensitive?: boolean;
}

/**
 * Logic node configuration
 */
interface LogicNodeConfig {
  /** Type of logic operation */
  operation: LogicOperationType;
  
  /** Conditions for if-else, switch, etc. */
  conditions?: ConditionConfig[];
  
  /** Boolean logic expression */
  expression?: string;
  
  /** Switch cases */
  cases?: Array<{
    value: any;
    output: any;
    conditions?: ConditionConfig[];
  }>;
  
  /** Default case for switch */
  defaultCase?: {
    output: any;
  };
  
  /** Loop configuration */
  loop?: {
    /** Array to iterate over */
    array: any[];
    
    /** Variable name for current item */
    itemVariable?: string;
    
    /** Variable name for index */
    indexVariable?: string;
    
    /** Max iterations (safety limit) */
    maxIterations?: number;
  };
  
  /** While loop configuration */
  while?: {
    /** Condition to check */
    condition: ConditionConfig;
    
    /** Max iterations (safety limit) */
    maxIterations?: number;
    
    /** Variable to increment */
    incrementVariable?: string;
    
    /** Increment step */
    incrementStep?: number;
  };
  
  /** Output configuration */
  output?: {
    /** True case output */
    onTrue?: any;
    
    /** False case output */
    onFalse?: any;
    
    /** Custom output mapping */
    mapping?: Record<string, any>;
  };
  
  /** Execution options */
  options?: {
    /** Strict mode (throw on errors) */
    strictMode?: boolean;
    
    /** Allow undefined values */
    allowUndefined?: boolean;
    
    /** Stop on first error */
    stopOnError?: boolean;
    
    /** Timeout for complex operations */
    timeout?: number;
  };
}

/**
 * Logic execution result
 */
interface LogicExecutionResult {
  /** Result value */
  result: any;
  
  /** Condition evaluation results */
  conditionResults?: Array<{
    condition: ConditionConfig;
    result: boolean;
    leftValue: any;
    rightValue: any;
  }>;
  
  /** Loop execution details */
  loopDetails?: {
    iterations: number;
    results: any[];
    stopped: boolean;
    stopReason?: string;
  };
  
  /** Switch execution details */
  switchDetails?: {
    matchedCase?: any;
    usedDefault: boolean;
  };
  
  /** Execution metadata */
  metadata: {
    operationType: LogicOperationType;
    executionTime: number;
    evaluatedConditions: number;
    memoryUsage?: number;
  };
}

/**
 * Executor cho Logic nodes - handle if/else, loops, switches, boolean operations
 */
@Injectable()
export class LogicNodeExecutor extends BaseNodeExecutor {
  readonly nodeGroup = NodeGroupEnum.LOGIC;
  readonly supportedNodeTypes = [
    'if-else',
    'switch',
    'loop',
    'while-loop',
    'for-each',
    'filter',
    'map',
    'reduce',
    'find',
    'conditional',
    'boolean-logic',
    'compare',
    'decision',
    'branch',
  ];
  readonly executorName = 'LogicNodeExecutor';
  readonly version = '1.0.0';
  
  /**
   * Execute logic node
   */
  protected async executeNode(
    context: ExecutorContext,
    config: NodeExecutionConfig
  ): Promise<DetailedNodeExecutionResult> {
    const startTime = Date.now();
    
    try {
      // Parse logic configuration
      const logicConfig = this.parseLogicConfig(context);
      
      // Execute logic operation
      const result = await this.executeLogicOperation(logicConfig, context);
      
      const executionTime = Date.now() - startTime;
      
      return {
        success: true,
        outputData: {
          result: result.result,
          conditionResults: result.conditionResults,
          loopDetails: result.loopDetails,
          switchDetails: result.switchDetails,
          metadata: result.metadata,
        },
        metadata: {
          executionTime,
          customMetrics: {
            operationType: result.metadata.operationType,
            evaluatedConditions: result.metadata.evaluatedConditions,
            iterations: result.loopDetails?.iterations || 0,
          },
          logs: [
            `Logic operation: ${result.metadata.operationType}`,
            `Evaluated conditions: ${result.metadata.evaluatedConditions}`,
            `Execution time: ${executionTime}ms`,
          ],
        },
      };
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        success: false,
        error,
        metadata: {
          executionTime,
          logs: [
            `Logic operation failed: ${error.message}`,
            `Execution time: ${executionTime}ms`,
          ],
        },
      };
    }
  }
  
  /**
   * Validate logic node input
   */
  protected async validateNodeSpecificInput(
    context: ExecutorContext,
    result: ValidationResult
  ): Promise<void> {
    const params = context.node.parameters as any;
    
    // Validate operation type
    if (!params.operation) {
      result.errors.push({
        code: 'MISSING_OPERATION',
        message: 'Logic node requires an operation type',
        field: 'operation',
        severity: 'error',
      });
    } else if (!this.supportedNodeTypes.includes(params.operation)) {
      result.errors.push({
        code: 'INVALID_OPERATION',
        message: 'Invalid logic operation type',
        field: 'operation',
        currentValue: params.operation,
        expectedValue: this.supportedNodeTypes,
        severity: 'error',
      });
    }
    
    // Validate conditions
    if (params.conditions && Array.isArray(params.conditions)) {
      for (let i = 0; i < params.conditions.length; i++) {
        const condition = params.conditions[i];
        
        if (!condition.operator) {
          result.errors.push({
            code: 'MISSING_OPERATOR',
            message: `Condition ${i} missing operator`,
            field: `conditions[${i}].operator`,
            severity: 'error',
          });
        }
        
        const validOperators = ['==', '!=', '>', '<', '>=', '<=', 'contains', 'startsWith', 'endsWith', 'regex', 'in', 'notIn'];
        if (condition.operator && !validOperators.includes(condition.operator)) {
          result.errors.push({
            code: 'INVALID_OPERATOR',
            message: `Invalid operator in condition ${i}`,
            field: `conditions[${i}].operator`,
            currentValue: condition.operator,
            expectedValue: validOperators,
            severity: 'error',
          });
        }
      }
    }
    
    // Validate loop configuration
    if (params.loop) {
      if (!params.loop.array && !Array.isArray(params.loop.array)) {
        result.errors.push({
          code: 'INVALID_LOOP_ARRAY',
          message: 'Loop requires a valid array',
          field: 'loop.array',
          severity: 'error',
        });
      }
      
      if (params.loop.maxIterations && params.loop.maxIterations > 10000) {
        result.warnings.push({
          code: 'HIGH_ITERATION_COUNT',
          message: 'High iteration count may cause performance issues',
          field: 'loop.maxIterations',
          suggestion: 'Consider reducing max iterations or using pagination',
        });
      }
    }
    
    // Validate while loop
    if (params.while) {
      if (!params.while.condition) {
        result.errors.push({
          code: 'MISSING_WHILE_CONDITION',
          message: 'While loop requires a condition',
          field: 'while.condition',
          severity: 'error',
        });
      }
      
      if (!params.while.maxIterations) {
        result.warnings.push({
          code: 'NO_MAX_ITERATIONS',
          message: 'While loop without max iterations may run indefinitely',
          field: 'while.maxIterations',
          suggestion: 'Set maxIterations to prevent infinite loops',
        });
      }
    }
  }
  
  // Private helper methods
  
  private parseLogicConfig(context: ExecutorContext): LogicNodeConfig {
    const params = context.node.parameters as any;
    
    return {
      operation: params.operation,
      conditions: params.conditions,
      expression: params.expression,
      cases: params.cases,
      defaultCase: params.defaultCase,
      loop: params.loop,
      while: params.while,
      output: params.output,
      options: {
        strictMode: params.options?.strictMode || false,
        allowUndefined: params.options?.allowUndefined || true,
        stopOnError: params.options?.stopOnError || false,
        timeout: params.options?.timeout || 30000,
      },
    };
  }
  
  private async executeLogicOperation(
    config: LogicNodeConfig,
    context: ExecutorContext
  ): Promise<LogicExecutionResult> {
    const startTime = Date.now();
    let evaluatedConditions = 0;
    
    switch (config.operation) {
      case 'if-else':
      case 'conditional':
        return this.executeIfElse(config, context);
        
      case 'switch':
        return this.executeSwitch(config, context);
        
      case 'loop':
      case 'for-each':
        return this.executeLoop(config, context);
        
      case 'while':
        return this.executeWhileLoop(config, context);
        
      case 'filter':
        return this.executeFilter(config, context);
        
      case 'map':
        return this.executeMap(config, context);
        
      case 'compare':
        return this.executeCompare(config, context);
        
      case 'boolean-logic':
        return this.executeBooleanLogic(config, context);
        
      default:
        throw new Error(`Unsupported logic operation: ${config.operation}`);
    }
  }
  
  private async executeIfElse(
    config: LogicNodeConfig,
    context: ExecutorContext
  ): Promise<LogicExecutionResult> {
    const startTime = Date.now();
    
    if (!config.conditions || config.conditions.length === 0) {
      throw new Error('If-else operation requires conditions');
    }
    
    const conditionResults: LogicExecutionResult['conditionResults'] = [];
    let finalResult = false;
    
    // Evaluate all conditions (AND logic by default)
    for (const condition of config.conditions) {
      const result = await this.evaluateCondition(condition, context);
      conditionResults.push(result);
      
      if (!result.result) {
        finalResult = false;
        break;
      }
      finalResult = true;
    }
    
    // Determine output
    const output = finalResult 
      ? config.output?.onTrue 
      : config.output?.onFalse;
    
    return {
      result: output !== undefined ? output : finalResult,
      conditionResults,
      metadata: {
        operationType: config.operation,
        executionTime: Date.now() - startTime,
        evaluatedConditions: conditionResults.length,
      },
    };
  }
  
  private async executeSwitch(
    config: LogicNodeConfig,
    context: ExecutorContext
  ): Promise<LogicExecutionResult> {
    const startTime = Date.now();
    
    if (!config.cases || config.cases.length === 0) {
      throw new Error('Switch operation requires cases');
    }
    
    const inputValue = context.inputData;
    let matchedCase: any = null;
    let usedDefault = false;
    
    // Find matching case
    for (const caseConfig of config.cases) {
      if (caseConfig.conditions) {
        // Evaluate conditions for this case
        let conditionsMet = true;
        for (const condition of caseConfig.conditions) {
          const result = await this.evaluateCondition(condition, context);
          if (!result.result) {
            conditionsMet = false;
            break;
          }
        }
        
        if (conditionsMet) {
          matchedCase = caseConfig;
          break;
        }
      } else {
        // Simple value comparison
        if (this.compareValues(inputValue, caseConfig.value, '==')) {
          matchedCase = caseConfig;
          break;
        }
      }
    }
    
    // Use default case if no match
    if (!matchedCase && config.defaultCase) {
      matchedCase = config.defaultCase;
      usedDefault = true;
    }
    
    const result = matchedCase ? matchedCase.output : null;
    
    return {
      result,
      switchDetails: {
        matchedCase: matchedCase?.value,
        usedDefault,
      },
      metadata: {
        operationType: config.operation,
        executionTime: Date.now() - startTime,
        evaluatedConditions: config.cases.length,
      },
    };
  }
  
  private async executeLoop(
    config: LogicNodeConfig,
    context: ExecutorContext
  ): Promise<LogicExecutionResult> {
    const startTime = Date.now();
    
    if (!config.loop?.array) {
      throw new Error('Loop operation requires an array');
    }
    
    const array = config.loop.array;
    const maxIterations = config.loop.maxIterations || array.length;
    const results: any[] = [];
    let iterations = 0;
    let stopped = false;
    let stopReason: string | undefined;
    
    for (let i = 0; i < Math.min(array.length, maxIterations); i++) {
      iterations++;
      const item = array[i];
      
      // Create execution context for this iteration
      const iterationContext = {
        ...context,
        inputData: {
          ...context.inputData,
          [config.loop.itemVariable || 'item']: item,
          [config.loop.indexVariable || 'index']: i,
        },
      };
      
      // Process item (this would typically involve calling another node or operation)
      const itemResult = item; // Simplified - in real implementation, this would process the item
      results.push(itemResult);
      
      // Check timeout
      if (Date.now() - startTime > (config.options?.timeout || 30000)) {
        stopped = true;
        stopReason = 'timeout';
        break;
      }
    }
    
    return {
      result: results,
      loopDetails: {
        iterations,
        results,
        stopped,
        stopReason,
      },
      metadata: {
        operationType: config.operation,
        executionTime: Date.now() - startTime,
        evaluatedConditions: 0,
      },
    };
  }
  
  private async executeWhileLoop(
    config: LogicNodeConfig,
    context: ExecutorContext
  ): Promise<LogicExecutionResult> {
    const startTime = Date.now();
    
    if (!config.while?.condition) {
      throw new Error('While loop requires a condition');
    }
    
    const maxIterations = config.while.maxIterations || 1000;
    const results: any[] = [];
    let iterations = 0;
    let stopped = false;
    let stopReason: string | undefined;
    
    let currentContext = { ...context };
    
    while (iterations < maxIterations) {
      // Evaluate condition
      const conditionResult = await this.evaluateCondition(config.while.condition, currentContext);
      
      if (!conditionResult.result) {
        break;
      }
      
      iterations++;
      
      // Execute iteration (simplified)
      const iterationResult = currentContext.inputData;
      results.push(iterationResult);
      
      // Update context for next iteration
      if (config.while.incrementVariable && config.while.incrementStep) {
        const currentValue = currentContext.inputData[config.while.incrementVariable] || 0;
        currentContext.inputData[config.while.incrementVariable] = currentValue + config.while.incrementStep;
      }
      
      // Check timeout
      if (Date.now() - startTime > (config.options?.timeout || 30000)) {
        stopped = true;
        stopReason = 'timeout';
        break;
      }
    }
    
    if (iterations >= maxIterations) {
      stopped = true;
      stopReason = 'max_iterations';
    }
    
    return {
      result: results,
      loopDetails: {
        iterations,
        results,
        stopped,
        stopReason,
      },
      metadata: {
        operationType: config.operation,
        executionTime: Date.now() - startTime,
        evaluatedConditions: iterations,
      },
    };
  }
  
  private async executeFilter(
    config: LogicNodeConfig,
    context: ExecutorContext
  ): Promise<LogicExecutionResult> {
    const startTime = Date.now();
    
    if (!config.loop?.array) {
      throw new Error('Filter operation requires an array');
    }
    
    if (!config.conditions || config.conditions.length === 0) {
      throw new Error('Filter operation requires conditions');
    }
    
    const array = config.loop.array;
    const filteredResults: any[] = [];
    let evaluatedConditions = 0;
    
    for (const item of array) {
      const itemContext = {
        ...context,
        inputData: item,
      };
      
      let passesFilter = true;
      
      for (const condition of config.conditions) {
        const result = await this.evaluateCondition(condition, itemContext);
        evaluatedConditions++;
        
        if (!result.result) {
          passesFilter = false;
          break;
        }
      }
      
      if (passesFilter) {
        filteredResults.push(item);
      }
    }
    
    return {
      result: filteredResults,
      metadata: {
        operationType: config.operation,
        executionTime: Date.now() - startTime,
        evaluatedConditions,
      },
    };
  }
  
  private async executeMap(
    config: LogicNodeConfig,
    context: ExecutorContext
  ): Promise<LogicExecutionResult> {
    const startTime = Date.now();
    
    if (!config.loop?.array) {
      throw new Error('Map operation requires an array');
    }
    
    const array = config.loop.array;
    const mappedResults: any[] = [];
    
    for (let i = 0; i < array.length; i++) {
      const item = array[i];
      
      // Apply mapping (simplified - in real implementation, this would use a mapping function)
      const mappedItem = config.output?.mapping 
        ? this.applyMapping(item, config.output.mapping)
        : item;
      
      mappedResults.push(mappedItem);
    }
    
    return {
      result: mappedResults,
      metadata: {
        operationType: config.operation,
        executionTime: Date.now() - startTime,
        evaluatedConditions: 0,
      },
    };
  }
  
  private async executeCompare(
    config: LogicNodeConfig,
    context: ExecutorContext
  ): Promise<LogicExecutionResult> {
    const startTime = Date.now();
    
    if (!config.conditions || config.conditions.length === 0) {
      throw new Error('Compare operation requires conditions');
    }
    
    const condition = config.conditions[0];
    const result = await this.evaluateCondition(condition, context);
    
    return {
      result: result.result,
      conditionResults: [result],
      metadata: {
        operationType: config.operation,
        executionTime: Date.now() - startTime,
        evaluatedConditions: 1,
      },
    };
  }
  
  private async executeBooleanLogic(
    config: LogicNodeConfig,
    context: ExecutorContext
  ): Promise<LogicExecutionResult> {
    const startTime = Date.now();
    
    if (!config.expression) {
      throw new Error('Boolean logic operation requires an expression');
    }
    
    // Simple boolean expression evaluation (in real implementation, use a proper expression parser)
    const result = this.evaluateBooleanExpression(config.expression, context);
    
    return {
      result,
      metadata: {
        operationType: config.operation,
        executionTime: Date.now() - startTime,
        evaluatedConditions: 1,
      },
    };
  }
  
  private async evaluateCondition(
    condition: ConditionConfig,
    context: ExecutorContext
  ): Promise<{
    condition: ConditionConfig;
    result: boolean;
    leftValue: any;
    rightValue: any;
  }> {
    const leftValue = this.resolveValue(condition.left, context);
    const rightValue = this.resolveValue(condition.right, context);
    
    const result = this.compareValues(leftValue, rightValue, condition.operator, condition.caseSensitive);
    
    return {
      condition,
      result,
      leftValue,
      rightValue,
    };
  }
  
  private resolveValue(value: any, context: ExecutorContext): any {
    if (typeof value === 'string' && value.startsWith('$')) {
      // Variable reference
      const varName = value.substring(1);
      return context.inputData[varName];
    }
    
    return value;
  }
  
  private compareValues(
    left: any,
    right: any,
    operator: ConditionConfig['operator'],
    caseSensitive: boolean = true
  ): boolean {
    switch (operator) {
      case '==':
        return left == right;
      case '!=':
        return left != right;
      case '>':
        return left > right;
      case '<':
        return left < right;
      case '>=':
        return left >= right;
      case '<=':
        return left <= right;
      case 'contains':
        if (typeof left === 'string' && typeof right === 'string') {
          return caseSensitive 
            ? left.includes(right)
            : left.toLowerCase().includes(right.toLowerCase());
        }
        return false;
      case 'startsWith':
        if (typeof left === 'string' && typeof right === 'string') {
          return caseSensitive
            ? left.startsWith(right)
            : left.toLowerCase().startsWith(right.toLowerCase());
        }
        return false;
      case 'endsWith':
        if (typeof left === 'string' && typeof right === 'string') {
          return caseSensitive
            ? left.endsWith(right)
            : left.toLowerCase().endsWith(right.toLowerCase());
        }
        return false;
      case 'regex':
        if (typeof left === 'string' && typeof right === 'string') {
          const flags = caseSensitive ? 'g' : 'gi';
          const regex = new RegExp(right, flags);
          return regex.test(left);
        }
        return false;
      case 'in':
        return Array.isArray(right) ? right.includes(left) : false;
      case 'notIn':
        return Array.isArray(right) ? !right.includes(left) : true;
      default:
        return false;
    }
  }
  
  private applyMapping(item: any, mapping: Record<string, any>): any {
    const result: any = {};
    
    for (const [key, value] of Object.entries(mapping)) {
      if (typeof value === 'string' && value.startsWith('$')) {
        const fieldName = value.substring(1);
        result[key] = item[fieldName];
      } else {
        result[key] = value;
      }
    }
    
    return result;
  }
  
  private evaluateBooleanExpression(expression: string, context: ExecutorContext): boolean {
    // Simplified boolean expression evaluation
    // In real implementation, use a proper expression parser like mathjs
    
    // Replace variables
    let processedExpression = expression;
    const variablePattern = /\$(\w+)/g;
    processedExpression = processedExpression.replace(variablePattern, (match, varName) => {
      const value = context.inputData[varName];
      return JSON.stringify(value);
    });
    
    try {
      // WARNING: eval is dangerous - use a proper expression parser in production
      return Boolean(eval(processedExpression));
    } catch (error) {
      this.logger.error('Failed to evaluate boolean expression:', error);
      return false;
    }
  }
}
