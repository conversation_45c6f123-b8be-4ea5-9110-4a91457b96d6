// Re-export types from Zod schemas (single source of truth)
export type {
  GenderEnum,
  ProfileAgent,
  TrimmingType,
  SystemModelConfig,
  SystemAgentConfig,
  SystemAgentConfigMap,
} from '../schemas/agent-system.schema';

// Re-export schemas for validation
export {
  GenderEnumSchema,
  ProfileAgentSchema,
  TrimmingTypeSchema,
  SystemModelConfigSchema,
  SystemAgentConfigSchema,
  SystemAgentConfigMapSchema,
} from '../schemas/agent-system.schema';

import type {
  SystemAgentConfig,
  SystemAgentConfigMap,
  SystemModelConfig,
} from '../schemas/agent-system.schema';

/**
 * Type guard to check if an agent config is a system agent
 */
export function isSystemAgentConfig(config: any): config is SystemAgentConfig {
  return config?.model?.type === 'SYSTEM';
}

/**
 * Type guard to check if a model config is a system model
 */
export function isSystemModelConfig(model: any): model is SystemModelConfig {
  return model?.type === 'SYSTEM';
}

/**
 * Helper function to get system agent IDs from a config map
 */
export function getSystemAgentIds(configMap: SystemAgentConfigMap): string[] {
  return Object.keys(configMap);
}

/**
 * Helper function to get supervisor agent from config map
 */
export function getSupervisorAgent(
  configMap: SystemAgentConfigMap,
): SystemAgentConfig | undefined {
  return Object.values(configMap).find((config) => config.isSupervisor);
}

/**
 * Helper function to get non-supervisor agents from config map
 */
export function getWorkerAgents(
  configMap: SystemAgentConfigMap,
): SystemAgentConfig[] {
  return Object.values(configMap).filter((config) => !config.isSupervisor);
}
