import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import OpenAI from 'openai';
import { Model } from 'openai/resources/models';
import * as os from 'os';
import * as path from 'path';
import { S3Service } from '../s3';
import { CreateFineTuningJobParams, FineTuningJobResponse } from './interfaces/openai.interface';

@Injectable()
export class OpenAiService {
  private readonly openai: OpenAI;
  private readonly logger = new Logger(OpenAiService.name);
  private readonly apiKey: string;
  private readonly baseUrl: string;

  constructor(
    private readonly s3Service: S3Service,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.apiKey = this.configService.get<string>('RAG_API', '');
    this.baseUrl = this.configService.get<string>(
      'FAST_API_URL',
      'http://localhost:8000/',
    );

    if (!this.apiKey) {
      this.logger.warn('RAG API key is not configured');
    }

    if (!this.baseUrl) {
      this.logger.warn('Fast API URL is not configured');
    }
  }

  /**
   * Lấy instance của OpenAI với API key được truyền vào
   * @param apiKey API key của OpenAI
   * @returns Instance của OpenAI
   */
  private getOpenai(apiKey: string): OpenAI {
    return new OpenAI({
      apiKey: apiKey,
    });
  }

  /**
   * Tạo config cho request API với X-API-Key header
   * @returns AxiosRequestConfig
   */
  private createRequestConfig() {
    return {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey,
      },
    };
  }

  /**
   * Lấy danh sách model từ OpenAI
   * @returns Danh sách model từ OpenAI
   * @throws AppException nếu có lỗi khi lấy danh sách model
   */
  async listModels(apiKey: string): Promise<Model[]> {
    try {
      const openai = this.getOpenai(apiKey);
      // Thực hiện gọi API để lấy danh sách model
      const response = await openai.models.list();

      // Trả về toàn bộ danh sách model
      this.logger.log(`Retrieved ${response.data.length} models from OpenAI`);
      return response.data;
    } catch (error: any) {
      this.logger.error(
        `Error retrieving models from OpenAI: ${error.message}`,
        error.stack,
      );

      // Xử lý các lỗi khi kết nối OpenAI API
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng OpenAI API',
        );
      }

      if (error.name === 'TimeoutError' || error.message.includes('timeout')) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      if (error.name === 'NetworkError' || error.message.includes('network')) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến OpenAI API',
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi lấy danh sách model: ' + error.message,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết về một model từ OpenAI
   * @param modelId ID của model cần lấy thông tin
   * @param apiKey
   * @returns Thông tin chi tiết về model
   * @throws AppException nếu có lỗi khi lấy thông tin model
   */
  async retrieveModel(modelId: string, apiKey: string): Promise<Model> {
    try {
      const openai = this.getOpenai(apiKey);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000);

      const response = await openai.models.retrieve(modelId, {
        signal: controller.signal,
      });
      clearTimeout(timeoutId);

      this.logger.log(`Retrieved model information for: ${modelId}`);
      return response;
    } catch (error: any) {
      this.logger.error(
        `OpenAI API error retrieving model: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng OpenAI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      // Xử lý lỗi kết nối
      if (
        error.name === 'NetworkError' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.message.includes('network')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến OpenAI API',
        );
      }

      // Xử lý lỗi không tìm thấy model
      if (error.status === 404) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Không tìm thấy model với ID: ' + modelId,
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi lấy thông tin model: ' + error.message,
      );
    }
  }

  /**
   * Upload file dữ liệu huấn luyện lên OpenAI
   * @param fileKey S3 key của file dữ liệu huấn luyện (định dạng JSONL)
   * @param apiKey API key của OpenAI
   * @returns ID của file đã upload
   * @throws AppException nếu có lỗi khi upload file
   */
  async uploadTrainingFile(fileKey: string, apiKey: string): Promise<string> {
    try {
      const openai = this.getOpenai(apiKey);

      // Tạo thư mục tạm để lưu file
      const tempDir = fs.mkdtempSync(
        path.join(os.tmpdir(), 'openai-training-'),
      );
      const tempFilePath = path.join(tempDir, path.basename(fileKey));

      // Tải file trực tiếp từ S3 dưới dạng byte array
      const fileBytes = await this.s3Service.downloadFileAsBytes(fileKey);

      // Ghi file vào thư mục tạm
      fs.writeFileSync(tempFilePath, Buffer.from(fileBytes));

      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 seconds timeout

      // Upload file lên OpenAI với purpose là fine-tune
      const fileUpload = await openai.files.create(
        {
          file: fs.createReadStream(tempFilePath),
          purpose: 'fine-tune',
        },
        {
          signal: controller.signal,
        },
      );

      clearTimeout(timeoutId);

      // Xóa file tạm
      try {
        fs.unlinkSync(tempFilePath);
        fs.rmdirSync(tempDir);
      } catch (cleanupError) {
        this.logger.warn(
          `Failed to clean up temp files: ${cleanupError.message}`,
        );
      }

      this.logger.log(`Training file uploaded successfully: ${fileUpload.id}`);
      return fileUpload.id;
    } catch (error: any) {
      this.logger.error(
        `OpenAI API error uploading training file: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng OpenAI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      // Xử lý lỗi kết nối
      if (
        error.name === 'NetworkError' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.message.includes('network')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến OpenAI API',
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi upload file dữ liệu huấn luyện: ' + error.message,
      );
    }
  }

  /**
   * Tạo fine-tuning job trên OpenAI
   * @param params Tham số cho fine-tuning job
   * @param apiKey API key của OpenAI
   * @returns Thông tin về fine-tuning job đã tạo
   * @throws AppException nếu có lỗi khi tạo fine-tuning job
   */
  async createFineTuningJob(
    params: CreateFineTuningJobParams,
    apiKey: string,
  ): Promise<FineTuningJobResponse> {
    try {
      const openai = this.getOpenai(apiKey);

      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 seconds timeout

      // Chuẩn bị tham số cho fine-tuning job
      const fineTuningParams: any = {
        training_file: params.trainingFileId,
        model: params.model,
      };

      // Thêm các tham số tùy chọn nếu có
      if (params.suffix) {
        fineTuningParams.suffix = params.suffix;
      }

      if (params.validationFileId) {
        fineTuningParams.validation_file = params.validationFileId;
      }

      if (params.hyperparameters) {
        fineTuningParams.hyperparameters = {};

        if (params.hyperparameters.nEpochs !== undefined) {
          fineTuningParams.hyperparameters.n_epochs =
            params.hyperparameters.nEpochs;
        }

        if (params.hyperparameters.batchSize !== undefined) {
          fineTuningParams.hyperparameters.batch_size =
            params.hyperparameters.batchSize;
        }

        if (params.hyperparameters.learningRateMultiplier !== undefined) {
          fineTuningParams.hyperparameters.learning_rate_multiplier =
            params.hyperparameters.learningRateMultiplier;
        }
      }

      // Gọi API để tạo fine-tuning job
      const response = await openai.fineTuning.jobs.create(fineTuningParams, {
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      this.logger.log(`Fine-tuning job created successfully: ${response.id}`);

      // Chuyển đổi response sang định dạng FineTuningJobResponse
      return {
        id: response.id,
        object: response.object,
        createdAt: response.created_at,
        updatedAt: response.created_at, // OpenAI API không trả về updated_at, sử dụng created_at thay thế
        model: response.model,
        fineTunedModel: response.fine_tuned_model,
        organizationId: response.organization_id,
        status: response.status,
        trainingFile: response.training_file,
        validationFile: response.validation_file,
        resultFiles: response.result_files,
        error: response.error,
        hyperparameters: {
          nEpochs: response.hyperparameters.n_epochs || 3, // Giá trị mặc định nếu không có
          batchSize: response.hyperparameters.batch_size || 'auto', // Giá trị mặc định nếu không có
          learningRateMultiplier:
            response.hyperparameters.learning_rate_multiplier || 'auto', // Giá trị mặc định nếu không có
        },
        trainedTokens: response.trained_tokens || 0,
      };
    } catch (error: any) {
      this.logger.error(
        `OpenAI API error creating fine-tuning job: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng OpenAI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      // Xử lý lỗi kết nối
      if (
        error.name === 'NetworkError' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.message.includes('network')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến OpenAI API',
        );
      }

      // Xử lý lỗi không tìm thấy file
      if (error.status === 404) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Không tìm thấy file dữ liệu huấn luyện hoặc model cơ sở',
        );
      }

      // Xử lý lỗi validation
      if (error.status === 400) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Lỗi validation: ' +
          (error.message || 'Dữ liệu huấn luyện không hợp lệ'),
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi tạo fine-tuning job: ' + error.message,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết về fine-tuning job
   * @param jobId ID của fine-tuning job
   * @param apiKey API key của OpenAI
   * @returns Thông tin chi tiết về fine-tuning job
   * @throws AppException nếu có lỗi khi lấy thông tin job
   */
  async getFineTuningJob(
    jobId: string,
    apiKey: string,
  ): Promise<FineTuningJobResponse> {
    try {
      const openai = this.getOpenai(apiKey);

      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Gọi API để lấy thông tin fine-tuning job
      const response = await openai.fineTuning.jobs.retrieve(jobId, {
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      this.logger.log(`Retrieved fine-tuning job: ${response.id}`);

      // Chuyển đổi response sang định dạng FineTuningJobResponse
      return {
        id: response.id,
        object: response.object,
        createdAt: response.created_at,
        updatedAt: response.created_at, // OpenAI API không trả về updated_at
        model: response.model,
        fineTunedModel: response.fine_tuned_model,
        organizationId: response.organization_id,
        status: response.status,
        trainingFile: response.training_file,
        validationFile: response.validation_file,
        resultFiles: response.result_files,
        error: response.error,
        hyperparameters: {
          nEpochs: response.hyperparameters.n_epochs || 3,
          batchSize: response.hyperparameters.batch_size || 'auto',
          learningRateMultiplier:
            response.hyperparameters.learning_rate_multiplier || 'auto',
        },
        trainedTokens: response.trained_tokens || 0,
      };
    } catch (error: any) {
      this.logger.error(
        `OpenAI API error retrieving fine-tuning job: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng OpenAI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      // Xử lý lỗi kết nối
      if (
        error.name === 'NetworkError' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.message.includes('network')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến OpenAI API',
        );
      }

      // Xử lý lỗi không tìm thấy job
      if (error.status === 404) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Không tìm thấy fine-tuning job với ID: ' + jobId,
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi lấy thông tin fine-tuning job: ' + error.message,
      );
    }
  }

  /**
   * Lấy danh sách fine-tuning jobs
   * @param apiKey API key của OpenAI
   * @param limit Số lượng job tối đa trả về (mặc định: 20)
   * @param after ID của job để lấy các job sau đó (phân trang)
   * @returns Danh sách fine-tuning jobs
   * @throws AppException nếu có lỗi khi lấy danh sách jobs
   */
  async listFineTuningJobs(
    apiKey: string,
    limit: number = 20,
    after?: string,
  ): Promise<FineTuningJobResponse[]> {
    try {
      const openai = this.getOpenai(apiKey);

      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Chuẩn bị tham số cho request
      const params: any = { limit };
      if (after) {
        params.after = after;
      }

      // Gọi API để lấy danh sách fine-tuning jobs
      const response = await openai.fineTuning.jobs.list(params, {
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      this.logger.log(`Retrieved ${response.data.length} fine-tuning jobs`);

      // Chuyển đổi response sang định dạng FineTuningJobResponse[]
      return response.data.map((job) => ({
        id: job.id,
        object: job.object,
        createdAt: job.created_at,
        updatedAt: job.created_at, // OpenAI API không trả về updated_at
        model: job.model,
        fineTunedModel: job.fine_tuned_model,
        organizationId: job.organization_id,
        status: job.status,
        trainingFile: job.training_file,
        validationFile: job.validation_file,
        resultFiles: job.result_files,
        error: job.error,
        hyperparameters: {
          nEpochs: job.hyperparameters.n_epochs || 3,
          batchSize: job.hyperparameters.batch_size || 'auto',
          learningRateMultiplier:
            job.hyperparameters.learning_rate_multiplier || 'auto',
        },
        trainedTokens: job.trained_tokens || 0,
      }));
    } catch (error: any) {
      this.logger.error(
        `OpenAI API error listing fine-tuning jobs: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng OpenAI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      // Xử lý lỗi kết nối
      if (
        error.name === 'NetworkError' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.message.includes('network')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến OpenAI API',
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi lấy danh sách fine-tuning jobs: ' + error.message,
      );
    }
  }

  /**
   * Hủy fine-tuning job
   * @param jobId ID của fine-tuning job cần hủy
   * @param apiKey API key của OpenAI
   * @returns Thông tin về fine-tuning job đã hủy
   * @throws AppException nếu có lỗi khi hủy job
   */
  async cancelFineTuningJob(
    jobId: string,
    apiKey: string,
  ): Promise<FineTuningJobResponse> {
    try {
      const openai = this.getOpenai(apiKey);

      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Gọi API để hủy fine-tuning job
      const response = await openai.fineTuning.jobs.cancel(jobId, {
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      this.logger.log(`Cancelled fine-tuning job: ${response.id}`);

      // Chuyển đổi response sang định dạng FineTuningJobResponse
      return {
        id: response.id,
        object: response.object,
        createdAt: response.created_at,
        updatedAt: response.created_at, // OpenAI API không trả về updated_at
        model: response.model,
        fineTunedModel: response.fine_tuned_model,
        organizationId: response.organization_id,
        status: response.status,
        trainingFile: response.training_file,
        validationFile: response.validation_file,
        resultFiles: response.result_files,
        error: response.error,
        hyperparameters: {
          nEpochs: response.hyperparameters.n_epochs || 3,
          batchSize: response.hyperparameters.batch_size || 'auto',
          learningRateMultiplier:
            response.hyperparameters.learning_rate_multiplier || 'auto',
        },
        trainedTokens: response.trained_tokens || 0,
      };
    } catch (error: any) {
      this.logger.error(
        `OpenAI API error cancelling fine-tuning job: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng OpenAI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến OpenAI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      // Xử lý lỗi kết nối
      if (
        error.name === 'NetworkError' ||
        error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.message.includes('network')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến OpenAI API',
        );
      }

      // Xử lý lỗi không tìm thấy job
      if (error.status === 404) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Không tìm thấy fine-tuning job với ID: ' + jobId,
        );
      }

      // Xử lý lỗi không thể hủy job
      if (error.status === 400) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Không thể hủy fine-tuning job: ' +
          (error.message || 'Job có thể đã hoàn thành hoặc đã bị hủy'),
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi hủy fine-tuning job: ' + error.message,
      );
    }
  }
}
