import { ProviderFineTuneEnum, FineTuneJobStatus, FineTuneContextEnum, ModelTypeEnum } from '../constants';

/**
 * Interface cho training data
 */
export interface TrainingDataInterface {
  /**
   * Dữ liệu training (JSONL format)
   */
  trainData: string;

  /**
   * Dữ liệu validation (JSONL format, optional)
   */
  validationData?: string;

  /**
   * Tên file training
   */
  trainFileName?: string;

  /**
   * Tên file validation
   */
  validationFileName?: string;
}

/**
 * Interface cho job data fine-tuning đơn giản
 * Chỉ nhận jobId từ provider và thực hiện polling status
 */
export interface FineTuneJobData {
  /**
   * ID của fine-tuning job từ provider (format: ftjob:aaaaa)
   */
  jobId: string;

  /**
   * ID của user model fine tune record
   */
  userModelFineTuneId: string;

  /**
   * Nhà cung cấp AI
   */
  provider: ProviderFineTuneEnum;

  /**
   * Thời gian tạo job
   */
  timestamp: number;
}

/**
 * Interface cho thông tin user model fine tune
 */
export interface UserModelFineTuneInfo {
  /**
   * ID của user model fine tune
   */
  id: string;

  /**
   * ID của fine tune history
   */
  detailId: string;

  /**
   * ID người dùng (nếu có)
   */
  userId?: number;

  /**
   * Số R-Points đã trừ
   */
  pointsDeducted: number;

  /**
   * Trạng thái đã hoàn point chưa
   */
  pointsRefunded: boolean;

  /**
   * Nhà cung cấp
   */
  provider: ProviderFineTuneEnum;
}

/**
 * Interface cho response từ provider
 */
export interface ProviderFineTuneResponse {
  /**
   * ID của job từ provider
   */
  id: string;

  /**
   * Trạng thái hiện tại
   */
  status: string;

  /**
   * ID của model sau khi fine-tune (nếu thành công)
   */
  fineTunedModel?: string | null;

  /**
   * Thông tin lỗi (nếu thất bại)
   */
  error?: {
    message: string;
    code?: string;
  };

  /**
   * Metadata từ provider
   */
  metadata?: Record<string, any>;

  /**
   * Thời gian tạo
   */
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  updatedAt: number;
}

/**
 * Interface cho status update
 */
export interface FineTuneStatusUpdate {
  /**
   * ID của history record
   */
  historyId: string;

  /**
   * Trạng thái mới
   */
  status: FineTuneJobStatus;

  /**
   * ID của model fine-tuned (nếu thành công)
   */
  fineTunedModelId?: string;

  /**
   * Thông tin lỗi (nếu thất bại)
   */
  errorMessage?: string;

  /**
   * Metadata cập nhật
   */
  metadata?: Record<string, any>;

  /**
   * Thời gian cập nhật
   */
  updatedAt: number;
}

/**
 * Interface cho monitoring job data từ BE app
 * Job này được tạo sau khi BE app đã tạo fine-tune job với provider
 */
export interface MonitoringJobData {
  /**
   * ID của model trong bảng models (được sử dụng làm historyId)
   */
  historyId: string;

  /**
   * ID của fine-tuning job từ provider (OpenAI: ftjob-xxx, Google: projects/.../tuningJobs/xxx)
   */
  providerJobId: string;

  /**
   * Nhà cung cấp AI
   */
  provider: ProviderFineTuneEnum;

  /**
   * ID người dùng (nullable cho system models)
   */
  userId?: number;

  /**
   * Thời gian tạo job
   */
  timestamp?: number;
}

/**
 * Interface cho kết quả monitoring
 */
export interface MonitoringResult {
  /**
   * Có nên tiếp tục polling không
   */
  shouldContinuePolling: boolean;

  /**
   * Trạng thái hiện tại từ provider
   */
  status: string;

  /**
   * ID của model fine-tuned (nếu thành công)
   */
  fineTunedModelId?: string;

  /**
   * Thông tin lỗi (nếu thất bại)
   */
  error?: string;

  /**
   * Metadata từ provider
   */
  metadata?: Record<string, any>;
}
