import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ZaloCustomerStatus } from '../enums';

@Entity({ name: 'zalo_customers' })
export class ZaloCustomer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id', type: 'int' })
  userId: number;

  @Column({ name: 'oa_id', length: 50 })
  oaId: string;

  @Column({ name: 'zalo_user_id', length: 50 })
  zaloUserId: string;

  @Column({ name: 'user_id_by_app', length: 50, nullable: true })
  userIdByApp?: string;

  @Column({ name: 'user_external_id', length: 255, nullable: true })
  userExternalId?: string;

  @Column({ name: 'display_name', length: 255, nullable: true })
  displayName?: string;

  @Column({ name: 'user_alias', length: 255, nullable: true })
  userAlias?: string;

  @Column({ name: 'is_sensitive', default: false })
  isSensitive: boolean;

  @Column({ name: 'user_is_follower', default: false })
  userIsFollower: boolean;

  @Column({ name: 'user_last_interaction_date', length: 20, nullable: true })
  userLastInteractionDate?: string;

  @Column({ name: 'avatar', length: 255, nullable: true })
  avatar?: string;

  @Column({ name: 'tags_and_notes_info', type: 'jsonb', nullable: true })
  tagsAndNotesInfo?: Record<string, any>;

  @Column({ name: 'shared_info', type: 'jsonb', nullable: true })
  sharedInfo?: Record<string, any>;

  @Column({ name: 'dynamic_param', length: 100, nullable: true })
  dynamicParam?: string;

  @Column({ name: 'raw_api_response', type: 'jsonb', nullable: true })
  rawApiResponse?: Record<string, any>;

  @Column({ name: 'customer_id', type: 'uuid', nullable: true })
  customerId?: string;

  @Column({ name: 'first_interaction_at', type: 'bigint' })
  firstInteractionAt: number;

  @Column({ name: 'last_interaction_at', type: 'bigint' })
  lastInteractionAt: number;

  @Column({ name: 'interaction_count', type: 'int', default: 1 })
  interactionCount: number;

  @Column({
    name: 'status',
    type: 'enum',
    enum: ZaloCustomerStatus,
    default: ZaloCustomerStatus.ACTIVE,
  })
  status: ZaloCustomerStatus;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
