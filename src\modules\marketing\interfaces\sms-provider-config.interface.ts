/**
 * Interface cơ bản cho cấu hình nhà cung cấp SMS
 */
export interface BaseSmsProviderConfig {
  /**
   * Tên nhà cung cấp
   */
  providerName: string;
}

/**
 * Interface cho cấu hình FPT SMS (chỉ lưu trong additionalSettings)
 */
export interface FptSmsProviderConfig {
  /**
   * Client ID dùng cho xác thực <PERSON>Auth (SMS_CLIENT_ID)
   */
  clientId: string;
}

/**
 * Interface cho cấu hình Twilio SMS
 */
export interface TwilioSmsProviderConfig extends BaseSmsProviderConfig {
  providerName: 'TWILIO';

  /**
   * Account SID của Twilio
   */
  accountSid: string;

  /**
   * Auth Token của Twilio
   */
  authToken: string;

  /**
   * Số điện thoại gửi tin nhắn
   */
  fromPhone?: string;

  /**
   * Messaging Service SID (tùy chọn)
   */
  messagingServiceSid?: string;
}

/**
 * Interface cho cấu hình SpeedSMS
 */
export interface SpeedSmsProviderConfig extends BaseSmsProviderConfig {
  providerName: 'SPEED_SMS';

  /**
   * Token API của SpeedSMS
   */
  apiToken: string;

  /**
   * URL API của SpeedSMS
   */
  apiUrl?: string;

  /**
   * Loại SMS (2: CSKH, 3: Brandname, 4: Notify, 5: App Android, 6: Số cố định)
   */
  smsType?: number;

  /**
   * Tên người gửi (bắt buộc nếu smsType = 3 hoặc 5)
   */
  sender?: string;
}

/**
 * Interface cho cấu hình Vonage SMS
 */
export interface VonageSmsProviderConfig extends BaseSmsProviderConfig {
  providerName: 'VONAGE';

  /**
   * API Key của Vonage
   */
  apiKey: string;

  /**
   * API Secret của Vonage
   */
  apiSecret: string;

  /**
   * Tên người gửi
   */
  from?: string;
}

/**
 * Union type cho tất cả các loại cấu hình SMS provider
 */
export type SmsProviderConfig =
  | FptSmsProviderConfig
  | TwilioSmsProviderConfig
  | SpeedSmsProviderConfig
  | VonageSmsProviderConfig;

/**
 * Enum cho các loại nhà cung cấp SMS
 */
export enum SmsProviderType {
  FPT_SMS = 'FPT_SMS',
  TWILIO = 'TWILIO',
  SPEED_SMS = 'SPEED_SMS',
  VONAGE = 'VONAGE',
}
