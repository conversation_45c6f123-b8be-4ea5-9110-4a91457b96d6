import { Connection } from '../../../entities/connection.entity';
import { Node } from '../../../entities/node.entity';

/**
 * Trạng thái thực thi của một node
 */
export interface NodeExecutionState {
  /** ID của node */
  id: string;
  
  /** Trạng thái hiện tại */
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  
  /** Dữ liệu đầu vào */
  inputData: any;
  
  /** Dữ liệu đầu ra */
  outputData: any;
  
  /** Lỗi nếu có */
  error?: Error;
  
  /** Thời gian bắt đầu */
  startTime?: number;
  
  /** Thời gian kết thúc */
  endTime?: number;
  
  /** Số lần retry */
  retryCount: number;
  
  /** Node definition */
  node: Node;

  /** Metadata bổ sung cho node execution */
  metadata?: {
    executionTime?: number;
    memoryUsage?: number;
    [key: string]: any;
  };
}

/**
 * Metadata của workflow execution
 */
export interface WorkflowExecutionMetadata {
  /** Thời gian bắt đầu */
  startTime: number;
  
  /** Thời gian kết thúc */
  endTime?: number;
  
  /** ID của user thực thi */
  userId: number;
  
  /** Số lần retry tổng */
  retryCount: number;
  
  /** Loại trigger */
  triggerType: 'manual' | 'webhook' | 'schedule';
  
  /** Source trigger */
  source?: string;
  
  /** Webhook ID nếu trigger từ webhook */
  webhookId?: string;
  
  /** Schedule ID nếu trigger từ schedule */
  scheduleId?: string;
  
  /** Priority */
  priority?: number;

  /** Tổng số nodes trong workflow */
  totalNodes: number;

  /** Số nodes đã hoàn thành */
  completedNodes: number;

  /** Số nodes thất bại */
  failedNodes: number;
}

/**
 * Dependency graph cho workflow nodes
 */
export interface DependencyGraph {
  /** Map từ nodeId đến danh sách nodeIds mà nó phụ thuộc */
  dependencies: Map<string, string[]>;

  /** Map từ nodeId đến danh sách nodeIds phụ thuộc vào nó */
  dependents: Map<string, string[]>;

  /** Danh sách nodes không có dependencies (có thể chạy đầu tiên) */
  rootNodes: string[];

  /** Danh sách nodes không có dependents (nodes cuối) */
  leafNodes: string[];
}

/**
 * Context của XState workflow machine
 */
export interface WorkflowContext {
  /** ID của workflow */
  workflowId: string;
  
  /** ID của execution */
  executionId: string;
  
  /** Map các node execution states */
  nodes: Map<string, NodeExecutionState>;
  
  /** Danh sách connections */
  connections: Connection[];
  
  /** Node hiện tại đang thực thi */
  currentNode?: string;

  /** Danh sách nodes đang chạy song song */
  runningNodes: string[];

  /** Dữ liệu output từ các nodes */
  executionData: Map<string, any>;
  
  /** Map các lỗi */
  errors: Map<string, Error>;
  
  /** Dữ liệu trigger ban đầu */
  triggerData: any;
  
  /** Metadata của execution */
  metadata: WorkflowExecutionMetadata;
  
  /** Dependency graph */
  dependencyGraph: DependencyGraph;

  /** Nodes sẵn sàng thực thi */
  readyNodes: string[];

  /** Danh sách nodes đang chờ dependencies */
  waitingNodes: string[];
  
  /** Tùy chọn execution */
  options?: {
    enableSSE?: boolean;
    timeout?: number;
    retryOnFailure?: boolean;
    skipValidation?: boolean;
    maxConcurrency?: number;
  };

  /** Workflow settings từ database */
  workflowSettings?: {
    defaultNodeTimeout?: number;
    pauseOnError?: boolean;
    enableNotifications?: boolean;
    resultWebhookUrl?: string;
  };
}

/**
 * Kết quả thực thi node
 */
export interface NodeExecutionResult {
  /** Thành công hay không */
  success: boolean;
  
  /** Dữ liệu output */
  outputData?: any;
  
  /** Lỗi nếu có */
  error?: Error;
  
  /** Metadata bổ sung */
  metadata?: {
    executionTime: number;
    memoryUsage?: number;
    httpStatusCode?: number;
    tokenUsage?: {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
    };
    [key: string]: any;
  };
}

/**
 * Kết quả từ LangGraph agent
 */
export interface LangGraphExecutionResult {
  /** Thành công hay không */
  success: boolean;
  
  /** Kết quả từ agent */
  result?: any;
  
  /** Messages từ agent conversation */
  messages?: any[];
  
  /** Lỗi nếu có */
  error?: Error;
  
  /** Metadata từ LangGraph */
  metadata?: {
    threadId: string;
    checkpointId?: string;
    executionTime: number;
    tokenUsage?: {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
    };
  };
}

/**
 * Configuration cho node execution
 */
export interface NodeExecutionConfig {
  /** Timeout cho node (ms) */
  timeout?: number;
  
  /** Có retry khi fail không */
  retryOnFail?: boolean;
  
  /** Số lần retry tối đa */
  maxRetries?: number;
  
  /** Thời gian chờ giữa các retry (ms) */
  retryDelay?: number;
  
  /** Có skip validation không */
  skipValidation?: boolean;
  
  /** Có gửi SSE events không */
  enableSSE?: boolean;

  /** Custom headers cho HTTP nodes */
  headers?: Record<string, string>;

  /** Environment variables */
  env?: Record<string, string>;
}
