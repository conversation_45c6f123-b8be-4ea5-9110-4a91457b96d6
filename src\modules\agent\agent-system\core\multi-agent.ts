import { END, START, StateGraph } from '@langchain/langgraph';
import {
  AgentState,
  createReactAgent,
  CustomRunnableConfig,
  GraphState,
} from './react-agent-executor';
import {
  HumanMessage,
  isAIMessage,
  isHumanMessage,
  isToolMessage,
  RemoveMessage,
} from '@langchain/core/messages';
import {
  DEFAULT_TRIMMING_STRATEGY,
  DEFAULT_TRIMMING_THRESHOLD,
  SUPERVISOR_TAG,
  WORKER_TAG,
} from './constants';
import { trimMessagesWithStrategy } from './message-trimmer';
import { saver } from './checkpoint-saver';
import { Logger } from '@nestjs/common';
import { initChatModel } from 'langchain/chat_models/universal';
import { toBeDeleted } from '../../constants/special-message-id';

const logger = new Logger('MultiAgent');
const supervisorGraph = createReactAgent({
  checkpointSaver: saver,
}).withConfig({
  tags: [SUPERVISOR_TAG],
});

const workerGraph = createReactAgent({
  checkpointSaver: saver,
}).withConfig({
  tags: [WORKER_TAG],
});

function makeCallWorker(worker: typeof workerGraph) {
  return async (
    state: (typeof GraphState)['State'],
    config: CustomRunnableConfig,
  ) => {
    const activeAgentId = state.activeAgent;
    const agentConfig = config?.configurable?.agentConfigMap?.[activeAgentId];
    if (!agentConfig) {
      throw new Error(`No configuration found for agent: ${activeAgentId}`);
    }
    const input = state.messages.filter(
      (msg) =>
        (isAIMessage(msg) || isToolMessage(msg) || isHumanMessage(msg)) &&
        msg.response_metadata?.invoker === agentConfig.id,
    );
    const workerState: (typeof GraphState)['State'] = {
      messages: input,
      activeAgent: activeAgentId,
      metadata: state.metadata,
    };
    const output = await worker.invoke(workerState, config);

    return {
      messages: [
        output.messages.at(-1),
        new HumanMessage(
          `Worker finishes, decide whether to continue working with the workers or response to the user`,
        ),
      ],
      activeAgent: config.configurable?.supervisorAgentId,
      isSupervisor: true,
    };
  };
}

const messageTrimmerNode = async (
  state: AgentState,
  config?: CustomRunnableConfig,
) => {
  logger.debug('reached mesage trimmer node');
  logger.debug(`before trimming: ${state.messages.length}`);
  const { messages } = state;
  logger.warn('extract messages state');
  const activeAgentId = state.activeAgent;
  const agentConfig = config?.configurable?.agentConfigMap?.[activeAgentId];
  if (!agentConfig) {
    logger.debug('no worker config, skip');
    return { messages: [] }; // Return empty messages array instead of empty object
  }
  if (agentConfig.id !== config?.configurable?.supervisorAgentId) {
    logger.debug('not supervisor, skip');
    return { messages: [] }; // Return empty messages array instead of empty object
  }

  const {
    type = DEFAULT_TRIMMING_STRATEGY,
    threshold = DEFAULT_TRIMMING_THRESHOLD,
  } = agentConfig.trimmingConfig;
  const model = await initChatModel(
    `${agentConfig.model.provider.toLowerCase()}:${agentConfig.model.name}`,
    {},
  );
  const strategy = await trimMessagesWithStrategy(
    messages,
    type,
    threshold,
    model,
  );

  // Ensure strategy has a valid messages array
  if (!strategy || !strategy.messages) {
    logger.warn(
      'Trimming strategy returned invalid result, using empty messages array',
    );
    return { messages: [] };
  }

  logger.debug(
    `removed ${
      strategy.messages
        ? strategy.messages.filter((x: any) => x instanceof RemoveMessage)
            .length
        : 0
    } messages`,
  );

  // Final safety check: ensure we're returning a valid object with messages array
  if (
    !strategy ||
    typeof strategy !== 'object' ||
    !Array.isArray(strategy.messages)
  ) {
    logger.error(
      'Message trimmer returned invalid format, using empty messages array',
    );
    return { messages: [] };
  }

  logger.debug(`remove image messages`);
  const removeImageMessages = strategy.messages
    .filter((m) => m.content?.[0]?.type === 'image_url')
    .map((m) => new RemoveMessage({ id: m.id }));

  strategy.messages.push(...removeImageMessages);
  return strategy;
};

const workflow = new StateGraph(GraphState)
  .addNode('supervisor', supervisorGraph, {
    ends: ['worker', 'messageTrimmer'],
  })
  .addNode('worker', makeCallWorker(workerGraph), {
    subgraphs: [workerGraph],
  })
  .addNode('messageTrimmer', messageTrimmerNode)
  .addEdge(START, 'supervisor')
  .addEdge('worker', 'supervisor')
  .addEdge('supervisor', 'messageTrimmer')
  .addEdge('messageTrimmer', END)
  .compile({ checkpointer: saver });

export { workflow };
