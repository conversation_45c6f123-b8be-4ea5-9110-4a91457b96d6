import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

/**
 * Interface for structured content of customer memories
 * Enforces consistent data structure for JSONB fields
 */
export interface CustomerMemoryStructuredContent {
  title: string;
  reason: string;
  content: string;
}

/**
 * Interface for metadata of customer memories
 * Supports additional attributes for memory records
 */
export interface CustomerMemoryMetadata {
  source?: string;
  tags?: string[];
  priority?: 'low' | 'medium' | 'high';
  category?: string;
  lastUpdated?: number;
  [key: string]: any;
}

/**
 * Customer Memory entity
 * Stores customer-specific memories and facts for AI agent context
 */
@Entity('customer_memories')
@Index('idx_customer_memories_customer_id', ['customerId'])
export class CustomerMemory {
  /**
   * UUID primary key for the memory record
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Foreign key reference to user_convert_customers.id
   */
  @Column({ name: 'customer_id', type: 'uuid', nullable: false })
  customerId: string;

  /**
   * Customer information in JSON format with {title, reason, content} structure
   */
  @Column({ 
    name: 'structured_content', 
    type: 'jsonb', 
    nullable: false,
    comment: 'Customer information in JSON format with {title, reason, content} structure'
  })
  structuredContent: CustomerMemoryStructuredContent;

  /**
   * Additional metadata for the memory record in JSONB format
   */
  @Column({ 
    name: 'metadata', 
    type: 'jsonb', 
    nullable: true,
    default: '{}',
    comment: 'Additional metadata for the memory record in JSONB format'
  })
  metadata?: CustomerMemoryMetadata;

  /**
   * Creation timestamp in Unix epoch milliseconds
   */
  @Column({ 
    name: 'created_at', 
    type: 'bigint', 
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    nullable: false,
    comment: 'Creation timestamp in Unix epoch milliseconds'
  })
  createdAt: number;


}
