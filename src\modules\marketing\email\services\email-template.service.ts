import { Injectable, Logger } from '@nestjs/common';

/**
 * Service xử lý template email với biến tùy chỉnh
 */
@Injectable()
export class EmailTemplateService {
  private readonly logger = new Logger(EmailTemplateService.name);

  /**
   * Inject biến tùy chỉnh vào template
   * @param template Template chứa biến dạng {{variable}}
   * @param variables Dữ liệu biến để thay thế
   * @returns Template đã được inject biến
   */
  injectVariables(template: string, variables: Record<string, any>): string {
    if (!template || !variables) {
      return template;
    }

    try {
      let result = template;

      // Tìm tất cả biến dạng {{variable}} trong template
      const variablePattern = /\{\{([^}]+)\}\}/g;
      let match;

      while ((match = variablePattern.exec(template)) !== null) {
        const variableName = match[1].trim();
        const variableValue = this.getNestedValue(variables, variableName);

        if (variableValue !== undefined && variableValue !== null) {
          // Thay thế biến bằng giá trị thực
          const placeholder = match[0]; // {{variable}}
          result = result.replace(
            new RegExp(this.escapeRegExp(placeholder), 'g'),
            String(variableValue),
          );
        } else {
          // Nếu không tìm thấy biến, giữ nguyên hoặc thay bằng chuỗi rỗng
          this.logger.warn(`Variable "${variableName}" not found in data`);
          const placeholder = match[0];
          result = result.replace(
            new RegExp(this.escapeRegExp(placeholder), 'g'),
            '',
          );
        }
      }

      return result;
    } catch (error) {
      this.logger.error(
        `Error injecting variables into template: ${error.message}`,
        error.stack,
      );
      return template; // Trả về template gốc nếu có lỗi
    }
  }

  /**
   * Inject pixel tracking vào nội dung email
   * @param content Nội dung email HTML
   * @param trackingId ID tracking duy nhất
   * @param baseUrl Base URL của server
   * @returns Nội dung email đã có pixel tracking
   */
  injectTrackingPixel(
    content: string,
    trackingId: string,
    baseUrl: string = '',
  ): string {
    if (!content || !trackingId) {
      return content;
    }

    try {
      const trackingPixel = `<img src="${baseUrl}/api/email-tracking/pixel/${trackingId}" width="1" height="1" style="display:none;" alt="" />`;

      // Thêm pixel tracking trước thẻ đóng </body> hoặc cuối nội dung
      if (content.includes('</body>')) {
        return content.replace('</body>', `${trackingPixel}</body>`);
      } else {
        return content + trackingPixel;
      }
    } catch (error) {
      this.logger.error(
        `Error injecting tracking pixel: ${error.message}`,
        error.stack,
      );
      return content;
    }
  }

  /**
   * Inject link tracking vào tất cả links trong nội dung email
   * @param content Nội dung email HTML
   * @param trackingId ID tracking duy nhất
   * @param baseUrl Base URL của server
   * @returns Nội dung email đã có link tracking
   */
  injectLinkTracking(
    content: string,
    trackingId: string,
    baseUrl: string = '',
  ): string {
    if (!content || !trackingId) {
      return content;
    }

    try {
      // Regex để tìm tất cả thẻ <a> với href
      const linkRegex = /<a\s+([^>]*?)href\s*=\s*["']([^"']+)["']([^>]*?)>/gi;

      return content.replace(
        linkRegex,
        (match, beforeHref, originalUrl, afterHref) => {
          // Bỏ qua nếu URL đã là tracking URL hoặc là mailto/tel
          if (
            originalUrl.includes('/api/email-tracking/') ||
            originalUrl.startsWith('mailto:') ||
            originalUrl.startsWith('tel:') ||
            originalUrl.startsWith('#') ||
            originalUrl.startsWith('javascript:')
          ) {
            return match;
          }

          // Tạo tracking URL
          const encodedUrl = encodeURIComponent(originalUrl);
          const trackingUrl = `${baseUrl}/api/email-tracking/click/${trackingId}?url=${encodedUrl}`;

          return `<a ${beforeHref}href="${trackingUrl}"${afterHref}>`;
        },
      );
    } catch (error) {
      this.logger.error(
        `Error injecting link tracking: ${error.message}`,
        error.stack,
      );
      return content;
    }
  }

  /**
   * Lấy giá trị nested từ object bằng dot notation
   * @param obj Object chứa dữ liệu
   * @param path Đường dẫn đến giá trị (vd: user.name, profile.address.city)
   * @returns Giá trị tìm được hoặc undefined
   */
  private getNestedValue(obj: any, path: string): any {
    try {
      return path.split('.').reduce((current, key) => {
        return current && current[key] !== undefined ? current[key] : undefined;
      }, obj);
    } catch (error) {
      return undefined;
    }
  }

  /**
   * Escape special characters for RegExp
   * @param string Chuỗi cần escape
   * @returns Chuỗi đã escape
   */
  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Validate template syntax
   * @param template Template cần validate
   * @returns True nếu template hợp lệ
   */
  validateTemplate(template: string): boolean {
    if (!template) {
      return true;
    }

    try {
      // Kiểm tra cặp ngoặc {{ }} có đúng không
      const openBraces = (template.match(/\{\{/g) || []).length;
      const closeBraces = (template.match(/\}\}/g) || []).length;

      return openBraces === closeBraces;
    } catch (error) {
      this.logger.error(`Error validating template: ${error.message}`);
      return false;
    }
  }

  /**
   * Lấy danh sách tất cả biến trong template
   * @param template Template cần phân tích
   * @returns Mảng tên biến
   */
  extractVariables(template: string): string[] {
    if (!template) {
      return [];
    }

    try {
      const variables: string[] = [];
      const variablePattern = /\{\{([^}]+)\}\}/g;
      let match;

      while ((match = variablePattern.exec(template)) !== null) {
        const variableName = match[1].trim();
        if (!variables.includes(variableName)) {
          variables.push(variableName);
        }
      }

      return variables;
    } catch (error) {
      this.logger.error(`Error extracting variables: ${error.message}`);
      return [];
    }
  }

  /**
   * Enhanced link tracking với options
   * @param content Email content
   * @param trackingId Tracking ID
   * @param baseUrl Base URL
   * @param options Tracking options
   * @returns Content with enhanced link tracking
   */
  injectAdvancedLinkTracking(
    content: string,
    trackingId: string,
    baseUrl: string = '',
    options: {
      excludeDomains?: string[];
      includeUtmParams?: boolean;
      customParams?: Record<string, string>;
    } = {},
  ): string {
    if (!content || !trackingId) {
      return content;
    }

    try {
      const linkRegex = /<a\s+([^>]*?)href\s*=\s*["']([^"']+)["']([^>]*?)>/gi;
      let linkIndex = 0;

      return content.replace(
        linkRegex,
        (match, beforeHref, originalUrl, afterHref) => {
          linkIndex++;

          if (this.shouldSkipUrl(originalUrl, options.excludeDomains)) {
            return match;
          }

          if (!this.isValidTrackableUrl(originalUrl)) {
            this.logger.warn(`Invalid URL for tracking: ${originalUrl}`);
            return match;
          }

          let trackingUrl = originalUrl;

          if (options.includeUtmParams) {
            trackingUrl = this.addUtmParameters(trackingUrl, trackingId);
          }

          if (options.customParams) {
            trackingUrl = this.addCustomParameters(
              trackingUrl,
              options.customParams,
            );
          }

          const encodedUrl = encodeURIComponent(trackingUrl);
          const finalTrackingUrl = `${baseUrl}/api/email-tracking/click/${trackingId}?url=${encodedUrl}&idx=${linkIndex}`;

          const trackingAttrs = `data-tracking-id="${trackingId}" data-link-index="${linkIndex}"`;

          return `<a ${beforeHref}href="${finalTrackingUrl}" ${trackingAttrs}${afterHref}>`;
        },
      );
    } catch (error) {
      this.logger.error(
        `Error injecting advanced link tracking: ${error.message}`,
        error.stack,
      );
      return content;
    }
  }

  /**
   * Check if URL should be skipped
   */
  private shouldSkipUrl(url: string, excludeDomains: string[] = []): boolean {
    if (
      url.includes('/api/email-tracking/') ||
      url.startsWith('mailto:') ||
      url.startsWith('tel:') ||
      url.startsWith('#') ||
      url.startsWith('javascript:') ||
      url.startsWith('data:')
    ) {
      return true;
    }

    if (excludeDomains.length > 0) {
      try {
        const parsedUrl = new URL(url);
        const hostname = parsedUrl.hostname.toLowerCase();
        return excludeDomains.some((domain) =>
          hostname.includes(domain.toLowerCase()),
        );
      } catch {
        return true;
      }
    }

    return false;
  }

  /**
   * Validate if URL is trackable
   */
  private isValidTrackableUrl(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      return ['http:', 'https:'].includes(parsedUrl.protocol);
    } catch {
      return false;
    }
  }

  /**
   * Add UTM parameters to URL
   */
  private addUtmParameters(url: string, trackingId: string): string {
    try {
      const parsedUrl = new URL(url);
      const params = parsedUrl.searchParams;

      if (!params.has('utm_source')) {
        params.set('utm_source', 'email');
      }
      if (!params.has('utm_medium')) {
        params.set('utm_medium', 'email_marketing');
      }
      if (!params.has('utm_campaign')) {
        const campaignId = trackingId.split('_')[0];
        params.set('utm_campaign', `campaign_${campaignId}`);
      }
      if (!params.has('utm_content')) {
        params.set('utm_content', trackingId);
      }

      return parsedUrl.toString();
    } catch {
      return url;
    }
  }

  /**
   * Add custom parameters to URL
   */
  private addCustomParameters(
    url: string,
    customParams: Record<string, string>,
  ): string {
    try {
      const parsedUrl = new URL(url);
      const params = parsedUrl.searchParams;

      for (const [key, value] of Object.entries(customParams)) {
        if (!params.has(key)) {
          params.set(key, value);
        }
      }

      return parsedUrl.toString();
    } catch {
      return url;
    }
  }
}
