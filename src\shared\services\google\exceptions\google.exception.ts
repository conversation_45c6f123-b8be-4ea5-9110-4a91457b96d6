import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '../../../../common/exceptions';

/**
 * Mã lỗi cho Google Services
 */
export const GOOGLE_ERROR_CODES = {
  // Google Authentication Errors (40001-40010)
  GOOGLE_AUTH_INVALID_TOKEN: new ErrorCode(
    40001,
    'Google access token không hợp lệ hoặc đã hết hạn',
    HttpStatus.UNAUTHORIZED,
  ),

  GOOGLE_AUTH_INSUFFICIENT_PERMISSIONS: new ErrorCode(
    40002,
    'Không có quyền truy cập Google API',
    HttpStatus.FORBIDDEN,
  ),

  GOOGLE_AUTH_QUOTA_EXCEEDED: new ErrorCode(
    40003,
    'Đã vượt quá giới hạn sử dụng Google API',
    HttpStatus.TOO_MANY_REQUESTS,
  ),

  // Google Sheets Errors (40011-40030)
  GOOGLE_SHEETS_SPREADSHEET_NOT_FOUND: new ErrorCode(
    40011,
    'Không tìm thấy spreadsheet',
    HttpStatus.NOT_FOUND,
  ),

  GOOGLE_SHEETS_INVALID_RANGE: new ErrorCode(
    40012,
    'Range không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  GOOGLE_SHEETS_INVALID_DATA: new ErrorCode(
    40013,
    'Dữ liệu không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  GOOGLE_SHEETS_SHEET_NOT_FOUND: new ErrorCode(
    40014,
    'Không tìm thấy sheet',
    HttpStatus.NOT_FOUND,
  ),

  GOOGLE_SHEETS_DUPLICATE_SHEET_NAME: new ErrorCode(
    40015,
    'Tên sheet đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),

  GOOGLE_SHEETS_API_ERROR: new ErrorCode(
    40016,
    'Lỗi khi gọi Google Sheets API',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Google Drive Errors (40031-40050)
  GOOGLE_DRIVE_FILE_NOT_FOUND: new ErrorCode(
    40031,
    'Không tìm thấy file',
    HttpStatus.NOT_FOUND,
  ),

  GOOGLE_DRIVE_FOLDER_NOT_FOUND: new ErrorCode(
    40032,
    'Không tìm thấy folder',
    HttpStatus.NOT_FOUND,
  ),

  GOOGLE_DRIVE_UPLOAD_FAILED: new ErrorCode(
    40033,
    'Lỗi khi upload file',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GOOGLE_DRIVE_DOWNLOAD_FAILED: new ErrorCode(
    40034,
    'Lỗi khi download file',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GOOGLE_DRIVE_PERMISSION_DENIED: new ErrorCode(
    40035,
    'Không có quyền truy cập file/folder',
    HttpStatus.FORBIDDEN,
  ),

  GOOGLE_DRIVE_STORAGE_QUOTA_EXCEEDED: new ErrorCode(
    40036,
    'Đã vượt quá dung lượng lưu trữ',
    HttpStatus.INSUFFICIENT_STORAGE,
  ),

  GOOGLE_DRIVE_API_ERROR: new ErrorCode(
    40037,
    'Lỗi khi gọi Google Drive API',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Google Docs Errors (40051-40070)
  GOOGLE_DOCS_DOCUMENT_NOT_FOUND: new ErrorCode(
    40051,
    'Không tìm thấy document',
    HttpStatus.NOT_FOUND,
  ),

  GOOGLE_DOCS_INVALID_CONTENT: new ErrorCode(
    40052,
    'Nội dung document không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  GOOGLE_DOCS_EDIT_PERMISSION_DENIED: new ErrorCode(
    40053,
    'Không có quyền chỉnh sửa document',
    HttpStatus.FORBIDDEN,
  ),

  GOOGLE_DOCS_API_ERROR: new ErrorCode(
    40054,
    'Lỗi khi gọi Google Docs API',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Google Calendar Errors (40071-40090)
  GOOGLE_CALENDAR_NOT_FOUND: new ErrorCode(
    40071,
    'Không tìm thấy calendar',
    HttpStatus.NOT_FOUND,
  ),

  GOOGLE_CALENDAR_EVENT_NOT_FOUND: new ErrorCode(
    40072,
    'Không tìm thấy event',
    HttpStatus.NOT_FOUND,
  ),

  GOOGLE_CALENDAR_INVALID_EVENT_DATA: new ErrorCode(
    40073,
    'Dữ liệu event không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  GOOGLE_CALENDAR_TIME_CONFLICT: new ErrorCode(
    40074,
    'Xung đột thời gian với event khác',
    HttpStatus.CONFLICT,
  ),

  GOOGLE_CALENDAR_API_ERROR: new ErrorCode(
    40075,
    'Lỗi khi gọi Google Calendar API',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Google Analytics Errors (40091-40110)
  GOOGLE_ANALYTICS_PROPERTY_NOT_FOUND: new ErrorCode(
    40091,
    'Không tìm thấy Google Analytics property',
    HttpStatus.NOT_FOUND,
  ),

  GOOGLE_ANALYTICS_INVALID_QUERY: new ErrorCode(
    40092,
    'Query không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  GOOGLE_ANALYTICS_INSUFFICIENT_PERMISSIONS: new ErrorCode(
    40093,
    'Không có quyền truy cập Google Analytics property',
    HttpStatus.FORBIDDEN,
  ),

  GOOGLE_ANALYTICS_API_ERROR: new ErrorCode(
    40094,
    'Lỗi khi gọi Google Analytics API',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // General Google API Errors (40111-40130)
  GOOGLE_API_NETWORK_ERROR: new ErrorCode(
    40111,
    'Lỗi kết nối đến Google API',
    HttpStatus.SERVICE_UNAVAILABLE,
  ),

  GOOGLE_API_TIMEOUT: new ErrorCode(
    40112,
    'Timeout khi gọi Google API',
    HttpStatus.GATEWAY_TIMEOUT,
  ),

  GOOGLE_API_CONFIGURATION_ERROR: new ErrorCode(
    40113,
    'Cấu hình Google API không hợp lệ',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GOOGLE_API_UNKNOWN_ERROR: new ErrorCode(
    40114,
    'Lỗi không xác định từ Google API',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
};

/**
 * Helper function để xử lý lỗi Google API chung
 * @param error Lỗi từ Google API
 * @param defaultErrorCode Mã lỗi mặc định nếu không xác định được lỗi cụ thể
 * @returns AppException với mã lỗi phù hợp
 */
export function handleGoogleApiError(error: any, defaultErrorCode: ErrorCode): ErrorCode {
  // Nếu đã là AppException thì giữ nguyên
  if (error.code && error.message && error.status) {
    return error;
  }

  // Xử lý các lỗi HTTP status cụ thể
  if (error.response?.status) {
    switch (error.response.status) {
      case 401:
        return GOOGLE_ERROR_CODES.GOOGLE_AUTH_INVALID_TOKEN;
      case 403:
        return GOOGLE_ERROR_CODES.GOOGLE_AUTH_INSUFFICIENT_PERMISSIONS;
      case 404:
        // Trả về error code cụ thể tùy theo service
        return defaultErrorCode;
      case 429:
        return GOOGLE_ERROR_CODES.GOOGLE_AUTH_QUOTA_EXCEEDED;
      case 500:
      case 502:
      case 503:
        return GOOGLE_ERROR_CODES.GOOGLE_API_NETWORK_ERROR;
      case 504:
        return GOOGLE_ERROR_CODES.GOOGLE_API_TIMEOUT;
      default:
        return defaultErrorCode;
    }
  }

  // Xử lý các lỗi network
  if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
    return GOOGLE_ERROR_CODES.GOOGLE_API_NETWORK_ERROR;
  }

  if (error.code === 'ETIMEDOUT' || error.code === 'ECONNABORTED') {
    return GOOGLE_ERROR_CODES.GOOGLE_API_TIMEOUT;
  }

  // Trả về error code mặc định
  return defaultErrorCode;
}
