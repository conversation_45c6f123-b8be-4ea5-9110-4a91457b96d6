/**
 * Interface cho cấu hình Google Docs API
 */
export interface GoogleDocsConfig {
  /**
   * Client ID từ Google Cloud Console
   */
  clientId: string;

  /**
   * Client Secret từ Google Cloud Console
   */
  clientSecret: string;

  /**
   * Redirect URI cho OAuth
   */
  redirectUri: string;
}

/**
 * Interface cho thông tin xác thực Google Docs
 */
export interface GoogleDocsCredentials {
  /**
   * Access Token
   */
  accessToken: string;

  /**
   * Refresh Token
   */
  refreshToken?: string;

  /**
   * Thời gian hết hạn của Access Token (Unix timestamp)
   */
  expiresAt?: number;
}

/**
 * Interface cho thông tin document
 */
export interface DocumentInfo {
  /**
   * ID của document
   */
  documentId: string;

  /**
   * Tiêu đề document
   */
  title: string;

  /**
   * Nội dung document
   */
  body?: DocumentBody;

  /**
   * Revision ID
   */
  revisionId: string;

  /**
   * Thời gian tạo
   */
  createdTime?: string;

  /**
   * Thời gian cập nhật cuối
   */
  modifiedTime?: string;
}

/**
 * Interface cho body của document
 */
export interface DocumentBody {
  /**
   * Danh sách content
   */
  content: StructuralElement[];
}

/**
 * Interface cho structural element
 */
export interface StructuralElement {
  /**
   * Start index
   */
  startIndex: number;

  /**
   * End index
   */
  endIndex: number;

  /**
   * Paragraph
   */
  paragraph?: Paragraph;

  /**
   * Section break
   */
  sectionBreak?: SectionBreak;

  /**
   * Table
   */
  table?: Table;

  /**
   * Table of contents
   */
  tableOfContents?: TableOfContents;
}

/**
 * Interface cho paragraph
 */
export interface Paragraph {
  /**
   * Danh sách elements
   */
  elements: ParagraphElement[];

  /**
   * Paragraph style
   */
  paragraphStyle?: ParagraphStyle;

  /**
   * Bullet
   */
  bullet?: Bullet;
}

/**
 * Interface cho paragraph element
 */
export interface ParagraphElement {
  /**
   * Start index
   */
  startIndex: number;

  /**
   * End index
   */
  endIndex: number;

  /**
   * Text run
   */
  textRun?: TextRun;

  /**
   * Auto text
   */
  autoText?: AutoText;

  /**
   * Page break
   */
  pageBreak?: PageBreak;

  /**
   * Column break
   */
  columnBreak?: ColumnBreak;

  /**
   * Footnote reference
   */
  footnoteReference?: FootnoteReference;

  /**
   * Horizontal rule
   */
  horizontalRule?: HorizontalRule;

  /**
   * Equation
   */
  equation?: Equation;

  /**
   * Inline object element
   */
  inlineObjectElement?: InlineObjectElement;
}

/**
 * Interface cho text run
 */
export interface TextRun {
  /**
   * Nội dung text
   */
  content: string;

  /**
   * Text style
   */
  textStyle?: TextStyle;
}

/**
 * Interface cho text style
 */
export interface TextStyle {
  /**
   * In đậm
   */
  bold?: boolean;

  /**
   * In nghiêng
   */
  italic?: boolean;

  /**
   * Gạch chân
   */
  underline?: boolean;

  /**
   * Gạch ngang
   */
  strikethrough?: boolean;

  /**
   * Chỉ số trên
   */
  superscript?: boolean;

  /**
   * Chỉ số dưới
   */
  subscript?: boolean;

  /**
   * Font size
   */
  fontSize?: Dimension;

  /**
   * Foreground color
   */
  foregroundColor?: OptionalColor;

  /**
   * Background color
   */
  backgroundColor?: OptionalColor;

  /**
   * Font family
   */
  weightedFontFamily?: WeightedFontFamily;

  /**
   * Link
   */
  link?: Link;
}

/**
 * Interface cho paragraph style
 */
export interface ParagraphStyle {
  /**
   * Heading ID
   */
  headingId?: string;

  /**
   * Named style type
   */
  namedStyleType?: string;

  /**
   * Alignment
   */
  alignment?: string;

  /**
   * Line spacing
   */
  lineSpacing?: number;

  /**
   * Direction
   */
  direction?: string;

  /**
   * Spacing mode
   */
  spacingMode?: string;

  /**
   * Space above
   */
  spaceAbove?: Dimension;

  /**
   * Space below
   */
  spaceBelow?: Dimension;

  /**
   * Border between
   */
  borderBetween?: ParagraphBorder;

  /**
   * Border top
   */
  borderTop?: ParagraphBorder;

  /**
   * Border bottom
   */
  borderBottom?: ParagraphBorder;

  /**
   * Border left
   */
  borderLeft?: ParagraphBorder;

  /**
   * Border right
   */
  borderRight?: ParagraphBorder;

  /**
   * Indent first line
   */
  indentFirstLine?: Dimension;

  /**
   * Indent start
   */
  indentStart?: Dimension;

  /**
   * Indent end
   */
  indentEnd?: Dimension;

  /**
   * Tab stops
   */
  tabStops?: TabStop[];

  /**
   * Keep lines together
   */
  keepLinesTogether?: boolean;

  /**
   * Keep with next
   */
  keepWithNext?: boolean;

  /**
   * Avoid widow and orphan
   */
  avoidWidowAndOrphan?: boolean;

  /**
   * Shading
   */
  shading?: Shading;
}

/**
 * Interface cho dimension
 */
export interface Dimension {
  /**
   * Magnitude
   */
  magnitude: number;

  /**
   * Unit
   */
  unit: string;
}

/**
 * Interface cho color
 */
export interface OptionalColor {
  /**
   * Color
   */
  color?: DocsColor;
}

/**
 * Interface cho color
 */
export interface DocsColor {
  /**
   * RGB color
   */
  rgbColor?: RgbColor;
}

/**
 * Interface cho RGB color
 */
export interface RgbColor {
  /**
   * Red
   */
  red: number;

  /**
   * Green
   */
  green: number;

  /**
   * Blue
   */
  blue: number;
}

/**
 * Interface cho weighted font family
 */
export interface WeightedFontFamily {
  /**
   * Font family
   */
  fontFamily: string;

  /**
   * Weight
   */
  weight: number;
}

/**
 * Interface cho link
 */
export interface Link {
  /**
   * URL
   */
  url?: string;

  /**
   * Bookmark ID
   */
  bookmarkId?: string;

  /**
   * Heading ID
   */
  headingId?: string;
}

/**
 * Interface cho tạo document request
 */
export interface CreateDocumentRequest {
  /**
   * Tiêu đề document
   */
  title: string;
}

/**
 * Interface cho batch update request
 */
export interface BatchUpdateDocumentRequest {
  /**
   * Danh sách requests
   */
  requests: Request[];

  /**
   * Write control
   */
  writeControl?: WriteControl;
}

/**
 * Interface cho request
 */
export interface Request {
  /**
   * Insert text
   */
  insertText?: InsertTextRequest;

  /**
   * Delete content range
   */
  deleteContentRange?: DeleteContentRangeRequest;

  /**
   * Replace all text
   */
  replaceAllText?: ReplaceAllTextRequest;

  /**
   * Update text style
   */
  updateTextStyle?: UpdateTextStyleRequest;

  /**
   * Update paragraph style
   */
  updateParagraphStyle?: UpdateParagraphStyleRequest;

  /**
   * Insert table
   */
  insertTable?: InsertTableRequest;

  /**
   * Insert page break
   */
  insertPageBreak?: InsertPageBreakRequest;
}

/**
 * Interface cho insert text request
 */
export interface InsertTextRequest {
  /**
   * Location
   */
  location: Location;

  /**
   * Text
   */
  text: string;
}

/**
 * Interface cho location
 */
export interface Location {
  /**
   * Index
   */
  index: number;

  /**
   * Segment ID
   */
  segmentId?: string;
}

/**
 * Interface cho delete content range request
 */
export interface DeleteContentRangeRequest {
  /**
   * Range
   */
  range: Range;
}

/**
 * Interface cho range
 */
export interface Range {
  /**
   * Start index
   */
  startIndex: number;

  /**
   * End index
   */
  endIndex: number;

  /**
   * Segment ID
   */
  segmentId?: string;
}

/**
 * Interface cho replace all text request
 */
export interface ReplaceAllTextRequest {
  /**
   * Contains text
   */
  containsText: SubstringMatchCriteria;

  /**
   * Replace text
   */
  replaceText: string;
}

/**
 * Interface cho substring match criteria
 */
export interface SubstringMatchCriteria {
  /**
   * Text
   */
  text: string;

  /**
   * Match case
   */
  matchCase?: boolean;
}

/**
 * Các interface khác cần thiết
 */
export interface SectionBreak {
  sectionStyle?: SectionStyle;
}

export interface SectionStyle {
  columnSeparatorStyle?: string;
  contentDirection?: string;
}

export interface Table {
  rows: number;
  columns: number;
  tableRows?: TableRow[];
  tableStyle?: TableStyle;
}

export interface TableRow {
  startIndex: number;
  endIndex: number;
  tableCells: TableCell[];
  tableRowStyle?: TableRowStyle;
}

export interface TableCell {
  startIndex: number;
  endIndex: number;
  content: StructuralElement[];
  tableCellStyle?: TableCellStyle;
}

export interface TableStyle {
  tableColumnProperties?: TableColumnProperties[];
}

export interface TableColumnProperties {
  width?: Dimension;
  widthType?: string;
}

export interface TableRowStyle {
  minRowHeight?: Dimension;
}

export interface TableCellStyle {
  rowSpan?: number;
  columnSpan?: number;
  backgroundColor?: OptionalColor;
  borderLeft?: TableCellBorder;
  borderRight?: TableCellBorder;
  borderTop?: TableCellBorder;
  borderBottom?: TableCellBorder;
  paddingLeft?: Dimension;
  paddingRight?: Dimension;
  paddingTop?: Dimension;
  paddingBottom?: Dimension;
  contentAlignment?: string;
}

export interface TableCellBorder {
  color?: OptionalColor;
  width?: Dimension;
  dashStyle?: string;
}

export interface TableOfContents {
  content: StructuralElement[];
}

export interface Bullet {
  listId: string;
  nestingLevel?: number;
  textStyle?: TextStyle;
}

export interface AutoText {
  type: string;
  textStyle?: TextStyle;
}

export interface PageBreak {
  textStyle?: TextStyle;
}

export interface ColumnBreak {
  textStyle?: TextStyle;
}

export interface FootnoteReference {
  footnoteId: string;
  footnoteNumber: string;
  textStyle?: TextStyle;
}

export interface HorizontalRule {
  textStyle?: TextStyle;
}

export interface Equation {
  suggestedInsertionIds?: string[];
  suggestedDeletionIds?: string[];
}

export interface InlineObjectElement {
  inlineObjectId: string;
  textStyle?: TextStyle;
}

export interface ParagraphBorder {
  color?: OptionalColor;
  width?: Dimension;
  padding?: Dimension;
  dashStyle?: string;
}

export interface TabStop {
  offset: Dimension;
  alignment: string;
}

export interface Shading {
  backgroundColor?: OptionalColor;
}

export interface WriteControl {
  requiredRevisionId?: string;
  targetRevisionId?: string;
}

export interface UpdateTextStyleRequest {
  range: Range;
  textStyle: TextStyle;
  fields: string;
}

export interface UpdateParagraphStyleRequest {
  range: Range;
  paragraphStyle: ParagraphStyle;
  fields: string;
}

export interface InsertTableRequest {
  location: Location;
  rows: number;
  columns: number;
}

export interface InsertPageBreakRequest {
  location: Location;
}
