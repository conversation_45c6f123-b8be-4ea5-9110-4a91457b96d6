import { Logger } from '@nestjs/common';
import { LangGraphEventHandler } from '../interfaces/event-handler.interface';
import { EventProcessingContext } from '../schemas';

/**
 * Abstract base class for LangGraph event handlers
 * Provides common functionality and enforces the Strategy Pattern contract
 */
export abstract class BaseLangGraphEventHandler
  implements LangGraphEventHandler
{
  protected readonly logger = new Logger(this.constructor.name);

  /**
   * Check if this handler can process the given event
   * @param event - LangGraph event type
   * @param data - Event data
   * @param tags - Event tags
   * @returns True if handler can process this event
   */
  abstract canHandle(event: string, data: any, tags: string[]): boolean;

  /**
   * Process the event
   * @param context - Event processing context
   * @returns Promise that resolves when processing is complete
   */
  abstract handle(context: EventProcessingContext): Promise<void>;

  /**
   * Extract role from LangGraph event tags
   * @param tags - Array of tags from LangGraph event
   * @param roleTags - Array of role tags to match against
   * @returns Role string or null if not found
   */
  protected getRoleFromTags(tags: string[], roleTags: string[]): string | null {
    for (const tag of tags) {
      if (roleTags.includes(tag)) {
        return tag === 'supervisor' || tag === 'supervisor_tool_call'
          ? 'supervisor'
          : 'worker';
      }
    }
    return null;
  }

  /**
   * Safely truncate text for logging
   * @param text - Text to truncate
   * @param maxLength - Maximum length (default: 50)
   * @returns Truncated text with ellipsis if needed
   */
  protected truncateText(text: string, maxLength: number = 50): string {
    if (text.length <= maxLength) {
      return text;
    }
    return `${text.substring(0, maxLength)}...`;
  }

  /**
   * Make text safe for logging by escaping newlines
   * @param text - Text to make safe
   * @returns Safe text for logging
   */
  protected makeSafeForLogging(text: string): string {
    return text.replace(/\n/g, '\\n').replace(/\r/g, '\\r');
  }

  /**
   * Extract tool name from event data
   * @param data - Event data
   * @returns Tool name if available, undefined otherwise
   */
  protected extractToolName(data: any): string | undefined {
    return data && typeof data === 'object' && 'name' in data
      ? data.name
      : undefined;
  }

  /**
   * Log event processing with consistent format
   * @param emoji - Emoji for the log
   * @param message - Log message
   * @param context - Event context
   * @param additionalData - Additional data to log
   */
  protected logEvent(
    emoji: string,
    message: string,
    context: EventProcessingContext,
    additionalData?: Record<string, any>,
  ): void {
    this.logger.debug(`${emoji} ${message}`, {
      threadId: context.threadId,
      event: context.event,
      tags: context.tags,
      ...additionalData,
    });
  }

  /**
   * Handle errors in event processing
   * @param error - Error that occurred
   * @param context - Event context
   * @param operation - Operation that failed
   */
  protected handleError(
    error: Error,
    context: EventProcessingContext,
    operation: string,
  ): void {
    this.logger.error(`Failed to ${operation} in ${this.constructor.name}:`, {
      threadId: context.threadId,
      event: context.event,
      error: error.message,
      stack: error.stack,
    });
  }
}
