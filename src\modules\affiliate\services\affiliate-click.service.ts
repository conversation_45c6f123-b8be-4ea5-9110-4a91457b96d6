import { Injectable, Logger } from '@nestjs/common';
import { Queue } from 'bullmq';
import { InjectQueue } from '@nestjs/bullmq';
import { QueueName } from '../../../queue';
import { AffiliateClickRepository } from '../repositories/affiliate-click.repository';
import { AffiliateClick } from '../entities/affiliate-click.entity';
import {
  AffiliateClickJobData,
  AffiliateClickBatchJobData,
} from '../dto/affiliate-click-job.dto';
import { AffiliateJobName } from '../constants';

/**
 * Service xử lý logic liên quan đến affiliate click
 */
@Injectable()
export class AffiliateClickService {
  private readonly logger = new Logger(AffiliateClickService.name);
  private pendingClicks: AffiliateClick[] = [];
  private batchTimer: NodeJS.Timeout | null = null;
  private readonly BATCH_SIZE = 100; // Số lượng tối đa trong một batch
  private readonly BATCH_TIMEOUT = 30000; // 30 giây

  constructor(
    @InjectQueue(QueueName.AFFILIATE_CLICK)
    private readonly affiliateClickQueue: Queue,
    private readonly affiliateClickRepository: AffiliateClickRepository,
  ) {}

  /**
   * Thêm một affiliate click vào hàng đợi
   * @param clickData Dữ liệu affiliate click
   */
  async addAffiliateClick(clickData: {
    affiliateAccountId: number;
    referralCode: string;
    ipAddress?: string;
    userAgent?: string;
    referrerUrl?: string;
    landingPage?: string;
    clickTime?: number;
  }): Promise<void> {
    // Đảm bảo clickTime luôn có giá trị
    if (!clickData.clickTime) {
      clickData.clickTime = Date.now();
    }

    // Thêm vào queue để xử lý
    await this.affiliateClickQueue.add('affiliate-click', {
      clickData,
    } as AffiliateClickJobData);

    this.logger.log(
      `Đã thêm affiliate click vào queue - Account: ${clickData.affiliateAccountId}, Code: ${clickData.referralCode}`,
    );
  }

  /**
   * Xử lý đối tượng affiliate click đã nhận từ queue
   * @param click Đối tượng affiliate click
   */
  async processAffiliateClick(click: AffiliateClick): Promise<void> {
    try {
      // Thêm click vào danh sách đang chờ xử lý
      this.pendingClicks.push(click);

      this.logger.debug(
        `Đã thêm affiliate click vào pending list - Total: ${this.pendingClicks.length}`,
      );

      // Nếu đã đủ số lượng hoặc đây là click đầu tiên (cần setup timer)
      if (this.pendingClicks.length >= this.BATCH_SIZE) {
        // Xử lý batch ngay lập tức nếu đạt đủ số lượng
        this.processBatch();
      } else if (this.pendingClicks.length === 1) {
        // Nếu đây là click đầu tiên, setup timer
        this.setupBatchTimer();
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý affiliate click: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Thiết lập timer để xử lý batch sau một khoảng thời gian
   */
  private setupBatchTimer(): void {
    // Clear timer cũ nếu có
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
    }

    // Thiết lập timer mới
    this.batchTimer = setTimeout(() => {
      if (this.pendingClicks.length > 0) {
        this.processBatch();
      }
    }, this.BATCH_TIMEOUT);
  }

  /**
   * Xử lý batch affiliate click hiện tại
   */
  private async processBatch(): Promise<void> {
    if (this.pendingClicks.length === 0) return;

    // Clear timer nếu đang có
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    const clicksToProcess = [...this.pendingClicks];
    this.pendingClicks = []; // Reset danh sách chờ

    this.logger.log(
      `Đang xử lý batch ${clicksToProcess.length} affiliate clicks`,
    );

    // Thêm job batch vào queue
    await this.affiliateClickQueue.add(
      AffiliateJobName.AFFILIATE_CLICK_BATCH,
      {
        clicks: clicksToProcess,
        timestamp: Date.now(),
      } as AffiliateClickBatchJobData,
      {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 5000, // 5 giây
        },
      },
    );
  }

  /**
   * Lưu batch affiliate click vào database
   * @param clicks Danh sách affiliate click cần lưu
   */
  async saveBatch(clicks: AffiliateClick[]): Promise<void> {
    try {
      this.logger.log(
        `Bắt đầu lưu batch gồm ${clicks.length} affiliate clicks vào database`,
      );

      // Lưu batch vào database
      await this.affiliateClickRepository.saveBatch(clicks);

      this.logger.log(
        `Đã lưu thành công ${clicks.length} affiliate clicks vào database`,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi lưu batch affiliate clicks: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
