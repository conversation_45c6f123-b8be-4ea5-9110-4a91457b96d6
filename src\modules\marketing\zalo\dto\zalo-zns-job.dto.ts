import {
  IsString,
  IsOptional,
  IsObject,
  Is<PERSON><PERSON>,
  <PERSON>A<PERSON><PERSON>,
  <PERSON>N<PERSON><PERSON>,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Enum cho loại tin nhắn ZNS
 */
export enum ZnsMessageType {
  TRANSACTION = 'TRANSACTION',
  CUSTOMER_CARE = 'CUSTOMER_CARE',
  PROMOTION = 'PROMOTION',
}

/**
 * DTO cho job gửi ZNS đơn lẻ
 */
export class ZaloZnsSingleJobDto {
  @ApiProperty({ description: 'ID Official Account' })
  @IsString()
  oaId: string;

  @ApiProperty({ description: 'Số điện thoại nhận' })
  @IsString()
  phone: string;

  @ApiProperty({ description: 'ID template ZNS' })
  @IsString()
  templateId: string;

  @ApiProperty({ description: 'Dữ liệu cho template' })
  @IsObject()
  templateData: Record<string, any>;

  @ApiPropertyOptional({ description: 'ID chiến dịch (nếu có)' })
  @IsOptional()
  @IsNumber()
  campaignId?: number;

  @ApiPropertyOptional({ description: 'Tracking ID (tùy chọn)' })
  @IsOptional()
  @IsString()
  trackingId?: string;

  @ApiPropertyOptional({ description: 'Loại tin nhắn', enum: ZnsMessageType })
  @IsOptional()
  @IsEnum(ZnsMessageType)
  messageType?: ZnsMessageType;

  @ApiPropertyOptional({ description: 'Số lần retry' })
  @IsOptional()
  @IsNumber()
  retryCount?: number;
}

/**
 * DTO cho job gửi ZNS theo chiến dịch
 */
export class ZaloZnsCampaignJobDto {
  @ApiProperty({ description: 'ID chiến dịch' })
  @IsNumber()
  campaignId: number;

  @ApiProperty({ description: 'ID Official Account' })
  @IsString()
  oaId: string;

  @ApiProperty({ description: 'ID template ZNS' })
  @IsString()
  templateId: string;

  @ApiProperty({ description: 'Dữ liệu cho template' })
  @IsObject()
  templateData: Record<string, any>;

  @ApiProperty({ description: 'Danh sách số điện thoại' })
  @IsArray()
  @IsString({ each: true })
  phoneList: string[];

  @ApiPropertyOptional({ description: 'Loại tin nhắn', enum: ZnsMessageType })
  @IsOptional()
  @IsEnum(ZnsMessageType)
  messageType?: ZnsMessageType;

  @ApiPropertyOptional({ description: 'Batch size (số tin nhắn gửi cùng lúc)' })
  @IsOptional()
  @IsNumber()
  batchSize?: number;

  @ApiPropertyOptional({ description: 'Delay giữa các batch (milliseconds)' })
  @IsOptional()
  @IsNumber()
  batchDelay?: number;
}

/**
 * DTO cho job gửi batch ZNS
 */
export class ZaloZnsBatchJobDto {
  @ApiProperty({ description: 'ID Official Account' })
  @IsString()
  oaId: string;

  @ApiProperty({ description: 'Danh sách tin nhắn ZNS' })
  @IsArray()
  messages: {
    phone: string;
    templateId: string;
    templateData: Record<string, any>;
    trackingId?: string;
    messageType?: ZnsMessageType;
  }[];

  @ApiPropertyOptional({ description: 'ID chiến dịch (nếu có)' })
  @IsOptional()
  @IsNumber()
  campaignId?: number;

  @ApiPropertyOptional({ description: 'Batch index (thứ tự batch)' })
  @IsOptional()
  @IsNumber()
  batchIndex?: number;

  @ApiPropertyOptional({ description: 'Tổng số batch' })
  @IsOptional()
  @IsNumber()
  totalBatches?: number;
}
