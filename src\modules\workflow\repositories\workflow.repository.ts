import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, FindManyOptions } from 'typeorm';
import { Workflow } from '../entities/workflow.entity';
import { IWorkflowSettings } from '../interfaces/workflow.interface';

/**
 * Repository cho Workflow entity
 * Chứa các method CRUD và business logic cho workflows
 */
@Injectable()
export class WorkflowRepository {
  constructor(
    @InjectRepository(Workflow)
    private readonly repository: Repository<Workflow>,
  ) {}

  /**
   * Tạo workflow mới
   */
  async create(workflowData: Partial<Workflow>): Promise<Workflow> {
    const workflow = this.repository.create({
      ...workflowData,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    return await this.repository.save(workflow);
  }

  /**
   * Tìm workflow theo ID
   */
  async findById(id: string): Promise<Workflow | null> {
    return await this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm tất cả workflows
   */
  async findAll(options?: FindManyOptions<Workflow>): Promise<Workflow[]> {
    return await this.repository.find(options);
  }

  /**
   * Tìm workflows theo user ID
   */
  async findByUserId(userId: number): Promise<Workflow[]> {
    return await this.repository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tìm workflows theo employee ID
   */
  async findByEmployeeId(employeeId: number): Promise<Workflow[]> {
    return await this.repository.find({
      where: { employeeId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tìm workflows active
   */
  async findActiveWorkflows(): Promise<Workflow[]> {
    return await this.repository.find({
      where: { isActive: true },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tìm workflows theo tên
   */
  async findByName(name: string): Promise<Workflow[]> {
    return await this.repository
      .createQueryBuilder('workflow')
      .where('workflow.name ILIKE :name', { name: `%${name}%` })
      .orderBy('workflow.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Cập nhật workflow
   */
  async update(id: string, updateData: Partial<Workflow>): Promise<Workflow | null> {
    const updateResult = await this.repository.update(id, {
      ...updateData,
      updatedAt: Date.now(),
    });

    if (updateResult.affected === 0) {
      return null;
    }

    return await this.findById(id);
  }

  /**
   * Cập nhật settings của workflow
   */
  async updateSettings(id: string, settings: IWorkflowSettings): Promise<Workflow | null> {
    return await this.update(id, { settings });
  }

  /**
   * Kích hoạt/vô hiệu hóa workflow
   */
  async toggleActive(id: string, isActive: boolean): Promise<Workflow | null> {
    return await this.update(id, { isActive });
  }

  /**
   * Xóa workflow
   */
  async delete(id: string): Promise<boolean> {
    const deleteResult = await this.repository.delete(id);
    return (deleteResult.affected || 0) > 0;
  }

  /**
   * Xóa mềm workflow (đánh dấu là inactive)
   */
  async softDelete(id: string): Promise<Workflow | null> {
    return await this.update(id, { isActive: false });
  }

  /**
   * Đếm số lượng workflows
   */
  async count(where?: FindOptionsWhere<Workflow>): Promise<number> {
    return await this.repository.count({ where });
  }

  /**
   * Đếm workflows theo user
   */
  async countByUserId(userId: number): Promise<number> {
    return await this.repository.count({
      where: { userId },
    });
  }

  /**
   * Đếm workflows active
   */
  async countActiveWorkflows(): Promise<number> {
    return await this.repository.count({
      where: { isActive: true },
    });
  }

  /**
   * Tìm workflows được tạo trong khoảng thời gian
   */
  async findByDateRange(startDate: number, endDate: number): Promise<Workflow[]> {
    return await this.repository
      .createQueryBuilder('workflow')
      .where('workflow.createdAt >= :startDate', { startDate })
      .andWhere('workflow.createdAt <= :endDate', { endDate })
      .orderBy('workflow.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Tìm workflows với pagination
   */
  async findWithPagination(
    page: number = 1,
    limit: number = 10,
    where?: FindOptionsWhere<Workflow>,
  ): Promise<{ workflows: Workflow[]; total: number; totalPages: number }> {
    const skip = (page - 1) * limit;
    
    const [workflows, total] = await this.repository.findAndCount({
      where,
      skip,
      take: limit,
      order: { createdAt: 'DESC' },
    });

    const totalPages = Math.ceil(total / limit);

    return {
      workflows,
      total,
      totalPages,
    };
  }

  /**
   * Bulk update workflows
   */
  async bulkUpdate(ids: string[], updateData: Partial<Workflow>): Promise<number> {
    const updateResult = await this.repository
      .createQueryBuilder()
      .update(Workflow)
      .set({
        ...updateData,
        updatedAt: Date.now(),
      })
      .whereInIds(ids)
      .execute();

    return updateResult.affected || 0;
  }

  /**
   * Bulk delete workflows
   */
  async bulkDelete(ids: string[]): Promise<number> {
    const deleteResult = await this.repository
      .createQueryBuilder()
      .delete()
      .from(Workflow)
      .whereInIds(ids)
      .execute();

    return deleteResult.affected || 0;
  }
}
