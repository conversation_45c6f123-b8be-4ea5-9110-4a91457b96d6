# Zalo GMF Group Management Service

## Tổng quan

`ZaloGmfGroupManagementService` là service xử lý các API quản lý nhóm chat của Zalo Official Account thông qua Group Management Framework (GMF). Service này cung cấp đầy đủ các chức năng quản lý nhóm từ tạo, c<PERSON><PERSON>h<PERSON>, quản lý thành viên đến xóa nhóm.

## Điều kiện sử dụng

### Điều kiện chung
- OA phải được cấp quyền sử dụng tính năng GMF (Group Message Framework)
- Access token phải có scope "manage_group" và "group_message"
- OA phải có trạng thái hoạt động và được xác thực
- <PERSON><PERSON> thủ các giới hạn về số lượng nhóm và thành viên

### Giới hạn và ràng buộc
- **<PERSON><PERSON> nhóm tối đa**: theo g<PERSON><PERSON> d<PERSON> vụ (thường 10-100 nhóm)
- **<PERSON><PERSON> thành viên tối đa mỗi nhóm**: 200 người
- **Tần suất tạo nhóm**: tối đa 10 nhóm/ngày
- **Tần suất mời thành viên**: tối đa 500 lời mời/ngày

## API Methods

### 1. Tạo nhóm mới

```typescript
async createGroup(
  accessToken: string,
  groupData: ZaloCreateGroupRequest,
): Promise<ZaloCreateGroupResult>
```

**Điều kiện:**
- Tên nhóm không được trống và tối đa 100 ký tự
- Danh sách thành viên ban đầu tối đa 200 người
- Tất cả thành viên phải đã tương tác với OA trước đó
- Chưa vượt quá giới hạn số nhóm cho phép

**Ví dụ:**
```typescript
const groupData: ZaloCreateGroupRequest = {
  group_name: "Nhóm hỗ trợ khách hàng",
  description: "Nhóm hỗ trợ khách hàng VIP",
  avatar_url: "https://example.com/avatar.jpg",
  member_uids: ["user1", "user2", "user3"]
};

const result = await gmfService.createGroup(accessToken, groupData);
console.log(`Nhóm đã tạo với ID: ${result.group_id}`);
```

### 2. Lấy thông tin nhóm

```typescript
async getGroupInfo(
  accessToken: string,
  groupId: string,
): Promise<ZaloGroupInfo>
```

**Điều kiện:**
- OA phải là thành viên của nhóm
- Nhóm phải đang hoạt động (không bị xóa)

### 3. Cập nhật thông tin nhóm

```typescript
async updateGroupInfo(
  accessToken: string,
  groupId: string,
  updateData: ZaloUpdateGroupRequest,
): Promise<{ success: boolean }>
```

**Điều kiện:**
- OA phải là admin của nhóm
- Tên nhóm mới (nếu có) tối đa 100 ký tự
- Mô tả mới (nếu có) tối đa 500 ký tự

### 4. Cập nhật avatar nhóm

```typescript
async updateGroupAvatar(
  accessToken: string,
  groupId: string,
  avatarData: ZaloUpdateGroupAvatarRequest,
): Promise<{ success: boolean }>
```

**Điều kiện:**
- OA phải là admin của nhóm
- Avatar phải được upload trước qua API upload
- Định dạng: JPG, PNG
- Kích thước tối đa: 5MB

### 5. Mời thành viên vào nhóm

```typescript
async inviteMembers(
  accessToken: string,
  groupId: string,
  inviteData: ZaloInviteMemberRequest,
): Promise<{ success: boolean; invited_count: number }>
```

**Điều kiện:**
- OA phải là admin của nhóm
- Tối đa 50 người mỗi lần mời
- Tất cả user được mời phải đã tương tác với OA
- Nhóm chưa đạt giới hạn tối đa thành viên (200 người)

### 6. Quản lý thành viên chờ duyệt

#### Lấy danh sách thành viên chờ duyệt
```typescript
async getPendingMembers(
  accessToken: string,
  groupId: string,
  offset?: number,
  count?: number,
): Promise<{ members: ZaloPendingMember[]; total: number }>
```

#### Duyệt thành viên
```typescript
async acceptMembers(
  accessToken: string,
  groupId: string,
  memberData: ZaloMemberActionRequest,
): Promise<{ success: boolean; accepted_count: number }>
```

#### Từ chối thành viên
```typescript
async rejectMembers(
  accessToken: string,
  groupId: string,
  memberData: ZaloMemberActionRequest,
): Promise<{ success: boolean; rejected_count: number }>
```

### 7. Quản lý admin

#### Thêm quyền admin
```typescript
async addAdmins(
  accessToken: string,
  groupId: string,
  adminData: ZaloAdminActionRequest,
): Promise<{ success: boolean; added_count: number }>
```

#### Xóa quyền admin
```typescript
async removeAdmins(
  accessToken: string,
  groupId: string,
  adminData: ZaloAdminActionRequest,
): Promise<{ success: boolean; removed_count: number }>
```

**Điều kiện:**
- Không thể xóa quyền admin của chính mình
- Phải còn ít nhất 1 admin sau khi xóa
- Không thể xóa quyền admin của OA

### 8. Xóa thành viên

```typescript
async removeMembers(
  accessToken: string,
  groupId: string,
  memberData: ZaloRemoveMemberRequest,
): Promise<{ success: boolean; removed_count: number }>
```

**Điều kiện:**
- OA phải là admin của nhóm
- Không thể xóa admin khác (phải xóa quyền admin trước)
- Không thể xóa chính mình
- Không thể xóa OA khỏi nhóm

### 9. Xóa nhóm

```typescript
async deleteGroup(
  accessToken: string,
  groupId: string,
): Promise<{ success: boolean }>
```

**Điều kiện:**
- OA phải là admin của nhóm
- Chỉ có thể xóa nhóm do OA tạo ra
- Tất cả dữ liệu nhóm sẽ bị xóa vĩnh viễn

### 10. Lấy danh sách nhóm của OA

```typescript
async getGroupsOfOA(
  accessToken: string,
  offset?: number,
  count?: number,
): Promise<{ groups: ZaloGroupInfo[]; total: number }>
```

### 11. Lấy thông tin hạn mức

```typescript
async getGroupQuota(
  accessToken: string,
): Promise<ZaloGroupQuota>
```

### 12. Lấy cuộc trò chuyện gần đây

```typescript
async getRecentChats(
  accessToken: string,
  offset?: number,
  count?: number,
): Promise<{ chats: ZaloRecentChat[]; total: number }>
```

### 13. Lấy lịch sử tin nhắn nhóm

```typescript
async getGroupConversation(
  accessToken: string,
  groupId: string,
  offset?: number,
  count?: number,
  fromTime?: number,
  toTime?: number,
): Promise<{ messages: ZaloGroupConversationMessage[]; total: number }>
```

## Xử lý lỗi

Service sử dụng `AppException` để xử lý lỗi với các mã lỗi:
- `VALIDATION_ERROR`: Lỗi validation dữ liệu đầu vào
- `EXTERNAL_SERVICE_ERROR`: Lỗi từ Zalo API

## Ví dụ sử dụng đầy đủ

```typescript
import { ZaloGmfGroupManagementService } from '@/shared/services/zalo';

@Injectable()
export class GroupManagementController {
  constructor(
    private readonly gmfService: ZaloGmfGroupManagementService,
  ) {}

  async createAndManageGroup() {
    const accessToken = 'your_access_token';
    
    // 1. Tạo nhóm mới
    const newGroup = await this.gmfService.createGroup(accessToken, {
      group_name: "Nhóm hỗ trợ",
      description: "Nhóm hỗ trợ khách hàng",
      member_uids: ["user1", "user2"]
    });
    
    // 2. Mời thêm thành viên
    await this.gmfService.inviteMembers(accessToken, newGroup.group_id, {
      member_uids: ["user3", "user4"]
    });
    
    // 3. Thêm admin
    await this.gmfService.addAdmins(accessToken, newGroup.group_id, {
      admin_uids: ["user1"]
    });
    
    // 4. Lấy thông tin nhóm
    const groupInfo = await this.gmfService.getGroupInfo(accessToken, newGroup.group_id);
    
    return groupInfo;
  }
}
```

## Tài liệu tham khảo

- [Zalo GMF Create Group](https://developers.zalo.me/docs/official-account/nhom-chat-gmf/quan-ly/create_group)
- [Zalo GMF Group Management](https://developers.zalo.me/docs/official-account/nhom-chat-gmf/quan-ly)
- [Zalo Official Account Documentation](https://developers.zalo.me/docs/official-account)
