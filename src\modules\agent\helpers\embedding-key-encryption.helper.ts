import * as crypto from 'crypto';
import { env } from '../../../config';

/**
 * @file Encryption helper for embedding API keys
 * This module provides stateless functions for encrypting and decrypting API keys
 * specifically used for OpenAI embedding operations. Uses the same algorithm as
 * api-key-encryption.helper.ts but with a different encryption key for security isolation.
 */

// --- Configuration and Constants ---

const ALGORITHM: string = 'aes-256-cbc';
const ENCODING: BufferEncoding = 'hex';
const TEXT_ENCODING: crypto.Encoding = 'utf8';

// Embedding-specific encryption key (different from LLM API keys)
const EMBEDDING_SECRET: string | undefined =
  env.embeddingSystemEncryptionKey?.EMBEDDING_SECRET_KEY;

/**
 * The core decryption function for embedding keys
 * @param {string} encryptedText - The IV-prefixed encrypted string
 * @param {string} secret - The secret used to derive the decryption key
 * @returns {string} The original decrypted string
 */
const decrypt = (encryptedText: string, secret: string): string => {
  // Create key from secretKey by hashing with SHA-256
  const key = crypto
    .createHash('sha256')
    .update(secret)
    .digest('base64')
    .substring(0, 32);

  // Split IV and encrypted string
  const textParts = encryptedText.split(':');
  const iv = Buffer.from(textParts[0], 'hex');
  const encryptedData = textParts[1];

  // Create decipher
  const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);

  // Decrypt
  let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
  decrypted += decipher.final('utf8');

  return decrypted;
};

/**
 * Encrypt an embedding API key (for storing new keys)
 * @param {string} plainText - The plain text API key
 * @param {string} secret - The secret used to derive the encryption key
 * @returns {string} The encrypted string with IV prefix
 */
const encrypt = (plainText: string, secret: string): string => {
  // Create key from secretKey by hashing with SHA-256
  const key = crypto
    .createHash('sha256')
    .update(secret)
    .digest('base64')
    .substring(0, 32);

  // Generate random IV
  const iv = crypto.randomBytes(16);

  // Create cipher
  const cipher = crypto.createCipheriv(ALGORITHM, key, iv);

  // Encrypt
  let encrypted = cipher.update(plainText, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  // Return IV:encrypted format
  return `${iv.toString('hex')}:${encrypted}`;
};

/**
 * Decrypts an embedding API key
 * @param {string} encryptedApiKey The encrypted API key
 * @returns {string} The original API key
 * @throws {Error} If the embedding secret is not configured
 */
const decryptEmbeddingApiKey = (encryptedApiKey: string): string => {
  if (!EMBEDDING_SECRET) {
    throw new Error('EMBEDDING_SECRET_KEY is not configured.');
  }
  return decrypt(encryptedApiKey, EMBEDDING_SECRET);
};

/**
 * Encrypts an embedding API key
 * @param {string} plainApiKey The plain text API key
 * @returns {string} The encrypted API key
 * @throws {Error} If the embedding secret is not configured
 */
const encryptEmbeddingApiKey = (plainApiKey: string): string => {
  if (!EMBEDDING_SECRET) {
    throw new Error('EMBEDDING_SECRET_KEY is not configured.');
  }
  return encrypt(plainApiKey, EMBEDDING_SECRET);
};

/**
 * Exporting the public functions for embedding key encryption
 */
export const embeddingKeyEncryption = {
  decryptEmbeddingApiKey,
  encryptEmbeddingApiKey,
};
