import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { SenderTypeEnum } from '../enums/sender-type.enum';

/**
 * Entity đại diện cho bảng sms_marketing_history trong cơ sở dữ liệu
 * Lư<PERSON> trữ lịch sử gửi SMS marketing với thông tin chi tiết từ FPT SMS API
 */
@Entity('sms_marketing_history')
export class SmsMarketingHistory {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của campaign SMS marketing
   */
  @Column({ name: 'campaign_id', type: 'integer' })
  campaignId: number;

  /**
   * ID của tin nhắn đã gửi (từ FPT SMS API)
   */
  @Column({ name: 'message_id', type: 'varchar', length: 100, nullable: true })
  messageId: string | null;

  /**
   * <PERSON>ố điện thoại đã gửi tin
   */
  @Column({ name: 'phone', type: 'varchar', length: 20 })
  phone: string;

  /**
   * Tên brandname đã gửi tin
   */
  @Column({ name: 'brand_name', type: 'varchar', length: 100, nullable: true })
  brandName: string | null;

  /**
   * Nội dung tin nhắn đã gửi
   */
  @Column({ name: 'message', type: 'text' })
  message: string;

  /**
   * ID của đối tác đã gửi tin (từ FPT SMS API)
   */
  @Column({ name: 'partner_id', type: 'varchar', length: 100, nullable: true })
  partnerId: string | null;

  /**
   * Nhà mạng của số điện thoại trên (từ FPT SMS API)
   */
  @Column({ name: 'telco', type: 'varchar', length: 50, nullable: true })
  telco: string | null;

  /**
   * Loại chiến dịch SMS (OTP hoặc CAMPAIGN_ADS)
   */
  @Column({ name: 'campaign_type', type: 'varchar', length: 20 })
  campaignType: string;

  /**
   * Trạng thái gửi SMS (SUCCESS, FAILED)
   */
  @Column({ name: 'status', type: 'varchar', length: 20 })
  status: string;

  /**
   * Thông báo lỗi nếu gửi thất bại
   */
  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage: string | null;

  /**
   * Mã lỗi từ FPT SMS API
   */
  @Column({ name: 'error_code', type: 'integer', nullable: true })
  errorCode: number | null;

  /**
   * Kiểm tra số điện thoại có phải Việt Nam hay quốc tế
   */
  @Column({ name: 'is_vietnamese_number', type: 'boolean', default: true })
  isVietnameseNumber: boolean;

  /**
   * Thời gian gửi SMS (Unix timestamp)
   */
  @Column({ name: 'sent_at', type: 'bigint' })
  sentAt: number;

  /**
   * Thời gian tạo bản ghi (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Loại người gửi SMS (USER hoặc EMPLOYEE)
   */
  @Column({
    name: 'sender_type',
    type: 'enum',
    enum: SenderTypeEnum,
    nullable: true
  })
  senderType: SenderTypeEnum | null;

  /**
   * ID của người gửi SMS (user_id hoặc employee_id)
   */
  @Column({ name: 'sender_id', type: 'integer', nullable: true })
  senderId: number | null;
}
