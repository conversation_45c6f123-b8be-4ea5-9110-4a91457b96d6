import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ZaloVideoUpload } from './entities/zalo-video-upload.entity';
import { ZaloVideoUploadRepository } from './repositories/zalo-video-upload.repository';

/**
 * Module marketing trong worker
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([ZaloVideoUpload]),
  ],
  providers: [ZaloVideoUploadRepository],
  exports: [ZaloVideoUploadRepository],
})
export class MarketingModule {}
