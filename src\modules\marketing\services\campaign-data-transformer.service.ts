import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserAudience } from '../entities/user-audience.entity';
import { UserSegment } from '../entities/user-segment.entity';
import { CampaignAudience, CampaignSegment } from '../types/campaign.types';

/**
 * Service để chuyển đổi dữ liệu campaign từ ID sang object đầ<PERSON> đủ (Worker)
 */
@Injectable()
export class CampaignDataTransformerService {
  private readonly logger = new Logger(CampaignDataTransformerService.name);

  constructor(
    @InjectRepository(UserAudience)
    private readonly userAudienceRepository: Repository<UserAudience>,
    @InjectRepository(UserSegment)
    private readonly userSegmentRepository: Repository<UserSegment>,
  ) {}

  /**
   * Chuyển đổi danh sách audience ID thành danh sách audience object
   * @param audienceIds Danh sách ID của audience
   * @returns Danh sách audience object với id, name, email
   */
  async transformAudienceIds(audienceIds: number[]): Promise<CampaignAudience[]> {
    try {
      if (!audienceIds || audienceIds.length === 0) {
        return [];
      }

      const audiences = await this.userAudienceRepository
        .createQueryBuilder('audience')
        .select(['audience.id', 'audience.name', 'audience.email'])
        .where('audience.id IN (:...ids)', { ids: audienceIds })
        .getMany();

      return audiences.map(audience => ({
        name: audience.name,
        email: audience.email,
      }));
    } catch (error) {
      this.logger.error(`Error transforming audience IDs: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Chuyển đổi segment ID thành segment object
   * @param segmentId ID của segment
   * @returns Segment object với id, name, description
   */
  async transformSegmentId(segmentId: number): Promise<CampaignSegment | null> {
    try {
      if (!segmentId) {
        return null;
      }

      const segment = await this.userSegmentRepository
        .createQueryBuilder('segment')
        .select(['segment.id', 'segment.name', 'segment.description'])
        .where('segment.id = :id', { id: segmentId })
        .getOne();

      if (!segment) {
        this.logger.warn(`Segment with ID ${segmentId} not found`);
        return null;
      }

      return {
        id: segment.id,
        name: segment.name,
        description: segment.description,
      };
    } catch (error) {
      this.logger.error(`Error transforming segment ID: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Chuyển đổi audience ID thành audience object (cho history)
   * @param audienceId ID của audience
   * @returns Audience object với id, name, email
   */
  async transformAudienceId(audienceId: number): Promise<CampaignAudience | null> {
    try {
      if (!audienceId) {
        return null;
      }

      const audience = await this.userAudienceRepository
        .createQueryBuilder('audience')
        .select(['audience.id', 'audience.name', 'audience.email'])
        .where('audience.id = :id', { id: audienceId })
        .getOne();

      if (!audience) {
        this.logger.warn(`Audience with ID ${audienceId} not found`);
        return null;
      }

      return {
        name: audience.name,
        email: audience.email,
      };
    } catch (error) {
      this.logger.error(`Error transforming audience ID: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Lấy danh sách email từ audiences
   * @param audiences Danh sách audience
   * @returns Danh sách email
   */
  extractEmailsFromAudiences(audiences: CampaignAudience[]): string[] {
    if (!audiences || audiences.length === 0) {
      return [];
    }

    return audiences
      .map(audience => audience.email)
      .filter(email => email && email.trim() !== '');
  }

  /**
   * Lấy danh sách email từ audiences (duplicate method - kept for compatibility)
   * @param audiences Danh sách audience
   * @returns Danh sách email
   */
  extractEmailsFromAudiencesAlt(audiences: CampaignAudience[]): string[] {
    return this.extractEmailsFromAudiences(audiences);
  }

  /**
   * Tìm audience theo email
   * @param audiences Danh sách audience
   * @param email Email cần tìm
   * @returns Audience object hoặc null
   */
  findAudienceByEmail(audiences: CampaignAudience[], email: string): CampaignAudience | null {
    if (!audiences || audiences.length === 0 || !email) {
      return null;
    }

    return audiences.find(audience => audience.email === email) || null;
  }

  /**
   * Validate audience data
   * @param audiences Danh sách audience để validate
   * @returns True nếu tất cả audience hợp lệ
   */
  async validateAudiences(audiences: CampaignAudience[]): Promise<boolean> {
    try {
      if (!audiences || audiences.length === 0) {
        return true;
      }

      const audienceEmails = audiences.map(a => a.email);
      const existingAudiences = await this.userAudienceRepository
        .createQueryBuilder('audience')
        .select(['audience.email'])
        .where('audience.email IN (:...emails)', { emails: audienceEmails })
        .getMany();

      const existingEmails = existingAudiences.map(a => a.email);
      const missingEmails = audienceEmails.filter(email => !existingEmails.includes(email));

      if (missingEmails.length > 0) {
        this.logger.warn(`Audiences with emails ${missingEmails.join(', ')} not found`);
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(`Error validating audiences: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Validate segment data
   * @param segment Segment để validate
   * @returns True nếu segment hợp lệ
   */
  async validateSegment(segment: CampaignSegment): Promise<boolean> {
    try {
      if (!segment) {
        return true;
      }

      const existingSegment = await this.userSegmentRepository
        .createQueryBuilder('segment')
        .select(['segment.id'])
        .where('segment.id = :id', { id: segment.id })
        .getOne();

      if (!existingSegment) {
        this.logger.warn(`Segment with ID ${segment.id} not found`);
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(`Error validating segment: ${error.message}`, error.stack);
      return false;
    }
  }
}
