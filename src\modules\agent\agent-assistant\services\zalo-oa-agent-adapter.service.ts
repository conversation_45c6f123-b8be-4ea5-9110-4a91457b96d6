import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Integration } from '../../../../shared/entities/integration.entity';
import { IntegrationProvider } from '../../../../shared/entities/integration-provider.entity';

/**
 * Interface cho Zalo OA metadata trong Integration
 */
interface ZaloOAMetadata {
  oaId: string;
  name: string;
  description?: string;
  avatarUrl?: string;
  expiresAt: number;
  status: string;
  createdAt: number;
  updatedAt: number;
}

/**
 * Adapter service để tương thích với ZaloOfficialAccount entity cũ trong agent module
 * Truy vấn dữ liệu từ Integration table thay vì zalo_official_account table
 */
@Injectable()
export class ZaloOAAgentAdapterService {
  private readonly logger = new Logger(ZaloOAAgentAdapterService.name);

  constructor(
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
    @InjectRepository(IntegrationProvider)
    private readonly integrationProviderRepository: Repository<IntegrationProvider>,
  ) {}

  /**
   * Get OA by OA ID (tương thích với getOaByOaId)
   */
  async getOaByOaId(oaId: string): Promise<any | null> {
    try {
      // Tìm provider ZALO_OA
      const provider = await this.integrationProviderRepository.findOne({
        where: { type: 'ZALO_OA' as any }
      });

      if (!provider) {
        this.logger.error('ZALO_OA provider not found');
        return null;
      }

      // Tìm integration theo oaId trong metadata với status active
      const integration = await this.integrationRepository
        .createQueryBuilder('integration')
        .where('integration.typeId = :typeId', { typeId: provider.id })
        .andWhere("integration.metadata->>'oaId' = :oaId", { oaId })
        .andWhere("integration.metadata->>'status' = :status", { status: 'active' })
        .getOne();

      if (!integration) {
        return null;
      }

      return this.mapIntegrationToZaloOA(integration);
    } catch (error) {
      this.logger.error(`Error finding Zalo OA by oaId ${oaId}:`, error);
      return null;
    }
  }

  /**
   * Check if access token is valid (not expired)
   * @param oa OA data to check
   * @returns true if token is valid
   */
  isTokenValid(oa: any): boolean {
    if (!oa.accessToken || oa.accessToken === '[ENCRYPTED]') {
      // Với encrypted token, chỉ check expiry time
      const now = Date.now();
      return oa.expiresAt > now;
    }

    const now = Date.now();
    return oa.expiresAt > now;
  }

  /**
   * Map Integration entity sang format ZaloOfficialAccount cũ
   */
  private mapIntegrationToZaloOA(integration: Integration): any {
    const metadata = integration.metadata as any as ZaloOAMetadata;

    return {
      id: integration.id,
      userId: integration.userId,
      employeeId: integration.employeeId,
      oaId: metadata.oaId,
      name: metadata.name,
      description: metadata.description,
      avatarUrl: metadata.avatarUrl,
      // accessToken và refreshToken không trả về trực tiếp vì đã mã hóa
      accessToken: '[ENCRYPTED]',
      refreshToken: '[ENCRYPTED]',
      expiresAt: metadata.expiresAt,
      status: metadata.status,
      createdAt: new Date(metadata.createdAt),
      updatedAt: new Date(metadata.updatedAt),
    };
  }
}
