import { Injectable, Logger } from '@nestjs/common';
import { GoogleAdsService } from './google-ads.service';
import { KeywordSearchResult } from '../interfaces/google-ads.interface';

@Injectable()
export class GoogleAdsKeywordService {
  private readonly logger = new Logger(GoogleAdsKeywordService.name);

  constructor(private readonly googleAdsService: GoogleAdsService) {}

  /**
   * L<PERSON>y danh sách từ khóa trong nhóm quảng cáo
   * @param customerId ID của customer
   * @param adGroupId ID của nhóm quảng cáo
   * @param refreshToken Refresh token (tùy chọn)
   * @returns Danh sách từ khóa
   */
  async listKeywords(customerId: string, adGroupId: string, refreshToken?: string): Promise<KeywordSearchResult[]> {
    try {
      return await this.googleAdsService.listKeywords(customerId, adGroupId, refreshToken);
    } catch (error) {
      this.logger.error(`Failed to list keywords: ${error.message}`);
      throw error;
    }
  }

  /**
   * Thêm từ khóa chính xác vào nhóm quảng cáo
   * @param customerId ID của customer
   * @param adGroupId ID của nhóm quảng cáo
   * @param keywordText Văn bản từ khóa
   * @param cpcBidMicros CPC tối đa (micro amount)
   * @param refreshToken Refresh token (tùy chọn)
   * @returns ID của từ khóa mới
   */
  async addExactMatchKeyword(
    customerId: string,
    adGroupId: string,
    keywordText: string,
    cpcBidMicros?: number,
    refreshToken?: string,
  ): Promise<string> {
    try {
      return await this.googleAdsService.addKeyword(
        customerId,
        {
          text: keywordText,
          adGroupId,
          matchType: 'EXACT',
          cpcBidMicros,
        },
        refreshToken,
      );
    } catch (error) {
      this.logger.error(`Failed to add exact match keyword: ${error.message}`);
      throw error;
    }
  }

  /**
   * Thêm từ khóa cụm từ vào nhóm quảng cáo
   * @param customerId ID của customer
   * @param adGroupId ID của nhóm quảng cáo
   * @param keywordText Văn bản từ khóa
   * @param cpcBidMicros CPC tối đa (micro amount)
   * @param refreshToken Refresh token (tùy chọn)
   * @returns ID của từ khóa mới
   */
  async addPhraseMatchKeyword(
    customerId: string,
    adGroupId: string,
    keywordText: string,
    cpcBidMicros?: number,
    refreshToken?: string,
  ): Promise<string> {
    try {
      return await this.googleAdsService.addKeyword(
        customerId,
        {
          text: keywordText,
          adGroupId,
          matchType: 'PHRASE',
          cpcBidMicros,
        },
        refreshToken,
      );
    } catch (error) {
      this.logger.error(`Failed to add phrase match keyword: ${error.message}`);
      throw error;
    }
  }

  /**
   * Thêm từ khóa rộng vào nhóm quảng cáo
   * @param customerId ID của customer
   * @param adGroupId ID của nhóm quảng cáo
   * @param keywordText Văn bản từ khóa
   * @param cpcBidMicros CPC tối đa (micro amount)
   * @param refreshToken Refresh token (tùy chọn)
   * @returns ID của từ khóa mới
   */
  async addBroadMatchKeyword(
    customerId: string,
    adGroupId: string,
    keywordText: string,
    cpcBidMicros?: number,
    refreshToken?: string,
  ): Promise<string> {
    try {
      return await this.googleAdsService.addKeyword(
        customerId,
        {
          text: keywordText,
          adGroupId,
          matchType: 'BROAD',
          cpcBidMicros,
        },
        refreshToken,
      );
    } catch (error) {
      this.logger.error(`Failed to add broad match keyword: ${error.message}`);
      throw error;
    }
  }

  /**
   * Thêm nhiều từ khóa vào nhóm quảng cáo
   * @param customerId ID của customer
   * @param adGroupId ID của nhóm quảng cáo
   * @param keywords Danh sách từ khóa
   * @param matchType Loại đối sánh
   * @param cpcBidMicros CPC tối đa (micro amount)
   * @param refreshToken Refresh token (tùy chọn)
   * @returns Danh sách ID của các từ khóa mới
   */
  async addKeywords(
    customerId: string,
    adGroupId: string,
    keywords: string[],
    matchType: 'EXACT' | 'PHRASE' | 'BROAD' = 'EXACT',
    cpcBidMicros?: number,
    refreshToken?: string,
  ): Promise<string[]> {
    try {
      const keywordIds = await Promise.all(
        keywords.map((keywordText) =>
          this.googleAdsService.addKeyword(
            customerId,
            {
              text: keywordText,
              adGroupId,
              matchType,
              cpcBidMicros,
            },
            refreshToken,
          ),
        ),
      );

      return keywordIds;
    } catch (error) {
      this.logger.error(`Failed to add keywords: ${error.message}`);
      throw error;
    }
  }

  /**
   * Phân tích từ khóa từ văn bản
   * @param text Văn bản cần phân tích
   * @returns Danh sách từ khóa đề xuất
   */
  analyzeKeywordsFromText(text: string): string[] {
    // Đây là một phương thức đơn giản để trích xuất từ khóa từ văn bản
    // Trong thực tế, bạn có thể sử dụng Google Ads API hoặc các thuật toán NLP phức tạp hơn

    // Loại bỏ dấu câu và chuyển thành chữ thường
    const cleanText = text.toLowerCase().replace(/[^\p{L}\p{N}\s]/gu, '');

    // Tách thành các từ
    const words = cleanText.split(/\s+/);

    // Lọc các từ ngắn và từ dừng
    const stopWords: string[] = ['và', 'hoặc', 'nhưng', 'vì', 'nên', 'là', 'của', 'có', 'không', 'để', 'the', 'and', 'or', 'but', 'for', 'with', 'in', 'on', 'at', 'to', 'from', 'by'];
    const filteredWords: string[] = words.filter((word) => word.length > 2 && !stopWords.includes(word));

    // Tạo các cụm từ 2-3 từ
    const phrases: string[] = [];
    for (let i = 0; i < filteredWords.length - 1; i++) {
      phrases.push(`${filteredWords[i]} ${filteredWords[i + 1]}`);

      if (i < filteredWords.length - 2) {
        phrases.push(`${filteredWords[i]} ${filteredWords[i + 1]} ${filteredWords[i + 2]}`);
      }
    }

    // Kết hợp từ đơn và cụm từ, loại bỏ trùng lặp
    const keywords = [...new Set([...filteredWords, ...phrases])];

    return keywords;
  }

  /**
   * Đề xuất từ khóa dựa trên từ khóa gốc
   * @param seedKeywords Danh sách từ khóa gốc
   * @returns Danh sách từ khóa đề xuất
   */
  suggestRelatedKeywords(seedKeywords: string[]): string[] {
    // Đây là một phương thức đơn giản để đề xuất từ khóa liên quan
    // Trong thực tế, bạn nên sử dụng Google Ads API để lấy đề xuất từ khóa

    const relatedKeywords: string[] = [];

    // Thêm biến thể số nhiều
    seedKeywords.forEach((keyword) => {
      relatedKeywords.push(keyword);

      // Thêm biến thể "mua", "bán", "giá" cho từ khóa
      relatedKeywords.push(`mua ${keyword}`);
      relatedKeywords.push(`bán ${keyword}`);
      relatedKeywords.push(`giá ${keyword}`);
      relatedKeywords.push(`${keyword} giá rẻ`);
      relatedKeywords.push(`${keyword} chất lượng cao`);
      relatedKeywords.push(`${keyword} tốt nhất`);
    });

    // Loại bỏ trùng lặp
    return [...new Set(relatedKeywords)];
  }
}
