import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { UserKeyLlm } from '../entities/user-key-llm.entity';

@Injectable()
export class UserKeyLlmRepository {
  constructor(
    @InjectRepository(UserKeyLlm)
    private readonly repository: Repository<UserKeyLlm>,
  ) {}

  async findById(id: string): Promise<UserKeyLlm | null> {
    return this.repository.findOne({ 
      where: { 
        id,
        deletedAt: IsNull()
      }
    });
  }
}
