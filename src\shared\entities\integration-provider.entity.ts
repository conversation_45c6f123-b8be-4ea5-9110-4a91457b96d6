import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Entity cho bảng integration_providers
 * L<PERSON>u thông tin các nhà cung cấp tích hợp (integration providers)
 */
@Entity('integration_providers')
export class IntegrationProvider {
  /**
   * <PERSON>h<PERSON><PERSON> chính, định danh duy nhất
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Mã định danh duy nhất cho loại tích hợp (ví dụ: GHTK, GHN, AHAMOVE)
   */
  @Column({
    type: 'varchar',
    unique: true,
    nullable: false,
    comment: 'Mã định danh duy nhất cho loại tích hợp (ví dụ: GHTK, GHN)'
  })
  type: string;

  /**
   * Schema dùng cho cấu hình MCP dưới dạng JSON
   */
  @Column({
    type: 'jsonb',
    nullable: true,
    name: 'mcp_schema',
    comment: 'Schema dùng cho cấu hình MCP dưới dạng JSON'
  })
  mcpSchema: any;

  /**
   * Thời điểm tạo
   */
  @Column({
    name: 'created_at',
    type: 'timestamp',
    default: () => 'now()',
    nullable: false,
    comment: 'Thời điểm tạo'
  })
  createdAt: Date;

  /**
   * Thời điểm cập nhật
   */
  @Column({
    name: 'updated_at',
    type: 'timestamp',
    default: () => 'now()',
    nullable: false,
    comment: 'Thời điểm cập nhật'
  })
  updatedAt: Date;
}
