import { tool, ToolRunnableConfig } from '@langchain/core/tools';
import { z } from 'zod';
import { Command, getCurrentTaskInput } from '@langchain/langgraph';
import { HumanMessage, ToolMessage } from '@langchain/core/messages';
import { CdnService } from '../../infra';
import { TimeIntervalEnum } from '@common/dto/time-interval.enum';
import { v4 } from 'uuid';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  CreateMemoryItem,
  MemoryEventType,
  UpdateMemoryEvent,
} from './interfaces';
import { AgentState } from './agent-system/core';
import {
  WebSearchService,
  WebSearchOptions,
} from './agent-system/services/websearch.service';

/**
 * Interface for the base64 conversion result
 */
export interface Base64Result {
  base64String: string;
  mimeType: string;
}

/**
 * Factory function to create the chooseImageFromContext tool with injected dependencies
 * @param cdnService - CDN service for generating signed URLs
 * @param streamUrlToBase64 - Function to convert URL streams to base64
 * @returns Configured chooseImageFromContext tool
 */
export function createChooseImageFromContextTool(
  cdnService: CdnService,
  streamUrlToBase64: (url: string) => Promise<Base64Result>,
) {
  return tool(
    async ({ reason, imageId }, toolRunnableConfig: ToolRunnableConfig) => {
      try {
        // ✅ Step 1: Access attachment image map from configurable
        const attachmentImageMap =
          toolRunnableConfig.configurable?.attachmentImageMap;

        if (!attachmentImageMap || typeof attachmentImageMap !== 'object') {
          throw new Error(
            'No attachment image map found in configurable context',
          );
        }

        // ✅ Step 2: Retrieve S3 key for the requested image ID
        const s3Key = attachmentImageMap[imageId];
        if (!s3Key) {
          throw new Error(
            `Image with ID "${imageId}" not found in attachment context. Available images: ${Object.keys(attachmentImageMap).join(', ')}`,
          );
        }

        // ✅ Step 3: Generate CDN URL for the S3 key
        const cdnUrl = cdnService.generateUrlView(
          s3Key,
          TimeIntervalEnum.FIVE_MINUTES,
        );
        if (!cdnUrl) {
          throw new Error(`Failed to generate CDN URL for S3 key: ${s3Key}`);
        }

        // ✅ Step 4: Convert image to base64
        const { base64String, mimeType } = await streamUrlToBase64(cdnUrl);
        if (!base64String) {
          throw new Error(
            `Failed to convert image to base64 from URL: ${cdnUrl}`,
          );
        }

        // ✅ Step 5: Create HumanMessage with base64 image content
        const imageMessage = new HumanMessage({
          id: v4(),
          content: [
            {
              type: 'image_url',
              image_url: {
                url: `data:${mimeType};base64,${base64String}`,
              },
            },
          ],
        });

        // Log the reason for including this image for debugging/audit purposes
        console.log(
          `Including image ${imageId} in response. Reason: ${reason}`,
        );
        const currentState = getCurrentTaskInput() as AgentState;
        const { messages } = currentState;
        const lastMessage = messages.at(-1) as any;
        const toolCallId = lastMessage.tool_calls?.[0].id;
        const toolMessage = new ToolMessage({
          content: `Image ${imageId} included in response. Reason: ${reason}`,
          tool_call_id: toolCallId,
        });

        // ✅ Step 6: Return Command to update LangGraph state
        return new Command({
          update: {
            messages: [toolMessage, imageMessage],
          },
        });
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error occurred';
        throw new Error(`Failed to process image selection: ${errorMessage}`);
      }
    },
    {
      name: 'choose_image_from_attachment_context',
      description:
        'Choose an image inside the attachment context to include as a clue for the response.',
      schema: z.object({
        reason: z
          .string()
          .nonempty()
          .describe('Reason for including the image in the response'),
        imageId: z
          .string()
          .nonempty()
          .describe('ID of the image to include in the response'),
      }),
    },
  );
}

// export const addImageToContext = tool();

/*
 * TODO: DYNAMIC FILE
 * export const chooseFileFromContext = tool();
 * export const addFileToContext = tool();
 * */

// ============================================================================
// MEMORY TOOLS - Zod Schemas
// ============================================================================

/**
 * Schema for individual memory record
 */
const MemoryRecordSchema = z.object({
  title: z
    .string()
    .min(1, 'Memory title cannot be empty')
    .max(200, 'Memory title too long (max 200 characters)')
    .describe('A brief title or summary of the memory'),
  content: z
    .string()
    .min(1, 'Memory content cannot be empty')
    .max(10000, 'Memory content too long (max 10,000 characters)')
    .describe('The detailed content to save to long-term memory'),
  type: z
    .enum(['user', 'agent'])
    .default('agent')
    .describe(
      'Type of memory: "user" for user memories, "agent" for agent memories (default: agent)',
    ),
  metadata: z
    .record(z.any())
    .optional()
    .describe('Optional metadata for the memory (tags, importance, etc.)'),
});

/**
 * Schema for save_memory tool input validation
 */
const SaveMemorySchema = z.object({
  reason: z
    .string()
    .min(1, 'Reason cannot be empty')
    .max(500, 'Reason too long (max 500 characters)')
    .describe('The reason why this content is being saved to memory'),
  memoryRecords: z
    .array(MemoryRecordSchema)
    .min(1, 'At least one memory record is required')
    .max(10, 'Cannot save more than 10 memory records at once')
    .describe('Array of memory records to save'),
});

/**
 * Schema for update_memory tool input validation
 */
const UpdateMemorySchema = z.object({
  memoryId: z
    .string()
    .uuid('Memory ID must be a valid UUID')
    .describe('ID of the memory to update (must be a valid UUID)'),
  memoryType: z
    .enum(['user', 'agent'])
    .describe('Type of memory: user or agent'),
  operation: z
    .enum(['content', 'title', 'reason', 'all'])
    .describe('What to update: content, title, reason, or all fields'),
  content: z
    .string()
    .min(1, 'Content cannot be empty')
    .max(10000, 'Content too long (max 10,000 characters)')
    .optional()
    .describe('New content (required if operation includes content)'),
  title: z
    .string()
    .min(1, 'Title cannot be empty')
    .max(200, 'Title too long (max 200 characters)')
    .optional()
    .describe('New title (required if operation includes title)'),
  reason: z
    .string()
    .min(1, 'Reason cannot be empty')
    .max(500, 'Reason too long (max 500 characters)')
    .optional()
    .describe('New reason (required if operation includes reason)'),
});

// ============================================================================
// MEMORY TOOLS - Factory Functions
// ============================================================================

/**
 * Factory function to create the save_memory tool with injected EventEmitter2
 *
 * @description Creates a LangChain tool that emits memory save events for batch asynchronous processing.
 * The tool validates input, creates multiple SaveMemoryEvents for each record, and emits them to the event system.
 * Supports saving multiple memories in a single operation with a contextual reason.
 * The actual memory processing happens asynchronously via the MemoryService @OnEvent handler.
 *
 * @param eventEmitter - EventEmitter2 instance for emitting memory save events
 * @returns Configured save_memory tool for LangChain agents
 */
export function createSaveMemoryTool(eventEmitter: EventEmitter2) {
  return tool(
    async (
      { reason, memoryRecords },
      toolRunnableConfig: ToolRunnableConfig,
    ) => {
      try {
        // Get userId from configurable context (always the human user)
        const userId = toolRunnableConfig.configurable?.userId;

        if (!userId) {
          throw new Error('User ID not found in configurable context');
        }

        // Get current agent UUID from LangGraph state
        const currentState = getCurrentTaskInput() as AgentState;
        const activeAgent = currentState.activeAgent;

        // Create memory items from all records (no ID - Postgres handles that)
        const memoryItems: CreateMemoryItem[] = memoryRecords.map((record) => ({
          content: record.content,
          timestamp: new Date(),
          metadata: {
            ...record.metadata,
            title: record.title,
            reason: reason,
            agentId: activeAgent,
            type: record.type,
          },
        }));

        // Create single batch save event
        const saveEvent = {
          userId: userId.toString(), // Always use user ID for batch context
          memoryItems,
          reason,
          agentId: activeAgent,
          eventType: MemoryEventType.SAVE,
          requestId: `save_memory_batch_${Date.now()}_${v4()}`,
          eventTimestamp: new Date(),
        };

        // Emit single event for batch processing
        eventEmitter.emit('memory.save.batch', saveEvent);

        const savedMemories = memoryRecords.map(
          (record) =>
            `${record.type}: "${record.title}" - ${record.content.substring(0, 50)}${record.content.length > 50 ? '...' : ''}`,
        );

        // Return comprehensive summary
        let response = `Memory save batch initiated successfully.\nReason: "${reason}"\n\n`;
        response += `✅ ${savedMemories.length} memories queued for processing:\n`;
        savedMemories.forEach((memory, index) => {
          response += `${index + 1}. ${memory}\n`;
        });
        response += `\nAll memories will be processed asynchronously.`;
        return response;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error occurred';
        throw new Error(`Failed to save memory batch: ${errorMessage}`);
      }
    },
    {
      name: 'save_memory',
      description:
        'Save multiple pieces of important information to long-term memory in a single batch operation. Provide a reason for saving and an array of memory records. Each record can be either user-specific or agent-specific. All memories will be processed asynchronously and made available for retrieval.',
      schema: SaveMemorySchema,
    },
  );
}

/**
 * Factory function to create the update_memory tool with injected EventEmitter2
 *
 * @description Creates a LangChain tool that emits memory update events for asynchronous processing.
 * Supports updating user memories and agent memories with granular field updates.
 * The actual memory processing happens asynchronously via the MemoryService @OnEvent handler.
 *
 * @param eventEmitter - EventEmitter2 instance for emitting memory update events
 * @returns Configured update_memory tool for LangChain agents
 */
export function createUpdateMemoryTool(eventEmitter: EventEmitter2) {
  return tool(
    async (
      { memoryId, memoryType, operation, content, title, reason },
      toolRunnableConfig: ToolRunnableConfig,
    ) => {
      try {
        // Get context information
        const userId = toolRunnableConfig.configurable?.userId;
        const currentState = getCurrentTaskInput() as AgentState;
        const agentId = currentState.activeAgent;

        if (!agentId) {
          throw new Error(
            'Cannot determine agent ID from current task context',
          );
        }

        if (memoryType === 'user' && !userId) {
          throw new Error('Cannot determine user ID for user memory update');
        }

        // Validate required fields based on operation
        const updates: { content?: string; title?: string; reason?: string } =
          {};

        if (operation === 'content' || operation === 'all') {
          if (!content) {
            throw new Error(
              'Content is required when operation includes content update',
            );
          }
          updates.content = content;
        }

        if (operation === 'title' || operation === 'all') {
          if (!title) {
            throw new Error(
              'Title is required when operation includes title update',
            );
          }
          updates.title = title;
        }

        if (operation === 'reason' || operation === 'all') {
          if (!reason) {
            throw new Error(
              'Reason is required when operation includes reason update',
            );
          }
          updates.reason = reason;
        }

        // Create update memory item
        const updateItem = {
          memoryId,
          memoryType,
          operation,
          updates,
        };

        // Create bulk update memory event (single item for now, but supports multiple)
        const updateEvent: UpdateMemoryEvent = {
          memoryItems: [updateItem],
          agentId,
          userId: userId?.toString(),
          eventType: MemoryEventType.UPDATE,
          requestId: `update_memory_batch_${Date.now()}_${v4()}`,
          eventTimestamp: new Date(),
        };

        // Emit event for asynchronous processing
        eventEmitter.emit('memory.update.batch', updateEvent);

        // Return immediate response
        let response = `✅ Memory update initiated successfully!\n\n`;
        response += `Memory ID: ${memoryId}\n`;
        response += `Memory Type: ${memoryType}\n`;
        response += `Operation: ${operation}\n`;

        if (updates.title) response += `New Title: "${updates.title}"\n`;
        if (updates.content)
          response += `New Content: "${updates.content.substring(0, 100)}${updates.content.length > 100 ? '...' : ''}"\n`;
        if (updates.reason) response += `New Reason: "${updates.reason}"\n`;

        response += `\nUpdate will be processed asynchronously.`;
        return response;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error occurred';
        throw new Error(`Failed to update memory: ${errorMessage}`);
      }
    },
    {
      name: 'update_memory',
      description:
        'Update an existing memory by ID. Memory IDs are available in the system prompt within <memory id="..."> tags. Can update user memories or agent memories. Specify what fields to update (content, title, reason, or all) and provide the new values. Only memories owned by the current agent/user can be updated.',
      schema: UpdateMemorySchema,
    },
  );
}

// ============================================================================
// WEB SEARCH TOOL - Schema and Factory Function
// ============================================================================

/**
 * Schema for web search tool input validation
 */
const WebSearchSchema = z.object({
  query: z
    .string()
    .min(1, 'Search query cannot be empty')
    .max(1000, 'Search query too long (max 1000 characters)')
    .describe('Search query to perform web search'),
  searchContext: z
    .enum(['low', 'medium', 'high'])
    .optional()
    .describe('Search context size for web search results (optional)'),
  userLocation: z
    .object({
      country: z
        .string()
        .length(2)
        .optional()
        .describe('Two-letter ISO country code (e.g., "US", "GB")'),
      city: z
        .string()
        .optional()
        .describe('City name for localized search (e.g., "Minneapolis")'),
      region: z
        .string()
        .optional()
        .describe('Region/state name for localized search (e.g., "Minnesota")'),
      timezone: z
        .string()
        .optional()
        .describe(
          'IANA timezone for localized search (e.g., "America/Chicago")',
        ),
    })
    .optional()
    .describe('User location for geographically refined search results'),
});

/**
 * Factory function to create the web_search tool with injected WebSearchService
 *
 * @description Creates a LangChain tool that performs web search using OpenAI's search-enabled models.
 * Supports geographic refinement through user location and search context size configuration.
 * Returns search results with citations formatted as text.
 *
 * @param webSearchService - WebSearchService instance for performing web searches
 * @returns Configured web_search tool for LangChain agents
 */
export function createWebSearchTool(webSearchService: WebSearchService) {
  return tool(
    async (
      { query, searchContext, userLocation },
      toolRunnableConfig: ToolRunnableConfig,
    ) => {
      try {
        const options: WebSearchOptions = {};

        if (searchContext) {
          options.search_context_size = searchContext;
        }

        if (userLocation) {
          options.user_location = {
            type: 'approximate',
            approximate: {
              ...(userLocation.country && { country: userLocation.country }),
              ...(userLocation.city && { city: userLocation.city }),
              ...(userLocation.region && { region: userLocation.region }),
              ...(userLocation.timezone && { timezone: userLocation.timezone }),
            },
          };
        }

        const result = await webSearchService.performWebSearch(query, options);

        // Return the search results with citations formatted as text
        let response = result.content;

        if (result.annotations && result.annotations.length > 0) {
          response += '\n\nSources:\n';
          result.annotations.forEach((annotation, index) => {
            if (annotation.type === 'url_citation') {
              response += `${index + 1}. ${annotation.url_citation.title}: ${annotation.url_citation.url}\n`;
            }
          });
        }

        return response;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error occurred';
        throw new Error(`Web search failed: ${errorMessage}`);
      }
    },
    {
      name: 'web_search',
      description:
        'Perform web search using OpenAI search-enabled models with real-time information and optional geographic refinement. Use this tool to find current information, news, facts, or answers that require up-to-date web data. Results include citations from reliable sources.',
      schema: WebSearchSchema,
    },
  );
}
