import { ModelTypeEnum } from '../enums/model-type.enum';
import { ApiKeyConfig } from './api-key-config.interface';
import { ModelMetadata } from './model-metadata.interface';

/**
 * Final model configuration interface
 * Complete configuration for an AI model including keys, metadata, and type
 */
export interface ModelConfig {
  /**
   * Unique model identifier
   */
  modelId: string;

  /**
   * Type of model (user, fine-tune, system)
   */
  modelType: ModelTypeEnum;

  /**
   * Associated API keys for the model
   */
  apiKeys: ApiKeyConfig[];

  /**
   * Model metadata and capabilities
   */
  modelMetadata: ModelMetadata;
}

/**
 * Internal resolved model interface
 * Used internally during model resolution process
 */
export interface ResolvedModel {
  /**
   * Unique model identifier
   */
  modelId: string;

  /**
   * Model registry ID for metadata lookup
   */
  modelRegistryId?: string;

  /**
   * Type of model (user, fine-tune, system)
   */
  type: ModelTypeEnum;

  /**
   * Associated API keys for the model
   */
  apiKeys: ApiKeyConfig[];
}
