import { AffiliateClick } from '../entities/affiliate-click.entity';

/**
 * Interface cho job xử lý affiliate click đơn lẻ
 */
export interface AffiliateClickJobData {
  /**
   * Dữ liệu của affiliate click
   */
  clickData: {
    affiliateAccountId: number;
    referralCode: string;
    ipAddress?: string;
    userAgent?: string;
    referrerUrl?: string;
    landingPage?: string;
    clickTime: number;
  };
}

/**
 * Interface cho job xử lý batch affiliate click
 */
export interface AffiliateClickBatchJobData {
  /**
   * Danh sách các affiliate click cần được lưu vào database
   */
  clicks: AffiliateClick[];

  /**
   * Thời gian job được tạo
   */
  timestamp: number;
}
