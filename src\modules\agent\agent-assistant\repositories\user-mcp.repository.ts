import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, IsNull } from 'typeorm';
import { UserMcp } from '../entities/user-mcp.entity';

@Injectable()
export class UserMcpRepository {
  constructor(
    @InjectRepository(UserMcp)
    private readonly repository: Repository<UserMcp>,
  ) {}

  async findByIds(ids: string[]): Promise<UserMcp[]> {
    if (ids.length === 0) {
      return [];
    }
    return this.repository.find({
      where: { 
        id: In(ids),
        deletedAt: IsNull()
      }
    });
  }
}
