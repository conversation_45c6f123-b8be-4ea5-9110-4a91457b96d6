/**
 * @file Interface cho Edit Fields node
 * 
 * Đ<PERSON>nh nghĩa type-safe interface cho node Edit Fields bao gồm:
 * - Parameters structure với validation
 * - Properties definition
 * - Input/Output data types
 * - Field operations (ADD, UPDATE, REMOVE, RENAME, COPY, MOVE)
 * - Value transformations và built-in functions
 */

import {
    IBaseNodeInput,
    IBaseNodeOutput,
    ITypedNodeExecution
} from '../../execute.interface';
import {
    ICondition
} from '../shared/condition-evaluation.interface';

// =================================================================
// SECTION 1: ENUMS & TYPES
// Định nghĩa các enum và type cho Edit Fields node
// =================================================================

/**
 * Field operation types
 */
export enum EFieldOperation {
    /** Add new field */
    ADD = 'add',

    /** Update existing field */
    UPDATE = 'update',

    /** Remove field */
    REMOVE = 'remove',

    /** Rename field */
    RENAME = 'rename',

    /** Copy field to another field */
    COPY = 'copy',

    /** Move field (rename + remove old) */
    MOVE = 'move'
}

/**
 * Value source types
 */
export enum EValueSource {
    /** Static/fixed value */
    STATIC = 'static',

    /** Copy value from another field */
    FIELD = 'field',

    /** JavaScript expression */
    EXPRESSION = 'expression',

    /** Built-in transformation function */
    FUNCTION = 'function'
}

/**
 * Built-in transformation functions
 */
export enum ETransformFunction {
    // String functions
    TO_UPPERCASE = 'to_uppercase',
    TO_LOWERCASE = 'to_lowercase',
    TRIM = 'trim',
    CAPITALIZE = 'capitalize',
    SLUG = 'slug',
    REVERSE = 'reverse',

    // Number functions
    TO_NUMBER = 'to_number',
    ROUND = 'round',
    ABSOLUTE = 'absolute',
    FORMAT_CURRENCY = 'format_currency',

    // Date functions
    TO_DATE = 'to_date',
    FORMAT_DATE = 'format_date',
    ADD_DAYS = 'add_days',
    GET_TIMESTAMP = 'get_timestamp',

    // Array functions
    TO_ARRAY = 'to_array',
    JOIN_ARRAY = 'join_array',
    ARRAY_LENGTH = 'array_length',
    ARRAY_UNIQUE = 'array_unique',

    // Object functions
    TO_JSON = 'to_json',
    FROM_JSON = 'from_json',
    GET_KEYS = 'get_keys',
    GET_VALUES = 'get_values',

    // Type conversion
    TO_STRING = 'to_string',
    TO_BOOLEAN = 'to_boolean'
}

/**
 * Condition types cho conditional operations
 */
export enum EConditionType {
    /** Always execute */
    ALWAYS = 'always',

    /** Execute if condition is true */
    IF_TRUE = 'if_true',

    /** Execute if field exists */
    IF_EXISTS = 'if_exists',

    /** Execute if field is empty */
    IF_EMPTY = 'if_empty'
}

// =================================================================
// SECTION 2: CONFIGURATION STRUCTURES
// =================================================================

/**
 * Function parameters cho built-in functions
 */
export interface IFunctionParameters {
    /** Decimal places cho ROUND function */
    decimal_places?: number;

    /** Currency code cho FORMAT_CURRENCY */
    currency_code?: string;

    /** Date format cho FORMAT_DATE */
    date_format?: string;

    /** Number of days cho ADD_DAYS */
    days?: number;

    /** Separator cho JOIN_ARRAY */
    separator?: string;

    /** Custom parameters */
    [key: string]: any;
}

/**
 * Single field operation configuration
 */
export interface IFieldOperation {
    /** Operation ID for tracking */
    id: string;

    /** Operation type */
    operation_type: EFieldOperation;

    /** Target field path (supports nested: user.profile.name) */
    target_field: string;

    /** New field name (cho RENAME, COPY, MOVE operations) */
    new_field_name?: string;

    /** Value source type */
    value_source?: EValueSource;

    /** Static value */
    static_value?: any;

    /** Source field path (cho FIELD value source) */
    source_field?: string;

    /** JavaScript expression (cho EXPRESSION value source) */
    expression?: string;

    /** Transform function (cho FUNCTION value source) */
    transform_function?: ETransformFunction;

    /** Function parameters */
    function_parameters?: IFunctionParameters;

    /** Condition type */
    condition_type: EConditionType;

    /** Condition for conditional execution */
    condition?: ICondition;

    /** Có enabled operation này không */
    enabled: boolean;

    /** Operation description */
    description?: string;
}

/**
 * Operation execution result
 */
export interface IFieldOperationResult {
    /** Original operation */
    operation: IFieldOperation;

    /** Success status */
    success: boolean;

    /** Result message */
    result?: string;

    /** Error message if failed */
    error?: string;

    /** Old value (for UPDATE operations) */
    old_value?: any;

    /** New value */
    new_value?: any;
}

// =================================================================
// SECTION 3: PARAMETERS INTERFACE
// =================================================================

/**
 * Interface cho parameters của Edit Fields node
 */
export interface IEditFieldsParameters {
    /** List of field operations to perform */
    field_operations: IFieldOperation[];

    /** Có stop execution khi có operation fails không */
    stop_on_error: boolean;

    /** Có preserve original data structure không */
    preserve_structure: boolean;

    /** Có validate field paths trước khi execute không */
    validate_paths: boolean;

    /** Custom variables có thể sử dụng trong expressions */
    variables?: Record<string, any>;
}

// =================================================================
// SECTION 4: INPUT/OUTPUT INTERFACES
// =================================================================

/**
 * Interface cho input data của Edit Fields node
 */
export interface IEditFieldsInput extends IBaseNodeInput {
    /** Data để edit fields */
    data: Record<string, any>;

    /** Context variables */
    variables?: Record<string, any>;

    /** Override operations */
    operation_overrides?: Partial<IFieldOperation>[];
}

/**
 * Interface cho output data của Edit Fields node
 */
export interface IEditFieldsOutput extends IBaseNodeOutput {
    /** Processed data với edited fields */
    data: Record<string, any>;

    /** Results của tất cả operations */
    operations_performed: IFieldOperationResult[];

    /** Edit execution metadata */
    edit_metadata: {
        /** Total operations configured */
        total_operations: number;

        /** Successful operations count */
        successful_operations: number;

        /** Failed operations count */
        failed_operations: number;

        /** Processing time (milliseconds) */
        processing_time: number;

        /** Fields added */
        fields_added: string[];

        /** Fields updated */
        fields_updated: string[];

        /** Fields removed */
        fields_removed: string[];

        /** Fields renamed */
        fields_renamed: Array<{ from: string; to: string }>;

        /** Có errors trong quá trình execute không */
        has_errors: boolean;
    };

    /** Original data (nếu preserve_structure = true) */
    original_data?: Record<string, any>;
}


/**
 * Type-safe node execution cho Edit Fields
 */
export type IEditFieldsNodeExecution = ITypedNodeExecution<
    IEditFieldsInput,
    IEditFieldsOutput,
    IEditFieldsParameters
>;

// =================================================================
// SECTION 7: HELPER FUNCTIONS
// =================================================================

/**
 * Helper function để get nested value từ object
 */
export function getFieldValue(obj: any, path: string): any {
    if (!path) return obj;
    return path.split('.').reduce((current, key) => {
        // Handle array indices like items[0]
        if (key.includes('[') && key.includes(']')) {
            const [arrayKey, indexStr] = key.split('[');
            const index = parseInt(indexStr.replace(']', ''));
            return current?.[arrayKey]?.[index];
        }
        return current?.[key];
    }, obj);
}

/**
 * Helper function để set nested value trong object
 */
export function setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;

    let current = obj;
    for (const key of keys) {
        if (key.includes('[') && key.includes(']')) {
            const [arrayKey, indexStr] = key.split('[');
            const index = parseInt(indexStr.replace(']', ''));

            if (!current[arrayKey]) current[arrayKey] = [];
            if (!current[arrayKey][index]) current[arrayKey][index] = {};
            current = current[arrayKey][index];
        } else {
            if (!current[key]) current[key] = {};
            current = current[key];
        }
    }

    if (lastKey.includes('[') && lastKey.includes(']')) {
        const [arrayKey, indexStr] = lastKey.split('[');
        const index = parseInt(indexStr.replace(']', ''));

        if (!current[arrayKey]) current[arrayKey] = [];
        current[arrayKey][index] = value;
    } else {
        current[lastKey] = value;
    }
}

/**
 * Helper function để remove nested value từ object
 */
export function removeNestedValue(obj: any, path: string): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;

    let current = obj;
    for (const key of keys) {
        if (key.includes('[') && key.includes(']')) {
            const [arrayKey, indexStr] = key.split('[');
            const index = parseInt(indexStr.replace(']', ''));
            current = current?.[arrayKey]?.[index];
        } else {
            current = current?.[key];
        }

        if (!current) return; // Path doesn't exist
    }

    if (lastKey.includes('[') && lastKey.includes(']')) {
        const [arrayKey, indexStr] = lastKey.split('[');
        const index = parseInt(indexStr.replace(']', ''));

        if (current[arrayKey] && Array.isArray(current[arrayKey])) {
            current[arrayKey].splice(index, 1);
        }
    } else {
        delete current[lastKey];
    }
}

/**
 * Helper function để check if field path exists
 */
export function hasNestedPath(obj: any, path: string): boolean {
    try {
        const value = getFieldValue(obj, path);
        return value !== undefined;
    } catch {
        return false;
    }
}

/**
 * Helper function để apply transform function
 */
export function applyTransformFunction(
    value: any,
    functionName: ETransformFunction,
    parameters?: IFunctionParameters
): any {
    switch (functionName) {
        // String functions
        case ETransformFunction.TO_UPPERCASE:
            return String(value || '').toUpperCase();

        case ETransformFunction.TO_LOWERCASE:
            return String(value || '').toLowerCase();

        case ETransformFunction.TRIM:
            return String(value || '').trim();

        case ETransformFunction.CAPITALIZE:
            const str = String(value || '');
            return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();

        case ETransformFunction.SLUG:
            return String(value || '')
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/^-+|-+$/g, '');

        case ETransformFunction.REVERSE:
            return String(value || '').split('').reverse().join('');

        // Number functions
        case ETransformFunction.TO_NUMBER:
            return parseFloat(value) || 0;

        case ETransformFunction.ROUND:
            const decimals = parameters?.decimal_places || 2;
            return Math.round(parseFloat(value) * Math.pow(10, decimals)) / Math.pow(10, decimals);

        case ETransformFunction.ABSOLUTE:
            return Math.abs(parseFloat(value) || 0);

        case ETransformFunction.FORMAT_CURRENCY:
            const currency = parameters?.currency_code || 'USD';
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: currency
            }).format(parseFloat(value) || 0);

        // Date functions
        case ETransformFunction.TO_DATE:
            return new Date(value).toISOString();

        case ETransformFunction.FORMAT_DATE:
            const format = parameters?.date_format || 'YYYY-MM-DD';
            // Simple format implementation - in real app would use date library
            const date = new Date(value);
            return date.toISOString().split('T')[0];

        case ETransformFunction.ADD_DAYS:
            const days = parameters?.days || 1;
            const baseDate = new Date(value);
            baseDate.setDate(baseDate.getDate() + days);
            return baseDate.toISOString();

        case ETransformFunction.GET_TIMESTAMP:
            return new Date(value).getTime();

        // Array functions
        case ETransformFunction.TO_ARRAY:
            return Array.isArray(value) ? value : [value];

        case ETransformFunction.JOIN_ARRAY:
            const separator = parameters?.separator || ', ';
            return Array.isArray(value) ? value.join(separator) : String(value);

        case ETransformFunction.ARRAY_LENGTH:
            return Array.isArray(value) ? value.length : 0;

        case ETransformFunction.ARRAY_UNIQUE:
            return Array.isArray(value) ? [...new Set(value)] : [value];

        // Object functions
        case ETransformFunction.TO_JSON:
            return JSON.stringify(value);

        case ETransformFunction.FROM_JSON:
            try {
                return JSON.parse(String(value));
            } catch {
                return value;
            }

        case ETransformFunction.GET_KEYS:
            return typeof value === 'object' && value !== null ? Object.keys(value) : [];

        case ETransformFunction.GET_VALUES:
            return typeof value === 'object' && value !== null ? Object.values(value) : [];

        // Type conversion
        case ETransformFunction.TO_STRING:
            return String(value);

        case ETransformFunction.TO_BOOLEAN:
            return Boolean(value);

        default:
            return value;
    }
}

/**
 * Helper function để evaluate condition
 */
export function evaluateCondition(
    data: any,
    condition: ICondition,
    conditionType: EConditionType
): boolean {
    switch (conditionType) {
        case EConditionType.ALWAYS:
            return true;

        case EConditionType.IF_EXISTS:
            return hasNestedPath(data, condition.field);

        case EConditionType.IF_EMPTY:
            const value = getFieldValue(data, condition.field);
            return value === null || value === undefined || value === '' ||
                (Array.isArray(value) && value.length === 0) ||
                (typeof value === 'object' && Object.keys(value).length === 0);

        case EConditionType.IF_TRUE:
            // Would use shared condition evaluation logic here
            // For now, simple implementation
            const fieldValue = getFieldValue(data, condition.field);
            return fieldValue === condition.value;

        default:
            return false;
    }
}

/**
 * Helper function để validate field operations
 */
export function validateEditFieldsParameters(params: Partial<IEditFieldsParameters>): {
    isValid: boolean;
    errors: string[];
} {
    const errors: string[] = [];

    if (!params.field_operations || params.field_operations.length === 0) {
        errors.push('At least one field operation is required');
    } else {
        params.field_operations.forEach((operation, index) => {
            if (!operation.id) {
                errors.push(`Operation ${index + 1}: ID is required`);
            }

            if (!operation.operation_type) {
                errors.push(`Operation ${index + 1}: Operation type is required`);
            }

            if (!operation.target_field) {
                errors.push(`Operation ${index + 1}: Target field is required`);
            }

            // Validate based on operation type
            switch (operation.operation_type) {
                case EFieldOperation.RENAME:
                case EFieldOperation.COPY:
                case EFieldOperation.MOVE:
                    if (!operation.new_field_name) {
                        errors.push(`Operation ${index + 1}: New field name is required for ${operation.operation_type} operation`);
                    }
                    break;

                case EFieldOperation.ADD:
                case EFieldOperation.UPDATE:
                    if (operation.value_source === EValueSource.FIELD && !operation.source_field) {
                        errors.push(`Operation ${index + 1}: Source field is required when value source is FIELD`);
                    }

                    if (operation.value_source === EValueSource.EXPRESSION && !operation.expression) {
                        errors.push(`Operation ${index + 1}: Expression is required when value source is EXPRESSION`);
                    }

                    if (operation.value_source === EValueSource.FUNCTION && !operation.transform_function) {
                        errors.push(`Operation ${index + 1}: Transform function is required when value source is FUNCTION`);
                    }
                    break;
            }

            // Validate condition
            if (operation.condition_type === EConditionType.IF_TRUE && !operation.condition) {
                errors.push(`Operation ${index + 1}: Condition is required when condition type is IF_TRUE`);
            }
        });

        // Check for duplicate operation IDs
        const ids = params.field_operations.map(op => op.id);
        const duplicateIds = ids.filter((id, index) => ids.indexOf(id) !== index);
        if (duplicateIds.length > 0) {
            errors.push(`Duplicate operation IDs found: ${duplicateIds.join(', ')}`);
        }
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}
