import { <PERSON>ti<PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { Addon } from './addon.entity';

/**
 * Entity quản lý dung lượng addon của user
 * <PERSON><PERSON> được refactor để bỏ dependency với UserSubscription và Payment
 * Quản lý usage trực tiếp theo user và addon
 */
@Entity('user_addon_usages')
export class UserAddonUsage {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'addon_id' })
  addonId: number;

  @Column({ length: 50, nullable: true, name: 'usage_unit' })
  usageUnit: string;

  @Column({ type: 'bigint', precision: 12, scale: 4, default: 0, name: 'usage_limit' })
  usageLimit: number;

  @Column({ type: 'bigint', precision: 12, scale: 4, default: 0, name: 'current_usage' })
  currentUsage: number;

  @Column({ type: 'bigint', precision: 12, scale: 4, default: 0, name: 'remaining_value' })
  remainingValue: number;

  @Column({ type: 'bigint', name: 'usage_period_start' })
  usagePeriodStart: number;

  @Column({ type: 'bigint', name: 'usage_period_end' })
  usagePeriodEnd: number;

  @Column({ type: 'varchar', length: 20, default: 'ACTIVE', name: 'status' })
  status: string;

  @Column({ type: 'bigint', name: 'last_updated_at' })
  lastUpdatedAt: number;

  // Relationship với Addon
  @ManyToOne(() => Addon)
  @JoinColumn({ name: 'addon_id' })
  addon: Addon;

  // ❌ Đã bỏ userSubscriptionId vì không cần UserSubscription nữa
}
