import { Module } from '@nestjs/common';
import { AgentSystemController } from './agent-system.controller';
import { UserAgentRunsQueries } from './services/user-agent-runs.queries';
import { UserMessagesQueries } from './services/user-messages.queries';
import { MessageContentService } from './services/message-content.service';
import { ChatDatabaseService } from './database.service';
import { AgentSystemService } from './services/agent-system.service';
import { UserUsageQueries } from './services/user-usage.queries';
import {
  ThreadConfigurationService,
  StreamingSetupService,
  LangGraphEventProcessorService,
  ThreadCompletionHandlerService,
  ValidationService,
  EmbeddingService,
  WebSearchService,
  MemoryQueriesService,
  MemoryService,
} from './services';
import { LangGraphEventRegistry } from './event-handlers';
import { HttpModule } from '@nestjs/axios';
import { EventEmitterModule } from '@nestjs/event-emitter';

@Module({
  imports: [
    HttpModule,
    // EventEmitterModule enables event-driven architecture for memory operations
    // Allows MemoryService to listen for 'memory.save' events from LangChain tools
    EventEmitterModule.forRoot(),
  ],
  controllers: [AgentSystemController],
  providers: [
    // Existing services
    UserAgentRunsQueries,
    UserMessagesQueries,
    MessageContentService,
    ChatDatabaseService,
    AgentSystemService,
    UserUsageQueries,

    // Refactored services
    ThreadConfigurationService,
    StreamingSetupService,
    LangGraphEventProcessorService,
    ThreadCompletionHandlerService,
    ValidationService,

    // Memory services (dependency: MemoryService -> MemoryQueriesService -> ChatDatabaseService)
    MemoryQueriesService, // Raw SQL operations for memory data
    MemoryService, // Business logic + @OnEvent('memory.save') handler

    // Embedding service (separate from memory operations, available for other features)
    EmbeddingService,

    // Web search service (for real-time web search functionality)
    WebSearchService,

    // Event handling services
    LangGraphEventRegistry,
  ],
  exports: [
    // Export MemoryService for use in other modules (e.g., LangChain tools)
    // Note: StreamingSetupService (in this module) can directly inject MemoryService
    // since they're in the same module - no export needed for internal usage
    MemoryService,
    // Export EmbeddingService for other features that need embeddings
    EmbeddingService,
  ],
})
export class AgentSystemModule {}
