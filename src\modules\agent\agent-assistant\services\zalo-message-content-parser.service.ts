import { Injectable, Logger } from '@nestjs/common';
import { MessageTypeEnum } from '../enums/message.enum';
import { ZaloAiMessage } from '../entities/zalo-ai-message.entity';

/**
 * Interface for parsed message content
 */
export interface ParsedMessageContent {
  /**
   * Main text content of the message
   */
  text: string;
  
  /**
   * Additional metadata about the message
   */
  metadata: {
    messageType: MessageTypeEnum;
    hasAttachments: boolean;
    attachmentCount?: number;
    [key: string]: any;
  };
  
  /**
   * Summary for reply context (concise description)
   */
  summary: string;
}

/**
 * Service for parsing different Zalo message types into structured content
 * Only handles the 4 message types that trigger auto-response:
 * - TEXT: Extract text content directly
 * - IMAGE: Extract image metadata and any caption text
 * - STICKER: Extract sticker information
 * - LINK: Extract URL, title, description from link attachment
 */
@Injectable()
export class ZaloMessageContentParserService {
  private readonly logger = new Logger(ZaloMessageContentParserService.name);

  /**
   * Parse message content based on message type
   * @param message ZaloAiMessage to parse
   * @returns Parsed message content or null if parsing fails
   */
  async parseMessageContent(message: ZaloAiMessage): Promise<ParsedMessageContent | null> {
    try {
      // Validate input
      if (!message) {
        this.logger.error('Cannot parse null or undefined message');
        return null;
      }

      if (!message.id) {
        this.logger.error('Message has no ID');
        return null;
      }

      if (!message.messageType) {
        this.logger.error(`Message ${message.id} has no message type`);
        return this.parseFallbackMessage(message);
      }

      this.logger.debug(`Parsing message ${message.id} of type ${message.messageType}`);

      switch (message.messageType) {
        case MessageTypeEnum.TEXT:
          return this.parseTextMessage(message);

        case MessageTypeEnum.IMAGE:
          return this.parseImageMessage(message);

        case MessageTypeEnum.STICKER:
          return this.parseStickerMessage(message);

        case MessageTypeEnum.LINK:
          return this.parseLinkMessage(message);

        default:
          this.logger.warn(`Unsupported message type for parsing: ${message.messageType}. Using fallback parser.`);
          return this.parseFallbackMessage(message);
      }
    } catch (error) {
      this.logger.error(`Unexpected error while parsing message ${message?.id || 'unknown'}:`, {
        error: error.message,
        stack: error.stack,
        messageId: message?.id,
        messageType: message?.messageType,
      });
      throw error;
    }
  }

  /**
   * Parse text message content
   */
  private parseTextMessage(message: ZaloAiMessage): ParsedMessageContent {
    const text = message.content || '';
    
    return {
      text,
      metadata: {
        messageType: MessageTypeEnum.TEXT,
        hasAttachments: false,
        originalLength: text.length,
      },
      summary: this.createTextSummary(text),
    };
  }

  /**
   * Parse image message content
   */
  private parseImageMessage(message: ZaloAiMessage): ParsedMessageContent {
    try {
      const rawData = message.rawWebhookData;
      let text = message.content || '';
      let imageInfo = {};

      // Extract image information from raw webhook data
      if (rawData?.message?.attachments) {
        try {
          const imageAttachments = rawData.message.attachments.filter(
            (att: any) => att?.type === 'image'
          );

          if (imageAttachments.length > 0) {
            const firstImage = imageAttachments[0];
            imageInfo = {
              imageUrl: firstImage?.payload?.url || null,
              thumbnailUrl: firstImage?.payload?.thumbnail || null,
              imageCount: imageAttachments.length,
            };

            this.logger.debug(`Extracted image info for message ${message.id}:`, imageInfo);
          } else {
            this.logger.debug(`No image attachments found in message ${message.id}`);
          }
        } catch (attachmentError) {
          this.logger.warn(`Failed to parse image attachments for message ${message.id}:`, attachmentError.message);
        }
      } else {
        this.logger.debug(`No raw webhook data or attachments found for image message ${message.id}`);
      }

      // If no text content, create descriptive text
      if (!text) {
        const imageCount = imageInfo['imageCount'] || 1;
        text = `[Image message${imageCount > 1 ? ` with ${imageCount} images` : ''}]`;
      }

      return {
        text,
        metadata: {
          messageType: MessageTypeEnum.IMAGE,
          hasAttachments: true,
          attachmentCount: imageInfo['imageCount'] || 1,
          ...imageInfo,
        },
        summary: `Image: ${this.createTextSummary(text)}`,
      };
    } catch (error) {
      this.logger.error(`Error parsing image message ${message.id}:`, error.message);
      // Return basic image message info
      return {
        text: message.content || '[Image message]',
        metadata: {
          messageType: MessageTypeEnum.IMAGE,
          hasAttachments: true,
          parseError: true,
        },
        summary: 'Image: [Parse error]',
      };
    }
  }

  /**
   * Parse sticker message content
   */
  private parseStickerMessage(message: ZaloAiMessage): ParsedMessageContent {
    const rawData = message.rawWebhookData;
    let text = message.content || '[Sticker]';
    let stickerInfo = {};

    // Extract sticker information from raw webhook data
    if (rawData?.message?.attachments) {
      const stickerAttachments = rawData.message.attachments.filter(
        (att: any) => att.type === 'sticker'
      );
      
      if (stickerAttachments.length > 0) {
        const firstSticker = stickerAttachments[0];
        stickerInfo = {
          stickerId: firstSticker.payload?.id,
          stickerCategory: firstSticker.payload?.category,
        };
        
        // Create more descriptive text if we have sticker info
        if (stickerInfo['stickerId']) {
          text = `[Sticker: ${stickerInfo['stickerId']}${stickerInfo['stickerCategory'] ? ` (${stickerInfo['stickerCategory']})` : ''}]`;
        }
      }
    }

    return {
      text,
      metadata: {
        messageType: MessageTypeEnum.STICKER,
        hasAttachments: true,
        attachmentCount: 1,
        ...stickerInfo,
      },
      summary: `Sticker: ${stickerInfo['stickerId'] || 'Unknown'}`,
    };
  }

  /**
   * Parse link message content
   */
  private parseLinkMessage(message: ZaloAiMessage): ParsedMessageContent {
    const rawData = message.rawWebhookData;
    let text = message.content || '';
    let linkInfo = {};

    // Extract link information from raw webhook data
    if (rawData?.message?.attachments) {
      const linkAttachments = rawData.message.attachments.filter(
        (att: any) => att.type === 'link'
      );
      
      if (linkAttachments.length > 0) {
        const firstLink = linkAttachments[0];
        linkInfo = {
          url: firstLink.payload?.url,
          title: firstLink.payload?.title,
          description: firstLink.payload?.description,
          thumbnailUrl: firstLink.payload?.thumbnail,
        };
      }
    }

    // If no text content, use link title or URL
    if (!text) {
      text = linkInfo['title'] || linkInfo['url'] || '[Link]';
    }

    return {
      text,
      metadata: {
        messageType: MessageTypeEnum.LINK,
        hasAttachments: true,
        attachmentCount: 1,
        ...linkInfo,
      },
      summary: `Link: ${linkInfo['title'] || 'Shared link'}${linkInfo['url'] ? ` (${linkInfo['url']})` : ''}`,
    };
  }

  /**
   * Fallback parser for unsupported message types
   */
  private parseFallbackMessage(message: ZaloAiMessage): ParsedMessageContent {
    const text = message.content || `[${message.messageType} message]`;
    
    return {
      text,
      metadata: {
        messageType: message.messageType,
        hasAttachments: false,
        fallback: true,
      },
      summary: `${message.messageType}: ${this.createTextSummary(text)}`,
    };
  }

  /**
   * Create a concise summary of text content
   */
  private createTextSummary(text: string): string {
    if (!text || text.trim().length === 0) {
      return 'Empty message';
    }

    const cleanText = text.trim();
    
    // If text is short, return as-is
    if (cleanText.length <= 50) {
      return cleanText;
    }

    // Truncate long text with ellipsis
    return cleanText.substring(0, 47) + '...';
  }
}
