import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Integration } from '../../entities/integration.entity';
import { IntegrationProvider } from '../../entities/integration-provider.entity';
import { KeyPairEncryptionService, ZaloOAPayload } from '../encryption/key-pair-encryption.service';

/**
 * Interface cho Zalo OA metadata trong Integration
 */
interface ZaloOAMetadata {
  oaId: string;
  name: string;
  description?: string;
  avatarUrl?: string;
  expiresAt: number;
  status: string;
  createdAt: number;
  updatedAt: number;
  backupTokens?: {
    accessToken: string;
    refreshToken: string;
  };
}

/**
 * Adapter service để tương thích với ZaloOfficialAccount entity cũ cho ZaloTokenUtilsService
 * Truy vấn dữ liệu từ Integration table thay vì zalo_official_account table
 */
@Injectable()
export class ZaloOATokenAdapterService {
  private readonly logger = new Logger(ZaloOATokenAdapterService.name);

  constructor(
    @InjectRepository(Integration)
    private readonly integrationRepository: Repository<Integration>,
    @InjectRepository(IntegrationProvider)
    private readonly integrationProviderRepository: Repository<IntegrationProvider>,
    private readonly keyPairEncryptionService: KeyPairEncryptionService,
  ) {}

  /**
   * Tìm Zalo OA theo oaId
   */
  async findOne(options: { where: { oaId: string; status?: string } }): Promise<any | null> {
    try {
      // Tìm provider ZALO_OA
      const provider = await this.integrationProviderRepository.findOne({
        where: { type: 'ZALO_OA' as any }
      });

      if (!provider) {
        this.logger.error('ZALO_OA provider not found');
        return null;
      }

      // Tìm integration theo oaId trong metadata
      let queryBuilder = this.integrationRepository
        .createQueryBuilder('integration')
        .where('integration.typeId = :typeId', { typeId: provider.id })
        .andWhere("integration.metadata->>'oaId' = :oaId", { oaId: options.where.oaId });

      // Thêm điều kiện status nếu có
      if (options.where.status) {
        queryBuilder = queryBuilder.andWhere("integration.metadata->>'status' = :status", { 
          status: options.where.status 
        });
      }

      const integration = await queryBuilder.getOne();

      if (!integration) {
        return null;
      }

      return this.mapIntegrationToZaloOA(integration);
    } catch (error) {
      this.logger.error(`Error finding Zalo OA by oaId ${options.where.oaId}:`, error);
      return null;
    }
  }

  /**
   * Cập nhật Zalo OA
   */
  async save(data: any): Promise<any> {
    try {
      // Tìm integration theo ID
      const integration = await this.integrationRepository.findOne({
        where: { id: data.id }
      });

      if (!integration) {
        throw new Error(`Integration with ID ${data.id} not found`);
      }

      // Cập nhật metadata
      const metadata = integration.metadata as ZaloOAMetadata;
      const updatedMetadata: ZaloOAMetadata = {
        ...metadata,
        expiresAt: data.expiresAt,
        status: data.status,
        updatedAt: Date.now(),
      };

      const updateData: any = {
        metadata: updatedMetadata as any,
      };

      // Nếu có token mới, mã hóa và cập nhật
      if (data.accessToken || data.refreshToken) {
        try {
          // Lấy token hiện tại để merge
          let currentTokens: ZaloOAPayload = { accessToken: '', refreshToken: '' };
          if (integration.encryptedConfig && integration.secretKey) {
            try {
              currentTokens = await this.getDecryptedTokens(integration.id);
            } catch (error) {
              this.logger.warn(`Cannot decrypt current tokens, using new tokens only: ${error.message}`);
            }
          }

          // Merge token mới với token hiện tại
          const newTokens: ZaloOAPayload = {
            accessToken: data.accessToken || currentTokens.accessToken,
            refreshToken: data.refreshToken || currentTokens.refreshToken,
          };

          // Mã hóa token mới với secretKey hiện tại (không tạo mới)
          const existingSecretKey = integration.secretKey;
          if (!existingSecretKey) {
            throw new Error('No existing secretKey found for integration');
          }

          const encryptedResult = this.keyPairEncryptionService.encryptObject(newTokens, existingSecretKey);
          updateData.encryptedConfig = encryptedResult.encryptedData;
          // Không cập nhật secretKey, giữ nguyên secretKey cũ

          this.logger.log(`Updated tokens for integration ${data.id} using existing secretKey`);
        } catch (error) {
          this.logger.error(`Error encrypting tokens for integration ${data.id}: ${error.message}`);
          throw error;
        }
      }

      // Cập nhật integration
      await this.integrationRepository.update(data.id, updateData);

      // Trả về object đã cập nhật
      return {
        ...data,
        updatedAt: new Date(updatedMetadata.updatedAt),
      };
    } catch (error) {
      this.logger.error(`Error saving Zalo OA:`, error);
      throw error;
    }
  }

  /**
   * Lấy token đã giải mã
   */
  async getDecryptedTokens(id: string): Promise<ZaloOAPayload> {
    const integration = await this.integrationRepository.findOne({
      where: { id },
    });

    if (!integration || !integration.encryptedConfig || !integration.secretKey) {
      throw new Error('Integration hoặc token không tồn tại');
    }

    return this.keyPairEncryptionService.decryptObject<ZaloOAPayload>(
      integration.encryptedConfig,
      integration.secretKey
    );
  }

  /**
   * Tìm Zalo OA theo oaId với token đã giải mã
   */
  async findOneWithTokens(options: { where: { oaId: string; status?: string } }): Promise<any | null> {
    const integration = await this.findIntegrationByOaId(options.where.oaId, options.where.status);

    if (!integration) {
      return null;
    }

    return this.mapIntegrationToZaloOAWithTokens(integration);
  }

  /**
   * Tìm Integration theo oaId
   */
  private async findIntegrationByOaId(oaId: string, status?: string): Promise<Integration | null> {
    try {
      // Tìm provider ZALO_OA
      const provider = await this.integrationProviderRepository.findOne({
        where: { type: 'ZALO_OA' as any }
      });

      if (!provider) {
        this.logger.error('ZALO_OA provider not found');
        return null;
      }

      // Tìm integration theo oaId trong metadata
      let queryBuilder = this.integrationRepository
        .createQueryBuilder('integration')
        .where('integration.typeId = :typeId', { typeId: provider.id })
        .andWhere("integration.metadata->>'oaId' = :oaId", { oaId });

      // Thêm điều kiện status nếu có
      if (status) {
        queryBuilder = queryBuilder.andWhere("integration.metadata->>'status' = :status", { status });
      }

      return await queryBuilder.getOne();
    } catch (error) {
      this.logger.error(`Error finding integration by oaId ${oaId}:`, error);
      return null;
    }
  }

  /**
   * Map Integration entity sang format ZaloOfficialAccount cũ với token đã giải mã
   */
  private async mapIntegrationToZaloOAWithTokens(integration: Integration): Promise<any> {
    const metadata = integration.metadata as any as ZaloOAMetadata;

    try {
      // Giải mã tokens
      const tokens = await this.getDecryptedTokens(integration.id);

      return {
        id: integration.id,
        userId: integration.userId,
        employeeId: integration.employeeId,
        oaId: metadata.oaId,
        name: metadata.name,
        description: metadata.description,
        avatarUrl: metadata.avatarUrl,
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresAt: metadata.expiresAt,
        status: metadata.status,
        createdAt: new Date(metadata.createdAt),
        updatedAt: new Date(metadata.updatedAt),
      };
    } catch (error) {
      this.logger.error(`Error decrypting tokens for integration ${integration.id}: ${error.message}`);

      // Kiểm tra xem có backup tokens trong metadata không
      if (metadata.backupTokens) {
        this.logger.warn(`Using backup tokens for integration ${integration.id}`);
        return {
          id: integration.id,
          userId: integration.userId,
          employeeId: integration.employeeId,
          oaId: metadata.oaId,
          name: metadata.name,
          description: metadata.description,
          avatarUrl: metadata.avatarUrl,
          accessToken: metadata.backupTokens.accessToken,
          refreshToken: metadata.backupTokens.refreshToken,
          expiresAt: metadata.expiresAt,
          status: 'NEEDS_REAUTH', // Đánh dấu cần xác thực lại
          createdAt: new Date(metadata.createdAt),
          updatedAt: new Date(metadata.updatedAt),
        };
      }

      // Fallback to encrypted format với status lỗi
      const fallbackResult = this.mapIntegrationToZaloOA(integration);
      fallbackResult.status = 'DECRYPT_ERROR';
      fallbackResult.accessToken = null;
      fallbackResult.refreshToken = null;

      this.logger.warn(`Integration ${integration.id} marked as DECRYPT_ERROR - tokens cannot be decrypted`);
      return fallbackResult;
    }
  }

  /**
   * Tìm tất cả Zalo OA
   */
  async findAll(): Promise<any[]> {
    try {
      // Tìm provider ZALO_OA
      const provider = await this.integrationProviderRepository.findOne({
        where: { type: 'ZALO_OA' as any }
      });

      if (!provider) {
        this.logger.warn('Không tìm thấy provider ZALO_OA');
        return [];
      }

      // Tìm tất cả Integration có type ZALO_OA
      const integrations = await this.integrationRepository.find({
        where: { typeId: provider.id },
        order: { createdAt: 'DESC' }
      });

      // Map sang format tương thích với ZaloOfficialAccount cũ
      return integrations.map(integration => this.mapIntegrationToZaloOA(integration));
    } catch (error) {
      this.logger.error('Error finding all Zalo OAs:', error);
      return [];
    }
  }

  /**
   * Tìm các tài khoản Zalo OA cần làm mới token
   * @param expiryThreshold Thời gian ngưỡng để xác định token sắp hết hạn
   */
  async findAccountsNeedingRefresh(expiryThreshold: number): Promise<any[]> {
    try {
      // Tìm provider ZALO_OA
      const provider = await this.integrationProviderRepository.findOne({
        where: { type: 'ZALO_OA' as any }
      });

      if (!provider) {
        this.logger.warn('Không tìm thấy provider ZALO_OA');
        return [];
      }

      // Tìm tất cả Integration có type ZALO_OA và status active
      const integrations = await this.integrationRepository
        .createQueryBuilder('integration')
        .where('integration.typeId = :typeId', { typeId: provider.id })
        .andWhere("integration.metadata->>'status' = :status", { status: 'active' })
        .andWhere('integration.encryptedConfig IS NOT NULL')
        .andWhere('integration.secretKey IS NOT NULL')
        .getMany();

      // Filter các integration có expiresAt < expiryThreshold
      const accountsNeedingRefresh: any[] = [];
      for (const integration of integrations) {
        const metadata = integration.metadata as any as ZaloOAMetadata;
        if (metadata.expiresAt && metadata.expiresAt < expiryThreshold) {
          // Map sang format tương thích với ZaloOfficialAccount cũ
          accountsNeedingRefresh.push(this.mapIntegrationToZaloOA(integration));
        }
      }

      return accountsNeedingRefresh;
    } catch (error) {
      this.logger.error('Error finding accounts needing refresh:', error);
      return [];
    }
  }

  /**
   * Map Integration entity sang format ZaloOfficialAccount cũ
   */
  private mapIntegrationToZaloOA(integration: Integration): any {
    const metadata = integration.metadata as any as ZaloOAMetadata;

    return {
      id: integration.id,
      userId: integration.userId,
      employeeId: integration.employeeId,
      oaId: metadata.oaId,
      name: metadata.name,
      description: metadata.description,
      avatarUrl: metadata.avatarUrl,
      // accessToken và refreshToken không trả về trực tiếp vì đã mã hóa
      accessToken: '[ENCRYPTED]',
      refreshToken: '[ENCRYPTED]',
      expiresAt: metadata.expiresAt,
      status: metadata.status,
      createdAt: new Date(metadata.createdAt),
      updatedAt: new Date(metadata.updatedAt),
    };
  }
}
