import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdminSegment } from '../entities/admin-segment.entity';

/**
 * Repository cho AdminSegment entity trong worker
 * Chỉ cần các method cơ bản để đọc dữ liệu
 */
@Injectable()
export class AdminSegmentRepository {
  constructor(
    @InjectRepository(AdminSegment)
    private readonly repository: Repository<AdminSegment>,
  ) {}

  /**
   * Tìm segment theo ID
   */
  async findById(id: number): Promise<AdminSegment | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm segment theo tên
   */
  async findByName(name: string): Promise<AdminSegment | null> {
    return this.repository.findOne({ where: { name } });
  }

  /**
   * Đ<PERSON>m tổng số segments
   */
  async count(): Promise<number> {
    return this.repository.count();
  }
}
