import { ZaloOfficialAccount } from '../entities';
import { PlatformContextEnum } from '../enums/platform-context.enum';
import { ReplyContext } from './reply-context.interface';

/**
 * Clean ZaloOfficialAccount type without redundant fields
 */
export type CleanZaloOfficialAccount = Omit<ZaloOfficialAccount, 'userId' | 'agentId'>;

/**
 * Platform context input interface
 * Input data for building platform-specific context
 */
export interface PlatformContextInput {
  /**
   * Zalo Official Account
   */
  zaloOfficialAccount?: ZaloOfficialAccount;

  /**
   * Zalo Customer ID
   */
  zaloCustomerId?: string;

  /**
   * Platform identifier
   */
  platform?: PlatformContextEnum;

  /**
   * ID of message being replied to (if any)
   */
  replyToMessageId?: string;
}

/**
 * Platform context interface
 * Complete platform-specific context for agent processing
 */
export interface PlatformContext {
  /**
   * Zalo Official Account (clean version without redundant fields)
   */
  zaloOfficialAccount?: CleanZaloOfficialAccount;

  /**
   * Zalo Customer ID
   */
  zaloCustomerId?: string;

  /**
   * Platform context identifier
   */
  platformContext?: PlatformContextEnum;

  /**
   * Reply context if this is a reply to another message
   */
  replyToContext?: ReplyContext;
}
