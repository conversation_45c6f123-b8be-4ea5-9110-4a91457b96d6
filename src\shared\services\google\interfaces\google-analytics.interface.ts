/**
 * Interface cho cấu hình Google Analytics API
 */
export interface GoogleAnalyticsConfig {
  /**
   * Client ID từ Google Cloud Console
   */
  clientId: string;

  /**
   * Client Secret từ Google Cloud Console
   */
  clientSecret: string;

  /**
   * Redirect URI cho OAuth
   */
  redirectUri: string;

  /**
   * Property ID mặc định
   */
  defaultPropertyId?: string;
}

/**
 * Interface cho thông tin xác thực Google Analytics
 */
export interface GoogleAnalyticsCredentials {
  /**
   * Access Token
   */
  accessToken: string;

  /**
   * Refresh Token
   */
  refreshToken?: string;

  /**
   * Thời gian hết hạn của Access Token (Unix timestamp)
   */
  expiresAt?: number;
}

/**
 * Interface cho property info
 */
export interface AnalyticsProperty {
  /**
   * Property ID
   */
  propertyId: string;

  /**
   * Display name
   */
  displayName: string;

  /**
   * Property type
   */
  propertyType: string;

  /**
   * Time zone
   */
  timeZone: string;

  /**
   * Currency code
   */
  currencyCode: string;

  /**
   * Industry category
   */
  industryCategory?: string;

  /**
   * Service level
   */
  serviceLevel?: string;

  /**
   * Create time
   */
  createTime: string;

  /**
   * Update time
   */
  updateTime: string;

  /**
   * Parent
   */
  parent?: string;
}

/**
 * Interface cho dimension
 */
export interface AnalyticsDimension {
  /**
   * Name
   */
  name: string;

  /**
   * Dimension expression
   */
  dimensionExpression?: DimensionExpression;
}

/**
 * Interface cho dimension expression
 */
export interface DimensionExpression {
  /**
   * Lower case
   */
  lowerCase?: CaseExpression;

  /**
   * Upper case
   */
  upperCase?: CaseExpression;

  /**
   * Concatenate
   */
  concatenate?: ConcatenateExpression;
}

/**
 * Interface cho case expression
 */
export interface CaseExpression {
  /**
   * Dimension name
   */
  dimensionName: string;
}

/**
 * Interface cho concatenate expression
 */
export interface ConcatenateExpression {
  /**
   * Dimension names
   */
  dimensionNames: string[];

  /**
   * Delimiter
   */
  delimiter?: string;
}

/**
 * Interface cho metric
 */
export interface AnalyticsMetric {
  /**
   * Name
   */
  name: string;

  /**
   * Expression
   */
  expression?: string;

  /**
   * Invisible
   */
  invisible?: boolean;
}

/**
 * Interface cho date range
 */
export interface DateRange {
  /**
   * Start date (YYYY-MM-DD)
   */
  startDate: string;

  /**
   * End date (YYYY-MM-DD)
   */
  endDate: string;

  /**
   * Name
   */
  name?: string;
}

/**
 * Interface cho filter
 */
export interface AnalyticsFilter {
  /**
   * Field name
   */
  fieldName: string;

  /**
   * String filter
   */
  stringFilter?: StringFilter;

  /**
   * In list filter
   */
  inListFilter?: InListFilter;

  /**
   * Numeric filter
   */
  numericFilter?: NumericFilter;

  /**
   * Between filter
   */
  betweenFilter?: BetweenFilter;

  /**
   * Not expression
   */
  notExpression?: boolean;
}

/**
 * Interface cho string filter
 */
export interface StringFilter {
  /**
   * Match type
   */
  matchType: 'EXACT' | 'BEGINS_WITH' | 'ENDS_WITH' | 'CONTAINS' | 'FULL_REGEXP' | 'PARTIAL_REGEXP';

  /**
   * Value
   */
  value: string;

  /**
   * Case sensitive
   */
  caseSensitive?: boolean;
}

/**
 * Interface cho in list filter
 */
export interface InListFilter {
  /**
   * Values
   */
  values: string[];

  /**
   * Case sensitive
   */
  caseSensitive?: boolean;
}

/**
 * Interface cho numeric filter
 */
export interface NumericFilter {
  /**
   * Operation
   */
  operation: 'EQUAL' | 'LESS_THAN' | 'LESS_THAN_OR_EQUAL' | 'GREATER_THAN' | 'GREATER_THAN_OR_EQUAL';

  /**
   * Value
   */
  value: NumericValue;
}

/**
 * Interface cho between filter
 */
export interface BetweenFilter {
  /**
   * From value
   */
  fromValue: NumericValue;

  /**
   * To value
   */
  toValue: NumericValue;
}

/**
 * Interface cho numeric value
 */
export interface NumericValue {
  /**
   * Int64 value
   */
  int64Value?: string;

  /**
   * Double value
   */
  doubleValue?: number;
}

/**
 * Interface cho order by
 */
export interface OrderBy {
  /**
   * Metric
   */
  metric?: MetricOrderBy;

  /**
   * Dimension
   */
  dimension?: DimensionOrderBy;

  /**
   * Pivot
   */
  pivot?: PivotOrderBy;

  /**
   * Desc
   */
  desc?: boolean;
}

/**
 * Interface cho metric order by
 */
export interface MetricOrderBy {
  /**
   * Metric name
   */
  metricName: string;
}

/**
 * Interface cho dimension order by
 */
export interface DimensionOrderBy {
  /**
   * Dimension name
   */
  dimensionName: string;

  /**
   * Order type
   */
  orderType?: 'ALPHANUMERIC' | 'CASE_INSENSITIVE_ALPHANUMERIC' | 'NUMERIC';
}

/**
 * Interface cho pivot order by
 */
export interface PivotOrderBy {
  /**
   * Metric name
   */
  metricName: string;

  /**
   * Pivot selections
   */
  pivotSelections: PivotSelection[];
}

/**
 * Interface cho pivot selection
 */
export interface PivotSelection {
  /**
   * Dimension name
   */
  dimensionName: string;

  /**
   * Dimension value
   */
  dimensionValue: string;
}

/**
 * Interface cho run report request
 */
export interface RunReportRequest {
  /**
   * Property
   */
  property: string;

  /**
   * Dimensions
   */
  dimensions?: AnalyticsDimension[];

  /**
   * Metrics
   */
  metrics?: AnalyticsMetric[];

  /**
   * Date ranges
   */
  dateRanges?: DateRange[];

  /**
   * Dimension filter
   */
  dimensionFilter?: FilterExpression;

  /**
   * Metric filter
   */
  metricFilter?: FilterExpression;

  /**
   * Offset
   */
  offset?: string;

  /**
   * Limit
   */
  limit?: string;

  /**
   * Metric aggregations
   */
  metricAggregations?: string[];

  /**
   * Order bys
   */
  orderBys?: OrderBy[];

  /**
   * Currency code
   */
  currencyCode?: string;

  /**
   * Cohort spec
   */
  cohortSpec?: CohortSpec;

  /**
   * Keep empty rows
   */
  keepEmptyRows?: boolean;

  /**
   * Return property quota
   */
  returnPropertyQuota?: boolean;
}

/**
 * Interface cho filter expression
 */
export interface FilterExpression {
  /**
   * And group
   */
  andGroup?: FilterExpressionList;

  /**
   * Or group
   */
  orGroup?: FilterExpressionList;

  /**
   * Not expression
   */
  notExpression?: FilterExpression;

  /**
   * Filter
   */
  filter?: AnalyticsFilter;
}

/**
 * Interface cho filter expression list
 */
export interface FilterExpressionList {
  /**
   * Expressions
   */
  expressions: FilterExpression[];
}

/**
 * Interface cho cohort spec
 */
export interface CohortSpec {
  /**
   * Cohorts
   */
  cohorts: Cohort[];

  /**
   * Cohorts range
   */
  cohortsRange?: CohortsRange;

  /**
   * Cohort report settings
   */
  cohortReportSettings?: CohortReportSettings;
}

/**
 * Interface cho cohort
 */
export interface Cohort {
  /**
   * Name
   */
  name: string;

  /**
   * Dimension
   */
  dimension: string;

  /**
   * Date range
   */
  dateRange: DateRange;
}

/**
 * Interface cho cohorts range
 */
export interface CohortsRange {
  /**
   * Granularity
   */
  granularity: 'DAILY' | 'WEEKLY' | 'MONTHLY';

  /**
   * Start offset
   */
  startOffset: number;

  /**
   * End offset
   */
  endOffset: number;
}

/**
 * Interface cho cohort report settings
 */
export interface CohortReportSettings {
  /**
   * Accumulate
   */
  accumulate?: boolean;
}

/**
 * Interface cho run report response
 */
export interface RunReportResponse {
  /**
   * Dimension headers
   */
  dimensionHeaders: DimensionHeader[];

  /**
   * Metric headers
   */
  metricHeaders: MetricHeader[];

  /**
   * Rows
   */
  rows: Row[];

  /**
   * Totals
   */
  totals: Row[];

  /**
   * Maximums
   */
  maximums: Row[];

  /**
   * Minimums
   */
  minimums: Row[];

  /**
   * Row count
   */
  rowCount: number;

  /**
   * Metadata
   */
  metadata: ResponseMetaData;

  /**
   * Property quota
   */
  propertyQuota?: PropertyQuota;

  /**
   * Kind
   */
  kind?: string;
}

/**
 * Interface cho dimension header
 */
export interface DimensionHeader {
  /**
   * Name
   */
  name: string;
}

/**
 * Interface cho metric header
 */
export interface MetricHeader {
  /**
   * Name
   */
  name: string;

  /**
   * Type
   */
  type: 'METRIC_TYPE_UNSPECIFIED' | 'TYPE_INTEGER' | 'TYPE_FLOAT' | 'TYPE_SECONDS' | 'TYPE_MILLISECONDS' | 'TYPE_MINUTES' | 'TYPE_HOURS' | 'TYPE_STANDARD' | 'TYPE_CURRENCY' | 'TYPE_FEET' | 'TYPE_MILES' | 'TYPE_METERS' | 'TYPE_KILOMETERS';
}

/**
 * Interface cho row
 */
export interface Row {
  /**
   * Dimension values
   */
  dimensionValues: DimensionValue[];

  /**
   * Metric values
   */
  metricValues: MetricValue[];
}

/**
 * Interface cho dimension value
 */
export interface DimensionValue {
  /**
   * Value
   */
  value: string;
}

/**
 * Interface cho metric value
 */
export interface MetricValue {
  /**
   * Value
   */
  value: string;
}

/**
 * Interface cho response metadata
 */
export interface ResponseMetaData {
  /**
   * Data loss from other row
   */
  dataLossFromOtherRow?: boolean;

  /**
   * Schema restriction response
   */
  schemaRestrictionResponse?: SchemaRestrictionResponse;

  /**
   * Currency code
   */
  currencyCode?: string;

  /**
   * Time zone
   */
  timeZone?: string;

  /**
   * Empty reason
   */
  emptyReason?: string;

  /**
   * Subject to thresholding
   */
  subjectToThresholding?: boolean;
}

/**
 * Interface cho schema restriction response
 */
export interface SchemaRestrictionResponse {
  /**
   * Active metric restrictions
   */
  activeMetricRestrictions: ActiveMetricRestriction[];
}

/**
 * Interface cho active metric restriction
 */
export interface ActiveMetricRestriction {
  /**
   * Metric name
   */
  metricName: string;

  /**
   * Restricted metric types
   */
  restrictedMetricTypes: string[];
}

/**
 * Interface cho property quota
 */
export interface PropertyQuota {
  /**
   * Tokens per day
   */
  tokensPerDay?: QuotaStatus;

  /**
   * Tokens per hour
   */
  tokensPerHour?: QuotaStatus;

  /**
   * Concurrent requests
   */
  concurrentRequests?: QuotaStatus;

  /**
   * Server errors per project per hour
   */
  serverErrorsPerProjectPerHour?: QuotaStatus;

  /**
   * Potentially thresholded requests per hour
   */
  potentiallyThresholdedRequestsPerHour?: QuotaStatus;
}

/**
 * Interface cho quota status
 */
export interface QuotaStatus {
  /**
   * Consumed
   */
  consumed?: number;

  /**
   * Remaining
   */
  remaining?: number;
}

/**
 * Interface cho realtime report request
 */
export interface RunRealtimeReportRequest {
  /**
   * Property
   */
  property: string;

  /**
   * Dimensions
   */
  dimensions?: AnalyticsDimension[];

  /**
   * Metrics
   */
  metrics?: AnalyticsMetric[];

  /**
   * Dimension filter
   */
  dimensionFilter?: FilterExpression;

  /**
   * Metric filter
   */
  metricFilter?: FilterExpression;

  /**
   * Limit
   */
  limit?: string;

  /**
   * Metric aggregations
   */
  metricAggregations?: string[];

  /**
   * Order bys
   */
  orderBys?: OrderBy[];

  /**
   * Return property quota
   */
  returnPropertyQuota?: boolean;

  /**
   * Minute ranges
   */
  minuteRanges?: MinuteRange[];
}

/**
 * Interface cho minute range
 */
export interface MinuteRange {
  /**
   * Start minutes ago
   */
  startMinutesAgo?: number;

  /**
   * End minutes ago
   */
  endMinutesAgo?: number;

  /**
   * Name
   */
  name?: string;
}

/**
 * Interface cho batch run reports request
 */
export interface BatchRunReportsRequest {
  /**
   * Property
   */
  property: string;

  /**
   * Requests
   */
  requests: RunReportRequest[];
}

/**
 * Interface cho batch run reports response
 */
export interface BatchRunReportsResponse {
  /**
   * Reports
   */
  reports: RunReportResponse[];

  /**
   * Kind
   */
  kind?: string;
}

/**
 * Interface cho get metadata request
 */
export interface GetMetadataRequest {
  /**
   * Name
   */
  name: string;
}

/**
 * Interface cho metadata
 */
export interface Metadata {
  /**
   * Name
   */
  name: string;

  /**
   * Dimensions
   */
  dimensions: DimensionMetadata[];

  /**
   * Metrics
   */
  metrics: MetricMetadata[];
}

/**
 * Interface cho dimension metadata
 */
export interface DimensionMetadata {
  /**
   * API name
   */
  apiName: string;

  /**
   * UI name
   */
  uiName: string;

  /**
   * Description
   */
  description: string;

  /**
   * Deprecated API names
   */
  deprecatedApiNames: string[];

  /**
   * Custom definition
   */
  customDefinition: boolean;

  /**
   * Category
   */
  category: string;
}

/**
 * Interface cho metric metadata
 */
export interface MetricMetadata {
  /**
   * API name
   */
  apiName: string;

  /**
   * UI name
   */
  uiName: string;

  /**
   * Description
   */
  description: string;

  /**
   * Type
   */
  type: string;

  /**
   * Expression
   */
  expression: string;

  /**
   * Custom definition
   */
  customDefinition: boolean;

  /**
   * Deprecated API names
   */
  deprecatedApiNames: string[];

  /**
   * Category
   */
  category: string;
}
