
/**
 * Interface for media with context data from JOIN query
 */
export interface MediaWithContextData {
  // Context fields
  contextId: string;
  threadId: string;
  zaloMediaId: string;
  contextType: string;
  humanNotes?: string;
  contextCreatedAt: number;

  // Media fields (nullable if media doesn't exist)
  mediaId?: string;
  fileName?: string;
  description?: string;
  s3Key?: string;
  mimeType?: string;
  tags?: string[];
  mediaCreatedAt?: number;
}