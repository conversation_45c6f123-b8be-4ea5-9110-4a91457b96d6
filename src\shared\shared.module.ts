import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { EmailService } from './services/email.service';
import { SystemEmailConfigurationService } from './services/system-email-configuration.service';
import { KeyPairEncryptionService } from './services/encryption/key-pair-encryption.service';
import { EncryptionService } from './services/encryption/encryption.service';
import { IntegrationService } from './services/integration.service';
// import { ClientsModule, Transport } from '@nestjs/microservices';
import { SystemConfiguration } from '../modules/system-configuration/entities/system-configuration.entity';
import { Integration } from './entities/integration.entity';
import { IntegrationProvider } from './entities/integration-provider.entity';


/**
 * Module chứa các service dùng chung trong toàn bộ ứng dụng
 */
@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([
      SystemConfiguration,
      Integration,
      IntegrationProvider,
    ]),
    ConfigModule,
  ],
  providers: [
    EmailService,
    SystemEmailConfigurationService,
    EncryptionService,
    KeyPairEncryptionService,
    IntegrationService,
  ],
  exports: [
    EmailService,
    SystemEmailConfigurationService,
    EncryptionService,
    KeyPairEncryptionService,
    IntegrationService,
    /* ClientsModule, */
  ],
})
export class SharedModule {}

/**
 * Parse Redis URL to extract connection options
 * @param redisUrl Redis URL string
 * @returns Redis connection options
 */
function parseRedisUrl(redisUrl: string) {
  try {
    const url = new URL(redisUrl);

    return {
      host: url.hostname,
      port: parseInt(url.port) || 6379,
      password: url.password || undefined,
      db: parseInt(url.pathname.slice(1)) || 0, // Extract DB from URL path
    };
  } catch (error) {
    // Fallback to default values if URL parsing fails
    return {
      host: 'localhost',
      port: 6379,
      db: 0,
    };
  }
}
