import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { ProviderShipmentTypeEnum } from '../enums/provider-shipment-type.enum';

/**
 * User provider shipment entity - represents shipment provider configurations for users
 */
@Entity('user_provider_shipments')
export class UserProviderShipment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'name', type: 'varchar', length: 255, nullable: true })
  name: string | null;

  @Column({ name: 'key', type: 'text' })
  key: string;

  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  @Column({
    name: 'type',
    type: 'enum',
    enum: ProviderShipmentTypeEnum,
  })
  type: ProviderShipmentTypeEnum;

  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;
}
