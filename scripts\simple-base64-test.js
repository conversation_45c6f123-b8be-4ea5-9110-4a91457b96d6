// Test Base64 đơn giản
console.log('=== Test Base64 Encoding/Decoding ===');

const testMessage = 'Mã xác thực của bạn là: 123456';
console.log('Original:', testMessage);

const encoded = Buffer.from(testMessage, 'utf8').toString('base64');
console.log('Encoded:', encoded);

const decoded = Buffer.from(encoded, 'base64').toString('utf8');
console.log('Decoded:', decoded);

console.log('Match:', testMessage === decoded ? 'YES' : 'NO');

// Test với ký tự tiếng Việt
const vietnameseMessage = 'Chào mừng bạn đến với RedAI! Mã OTP: 789012';
console.log('\nVietnamese Original:', vietnameseMessage);

const vietnameseEncoded = Buffer.from(vietnameseMessage, 'utf8').toString('base64');
console.log('Vietnamese Encoded:', vietnameseEncoded);

const vietnameseDecoded = Buffer.from(vietnameseEncoded, 'base64').toString('utf8');
console.log('Vietnamese Decoded:', vietnameseDecoded);

console.log('Vietnamese Match:', vietnameseMessage === vietnameseDecoded ? 'YES' : 'NO');
