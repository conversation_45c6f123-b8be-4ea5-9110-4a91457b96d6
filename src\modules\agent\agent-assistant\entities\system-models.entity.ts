import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { ProviderEnum } from '../enums/model-capabilities.enum';

/**
 * SystemModels entity
 * Stores system model configurations
 */
@Entity('system_models')
export class SystemModels {
  /**
   * UUID unique identifier for system model
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Model identifier
   */
  @Column({ name: 'model_id', type: 'varchar', length: 255, nullable: false })
  modelId: string;

  /**
   * Model registry ID reference to model_registry
   */
  @Column({ name: 'model_registry_id', type: 'uuid', nullable: true })
  modelRegistryId?: string;

  /**
   * Whether the model is active
   */
  @Column({ name: 'active', type: 'boolean', default: false, nullable: false })
  active: boolean;

  /**
   * Model provider
   */
  @Column({ name: 'provider', type: 'varchar', default: 'OPENAI', nullable: false })
  provider: ProviderEnum;

  /**
   * Whether the model is fine-tuned
   */
  @Column({ name: 'is_fine_tuned', type: 'boolean', default: false, nullable: false })
  isFineTuned: boolean;
}
