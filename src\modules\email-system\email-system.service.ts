import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdminTemplateEmail } from './entities/admin-template-email.entity';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { QueueName } from '../../queue';
import { EmailSystemJobDto } from './dto/email-system-job.dto';

/**
 * Service xử lý các chức năng liên quan đến email system
 */
@Injectable()
export class EmailSystemService {
  private readonly logger = new Logger(EmailSystemService.name);

  constructor(
    @InjectRepository(AdminTemplateEmail)
    private readonly adminTemplateEmailRepository: Repository<AdminTemplateEmail>,
    @InjectQueue(QueueName.EMAIL_SYSTEM)
    private readonly emailQueue: Queue,
  ) {}

  /**
   * Thêm job gửi email vào queue
   * @param jobData Dữ liệu job email
   * @returns Job ID
   */
  async addEmailJob(jobData: EmailSystemJobDto): Promise<string> {
    try {
      const job = await this.emailQueue.add('send-email', jobData);
      this.logger.log(`Đã thêm job email vào queue với ID: ${job.id}`);
      return job.id as string;
    } catch (error) {
      this.logger.error(
        `Lỗi khi thêm job email vào queue: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy template email theo category
   * @param category Danh mục email cần tìm
   * @returns Template email tìm thấy hoặc null
   */
  async getTemplateByCategory(
    category: string,
  ): Promise<AdminTemplateEmail | null> {
    try {
      const template = await this.adminTemplateEmailRepository.findOne({
        where: { category },
      });

      if (!template) {
        this.logger.warn(
          `Template email với category ${category} không tồn tại`,
        );
        return null;
      }

      return template;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy template email: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Thay thế các placeholder trong nội dung email
   * @param content Nội dung cần thay thế
   * @param data Dữ liệu để thay thế
   * @returns Nội dung đã được thay thế
   */
  replacePlaceholders(content: string, data: Record<string, any>): string {
    if (!content) return '';

    let replacedContent = content;

    // Thay thế các placeholder dạng {{key}} trong nội dung
    Object.entries(data).forEach(([key, value]) => {
      const placeholder = new RegExp(`{{${key}}}`, 'g');
      replacedContent = replacedContent.replace(placeholder, String(value));
    });

    return replacedContent;
  }
}
