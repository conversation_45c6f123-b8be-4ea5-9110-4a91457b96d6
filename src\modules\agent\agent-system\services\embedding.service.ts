import { Injectable, Logger } from '@nestjs/common';
import { backOff } from 'exponential-backoff';
import OpenAI from 'openai';
import { ChatDatabaseService } from '../database.service';
import { embeddingKeyEncryption } from '../../helpers/embedding-key-encryption.helper';

export interface EmbeddingKeyRecord {
  id: number;
  key: string;
  provider: string;
  created_at: number;
  updated_at: number;
  active: boolean;
}

export interface EmbeddingResponse {
  embedding: number[];
  model: string;
  usage: {
    prompt_tokens: number;
    total_tokens: number;
  };
}

export interface EmbeddingError {
  keyId: number;
  provider: string;
  error: string;
  statusCode?: number;
  retryAfter?: number;
  attemptNumber: number;
}

/**
 * Consolidated Embedding Service
 *
 * This service handles both API key management and embedding generation using OpenAI SDK
 * with exponential backoff across multiple API keys, caching mechanisms, and admin methods.
 */
@Injectable()
export class EmbeddingService {
  private readonly logger = new Logger(EmbeddingService.name);

  // Cache for decrypted keys and OpenAI clients
  private keyCache = new Map<number, string>();
  private clientCache = new Map<number, OpenAI>();
  private cacheExpiry = new Map<number, number>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor(private readonly databaseService: ChatDatabaseService) {
    setInterval(() => this.clearExpiredCache(), 60 * 1000);
  }

  private clearExpiredCache() {
    const now = Date.now();
    this.logger.debug('Running periodic cache cleanup...');
    for (const [keyId, expiry] of this.cacheExpiry.entries()) {
      if (now >= expiry) {
        this.clientCache.delete(keyId);
        this.keyCache.delete(keyId);
        this.cacheExpiry.delete(keyId);
        this.logger.debug(`Cleared expired cache for key ${keyId}`);
      }
    }
  }
  /**
   * Generate embedding with exponential backoff across multiple API keys
   */
  async generateEmbedding(
    text: string,
    model: string = 'text-embedding-3-small',
    provider: string = 'openai',
  ): Promise<EmbeddingResponse> {
    const startTime = Date.now();

    this.logger.debug(`Generating embedding for text`, {
      textLength: text.length,
      model,
      provider,
    });

    // Get active keys for the provider
    const keys = await this.getActiveEmbeddingKeysByProvider(provider);

    if (keys.length === 0) {
      throw new Error(
        `No active embedding keys found for provider: ${provider}`,
      );
    }

    const errors: EmbeddingError[] = [];
    let lastError: Error | null = null;

    // Try each key with exponential backoff
    for (let keyIndex = 0; keyIndex < keys.length; keyIndex++) {
      const key = keys[keyIndex];

      try {
        const result = await backOff(
          () => this.callEmbeddingAPI(text, model, key),
          {
            numOfAttempts: 3,
            startingDelay: 1000, // 1 second
            timeMultiple: 2, // Double the delay each time
            maxDelay: 30000, // Max 30 seconds
            jitter: 'full', // Add jitter to prevent thundering herd
            retry: (error: any, attemptNumber: number) => {
              const embeddingError: EmbeddingError = {
                keyId: key.id,
                provider: key.provider,
                error: error.message,
                statusCode: error.status || error.response?.status,
                retryAfter: error.headers?.['retry-after'],
                attemptNumber,
              };

              errors.push(embeddingError);

              this.logger.warn(`Embedding API call failed for key ${key.id}`, {
                keyId: key.id,
                keyIndex,
                attemptNumber,
                error: error.message,
                statusCode: error.status || error.response?.status,
                errorType: error.type || error.code,
                retryAfter: error.headers?.['retry-after'],
                willRetry: attemptNumber < 3,
              });

              // Retry logic: retry on 5xx errors, rate limits, and network errors
              const shouldRetry = this.shouldRetryError(error);

              if (!shouldRetry) {
                this.logger.warn(
                  `Not retrying key ${key.id} due to error type`,
                  {
                    keyId: key.id,
                    error: error.message,
                    statusCode: error.status || error.response?.status,
                    errorType: this.getErrorType(error),
                  },
                );
              }

              return shouldRetry;
            },
          },
        );

        const totalDuration = Date.now() - startTime;
        this.logger.log(`Successfully generated embedding`, {
          textLength: text.length,
          model,
          provider,
          keyId: key.id,
          keyIndex,
          totalDuration,
          embeddingDimensions: result.embedding.length,
          usage: result.usage,
          totalErrors: errors.length,
        });

        return result;
      } catch (error) {
        lastError = error;

        this.logger.warn(`All retry attempts exhausted for key ${key.id}`, {
          keyId: key.id,
          keyIndex,
          error: error.message,
          totalAttemptsForThisKey: 3,
        });

        // Continue to next key
        continue;
      }
    }

    // All keys exhausted
    const totalDuration = Date.now() - startTime;
    this.logger.error(`All embedding API keys exhausted`, {
      textLength: text.length,
      model,
      provider,
      totalKeys: keys.length,
      totalDuration,
      totalErrors: errors.length,
      errors: errors.slice(-5), // Log last 5 errors to avoid spam
    });

    throw new Error(
      `Failed to generate embedding after trying ${keys.length} keys. Last error: ${lastError?.message}`,
    );
  }

  /**
   * Get active embedding keys for a specific provider
   */
  private async getActiveEmbeddingKeysByProvider(
    provider: string,
  ): Promise<EmbeddingKeyRecord[]> {
    const selectQuery = `
      SELECT id, key, provider, created_at, updated_at, active
      FROM system_config_embedding_key
      WHERE active = true AND provider = $1
      ORDER BY id ASC
    `;

    try {
      const result = await this.databaseService.query(selectQuery, [provider]);

      this.logger.debug(
        `Retrieved ${result.length} active embedding keys for provider ${provider}`,
        {
          provider,
          keyCount: result.length,
        },
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to retrieve active embedding keys for provider ${provider}`,
        {
          provider,
          error: error.message,
          stack: error.stack,
        },
      );
      throw error;
    }
  }

  /**
   * Call embedding API with a specific key using OpenAI SDK
   */
  private async callEmbeddingAPI(
    text: string,
    model: string,
    keyRecord: EmbeddingKeyRecord,
  ): Promise<EmbeddingResponse> {
    const openaiClient = this.getOpenAIClient(keyRecord);

    try {
      const response = await openaiClient.embeddings.create({
        model,
        input: text,
        encoding_format: 'float',
      });

      // Extract the first embedding (since we're sending single input)
      const embeddingData = response.data[0];

      return {
        embedding: embeddingData.embedding,
        model: response.model,
        usage: response.usage,
      };
    } catch (error) {
      // OpenAI SDK throws specific error types
      this.logger.debug(`OpenAI API call failed for key ${keyRecord.id}`, {
        keyId: keyRecord.id,
        error: error.message,
        type: error.type,
        status: error.status,
        code: error.code,
      });

      throw error; // Re-throw for backoff handling
    }
  }

  /**
   * Get or create OpenAI client for a specific key with caching
   */
  private getOpenAIClient(keyRecord: EmbeddingKeyRecord): OpenAI {
    const now = Date.now();
    const expiry = this.cacheExpiry.get(keyRecord.id);

    // Check if cached client is still valid
    if (expiry && now < expiry && this.clientCache.has(keyRecord.id)) {
      return this.clientCache.get(keyRecord.id)!;
    }

    // Get decrypted API key
    const apiKey = this.getDecryptedKey(keyRecord);

    // Create new OpenAI client
    const openaiClient = new OpenAI({
      apiKey: apiKey,
      timeout: 30000, // 30 second timeout
      maxRetries: 3, // We handle retries ourselves with exponential-backoff
    });

    const newExpiry = now + this.CACHE_TTL;
    // Cache the client
    this.clientCache.set(keyRecord.id, openaiClient);
    this.cacheExpiry.set(keyRecord.id, newExpiry);

    // Schedule cleanup
    setTimeout(() => {
      // Check if the item hasn't been updated since
      if (this.cacheExpiry.get(keyRecord.id) === newExpiry) {
        this.clientCache.delete(keyRecord.id);
        this.keyCache.delete(keyRecord.id);
        this.cacheExpiry.delete(keyRecord.id);
        this.logger.debug(`Cleared expired cache for key ${keyRecord.id}`);
      }
    }, this.CACHE_TTL);

    this.logger.debug(
      `Created and cached OpenAI client for key ${keyRecord.id}`,
      {
        keyId: keyRecord.id,
        provider: keyRecord.provider,
        cacheExpiry: new Date(now + this.CACHE_TTL).toISOString(),
      },
    );

    return openaiClient;
  }

  /**
   * Determine if an error should be retried (OpenAI SDK specific)
   */
  private shouldRetryError(error: any): boolean {
    // Network/connection errors
    if (
      error.code === 'ECONNRESET' ||
      error.code === 'ENOTFOUND' ||
      error.code === 'ETIMEDOUT'
    ) {
      return true;
    }

    // OpenAI SDK error types
    if (error.type) {
      switch (error.type) {
        case 'server_error':
        case 'timeout':
        case 'connection_error':
          return true;
        case 'rate_limit_exceeded':
          return true;
        case 'invalid_request_error':
        case 'authentication_error':
        case 'permission_error':
          return false; // Don't retry client errors
        default:
          return false;
      }
    }

    // HTTP status codes (fallback)
    const status = error.status || error.response?.status;
    if (status) {
      // Retry on server errors (5xx)
      if (status >= 500) return true;
      // Retry on rate limiting (429)
      if (status === 429) return true;
      // Retry on specific 4xx errors that might be transient
      if (status === 408 || status === 409) return true;
    }

    // Default: don't retry
    return false;
  }

  /**
   * Get error type for logging (OpenAI SDK specific)
   */
  private getErrorType(error: any): string {
    if (error.type) {
      return error.type;
    }

    if (error.code) {
      return `network_${error.code}`;
    }

    const status = error.status || error.response?.status;
    if (status) {
      if (status >= 500) return 'server_error';
      if (status === 429) return 'rate_limit';
      if (status === 401) return 'unauthorized';
      if (status === 403) return 'forbidden';
      if (status === 400) return 'bad_request';
      return `http_${status}`;
    }

    return 'unknown_error';
  }

  /**
   * Get decrypted API key with caching
   */
  private getDecryptedKey(keyRecord: EmbeddingKeyRecord): string {
    const now = Date.now();
    const expiry = this.cacheExpiry.get(keyRecord.id);

    // Check if cached key is still valid
    if (expiry && now < expiry && this.keyCache.has(keyRecord.id)) {
      return this.keyCache.get(keyRecord.id)!;
    }

    // Decrypt and cache the key
    try {
      const decryptedKey = embeddingKeyEncryption.decryptEmbeddingApiKey(
        keyRecord.key,
      );
      this.keyCache.set(keyRecord.id, decryptedKey);
      // Note: We don't update cacheExpiry here since it's shared with client cache

      this.logger.debug(`Decrypted and cached embedding key`, {
        keyId: keyRecord.id,
        provider: keyRecord.provider,
      });

      return decryptedKey;
    } catch (error) {
      this.logger.error(`Failed to decrypt embedding key ${keyRecord.id}`, {
        keyId: keyRecord.id,
        provider: keyRecord.provider,
        error: error.message,
      });
      throw new Error(`Failed to decrypt embedding key: ${error.message}`);
    }
  }
}
