/**
 * DTO cho webhook events từ email providers
 */

/**
 * Base interface cho tất cả email webhook events
 */
export interface BaseEmailWebhookEvent {
  /**
   * Loại event
   */
  event: string;

  /**
   * Timestamp của event
   */
  timestamp: number;

  /**
   * Email người nhận
   */
  email: string;

  /**
   * Tracking ID (nếu có)
   */
  trackingId?: string;

  /**
   * Message ID từ email provider
   */
  messageId?: string;
}

/**
 * SendGrid webhook event
 */
export interface SendGridWebhookEvent extends BaseEmailWebhookEvent {
  /**
   * SendGrid specific fields
   */
  sg_message_id: string;
  sg_event_id: string;
  reason?: string;
  status?: string;
  response?: string;
  attempt?: number;
  url?: string; // For click events
  useragent?: string;
  ip?: string;
}

/**
 * Mailgun webhook event
 */
export interface MailgunWebhookEvent extends BaseEmailWebhookEvent {
  /**
   * Mailgun specific fields
   */
  'message-id': string;
  recipient: string;
  domain: string;
  reason?: string;
  code?: string;
  error?: string;
  description?: string;
  'notification-type'?: string;
  url?: string; // For click events
  'client-info'?: {
    'client-name'?: string;
    'client-os'?: string;
    'device-type'?: string;
    'user-agent'?: string;
  };
  geolocation?: {
    country?: string;
    region?: string;
    city?: string;
  };
}

/**
 * Amazon SES webhook event (via SNS)
 */
export interface SESWebhookEvent {
  /**
   * SNS message type
   */
  Type: 'Notification' | 'SubscriptionConfirmation';

  /**
   * SNS message
   */
  Message?: string;

  /**
   * Subscribe URL (for confirmation)
   */
  SubscribeURL?: string;

  /**
   * Parsed SES event (from Message field)
   */
  eventType?: 'send' | 'delivery' | 'bounce' | 'complaint' | 'reject';

  mail?: {
    messageId: string;
    timestamp: string;
    source: string;
    destination: string[];
    commonHeaders?: {
      subject?: string;
      from?: string[];
      to?: string[];
    };
  };

  delivery?: {
    timestamp: string;
    processingTimeMillis: number;
    recipients: string[];
    smtpResponse: string;
    reportingMTA: string;
  };

  bounce?: {
    bounceType: 'Permanent' | 'Transient';
    bounceSubType: string;
    timestamp: string;
    feedbackId: string;
    bouncedRecipients: Array<{
      emailAddress: string;
      action?: string;
      status?: string;
      diagnosticCode?: string;
    }>;
  };

  complaint?: {
    complainedRecipients: Array<{
      emailAddress: string;
    }>;
    timestamp: string;
    feedbackId: string;
    complaintFeedbackType?: string;
  };
}

/**
 * Generic SMTP webhook event
 */
export interface SMTPWebhookEvent extends BaseEmailWebhookEvent {
  /**
   * SMTP response code
   */
  smtpCode?: number;

  /**
   * SMTP response message
   */
  smtpResponse?: string;

  /**
   * Bounce type (hard/soft)
   */
  bounceType?: 'hard' | 'soft';

  /**
   * Bounce reason
   */
  bounceReason?: string;

  /**
   * Provider name
   */
  provider?: string;

  /**
   * Additional metadata
   */
  metadata?: Record<string, any>;
}

/**
 * Webhook verification result
 */
export interface WebhookVerificationResult {
  /**
   * Verification success
   */
  isValid: boolean;

  /**
   * Error message (if verification failed)
   */
  error?: string;

  /**
   * Provider name
   */
  provider: string;
}

/**
 * Processed webhook event result
 */
export interface ProcessedWebhookEvent {
  /**
   * Processing success
   */
  success: boolean;

  /**
   * Tracking ID extracted
   */
  trackingId?: string;

  /**
   * Event type processed
   */
  eventType?:
    | 'sent'
    | 'delivered'
    | 'opened'
    | 'clicked'
    | 'bounced'
    | 'failed';

  /**
   * Error message (if processing failed)
   */
  error?: string;

  /**
   * Events processed count
   */
  eventsProcessed?: number;
}

/**
 * Delivery status summary
 */
export interface DeliveryStatusSummary {
  /**
   * Campaign ID
   */
  campaignId: number;

  /**
   * Total emails sent
   */
  totalSent: number;

  /**
   * Successfully delivered
   */
  delivered: number;

  /**
   * Bounced emails
   */
  bounced: number;

  /**
   * Opened emails
   */
  opened: number;

  /**
   * Clicked emails
   */
  clicked: number;

  /**
   * Failed emails
   */
  failed: number;

  /**
   * Delivery rate (%)
   */
  deliveryRate: number;

  /**
   * Open rate (%)
   */
  openRate: number;

  /**
   * Click rate (%)
   */
  clickRate: number;

  /**
   * Bounce rate (%)
   */
  bounceRate: number;
}

/**
 * Email validation result
 */
export interface EmailValidationResult {
  /**
   * Email address
   */
  email: string;

  /**
   * Validation result
   */
  isValid: boolean;

  /**
   * Validation reason
   */
  reason?: string;

  /**
   * Risk level
   */
  riskLevel: 'low' | 'medium' | 'high';

  /**
   * Suggested action
   */
  suggestedAction?: 'send' | 'review' | 'block';
}
