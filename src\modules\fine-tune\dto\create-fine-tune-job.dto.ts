import { IsS<PERSON>, <PERSON><PERSON><PERSON>ber, IsOptional, IsEnum, IsObject, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { ProviderFineTuneEnum, FineTuneContextEnum, ModelTypeEnum } from '../constants';

/**
 * DTO cho training data
 */
export class TrainingDataDto {
  @ApiProperty({
    description: 'Dữ liệu training (JSONL format)',
    example: '{"messages": [{"role": "user", "content": "Hello"}, {"role": "assistant", "content": "Hi there!"}]}',
  })
  @IsString()
  trainData: string;

  @ApiProperty({
    description: 'Dữ liệu validation (JSONL format)',
    example: '{"messages": [{"role": "user", "content": "Test"}, {"role": "assistant", "content": "Test response"}]}',
    required: false,
  })
  @IsOptional()
  @IsString()
  validationData?: string;

  @ApiProperty({
    description: 'Tên file training',
    example: 'training_data.jsonl',
    required: false,
  })
  @IsOptional()
  @IsString()
  trainFileName?: string;

  @ApiProperty({
    description: 'Tên file validation',
    example: 'validation_data.jsonl',
    required: false,
  })
  @IsOptional()
  @IsString()
  validationFileName?: string;
}

/**
 * DTO cho hyperparameters
 */
export class HyperparametersDto {
  @ApiProperty({
    description: 'Số epoch để huấn luyện',
    example: 3,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  nEpochs?: number;

  @ApiProperty({
    description: 'Kích thước batch',
    example: 'auto',
    required: false,
  })
  @IsOptional()
  batchSize?: number | 'auto';

  @ApiProperty({
    description: 'Tốc độ học',
    example: 'auto',
    required: false,
  })
  @IsOptional()
  learningRateMultiplier?: number | 'auto';
}

/**
 * DTO để tạo fine-tune job
 */
export class CreateFineTuneJobDto {
  @ApiProperty({
    description: 'Context: ADMIN hoặc USER',
    enum: FineTuneContextEnum,
    example: FineTuneContextEnum.USER,
  })
  @IsEnum(FineTuneContextEnum)
  context: FineTuneContextEnum;

  @ApiProperty({
    description: 'ID người dùng (bắt buộc cho USER context)',
    example: 123,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  userId?: number;

  @ApiProperty({
    description: 'ID employee (cho ADMIN context)',
    example: 456,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  employeeId?: number;

  @ApiProperty({
    description: 'Nhà cung cấp AI',
    enum: ProviderFineTuneEnum,
    example: ProviderFineTuneEnum.OPENAI,
  })
  @IsEnum(ProviderFineTuneEnum)
  provider: ProviderFineTuneEnum;

  @ApiProperty({
    description: 'Loại model: SYSTEM hoặc USER',
    enum: ModelTypeEnum,
    example: ModelTypeEnum.SYSTEM,
  })
  @IsEnum(ModelTypeEnum)
  modelType: ModelTypeEnum;

  @ApiProperty({
    description: 'Model cơ sở để fine-tune',
    example: 'gpt-3.5-turbo',
  })
  @IsString()
  baseModel: string;

  @ApiProperty({
    description: 'Training data (nếu chưa có file ID)',
    type: TrainingDataDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => TrainingDataDto)
  trainingData?: TrainingDataDto;

  @ApiProperty({
    description: 'ID của training file đã upload',
    example: 'file-abc123',
    required: false,
  })
  @IsOptional()
  @IsString()
  trainingFileId?: string;

  @ApiProperty({
    description: 'ID của validation file (tùy chọn)',
    example: 'file-def456',
    required: false,
  })
  @IsOptional()
  @IsString()
  validationFileId?: string;

  @ApiProperty({
    description: 'Hyperparameters cho fine-tuning',
    type: HyperparametersDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => HyperparametersDto)
  hyperparameters?: HyperparametersDto;

  @ApiProperty({
    description: 'Metadata bổ sung',
    example: { description: 'Custom fine-tune for chatbot' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'Số R-Points cần hoàn nếu job fail (chỉ cho User + System model)',
    example: 1000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  pointsToRefund?: number;

  @ApiProperty({
    description: 'ID của model registry',
    example: 'uuid-model-registry',
  })
  @IsString()
  modelRegistryId: string;

  @ApiProperty({
    description: 'ID của user key LLM (cho user model)',
    example: 'uuid-user-key',
    required: false,
  })
  @IsOptional()
  @IsString()
  userKeyId?: string;
}
