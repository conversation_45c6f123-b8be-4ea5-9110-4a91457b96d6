/**
 * Interface cho cấu hình Google Calendar API
 */
export interface GoogleCalendarConfig {
  /**
   * Client ID từ Google Cloud Console
   */
  clientId: string;

  /**
   * Client Secret từ Google Cloud Console
   */
  clientSecret: string;

  /**
   * Redirect URI cho OAuth
   */
  redirectUri: string;
}

/**
 * Interface cho thông tin xác thực Google Calendar
 */
export interface GoogleCalendarCredentials {
  /**
   * Access Token
   */
  accessToken: string;

  /**
   * Refresh Token
   */
  refreshToken?: string;

  /**
   * Thời gian hết hạn của Access Token (Unix timestamp)
   */
  expiresAt?: number;
}

/**
 * Interface cho thông tin calendar
 */
export interface CalendarInfo {
  /**
   * ID của calendar
   */
  id: string;

  /**
   * Tên calendar
   */
  summary: string;

  /**
   * Mô tả calendar
   */
  description?: string;

  /**
   * Timezone
   */
  timeZone: string;

  /**
   * <PERSON><PERSON><PERSON> sắc
   */
  backgroundColor?: string;

  /**
   * Màu foreground
   */
  foregroundColor?: string;

  /**
   * Access role
   */
  accessRole?: string;

  /**
   * <PERSON><PERSON> phải primary calendar không
   */
  primary?: boolean;

  /**
   * <PERSON><PERSON> đ<PERSON> selected không
   */
  selected?: boolean;
}

/**
 * Interface cho event
 */
export interface CalendarEvent {
  /**
   * ID của event
   */
  id?: string;

  /**
   * Tiêu đề event
   */
  summary: string;

  /**
   * Mô tả event
   */
  description?: string;

  /**
   * Địa điểm
   */
  location?: string;

  /**
   * Thời gian bắt đầu
   */
  start: EventDateTime;

  /**
   * Thời gian kết thúc
   */
  end: EventDateTime;

  /**
   * Có phải all-day event không
   */
  allDay?: boolean;

  /**
   * Timezone
   */
  timeZone?: string;

  /**
   * Danh sách attendees
   */
  attendees?: EventAttendee[];

  /**
   * Organizer
   */
  organizer?: EventOrganizer;

  /**
   * Creator
   */
  creator?: EventCreator;

  /**
   * Recurrence rules
   */
  recurrence?: string[];

  /**
   * Recurring event ID
   */
  recurringEventId?: string;

  /**
   * Original start time
   */
  originalStartTime?: EventDateTime;

  /**
   * Status
   */
  status?: 'confirmed' | 'tentative' | 'cancelled';

  /**
   * Visibility
   */
  visibility?: 'default' | 'public' | 'private' | 'confidential';

  /**
   * Transparency
   */
  transparency?: 'opaque' | 'transparent';

  /**
   * Color ID
   */
  colorId?: string;

  /**
   * Reminders
   */
  reminders?: EventReminders;

  /**
   * Attachments
   */
  attachments?: EventAttachment[];

  /**
   * Conference data
   */
  conferenceData?: ConferenceData;

  /**
   * Thời gian tạo
   */
  created?: string;

  /**
   * Thời gian cập nhật cuối
   */
  updated?: string;

  /**
   * HTML link
   */
  htmlLink?: string;

  /**
   * iCal UID
   */
  iCalUID?: string;

  /**
   * Sequence
   */
  sequence?: number;

  /**
   * Guests can invite others
   */
  guestsCanInviteOthers?: boolean;

  /**
   * Guests can modify
   */
  guestsCanModify?: boolean;

  /**
   * Guests can see other guests
   */
  guestsCanSeeOtherGuests?: boolean;

  /**
   * Private copy
   */
  privateCopy?: boolean;

  /**
   * Locked
   */
  locked?: boolean;

  /**
   * Source
   */
  source?: EventSource;

  /**
   * Extended properties
   */
  extendedProperties?: ExtendedProperties;

  /**
   * Gadget
   */
  gadget?: EventGadget;

  /**
   * Anyone can add self
   */
  anyoneCanAddSelf?: boolean;

  /**
   * Hangout link
   */
  hangoutLink?: string;
}

/**
 * Interface cho event date time
 */
export interface EventDateTime {
  /**
   * Date time (ISO 8601)
   */
  dateTime?: string;

  /**
   * Date (YYYY-MM-DD)
   */
  date?: string;

  /**
   * Timezone
   */
  timeZone?: string;
}

/**
 * Interface cho event attendee
 */
export interface EventAttendee {
  /**
   * Email
   */
  email: string;

  /**
   * Display name
   */
  displayName?: string;

  /**
   * Response status
   */
  responseStatus?: 'needsAction' | 'declined' | 'tentative' | 'accepted';

  /**
   * Comment
   */
  comment?: string;

  /**
   * Additional guests
   */
  additionalGuests?: number;

  /**
   * Optional
   */
  optional?: boolean;

  /**
   * Resource
   */
  resource?: boolean;

  /**
   * Organizer
   */
  organizer?: boolean;

  /**
   * Self
   */
  self?: boolean;

  /**
   * ID
   */
  id?: string;
}

/**
 * Interface cho event organizer
 */
export interface EventOrganizer {
  /**
   * Email
   */
  email: string;

  /**
   * Display name
   */
  displayName?: string;

  /**
   * Self
   */
  self?: boolean;

  /**
   * ID
   */
  id?: string;
}

/**
 * Interface cho event creator
 */
export interface EventCreator {
  /**
   * Email
   */
  email: string;

  /**
   * Display name
   */
  displayName?: string;

  /**
   * Self
   */
  self?: boolean;

  /**
   * ID
   */
  id?: string;
}

/**
 * Interface cho event reminders
 */
export interface EventReminders {
  /**
   * Use default
   */
  useDefault?: boolean;

  /**
   * Overrides
   */
  overrides?: EventReminderOverride[];
}

/**
 * Interface cho reminder override
 */
export interface EventReminderOverride {
  /**
   * Method
   */
  method: 'email' | 'popup';

  /**
   * Minutes before
   */
  minutes: number;
}

/**
 * Interface cho event attachment
 */
export interface EventAttachment {
  /**
   * File URL
   */
  fileUrl: string;

  /**
   * Title
   */
  title?: string;

  /**
   * MIME type
   */
  mimeType?: string;

  /**
   * Icon link
   */
  iconLink?: string;

  /**
   * File ID
   */
  fileId?: string;
}

/**
 * Interface cho conference data
 */
export interface ConferenceData {
  /**
   * Entry points
   */
  entryPoints?: ConferenceEntryPoint[];

  /**
   * Conference solution
   */
  conferenceSolution?: ConferenceSolution;

  /**
   * Conference ID
   */
  conferenceId?: string;

  /**
   * Signature
   */
  signature?: string;

  /**
   * Notes
   */
  notes?: string;
}

/**
 * Interface cho conference entry point
 */
export interface ConferenceEntryPoint {
  /**
   * Entry point type
   */
  entryPointType: 'video' | 'phone' | 'sip' | 'more';

  /**
   * URI
   */
  uri?: string;

  /**
   * Label
   */
  label?: string;

  /**
   * PIN
   */
  pin?: string;

  /**
   * Access code
   */
  accessCode?: string;

  /**
   * Meeting code
   */
  meetingCode?: string;

  /**
   * Passcode
   */
  passcode?: string;

  /**
   * Password
   */
  password?: string;
}

/**
 * Interface cho conference solution
 */
export interface ConferenceSolution {
  /**
   * Key
   */
  key?: ConferenceSolutionKey;

  /**
   * Name
   */
  name?: string;

  /**
   * Icon URI
   */
  iconUri?: string;
}

/**
 * Interface cho conference solution key
 */
export interface ConferenceSolutionKey {
  /**
   * Type
   */
  type: string;
}

/**
 * Interface cho event source
 */
export interface EventSource {
  /**
   * URL
   */
  url: string;

  /**
   * Title
   */
  title: string;
}

/**
 * Interface cho extended properties
 */
export interface ExtendedProperties {
  /**
   * Private
   */
  private?: Record<string, string>;

  /**
   * Shared
   */
  shared?: Record<string, string>;
}

/**
 * Interface cho event gadget
 */
export interface EventGadget {
  /**
   * Type
   */
  type: string;

  /**
   * Title
   */
  title?: string;

  /**
   * Link
   */
  link?: string;

  /**
   * Icon link
   */
  iconLink?: string;

  /**
   * Width
   */
  width?: number;

  /**
   * Height
   */
  height?: number;

  /**
   * Display
   */
  display?: string;

  /**
   * Preferences
   */
  preferences?: Record<string, string>;
}

/**
 * Interface cho create event request
 */
export interface CreateEventRequest {
  /**
   * Calendar ID
   */
  calendarId: string;

  /**
   * Event data
   */
  event: CalendarEvent;

  /**
   * Conference data version
   */
  conferenceDataVersion?: number;

  /**
   * Max attendees
   */
  maxAttendees?: number;

  /**
   * Send notifications
   */
  sendNotifications?: boolean;

  /**
   * Send updates
   */
  sendUpdates?: 'all' | 'externalOnly' | 'none';

  /**
   * Supports attachments
   */
  supportsAttachments?: boolean;
}

/**
 * Interface cho update event request
 */
export interface UpdateEventRequest {
  /**
   * Calendar ID
   */
  calendarId: string;

  /**
   * Event ID
   */
  eventId: string;

  /**
   * Event data
   */
  event: Partial<CalendarEvent>;

  /**
   * Conference data version
   */
  conferenceDataVersion?: number;

  /**
   * Max attendees
   */
  maxAttendees?: number;

  /**
   * Send notifications
   */
  sendNotifications?: boolean;

  /**
   * Send updates
   */
  sendUpdates?: 'all' | 'externalOnly' | 'none';

  /**
   * Supports attachments
   */
  supportsAttachments?: boolean;
}

/**
 * Interface cho list events request
 */
export interface ListEventsRequest {
  /**
   * Calendar ID
   */
  calendarId: string;

  /**
   * Time min (ISO 8601)
   */
  timeMin?: string;

  /**
   * Time max (ISO 8601)
   */
  timeMax?: string;

  /**
   * Query string
   */
  q?: string;

  /**
   * Max results
   */
  maxResults?: number;

  /**
   * Page token
   */
  pageToken?: string;

  /**
   * Order by
   */
  orderBy?: 'startTime' | 'updated';

  /**
   * Show deleted
   */
  showDeleted?: boolean;

  /**
   * Show hidden invitations
   */
  showHiddenInvitations?: boolean;

  /**
   * Single events
   */
  singleEvents?: boolean;

  /**
   * Sync token
   */
  syncToken?: string;

  /**
   * Updated min
   */
  updatedMin?: string;
}

/**
 * Interface cho list events result
 */
export interface ListEventsResult {
  /**
   * Events
   */
  items: CalendarEvent[];

  /**
   * Next page token
   */
  nextPageToken?: string;

  /**
   * Next sync token
   */
  nextSyncToken?: string;

  /**
   * Summary
   */
  summary: string;

  /**
   * Description
   */
  description?: string;

  /**
   * Updated
   */
  updated: string;

  /**
   * Time zone
   */
  timeZone: string;

  /**
   * Access role
   */
  accessRole: string;

  /**
   * Default reminders
   */
  defaultReminders: EventReminderOverride[];
}

/**
 * Interface cho free/busy request
 */
export interface FreeBusyRequest {
  /**
   * Time min (ISO 8601)
   */
  timeMin: string;

  /**
   * Time max (ISO 8601)
   */
  timeMax: string;

  /**
   * Time zone
   */
  timeZone?: string;

  /**
   * Group expansion max
   */
  groupExpansionMax?: number;

  /**
   * Calendar expansion max
   */
  calendarExpansionMax?: number;

  /**
   * Items
   */
  items: FreeBusyRequestItem[];
}

/**
 * Interface cho free/busy request item
 */
export interface FreeBusyRequestItem {
  /**
   * ID
   */
  id: string;
}

/**
 * Interface cho free/busy result
 */
export interface FreeBusyResult {
  /**
   * Time min
   */
  timeMin: string;

  /**
   * Time max
   */
  timeMax: string;

  /**
   * Calendars
   */
  calendars: Record<string, FreeBusyCalendar>;

  /**
   * Groups
   */
  groups: Record<string, FreeBusyGroup>;
}

/**
 * Interface cho free/busy calendar
 */
export interface FreeBusyCalendar {
  /**
   * Busy times
   */
  busy: TimePeriod[];

  /**
   * Errors
   */
  errors?: FreeBusyError[];
}

/**
 * Interface cho time period
 */
export interface TimePeriod {
  /**
   * Start
   */
  start: string;

  /**
   * End
   */
  end: string;
}

/**
 * Interface cho free/busy group
 */
export interface FreeBusyGroup {
  /**
   * Calendars
   */
  calendars: string[];

  /**
   * Errors
   */
  errors?: FreeBusyError[];
}

/**
 * Interface cho free/busy error
 */
export interface FreeBusyError {
  /**
   * Domain
   */
  domain: string;

  /**
   * Reason
   */
  reason: string;
}

/**
 * Interface cho quick add event request
 */
export interface QuickAddEventRequest {
  /**
   * Calendar ID
   */
  calendarId: string;

  /**
   * Text
   */
  text: string;

  /**
   * Send notifications
   */
  sendNotifications?: boolean;
}

/**
 * Interface cho move event request
 */
export interface MoveEventRequest {
  /**
   * Calendar ID
   */
  calendarId: string;

  /**
   * Event ID
   */
  eventId: string;

  /**
   * Destination calendar ID
   */
  destination: string;

  /**
   * Send notifications
   */
  sendNotifications?: boolean;

  /**
   * Send updates
   */
  sendUpdates?: 'all' | 'externalOnly' | 'none';
}
