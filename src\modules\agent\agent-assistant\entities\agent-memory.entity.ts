import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

/**
 * Interface for structured content of agent memories
 * Enforces consistent data structure for JSONB fields
 */
export interface AgentMemoryStructuredContent {
  title: string;
  reason: string;
  content: string;
}

/**
 * Interface for metadata of agent memories
 * Supports additional attributes for memory records
 */
export interface AgentMemoryMetadata {
  source?: string;
  tags?: string[];
  priority?: 'low' | 'medium' | 'high';
  category?: string;
  lastUpdated?: number;
  [key: string]: any;
}

/**
 * Agent Memory entity
 * Stores agent-specific knowledge, skills, and personality traits
 */
@Entity('agent_memories')
@Index('idx_agent_memories_agent_id', ['agentId'])
export class AgentMemory {
  /**
   * UUID primary key for the memory record
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * UUID of the agent that owns this memory
   */
  @Column({ name: 'agent_id', type: 'uuid', nullable: false })
  agentId: string;

  /**
   * Knowledge content in JSON format with {title, reason, content} structure
   */
  @Column({ 
    name: 'structured_content', 
    type: 'jsonb', 
    nullable: false,
    comment: 'Knowledge content in JSON format (e.g., skill_name, description, examples)'
  })
  structuredContent: AgentMemoryStructuredContent;

  /**
   * Additional metadata for the memory record in JSONB format
   */
  @Column({ 
    name: 'metadata', 
    type: 'jsonb', 
    nullable: true,
    comment: 'Additional metadata for the memory record'
  })
  metadata?: AgentMemoryMetadata;

  /**
   * Creation timestamp in Unix epoch milliseconds
   */
  @Column({ 
    name: 'created_at', 
    type: 'bigint', 
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
    nullable: true,
    comment: 'Creation timestamp in Unix epoch milliseconds'
  })
  createdAt?: number;
}
