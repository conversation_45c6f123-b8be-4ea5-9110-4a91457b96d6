import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as vision from '@google-cloud/vision';

@Injectable()
export class GoogleVisionService {
  private client: vision.ImageAnnotatorClient;
  private readonly logger = new Logger(GoogleVisionService.name);

  constructor(private readonly configService: ConfigService) {
    try {
      // Lấy thông tin xác thực từ biến môi trường hoặc file
      const keyFilename = this.configService.get<string>('GOOGLE_APPLICATION_CREDENTIALS');

      if (!keyFilename) {
        this.logger.error('Missing Google Cloud Vision configuration');
        return;
      }

      // Khởi tạo Vision client
      this.client = new vision.ImageAnnotatorClient({
        keyFilename,
      });
      this.logger.log('Google Cloud Vision initialized');
    } catch (error) {
      this.logger.error(`Failed to initialize Google Cloud Vision: ${error.message}`);
    }
  }

  /**
   * <PERSON><PERSON><PERSON> hiện văn bản trong hình ảnh
   * @param imageBuffer Buffer của hình <PERSON>nh
   * @returns Văn bản được phát hiện
   */
  async detectText(imageBuffer: Buffer): Promise<string[]> {
    try {
      if (!this.client) {
        const keyFilename = this.configService.get<string>('GOOGLE_APPLICATION_CREDENTIALS');
        if (!keyFilename) {
          throw new Error('Missing Google Cloud Vision configuration');
        }
        this.client = new vision.ImageAnnotatorClient({
          keyFilename,
        });
      }

      const [result] = await this.client.textDetection(imageBuffer);
      const detections = result.textAnnotations || [];
      return detections
        .map((text) => text.description)
        .filter((description): description is string => Boolean(description));
    } catch (error) {
      this.logger.error(`Failed to detect text: ${error.message}`);
      throw error;
    }
  }

  /**
   * Phát hiện văn bản trong hình ảnh từ URL
   * @param imageUrl URL của hình ảnh
   * @returns Văn bản được phát hiện
   */
  async detectTextFromUrl(imageUrl: string): Promise<string[]> {
    try {
      if (!this.client) {
        const keyFilename = this.configService.get<string>('GOOGLE_APPLICATION_CREDENTIALS');
        if (!keyFilename) {
          throw new Error('Missing Google Cloud Vision configuration');
        }
        this.client = new vision.ImageAnnotatorClient({
          keyFilename,
        });
      }

      const [result] = await this.client.textDetection(imageUrl);
      const detections = result.textAnnotations || [];
      return detections
        .map((text) => text.description)
        .filter((description): description is string => Boolean(description));
    } catch (error) {
      this.logger.error(`Failed to detect text from URL: ${error.message}`);
      throw error;
    }
  }

  /**
   * Phát hiện nhãn trong hình ảnh
   * @param imageBuffer Buffer của hình ảnh
   * @returns Danh sách nhãn được phát hiện
   */
  async detectLabels(imageBuffer: Buffer): Promise<{ description: string; score: number }[]> {
    try {
      if (!this.client) {
        const keyFilename = this.configService.get<string>('GOOGLE_APPLICATION_CREDENTIALS');
        if (!keyFilename) {
          throw new Error('Missing Google Cloud Vision configuration');
        }
        this.client = new vision.ImageAnnotatorClient({
          keyFilename,
        });
      }

      const [result] = await this.client.labelDetection(imageBuffer);
      const labels = result.labelAnnotations || [];
      return labels
        .filter((label) => label.description && label.score)
        .map((label) => ({
          description: label.description as string,
          score: label.score as number,
        }));
    } catch (error) {
      this.logger.error(`Failed to detect labels: ${error.message}`);
      throw error;
    }
  }

  /**
   * Phát hiện khuôn mặt trong hình ảnh
   * @param imageBuffer Buffer của hình ảnh
   * @returns Thông tin về khuôn mặt được phát hiện
   */
  async detectFaces(imageBuffer: Buffer): Promise<any[]> {
    try {
      if (!this.client) {
        const keyFilename = this.configService.get<string>('GOOGLE_APPLICATION_CREDENTIALS');
        if (!keyFilename) {
          throw new Error('Missing Google Cloud Vision configuration');
        }
        this.client = new vision.ImageAnnotatorClient({
          keyFilename,
        });
      }

      const [result] = await this.client.faceDetection(imageBuffer);
      return result.faceAnnotations || [];
    } catch (error) {
      this.logger.error(`Failed to detect faces: ${error.message}`);
      throw error;
    }
  }

  /**
   * Phát hiện logo trong hình ảnh
   * @param imageBuffer Buffer của hình ảnh
   * @returns Danh sách logo được phát hiện
   */
  async detectLogos(imageBuffer: Buffer): Promise<{ description: string; score: number }[]> {
    try {
      if (!this.client) {
        const keyFilename = this.configService.get<string>('GOOGLE_APPLICATION_CREDENTIALS');
        if (!keyFilename) {
          throw new Error('Missing Google Cloud Vision configuration');
        }
        this.client = new vision.ImageAnnotatorClient({
          keyFilename,
        });
      }

      const [result] = await this.client.logoDetection(imageBuffer);
      const logos = result.logoAnnotations || [];
      return logos
        .filter((logo) => logo.description && logo.score)
        .map((logo) => ({
          description: logo.description as string,
          score: logo.score as number,
        }));
    } catch (error) {
      this.logger.error(`Failed to detect logos: ${error.message}`);
      throw error;
    }
  }

  /**
   * Phát hiện nội dung không phù hợp trong hình ảnh
   * @param imageBuffer Buffer của hình ảnh
   * @returns Thông tin về nội dung không phù hợp
   */
  async detectSafeSearch(imageBuffer: Buffer): Promise<any> {
    try {
      if (!this.client) {
        const keyFilename = this.configService.get<string>('GOOGLE_APPLICATION_CREDENTIALS');
        if (!keyFilename) {
          throw new Error('Missing Google Cloud Vision configuration');
        }
        this.client = new vision.ImageAnnotatorClient({
          keyFilename,
        });
      }

      const [result] = await this.client.safeSearchDetection(imageBuffer);
      return result.safeSearchAnnotation;
    } catch (error) {
      this.logger.error(`Failed to detect safe search: ${error.message}`);
      throw error;
    }
  }

  /**
   * Phát hiện đối tượng trong hình ảnh
   * @param imageBuffer Buffer của hình ảnh
   * @returns Danh sách đối tượng được phát hiện
   */
  async detectObjects(imageBuffer: Buffer): Promise<any[]> {
    try {
      if (!this.client) {
        const keyFilename = this.configService.get<string>('GOOGLE_APPLICATION_CREDENTIALS');
        if (!keyFilename) {
          throw new Error('Missing Google Cloud Vision configuration');
        }
        this.client = new vision.ImageAnnotatorClient({
          keyFilename,
        });
      }

      // Kiểm tra xem phương thức objectLocalization có tồn tại không
      if (!this.client.objectLocalization) {
        throw new Error('Object localization feature is not available');
      }

      const [result] = await this.client.objectLocalization(imageBuffer);
      return result?.localizedObjectAnnotations || [];
    } catch (error) {
      this.logger.error(`Failed to detect objects: ${error.message}`);
      throw error;
    }
  }
}
