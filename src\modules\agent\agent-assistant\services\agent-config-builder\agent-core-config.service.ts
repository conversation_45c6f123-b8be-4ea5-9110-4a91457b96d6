import { Injectable } from '@nestjs/common';
import { ProfileAgent } from '../../schemas';
import { AgentRepository, AgentUserRepository } from '../../repositories';
import { TrimmingTypeEnum } from '../../enums';
import { CoreAgentData } from '../../interfaces/core-agent-data.interface';


/**
 * Agent Core Configuration Service
 * 
 * Handles basic agent data retrieval and validation.
 * Responsible for: "Who is this agent?"
 */
@Injectable()
export class AgentCoreConfigService {
  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly agentUserRepository: AgentUserRepository,
  ) {}

  /**
   * Get core agent data with user ownership validation
   */
  async getCoreAgentData(agentId: string, userId: number): Promise<CoreAgentData> {
    // First validate agent access
    await this.validateAgentAccess(agentId, userId);

    // Get agent user data
    const agentUser = await this.agentUserRepository.findByIdAndUser(agentId, userId);
    if (!agentUser) {
      throw new Error('Agent not found or access denied');
    }

    // Get basic agent data
    const agent = await this.agentRepository.findById(agentId);
    if (!agent) {
      throw new Error('Agent not found');
    }

    return {
      id: agent.id,
      name: agent.name,
      description: 'Default Description', // Agent assistant doesn't have description field
      instruction: agent.instruction || 'You are a helpful AI assistant.',
      vectorStoreId: agent.vectorStoreId || null,
      isSupervisor: false, // User agents are never supervisors
      profile: this.transformProfile(agentUser.profile),
      trimmingConfig: {
        type: TrimmingTypeEnum.TOKEN, // Default to token-based trimming
        threshold: 8000, // Default token limit for cost optimization
      },
      modelConfig: this.parseModelConfig(agent.modelConfig),

      // Model-related properties from agentUser
      userModelId: agentUser.userModelId,
      modelFineTuneId: agentUser.modelFineTuneId,
      systemModelId: agentUser.systemModelId,
      keyLlmId: agentUser.keyLlmId,

      // Payment-related properties from agentUser
      paymentGatewayId: agentUser.paymentGatewayId,
      userProviderShipmentId: agentUser.userProviderShipmentId,
      receiverPayShippingFee: agentUser.receiverPayShippingFee,
      paymentMethods: agentUser.paymentMethods,

      // Strategy-related properties from agentUser
      strategyId: agentUser.strategyId,
    };
  }

  /**
   * Validate that user has access to the agent
   */
  async validateAgentAccess(agentId: string, userId: number): Promise<void> {
    const agentUser = await this.agentUserRepository.findByIdAndUser(agentId, userId);
    if (!agentUser || !agentUser.active) {
      throw new Error('Agent not found or access denied');
    }
  }

  /**
   * Get agent profile data
   */
  async getAgentProfile(agentId: string, userId: number): Promise<ProfileAgent | null> {
    const agentUser = await this.agentUserRepository.findByIdAndUser(agentId, userId);
    return this.transformProfile(agentUser?.profile);
  }

  /**
   * Transform AgentProfile to ProfileAgent (handle enum differences)
   */
  private transformProfile(profile: any): ProfileAgent | null {
    if (!profile) {
      return null;
    }

    return {
      gender: profile.gender ? profile.gender.toUpperCase() as any : undefined,
      dateOfBirth: profile.dateOfBirth,
      position: profile.position,
      education: profile.education,
      skills: profile.skills,
      personality: profile.personality,
      languages: profile.languages,
      nations: profile.nations,
    };
  }

  /**
   * Parse model configuration from Agent.modelConfig JSONB field
   */
  private parseModelConfig(modelConfigJson: any): { temperature?: number; top_p?: number; top_k?: number; max_tokens?: number; } | undefined {
    if (!modelConfigJson || typeof modelConfigJson !== 'object') {
      return undefined;
    }

    return {
      temperature: modelConfigJson.temperature,
      top_p: modelConfigJson.top_p,
      top_k: modelConfigJson.top_k,
      max_tokens: modelConfigJson.max_tokens,
    };
  }
}
