import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { QueueName } from '../../queue';
import { AffiliateClick } from './entities/affiliate-click.entity';
import { AffiliateClickRepository } from './repositories/affiliate-click.repository';
import { AffiliateClickProcessor } from './affiliate-click.processor';
import { AffiliateClickService } from './services/affiliate-click.service';

/**
 * Module quản lý các chức năng liên quan đến affiliate
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([AffiliateClick]),
    BullModule.registerQueue({
      name: QueueName.AFFILIATE_CLICK,
    }),
  ],
  providers: [
    AffiliateClickRepository,
    AffiliateClickProcessor,
    AffiliateClickService,
  ],
  exports: [AffiliateClickService],
})
export class AffiliateModule {}
