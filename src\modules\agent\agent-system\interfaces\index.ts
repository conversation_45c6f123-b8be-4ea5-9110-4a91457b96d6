// Domain-specific interfaces
export * from './agent-system.interface';
export * from './event-handler.interface';
export * from './message.interface';
export * from './completion.interface';
export * from './service.interface';
export * from './event';

// Re-export types from schemas for convenience
export type {
  ThreadConfiguration,
  CustomConfigurableType,
  StreamingComponents,
  EventProcessingContext,
  ProcessingResult,
  CompletionResult,
} from '../schemas';

// Re-export CompletionContext from completion interface (has proper EmitEventCallback typing)
export type { CompletionContext } from './completion.interface';
