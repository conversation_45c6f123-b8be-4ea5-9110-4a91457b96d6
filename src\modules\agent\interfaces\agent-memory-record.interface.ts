/**
 * @file Agent Memory Record Interface
 *
 * Defines the structure for agent memory records as stored in the database.
 * This interface aligns with the actual agent_memories table schema.
 */

/**
 * Agent memory record structure for database operations
 *
 * @interface AgentMemoryRecord
 * @description Represents an agent memory record as stored in the agent_memories table.
 * Matches the actual database schema with structured_content.
 *
 * @example
 * ```typescript
 * const agentMemory: AgentMemoryRecord = {
 *   id: "550e8400-e29b-41d4-a716-446655440001",
 *   agentId: "550e8400-e29b-41d4-a716-************",
 *   structuredContent: {
 *     skill_name: "TypeScript Best Practices",
 *     description: "Knowledge about TypeScript coding standards",
 *     examples: ["Use strict types", "Avoid any type"]
 *   },
 *   createdAt: 1705326000000,
 *   metadata: {
 *     confidence: 0.85,
 *     source: "training"
 *   }
 * };
 * ```
 */
export interface AgentMemoryRecord {
  /**
   * Unique identifier for the agent memory record (database primary key)
   *
   * @type {string}
   * @description UUID primary key from the agent_memories table.
   */
  id: string;

  /**
   * Agent identifier that owns this memory
   *
   * @type {string}
   * @description UUID foreign key reference to agents table.
   */
  agentId: string;

  /**
   * Structured content of the agent memory
   *
   * @type {Record<string, any>}
   * @description JSONB field containing structured knowledge data (skill_name, description, examples, etc.).
   */
  structuredContent: Record<string, any>;

  /**
   * Timestamp when the agent memory was created
   *
   * @type {number}
   * @description Database timestamp (created_at column) as Unix timestamp in milliseconds.
   */
  createdAt: number;

  /**
   * Additional metadata for the agent memory (optional)
   *
   * @type {Record<string, any> | null}
   * @description JSONB field containing additional context and metadata.
   */
  metadata?: Record<string, any> | null;
}

/**
 * Interface for creating a new agent memory record
 *
 * @description Omits auto-generated fields (id, createdAt) for new record creation.
 */
export type CreateAgentMemoryRecord = Omit<
  AgentMemoryRecord,
  'id' | 'createdAt'
> & {
  /**
   * Optional metadata for new records
   */
  metadata?: Record<string, any>;
};

/**
 * Interface for agent memory search results
 *
 * @description Extends AgentMemoryRecord with similarity score for search operations.
 */
export interface AgentMemorySearchResult extends AgentMemoryRecord {}

/**
 * Type guard to check if an object is a valid AgentMemoryRecord
 *
 * @param obj - Object to check
 * @returns True if the object conforms to the AgentMemoryRecord interface
 */
export function isAgentMemoryRecord(obj: any): obj is AgentMemoryRecord {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.agentId === 'string' &&
    typeof obj.structuredContent === 'object' &&
    typeof obj.createdAt === 'number' &&
    (obj.metadata === null ||
      obj.metadata === undefined ||
      (typeof obj.metadata === 'object' && obj.metadata !== null))
  );
}

/**
 * Database query result type for agent memory operations
 *
 * @description Raw database result that may need transformation to AgentMemoryRecord.
 */
export interface AgentMemoryQueryResult {
  id: string;
  agent_id: string;
  structured_content: Record<string, any>;
  created_at: number | string;
  metadata?: Record<string, any> | null;
}

/**
 * Transform database query result to AgentMemoryRecord
 *
 * @param queryResult - Raw database query result
 * @returns Properly typed AgentMemoryRecord
 */
export function transformAgentMemoryQueryResult(
  queryResult: AgentMemoryQueryResult,
): AgentMemoryRecord {
  return {
    id: queryResult.id,
    agentId: queryResult.agent_id,
    structuredContent: queryResult.structured_content,
    createdAt:
      typeof queryResult.created_at === 'string'
        ? parseInt(queryResult.created_at, 10)
        : queryResult.created_at,
    metadata: queryResult.metadata || null,
  };
}
