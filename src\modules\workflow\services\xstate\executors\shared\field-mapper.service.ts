import { Injectable, Logger } from '@nestjs/common';
import {
  IFieldOperation,
  EFieldOperation,
  EValueSource,
  ETransformFunction,
  EConditionType
} from '../../../../interfaces';

/**
 * Shared service for field mapping and transformation
 * Used by EDIT_FIELDS, MERGE nodes
 */
@Injectable()
export class FieldMapperService {
  private readonly logger = new Logger(FieldMapperService.name);

  /**
   * Apply field operations to input data
   */
  async applyFieldOperations(
    inputData: Record<string, any>,
    operations: IFieldOperation[]
  ): Promise<{
    result: Record<string, any>;
    statistics: {
      totalOperations: number;
      successfulOperations: number;
      failedOperations: number;
      errors: Array<{
        operation: IFieldOperation;
        error: string;
      }>;
    };
  }> {
    const startTime = Date.now();
    const result: Record<string, any> = { ...inputData };
    const errors: Array<{ operation: IFieldOperation; error: string }> = [];
    let successfulOperations = 0;

    this.logger.debug(`Applying ${operations.length} field operations`);

    for (const operation of operations) {
      try {
        if (!operation.enabled) {
          continue;
        }

        // Check condition
        const shouldExecute = this.shouldExecuteOperation(operation, result);
        if (!shouldExecute) {
          continue;
        }

        // Execute operation
        this.executeFieldOperation(result, operation);
        successfulOperations++;

      } catch (error) {
        this.logger.warn(`Failed to apply operation ${operation.id}:`, error);
        errors.push({
          operation,
          error: error.message
        });
      }
    }

    const executionTime = Date.now() - startTime;
    this.logger.debug(`Field operations completed: ${successfulOperations}/${operations.length} successful (${executionTime}ms)`);

    return {
      result,
      statistics: {
        totalOperations: operations.length,
        successfulOperations,
        failedOperations: errors.length,
        errors
      }
    };
  }

  /**
   * Merge multiple objects with conflict resolution
   */
  async mergeObjects(
    objects: Record<string, any>[],
    strategy: 'overwrite' | 'merge' | 'keep_first' | 'keep_last' = 'overwrite'
  ): Promise<Record<string, any>> {
    if (objects.length === 0) {
      return {};
    }

    if (objects.length === 1) {
      return { ...objects[0] };
    }

    this.logger.debug(`Merging ${objects.length} objects with strategy: ${strategy}`);

    switch (strategy) {
      case 'overwrite':
        return Object.assign({}, ...objects);

      case 'merge':
        return this.mergeDeep(objects);

      case 'keep_first':
        return this.mergeKeepFirst(objects);

      case 'keep_last':
        return Object.assign({}, ...objects);

      default:
        throw new Error(`Unsupported merge strategy: ${strategy}`);
    }
  }

  // Private helper methods

  private shouldExecuteOperation(operation: IFieldOperation, data: Record<string, any>): boolean {
    switch (operation.condition_type) {
      case EConditionType.ALWAYS:
        return true;

      case EConditionType.IF_TRUE:
        return true;

      case EConditionType.IF_EXISTS:
        return this.getFieldValue(data, operation.target_field) !== undefined;

      case EConditionType.IF_EMPTY:
        const value = this.getFieldValue(data, operation.target_field);
        return value === undefined || value === null || value === '';

      default:
        return true;
    }
  }

  private executeFieldOperation(data: Record<string, any>, operation: IFieldOperation): void {
    switch (operation.operation_type) {
      case EFieldOperation.ADD:
      case EFieldOperation.UPDATE:
        const value = this.getOperationValue(operation, data);
        this.setFieldValue(data, operation.target_field, value);
        break;

      case EFieldOperation.COPY:
        if (operation.source_field) {
          const sourceValue = this.getFieldValue(data, operation.source_field);
          this.setFieldValue(data, operation.target_field, sourceValue);
        }
        break;

      case EFieldOperation.MOVE:
        if (operation.source_field) {
          const sourceValue = this.getFieldValue(data, operation.source_field);
          this.setFieldValue(data, operation.target_field, sourceValue);
          this.deleteFieldValue(data, operation.source_field);
        }
        break;

      case EFieldOperation.RENAME:
        if (operation.new_field_name) {
          const currentValue = this.getFieldValue(data, operation.target_field);
          this.setFieldValue(data, operation.new_field_name, currentValue);
          this.deleteFieldValue(data, operation.target_field);
        }
        break;

      case EFieldOperation.REMOVE:
        this.deleteFieldValue(data, operation.target_field);
        break;

      default:
        this.logger.warn(`Unsupported operation type: ${operation.operation_type}`);
    }
  }

  private getOperationValue(operation: IFieldOperation, data: Record<string, any>): any {
    switch (operation.value_source) {
      case EValueSource.STATIC:
        return operation.static_value;

      case EValueSource.FIELD:
        return operation.source_field ? this.getFieldValue(data, operation.source_field) : undefined;

      case EValueSource.EXPRESSION:
        try {
          return operation.expression ? eval(operation.expression) : undefined;
        } catch {
          return undefined;
        }

      case EValueSource.FUNCTION:
        return this.applyTransformFunction(
          operation.source_field ? this.getFieldValue(data, operation.source_field) : operation.static_value,
          operation.transform_function,
          operation.function_parameters
        );

      default:
        return operation.static_value;
    }
  }

  private applyTransformFunction(value: any, func?: ETransformFunction, _params?: any): any {
    if (!func) return value;

    switch (func) {
      case ETransformFunction.TO_UPPERCASE:
        return String(value).toUpperCase();

      case ETransformFunction.TO_LOWERCASE:
        return String(value).toLowerCase();

      case ETransformFunction.TRIM:
        return String(value).trim();

      case ETransformFunction.TO_NUMBER:
        const num = Number(value);
        return isNaN(num) ? value : num;

      case ETransformFunction.TO_BOOLEAN:
        return Boolean(value);

      default:
        return value;
    }
  }

  private mergeDeep(objects: Record<string, any>[]): Record<string, any> {
    const result: Record<string, any> = {};

    for (const obj of objects) {
      this.deepMergeInto(result, obj);
    }

    return result;
  }

  private deepMergeInto(target: Record<string, any>, source: Record<string, any>): void {
    for (const [key, value] of Object.entries(source)) {
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        if (!target[key] || typeof target[key] !== 'object' || Array.isArray(target[key])) {
          target[key] = {};
        }
        this.deepMergeInto(target[key], value);
      } else {
        target[key] = value;
      }
    }
  }

  private mergeKeepFirst(objects: Record<string, any>[]): Record<string, any> {
    const result: Record<string, any> = {};

    for (const obj of objects) {
      for (const [key, value] of Object.entries(obj)) {
        if (!(key in result)) {
          result[key] = value;
        }
      }
    }

    return result;
  }

  private getFieldValue(obj: Record<string, any>, fieldPath: string): any {
    try {
      return fieldPath.split('.').reduce((current, key) => {
        if (current === null || current === undefined) {
          return undefined;
        }
        return current[key];
      }, obj);
    } catch (error) {
      return undefined;
    }
  }

  private setFieldValue(obj: Record<string, any>, fieldPath: string, value: any): void {
    const keys = fieldPath.split('.');
    const lastKey = keys.pop()!;

    let current = obj;
    for (const key of keys) {
      if (!(key in current) || typeof current[key] !== 'object' || current[key] === null) {
        current[key] = {};
      }
      current = current[key];
    }

    current[lastKey] = value;
  }

  private deleteFieldValue(obj: Record<string, any>, fieldPath: string): void {
    const keys = fieldPath.split('.');
    const lastKey = keys.pop()!;

    let current = obj;
    for (const key of keys) {
      if (!(key in current) || typeof current[key] !== 'object' || current[key] === null) {
        return;
      }
      current = current[key];
    }

    delete current[lastKey];
  }
}