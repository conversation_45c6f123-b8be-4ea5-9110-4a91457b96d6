import {
  Controller,
  Post,
  Get,
  Delete,
  Param,
  Body,
  Logger,
} from '@nestjs/common';
import { EmailMarketingService } from './services';

/**
 * Controller quản lý email marketing campaigns
 */
@Controller('api/email-marketing')
export class EmailMarketingController {
  private readonly logger = new Logger(EmailMarketingController.name);

  constructor(private readonly emailMarketingService: EmailMarketingService) {}

  /**
   * Tạo jobs email marketing cho một campaign
   * @param campaignId ID của campaign
   * @returns Kết quả tạo jobs
   */
  @Post('campaigns/:campaignId/jobs')
  async createCampaignJobs(@Param('campaignId') campaignId: string) {
    try {
      const id = parseInt(campaignId);
      if (isNaN(id)) {
        return {
          success: false,
          message: 'Invalid campaign ID',
        };
      }

      const jobCount =
        await this.emailMarketingService.createEmailMarketingJobs(id);

      return {
        success: true,
        message: `Created ${jobCount} email marketing jobs`,
        data: {
          campaignId: id,
          jobCount,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error creating campaign jobs: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        message: error.message,
      };
    }
  }

  /**
   * Hủy tất cả jobs của một campaign
   * @param campaignId ID của campaign
   * @returns Kết quả hủy jobs
   */
  @Delete('campaigns/:campaignId/jobs')
  async cancelCampaignJobs(@Param('campaignId') campaignId: string) {
    try {
      const id = parseInt(campaignId);
      if (isNaN(id)) {
        return {
          success: false,
          message: 'Invalid campaign ID',
        };
      }

      const canceledCount =
        await this.emailMarketingService.cancelCampaignJobs(id);

      return {
        success: true,
        message: `Canceled ${canceledCount} jobs`,
        data: {
          campaignId: id,
          canceledCount,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error canceling campaign jobs: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        message: error.message,
      };
    }
  }

  /**
   * Lấy thống kê campaign
   * @param campaignId ID của campaign
   * @returns Thống kê campaign
   */
  @Get('campaigns/:campaignId/stats')
  async getCampaignStats(@Param('campaignId') campaignId: string) {
    try {
      const id = parseInt(campaignId);
      if (isNaN(id)) {
        return {
          success: false,
          message: 'Invalid campaign ID',
        };
      }

      const stats = await this.emailMarketingService.getCampaignStats(id);

      return {
        success: true,
        data: stats,
      };
    } catch (error) {
      this.logger.error(
        `Error getting campaign stats: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        message: error.message,
      };
    }
  }

  /**
   * Lấy trạng thái queue
   * @returns Thông tin trạng thái queue
   */
  @Get('queue/status')
  async getQueueStatus() {
    try {
      const status = await this.emailMarketingService.getQueueStatus();

      return {
        success: true,
        data: status,
      };
    } catch (error) {
      this.logger.error(
        `Error getting queue status: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        message: error.message,
      };
    }
  }

  /**
   * Test endpoint để tạo campaign mẫu
   * @param body Dữ liệu test
   * @returns Kết quả test
   */
  @Post('test/create-sample-jobs')
  async createSampleJobs(@Body() body: { campaignId: number; count?: number }) {
    try {
      const { campaignId, count = 1 } = body;

      if (!campaignId) {
        return {
          success: false,
          message: 'Campaign ID is required',
        };
      }

      const jobCount =
        await this.emailMarketingService.createEmailMarketingJobs(campaignId);

      return {
        success: true,
        message: `Test completed. Created ${jobCount} jobs for campaign ${campaignId}`,
        data: {
          campaignId,
          jobCount,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error creating sample jobs: ${error.message}`,
        error.stack,
      );
      return {
        success: false,
        message: error.message,
      };
    }
  }
}
