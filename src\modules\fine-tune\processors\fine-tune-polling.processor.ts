import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { FineTunePollingJobData, PollingResult, StatusUpdateData } from '../interfaces';
import { FineTuneJobName } from '../constants/fine-tune-job-name.enum';
import { ProviderFineTuneEnum } from '../constants/provider.enum';
import { FineTunePollingRepository } from '../repositories/fine-tune-polling.repository';
import { OpenAIFineTuneService } from '../services/openai-fine-tune.service';
import { GoogleFineTuneService } from '../services/google-fine-tune.service';
import { FineTuneLoggingService } from '../services/fine-tune-logging.service';
import { KeyPairEncryptionService } from '../../../shared/services/encryption/key-pair-encryption.service';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { QueueName } from '../../../queue';

/**
 * Processor để xử lý fine-tune polling jobs
 */
@Injectable()
@Processor(QueueName.FINE_TUNE)
export class FineTunePollingProcessor extends WorkerHost {
  private readonly logger = new Logger(FineTunePollingProcessor.name);
  private readonly POLLING_INTERVAL = 5 * 60 * 1000; // 5 phút
  private readonly MAX_POLLING_ATTEMPTS = 288; // 24 giờ với interval 5 phút

  constructor(
    private readonly repository: FineTunePollingRepository,
    private readonly openaiService: OpenAIFineTuneService,
    private readonly googleService: GoogleFineTuneService,
    private readonly loggingService: FineTuneLoggingService,
    private readonly keyPairEncryptionService: KeyPairEncryptionService,
    @InjectQueue(QueueName.FINE_TUNE) private readonly fineTuneQueue: Queue,
  ) {
    super();
  }

  /**
   * Xử lý job polling fine-tune status
   */
  async process(job: Job<FineTunePollingJobData, any, string>): Promise<void> {
    // Chỉ xử lý job FINE_TUNE_POLLING
    if (job.name !== FineTuneJobName.FINE_TUNE_POLLING) {
      this.logger.warn('Unknown job type', { jobName: job.name });
      return;
    }

    await this.handleFineTunePolling(job);
  }

  /**
   * Xử lý job polling fine-tune status
   */
  private async handleFineTunePolling(job: Job<FineTunePollingJobData>): Promise<void> {
    const { userId, modelFineTuneId, provider } = job.data;
    const startTime = Date.now();

    this.loggingService.logJobStart(job.data, job.id || 'unknown', job.attemptsMade + 1);

    try {
      // Lấy thông tin job từ database
      const jobInfo = await this.repository.getFineTuneJobInfo(
        userId,
        modelFineTuneId,
        provider,
      );

      if (!jobInfo) {
        this.loggingService.logJobInfoNotFound(job.data);
        return;
      }

      // Kiểm tra xem job có cần tiếp tục polling không
      const shouldContinue = await this.repository.isJobBeingPolled(
        modelFineTuneId,
        provider,
      );

      if (!shouldContinue) {
        this.loggingService.logJobNoLongerNeeded(job.data);
        return;
      }

      // Decrypt API key using KeyPairEncryptionService
      const decryptedApiKey = this.keyPairEncryptionService.decryptObject<string>(
        jobInfo.encryptedApiKey,
        jobInfo.publicKey
      );
      if (!decryptedApiKey) {
        this.loggingService.logApiKeyDecryptFailed(job.data);

        await this.updateJobStatus({
          modelFineTuneId,
          status: 'failed',
          isSuccess: false,
          error: 'Failed to decrypt API key',
        });
        return;
      }

      // Poll status từ provider
      const pollingResult = await this.pollProviderStatus(
        jobInfo.jobId,
        decryptedApiKey,
        provider,
      );

      // Cập nhật status trong database
      await this.handlePollingResult(modelFineTuneId, provider, pollingResult);

      // Nếu cần tiếp tục polling, schedule job tiếp theo
      if (pollingResult.shouldContinuePolling && job.attemptsMade < this.MAX_POLLING_ATTEMPTS) {
        await this.scheduleNextPolling(job.data);
      } else if (job.attemptsMade >= this.MAX_POLLING_ATTEMPTS) {
        this.logger.warn('Max polling attempts reached', {
          modelFineTuneId,
          provider,
          attempts: job.attemptsMade,
        });
        
        await this.updateJobStatus({
          modelFineTuneId,
          status: 'timeout',
          isSuccess: false,
          error: 'Polling timeout - max attempts reached',
        });
      }

    } catch (error) {
      this.logger.error('Error processing fine-tune polling job', {
        error: error.message,
        stack: error.stack,
        jobData: job.data,
        attempt: job.attemptsMade + 1,
      });

      // Nếu chưa đạt max attempts, tiếp tục thử
      if (job.attemptsMade < this.MAX_POLLING_ATTEMPTS) {
        await this.scheduleNextPolling(job.data);
      } else {
        await this.updateJobStatus({
          modelFineTuneId,
          status: 'failed',
          isSuccess: false,
          error: `Polling failed after ${job.attemptsMade + 1} attempts: ${error.message}`,
        });
      }
    }
  }

  /**
   * Poll status từ provider tương ứng
   */
  private async pollProviderStatus(
    jobId: string,
    apiKey: string,
    provider: ProviderFineTuneEnum,
  ): Promise<PollingResult> {
    this.logger.debug('Polling provider status', {
      jobId,
      provider,
    });

    try {
      switch (provider) {
        case ProviderFineTuneEnum.OPENAI:
          return await this.openaiService.getFineTuneJobStatus(jobId, apiKey);
          
        case ProviderFineTuneEnum.GEMINI:
          return await this.googleService.getFineTuneJobStatus(jobId, apiKey);
          
        default:
          this.logger.error('Unsupported provider', { provider });
          return {
            success: false,
            status: 'failed',
            error: `Unsupported provider: ${provider}`,
            shouldContinuePolling: false,
          };
      }
    } catch (error) {
      this.logger.error('Error polling provider status', {
        error: error.message,
        jobId,
        provider,
      });
      
      return {
        success: false,
        status: 'error',
        error: error.message,
        shouldContinuePolling: true, // Tiếp tục thử lại
      };
    }
  }

  /**
   * Xử lý kết quả polling và cập nhật database
   */
  private async handlePollingResult(
    modelFineTuneId: string,
    provider: ProviderFineTuneEnum,
    result: PollingResult,
  ): Promise<void> {
    this.logger.debug('Handling polling result', {
      modelFineTuneId,
      provider,
      result,
    });

    try {
      // Cập nhật status trong fine-tune histories
      await this.repository.updateFineTuneHistoryStatus(
        modelFineTuneId,
        provider,
        result.status,
        result.error,
      );

      // Nếu job đã hoàn thành (thành công hoặc thất bại), cập nhật user model fine-tune
      if (!result.shouldContinuePolling) {
        const updateData: StatusUpdateData = {
          modelFineTuneId,
          status: result.status,
          isSuccess: result.success,
          modelId: result.modelId,
          error: result.error,
        };

        await this.updateJobStatus(updateData);
      }
    } catch (error) {
      this.logger.error('Error handling polling result', {
        error: error.message,
        modelFineTuneId,
        provider,
        result,
      });
    }
  }

  /**
   * Cập nhật job status
   */
  private async updateJobStatus(updateData: StatusUpdateData): Promise<void> {
    try {
      const success = await this.repository.updateFineTuneStatus(updateData);
      
      if (success) {
        this.logger.log('Job status updated successfully', updateData);
      } else {
        this.logger.warn('Failed to update job status', updateData);
      }
    } catch (error) {
      this.logger.error('Error updating job status', {
        error: error.message,
        updateData,
      });
    }
  }

  /**
   * Schedule job polling tiếp theo
   */
  private async scheduleNextPolling(jobData: FineTunePollingJobData): Promise<void> {
    try {
      this.logger.debug('Scheduling next polling', {
        modelFineTuneId: jobData.modelFineTuneId,
        provider: jobData.provider,
        delay: this.POLLING_INTERVAL,
      });

      await this.fineTuneQueue.add(
        FineTuneJobName.FINE_TUNE_POLLING,
        {
          ...jobData,
          timestamp: Date.now(),
        },
        {
          delay: this.POLLING_INTERVAL,
          attempts: 1, // Mỗi job chỉ thử 1 lần, nếu fail sẽ schedule job mới
          removeOnComplete: 10,
          removeOnFail: 10,
        },
      );

      this.logger.debug('Next polling job scheduled successfully');
    } catch (error) {
      this.logger.error('Failed to schedule next polling', {
        error: error.message,
        jobData,
      });
    }
  }

  /**
   * Xử lý job failed
   */
  async handleFailedJob(job: Job<FineTunePollingJobData>, error: Error): Promise<void> {
    this.logger.error('Fine-tune polling job failed', {
      jobId: job.id,
      jobData: job.data,
      error: error.message,
      stack: error.stack,
      attemptsMade: job.attemptsMade,
    });

    const { modelFineTuneId } = job.data;

    // Cập nhật status thành failed nếu đã hết attempts
    if (job.attemptsMade >= this.MAX_POLLING_ATTEMPTS) {
      await this.updateJobStatus({
        modelFineTuneId,
        status: 'failed',
        isSuccess: false,
        error: `Job failed after ${job.attemptsMade} attempts: ${error.message}`,
      });
    }
  }

  /**
   * Xử lý job completed
   */
  async handleCompletedJob(job: Job<FineTunePollingJobData>): Promise<void> {
    this.logger.log('Fine-tune polling job completed', {
      jobId: job.id,
      jobData: job.data,
      attemptsMade: job.attemptsMade,
    });
  }
}
