import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { AddonBillingType } from '../enums/addon-billing-type.enum';
import { AddonType } from '../enums/addon-type.enum';

@Entity('addons')
export class Addon {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: AddonBillingType,
    name: 'billing_type',
  })
  billingType: AddonBillingType;

  @Column({ length: 50, nullable: true, name: 'volume_unit' })
  volumeUnit: string;

  @Column({ type: 'boolean', default: true, name: 'is_active' })
  isActive: boolean;

  @Column({ type: 'bigint', name: 'created_at' })
  createdAt: number;

  @Column({
    type: 'enum',
    enum: AddonType,
    nullable: true,
    name: 'type'
  })
  type: AddonType;
}
