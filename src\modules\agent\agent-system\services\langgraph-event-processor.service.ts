import { Injectable, Logger } from '@nestjs/common';
import { EventProcessorService } from '../interfaces/service.interface';
import {
  EventProcessingContext,
  ProcessingResult,
  StreamingComponents,
  ThreadConfiguration,
} from '../schemas';
import { LangGraphEventRegistry } from '../event-handlers/event-registry.service';
import {
  ChatModelStreamHandler,
  ChatModelEndHandler,
  ToolEventsHandler,
  InterruptEventsHandler,
} from '../event-handlers';
import { UserMessagesQueries } from './user-messages.queries';
import { UserUsageQueries } from './user-usage.queries';

/**
 * Service responsible for processing LangGraph streaming events
 * Extracted from AgentSystemService.processAgentThread method (Section 4: Event Processing Loop)
 * Integrates with Strategy Pattern architecture for clean event handling
 */
@Injectable()
export class LangGraphEventProcessorService implements EventProcessorService {
  private readonly logger = new Logger(LangGraphEventProcessorService.name);

  constructor(
    private readonly eventRegistry: LangGraphEventRegistry,
    private readonly userMessagesQueries: UserMessagesQueries,
    private readonly userUsageQueries: UserUsageQueries,
  ) {
    // Initialize event handlers on service creation
    this.initializeEventHandlers();
  }

  /**
   * Initialize and register all event handlers
   * Called once during service construction
   */
  private initializeEventHandlers(): void {
    const handlers = [
      new ChatModelStreamHandler(),
      new ChatModelEndHandler(),
      new ToolEventsHandler(),
      new InterruptEventsHandler(),
    ];

    this.eventRegistry.registerAll(handlers);

    this.logger.log(`Initialized ${handlers.length} event handlers`, {
      handlerNames: handlers.map((h) => h.constructor.name),
    });
  }

  /**
   * Process streaming events using the Strategy Pattern with registered handlers
   * @param streamingIterator - Iterator for streaming events from LangGraph
   * @param baseContext - Base context for event processing (without event-specific data)
   * @returns Processing result with completion status and accumulated tokens
   */
  async processEvents(
    streamingIterator: AsyncIterableIterator<any>,
    baseContext: Omit<EventProcessingContext, 'event' | 'data' | 'tags'>,
  ): Promise<ProcessingResult> {
    const result: ProcessingResult = {
      completed: false,
      partialTokens: baseContext.partialTokens,
      cancelled: false,
    };

    try {
      this.logger.debug(
        `Starting event processing for thread ${baseContext.threadId}`,
        {
          threadId: baseContext.threadId,
          runId: baseContext.runData.id,
          hasAbortController: !!baseContext.abortController,
        },
      );

      let eventCount = 0;
      let handledEventCount = 0;
      let unhandledEventCount = 0;

      // Process each event from the LangGraph streaming iterator
      for await (const { event, data, tags = [] } of streamingIterator) {
        eventCount++;

        // Check for cancellation before processing each event
        if (baseContext.abortController.signal.aborted) {
          this.logger.log(
            `Event processing cancelled for thread ${baseContext.threadId} after ${eventCount} events`,
            {
              threadId: baseContext.threadId,
              totalEvents: eventCount,
              handledEvents: handledEventCount,
              unhandledEvents: unhandledEventCount,
            },
          );
          result.cancelled = true;
          break;
        }

        // Build complete context for this specific event
        const context: EventProcessingContext = {
          ...baseContext,
          event,
          data,
          tags,
        };

        try {
          // Use Strategy Pattern to find and execute appropriate handler
          const handled = await this.processEvent(context);
          if (handled) {
            handledEventCount++;
          } else {
            unhandledEventCount++;
          }
        } catch (error) {
          this.logger.error(
            `Error processing event ${event} for thread ${baseContext.threadId}:`,
            {
              threadId: baseContext.threadId,
              runId: baseContext.runData.id,
              event,
              tags,
              error: error.message,
              stack: error.stack,
            },
          );

          // Continue processing other events even if one fails
          // This ensures resilience in the event processing pipeline
          unhandledEventCount++;
        }
      }

      if (!result.cancelled) {
        result.completed = true;
        this.logger.log(
          `Event processing completed for thread ${baseContext.threadId}`,
          {
            threadId: baseContext.threadId,
            runId: baseContext.runData.id,
            totalEvents: eventCount,
            handledEvents: handledEventCount,
            unhandledEvents: unhandledEventCount,
            partialTokensCount: baseContext.partialTokens.length,
          },
        );
      }
    } catch (error) {
      this.logger.error(
        `Fatal error in event processing for thread ${baseContext.threadId}:`,
        {
          threadId: baseContext.threadId,
          runId: baseContext.runData.id,
          error: error.message,
          stack: error.stack,
        },
      );
      result.error = error;
    }

    return result;
  }

  /**
   * Process a single event using the Strategy Pattern registry
   * @param context - Complete event processing context
   * @returns True if event was handled by a registered handler, false otherwise
   */
  private async processEvent(
    context: EventProcessingContext,
  ): Promise<boolean> {
    // Find appropriate handler for this event using the registry
    const handler = this.eventRegistry.getHandler(
      context.event,
      context.data,
      context.tags,
    );

    if (!handler) {
      this.logger.debug(`No handler found for event: ${context.event}`, {
        event: context.event,
        tags: context.tags,
        threadId: context.threadId,
        availableHandlers: this.eventRegistry.getStatistics().handlerNames,
      });
      return false;
    }

    try {
      // Execute the handler using Strategy Pattern
      await handler.handle(context);

      this.logger.debug(
        `Successfully processed event ${context.event} with ${handler.constructor.name}`,
        {
          event: context.event,
          handler: handler.constructor.name,
          threadId: context.threadId,
          tags: context.tags,
        },
      );

      return true;
    } catch (error) {
      this.logger.error(
        `Handler ${handler.constructor.name} failed to process event ${context.event}:`,
        {
          event: context.event,
          handler: handler.constructor.name,
          threadId: context.threadId,
          tags: context.tags,
          error: error.message,
          stack: error.stack,
        },
      );

      // Re-throw the error to be handled by the caller
      throw error;
    }
  }

  /**
   * Create event processing context from streaming components and thread configuration
   * @param components - Streaming components
   * @param config - Thread configuration
   * @param abortController - Abort controller for cancellation
   * @returns Base context for event processing (without event-specific data)
   */
  createBaseContext(
    components: StreamingComponents,
    config: ThreadConfiguration,
    abortController: AbortController,
  ): Omit<EventProcessingContext, 'event' | 'data' | 'tags'> {
    return {
      threadId: config.threadId,
      runData: config.runData,
      producer: components.producer,
      partialTokens: components.partialTokens,
      abortController,
      emitEventCallback: components.emitEventCallback,
      logger: this.logger,
      userMessagesQueries: this.userMessagesQueries,
      userUsageQueries: this.userUsageQueries,
    };
  }

  /**
   * Get processing statistics from the event registry
   * @returns Statistics about registered handlers and processing
   */
  getStatistics(): {
    registryStats: any;
    processorName: string;
  } {
    return {
      registryStats: this.eventRegistry.getStatistics(),
      processorName: this.constructor.name,
    };
  }

  /**
   * Check if the processor has handlers registered for common event types
   * @returns Validation result for handler coverage
   */
  validateHandlerCoverage(): {
    isValid: boolean;
    missingHandlers: string[];
    registeredHandlers: string[];
  } {
    const requiredEventTypes = [
      'on_chat_model_stream',
      'on_chat_model_end',
      'on_tool_start',
      'on_tool_end',
      'on_chain_stream',
    ];

    const stats = this.eventRegistry.getStatistics();
    const registeredHandlers = stats.handlerNames;

    // This is a simplified check - in practice, we'd need to test actual event handling
    const hasBasicHandlers = registeredHandlers.length > 0;

    return {
      isValid: hasBasicHandlers,
      missingHandlers: hasBasicHandlers ? [] : requiredEventTypes,
      registeredHandlers,
    };
  }
}
