import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloService } from './zalo.service';
import {
  ZaloConsultationMessage,
  ZaloConsultationTextMessage,
  ZaloConsultationImageMessage,
  ZaloConsultationQuoteMessage,
  ZaloConsultationStickerMessage,
} from './zalo.interface';

/**
 * Service xử lý các API tin nhắn tư vấn của Zalo Official Account
 * Tin nhắn tư vấn (CS - Customer Service) là loại tin nhắn đặc biệt
 * cho phép OA gửi tin nhắn chủ động đến người dùng trong khung thời gian nhất định
 */
@Injectable()
export class ZaloConsultationService {
  private readonly logger = new Logger(ZaloConsultationService.name);
  private readonly consultationApiUrl =
    'https://openapi.zalo.me/v3.0/oa/message/cs';

  constructor(
    private readonly zaloService: ZaloService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Gửi tin nhắn tư vấn văn bản
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param text Nội dung tin nhắn
   * @returns ID của tin nhắn
   */
  async sendConsultationTextMessage(
    accessToken: string,
    userId: string,
    text: string,
  ): Promise<{ message_id: string }> {
    try {
      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          text,
        },
      };

      this.logger.debug(
        `Sending consultation text message to user ${userId}: ${text}`,
      );
      return await this.zaloService.post<{ message_id: string }>(
        this.consultationApiUrl,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(
        `Error sending consultation text message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn tư vấn văn bản',
      );
    }
  }

  /**
   * Gửi tin nhắn tư vấn đính kèm ảnh
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param imageUrl URL của hình ảnh
   * @param message Nội dung tin nhắn kèm theo (nếu có)
   * @returns ID của tin nhắn
   */
  async sendConsultationImageMessage(
    accessToken: string,
    userId: string,
    imageUrl: string,
    message?: string,
  ): Promise<{ message_id: string }> {
    try {
      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'template',
            payload: {
              template_type: 'media',
              elements: [
                {
                  media_type: 'image',
                  url: imageUrl,
                  ...(message && { message }),
                },
              ],
            },
          },
        },
      };

      this.logger.debug(
        `Sending consultation image message to user ${userId}: ${imageUrl}`,
      );
      return await this.zaloService.post<{ message_id: string }>(
        this.consultationApiUrl,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(
        `Error sending consultation image message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn tư vấn đính kèm ảnh',
      );
    }
  }

  /**
   * Gửi tin nhắn tư vấn trích dẫn
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param text Nội dung tin nhắn
   * @param quotedMessageId ID của tin nhắn được trích dẫn
   * @param quotedContent Nội dung tin nhắn được trích dẫn
   * @returns ID của tin nhắn
   */
  async sendConsultationQuoteMessage(
    accessToken: string,
    userId: string,
    text: string,
    quotedMessageId: string,
    quotedContent: string,
  ): Promise<{ message_id: string }> {
    try {
      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          text,
          quote: {
            message_id: quotedMessageId,
            content: quotedContent,
          },
        },
      };

      this.logger.debug(
        `Sending consultation quote message to user ${userId}: ${text}`,
      );
      return await this.zaloService.post<{ message_id: string }>(
        this.consultationApiUrl,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(
        `Error sending consultation quote message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn tư vấn trích dẫn',
      );
    }
  }

  /**
   * Gửi tin nhắn tư vấn kèm sticker
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param stickerId ID của sticker
   * @param message Nội dung tin nhắn kèm theo (nếu có)
   * @returns ID của tin nhắn
   */
  async sendConsultationStickerMessage(
    accessToken: string,
    userId: string,
    stickerId: string,
    message?: string,
  ): Promise<{ message_id: string }> {
    try {
      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'template',
            payload: {
              template_type: 'sticker',
              sticker_id: stickerId,
              ...(message && { message }),
            },
          },
        },
      };

      this.logger.debug(
        `Sending consultation sticker message to user ${userId}: ${stickerId}`,
      );
      return await this.zaloService.post<{ message_id: string }>(
        this.consultationApiUrl,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(
        `Error sending consultation sticker message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn tư vấn kèm sticker',
      );
    }
  }

  /**
   * Gửi tin nhắn tư vấn đính kèm file
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param fileUrl URL của file đính kèm
   * @param filename Tên file
   * @param message Nội dung tin nhắn kèm theo (nếu có)
   * @returns ID của tin nhắn
   */
  async sendConsultationFileMessage(
    accessToken: string,
    userId: string,
    fileUrl: string,
    filename: string,
    message?: string,
  ): Promise<{ message_id: string }> {
    try {
      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'file',
            payload: {
              url: fileUrl,
              filename: filename,
              ...(message && { message }),
            },
          },
        },
      };

      this.logger.debug(
        `Sending consultation file message to user ${userId}: ${filename}`,
      );
      return await this.zaloService.post<{ message_id: string }>(
        this.consultationApiUrl,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(
        `Error sending consultation file message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn tư vấn đính kèm file',
      );
    }
  }

  /**
   * Gửi tin nhắn tư vấn yêu cầu thông tin người dùng
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param title Tiêu đề của form
   * @param elements Danh sách các trường thông tin cần thu thập
   * @param subtitle Mô tả ngắn gọn (nếu có)
   * @param imageUrl URL hình ảnh đại diện (nếu có)
   * @returns ID của tin nhắn
   */
  async sendConsultationRequestInfoMessage(
    accessToken: string,
    userId: string,
    title: string,
    elements: Array<{
      title: string;
      type: 'text' | 'phone' | 'email' | 'date';
      required?: boolean;
      placeholder?: string;
    }>,
    subtitle?: string,
    imageUrl?: string,
  ): Promise<{ message_id: string }> {
    try {
      const data = {
        recipient: {
          user_id: userId,
        },
        message: {
          attachment: {
            type: 'template',
            payload: {
              template_type: 'request_user_info',
              elements: [
                {
                  title,
                  ...(subtitle && { subtitle }),
                  ...(imageUrl && { image_url: imageUrl }),
                  elements: elements.map((element) => ({
                    title: element.title,
                    type: element.type,
                    required: element.required || false,
                    ...(element.placeholder && {
                      placeholder: element.placeholder,
                    }),
                  })),
                },
              ],
            },
          },
        },
      };

      this.logger.debug(
        `Sending consultation request info message to user ${userId}: ${title}`,
      );
      return await this.zaloService.post<{ message_id: string }>(
        this.consultationApiUrl,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(
        `Error sending consultation request info message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn tư vấn yêu cầu thông tin người dùng',
      );
    }
  }

  /**
   * Gửi tin nhắn tư vấn dựa vào loại tin nhắn
   * @param accessToken Access token của Official Account
   * @param userId ID của người dùng Zalo
   * @param message Tin nhắn tư vấn cần gửi
   * @returns ID của tin nhắn
   */
  async sendConsultationMessage(
    accessToken: string,
    userId: string,
    message: ZaloConsultationMessage,
  ): Promise<{ message_id: string }> {
    try {
      switch (message.type) {
        case 'consultation_text':
          return await this.sendConsultationTextMessage(
            accessToken,
            userId,
            message.text,
          );

        case 'consultation_image':
          return await this.sendConsultationImageMessage(
            accessToken,
            userId,
            message.url,
            message.message,
          );

        case 'consultation_quote':
          return await this.sendConsultationQuoteMessage(
            accessToken,
            userId,
            message.text,
            message.quote.message_id,
            message.quote.content,
          );

        case 'consultation_sticker':
          return await this.sendConsultationStickerMessage(
            accessToken,
            userId,
            message.sticker_id,
            message.message,
          );

        case 'consultation_file':
          return await this.sendConsultationFileMessage(
            accessToken,
            userId,
            message.url,
            message.filename,
            message.message,
          );

        case 'consultation_request_info':
          return await this.sendConsultationRequestInfoMessage(
            accessToken,
            userId,
            message.title,
            message.elements,
            message.subtitle,
            message.image_url,
          );

        default:
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            'Loại tin nhắn tư vấn không hợp lệ',
          );
      }
    } catch (error) {
      this.logger.error(
        `Error sending consultation message: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn tư vấn',
      );
    }
  }
}
