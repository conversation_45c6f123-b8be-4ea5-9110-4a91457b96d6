import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng admin_template_sms trong cơ sở dữ liệu
 * <PERSON>h sách template SMS bên admin
 */
@Entity('admin_template_sms')
export class AdminTemplateSms {
  /**
   * ID của template
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Tên mẫu
   */
  @Column({ name: 'name', length: 100, nullable: true, comment: 'Tên mẫu' })
  name: string;

  /**
   * <PERSON>h mục SMS
   */
  @Column({
    name: 'category',
    length: 100,
    nullable: true,
    unique: true,
    comment: 'Danh mục sms',
  })
  category: string;

  /**
   * Nội dung SMS
   */
  @Column({
    name: 'content',
    type: 'text',
    nullable: true,
    comment: 'Nội dung sms',
  })
  content: string;

  /**
   * Danh sách các placeholder
   */
  @Column({
    name: 'placeholders',
    type: 'json',
    nullable: true,
    comment: 'Danh sách các placeholder',
  })
  placeholders: any;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian tạo',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian cập nhật',
  })
  updatedAt: number;
}
