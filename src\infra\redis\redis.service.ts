// src/redis/redis.service.ts
import { Injectable, OnModuleDestroy } from '@nestjs/common';
import Redis from 'ioredis';
import { env } from '../../config';

@Injectable()
export class RedisService implements OnModuleDestroy {
  private readonly redis: Redis;

  constructor() {
    const url = env.external.REDIS_URL;
    this.redis = new Redis(url);
  }

  async xadd(
    stream: string,
    data: Record<string, string>,
  ): Promise<string | null> {
    return this.redis.xadd(stream, '*', ...Object.entries(data).flat());
  }

  async xread(stream: string, lastId: string = '$'): Promise<any> {
    return this.redis.xread('BLOCK', 0, 'STREAMS', stream, lastId);
  }

  // Basic Redis operations
  async get(key: string): Promise<string | null> {
    return this.redis.get(key);
  }

  async set(key: string, value: string, ttl?: number): Promise<string> {
    if (ttl) {
      return this.redis.setex(key, ttl, value);
    }
    return this.redis.set(key, value);
  }

  async del(key: string): Promise<number> {
    return this.redis.del(key);
  }

  // List operations
  async lpush(key: string, ...values: string[]): Promise<number> {
    return this.redis.lpush(key, ...values);
  }

  async lrange(key: string, start: number, stop: number): Promise<string[]> {
    return this.redis.lrange(key, start, stop);
  }

  async lrem(key: string, count: number, value: string): Promise<number> {
    return this.redis.lrem(key, count, value);
  }

  async ltrim(key: string, start: number, stop: number): Promise<string> {
    return this.redis.ltrim(key, start, stop);
  }

  async llen(key: string): Promise<number> {
    return this.redis.llen(key);
  }

  // TTL operations
  async expire(key: string, seconds: number): Promise<number> {
    return this.redis.expire(key, seconds);
  }

  async ttl(key: string): Promise<number> {
    return this.redis.ttl(key);
  }

  getRawClient() {
    return this.redis;
  }

  onModuleDestroy() {
    this.redis.disconnect();
  }
}
