import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

/**
 * Entity cho Zalo Video Upload tracking
 */
@Entity('zalo_video_uploads')
export class ZaloVideoUpload {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'oa_id', nullable: true })
  oaId?: string;

  @Column({ name: 'integration_id', nullable: true })
  integrationId?: string;

  @Column({ unique: true })
  token: string;

  @Column({ name: 'video_name' })
  videoName: string;

  @Column({ name: 'video_size' })
  videoSize: number;

  @Column({ name: 'mime_type' })
  mimeType: string;

  @Column({ nullable: true })
  description?: string;

  @Column()
  status: number;

  @Column({ name: 'status_message', nullable: true })
  statusMessage?: string;

  @Column({ name: 'convert_percent', default: 0 })
  convertPercent: number;

  @Column({ name: 'convert_error_code', default: 0 })
  convertErrorCode: number;

  @Column({ name: 'video_id', nullable: true })
  videoId?: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
