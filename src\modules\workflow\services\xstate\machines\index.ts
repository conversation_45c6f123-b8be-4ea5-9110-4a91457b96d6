import { MachineServicesProvider } from './machine-services';
import { nodeExecutionMachine } from './node-execution.machine';
import { workflowMachine } from './workflow.machine';

// XState Machines
export {
  workflowMachine,
  type WorkflowState,
  type WorkflowMachineEvent
} from './workflow.machine';

export {
  nodeExecutionMachine,
  type NodeExecutionState,
  type NodeExecutionMachineEvent
} from './node-execution.machine';

// Machine Services
export { MachineServicesProvider } from './machine-services';

// Machine Integration Service
export { MachineIntegrationService } from './machine-integration.service';

// Guards and Actions
export {
  workflowGuards,
  nodeExecutionGuards,
  allGuards,
  guardUtils
} from './guards';

export {
  workflowActions,
  nodeExecutionActions,
  allActions,
  actionUtils
} from './actions';

// Machine Configuration Helpers
export const createWorkflowMachineWithServices = (servicesProvider: MachineServicesProvider) => {
  return workflowMachine.provide({
    actors: servicesProvider.getMachineServices(),
  });
};

export const createNodeExecutionMachineWithServices = (servicesProvider: MachineServicesProvider) => {
  return nodeExecutionMachine.provide({
    actors: servicesProvider.getMachineServices(),
  });
};

// Complete machine setup with all configurations
export const createCompleteWorkflowMachine = (servicesProvider: MachineServicesProvider) => {
  return workflowMachine.provide({
    actors: servicesProvider.getMachineServices(),
    guards: allGuards,
    actions: allActions,
    delays: {
      CHECKPOINT_INTERVAL: ({ context }) => context.options?.checkpointInterval || 300000,
      TIMEOUT_DELAY: ({ context }) => context.options?.timeout || 3600000,
      RETRY_DELAY: ({ context }) => {
        const retryCount = context.metadata?.retryCount || 0;
        return Math.min(1000 * Math.pow(2, retryCount), 30000);
      },
    },
  });
};

export const createCompleteNodeExecutionMachine = (servicesProvider: MachineServicesProvider) => {
  return nodeExecutionMachine.provide({
    actors: servicesProvider.getMachineServices(),
    guards: allGuards,
    actions: allActions,
    delays: {
      RETRY_DELAY: ({ context }) => Math.min(1000 * Math.pow(2, context.retryCount), 30000),
      EXECUTION_TIMEOUT: ({ context }) => context.config?.timeout || 300000,
    },
  });
};
