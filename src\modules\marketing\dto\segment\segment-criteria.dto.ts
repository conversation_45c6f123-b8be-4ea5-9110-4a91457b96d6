import {
  IsArray,
  IsIn,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

/**
 * Enum cho các loại điều kiện
 */
export enum ConditionType {
  AND = 'and',
  OR = 'or',
}

/**
 * Enum cho các toán tử so sánh
 */
export enum OperatorType {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not_contains',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  IN = 'in',
  NOT_IN = 'not_in',
  EXISTS = 'exists',
  NOT_EXISTS = 'not_exists',
}

/**
 * DTO cho điều kiện lọc
 */
export class FilterConditionDto {
  /**
   * Tên trường cần lọc
   * @example "email"
   */
  @ApiProperty({
    description: 'Tên trường cần lọc',
    example: 'email',
  })
  @IsNotEmpty({ message: 'Tên trường không được để trống' })
  @IsString({ message: 'Tên trường phải là chuỗi' })
  field: string;

  /**
   * Toán tử so sánh
   * @example "contains"
   */
  @ApiProperty({
    description: 'Toán tử so sánh',
    enum: OperatorType,
    example: OperatorType.CONTAINS,
  })
  @IsNotEmpty({ message: 'Toán tử không được để trống' })
  @IsIn(Object.values(OperatorType), {
    message: `Toán tử phải là một trong các giá trị: ${Object.values(OperatorType).join(', ')}`,
  })
  operator: OperatorType;

  /**
   * Giá trị cần so sánh
   * @example "example.com"
   */
  @ApiProperty({
    description: 'Giá trị cần so sánh',
    example: 'example.com',
  })
  @IsOptional()
  value?: any;
}

/**
 * DTO cho nhóm điều kiện lọc
 */
export class SegmentCriteriaDto {
  /**
   * Loại điều kiện (AND/OR)
   * @example "and"
   */
  @ApiProperty({
    description: 'Loại điều kiện (AND/OR)',
    enum: ConditionType,
    example: ConditionType.AND,
  })
  @IsNotEmpty({ message: 'Loại điều kiện không được để trống' })
  @IsIn(Object.values(ConditionType), {
    message: `Loại điều kiện phải là một trong các giá trị: ${Object.values(ConditionType).join(', ')}`,
  })
  conditionType: ConditionType;

  /**
   * Danh sách các điều kiện lọc
   */
  @ApiProperty({
    description: 'Danh sách các điều kiện lọc',
    type: [FilterConditionDto],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'Danh sách điều kiện phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => FilterConditionDto)
  conditions?: FilterConditionDto[];

  /**
   * Danh sách các nhóm điều kiện con
   */
  @ApiProperty({
    description: 'Danh sách các nhóm điều kiện con',
    type: [SegmentCriteriaDto],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'Danh sách nhóm điều kiện phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => SegmentCriteriaDto)
  groups?: SegmentCriteriaDto[];
}
