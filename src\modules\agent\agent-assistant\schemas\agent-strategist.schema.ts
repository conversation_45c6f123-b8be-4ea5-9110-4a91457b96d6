import { z } from 'zod';
import {
  InputModalityEnum,
  OutputModalityEnum,
  SamplingParameterEnum,
  ModelFeatureEnum,
  ProviderEnum,
  TrimmingTypeEnum,
  ModelTypeEnum,
} from '../enums';

/**
 * Zod schema for strategy content step
 */
export const StrategyContentStepSchema = z.object({
  stepOrder: z.number().int().positive('Step order must be a positive integer'),
  content: z.string().min(1, 'Content cannot be empty'),
});

/**
 * Zod schema for model parameters (simplified for strategist)
 */
export const StrategistModelParametersSchema = z
  .object({
    temperature: z.number().min(0).max(2).optional(),
    topP: z.number().min(0).max(1).optional(),
    topK: z.number().int().positive().optional(),
    maxTokens: z.number().int().positive().optional(),
    maxOutputTokens: z.number().int().positive().optional(),
  })
  .optional();

/**
 * Zod schema for model pricing
 */
export const StrategistModelPricingSchema = z.object({
  inputRate: z.number().nonnegative('Input rate must be non-negative'),
  outputRate: z.number().nonnegative('Output rate must be non-negative'),
});

/**
 * Zod schema for strategist model configuration
 * Simplified version of AssistantModelConfigSchema - strategist only uses system models
 */
export const StrategistModelConfigSchema = z.object({
  name: z.string().min(1, 'Model name is required'),
  provider: z.nativeEnum(ProviderEnum),
  inputModalities: z.array(z.nativeEnum(InputModalityEnum)),
  outputModalities: z.array(z.nativeEnum(OutputModalityEnum)),
  samplingParameters: z.array(z.nativeEnum(SamplingParameterEnum)),
  features: z.array(z.nativeEnum(ModelFeatureEnum)),
  parameters: StrategistModelParametersSchema,
  pricing: StrategistModelPricingSchema,
  type: z.literal(ModelTypeEnum.SYSTEM), // Strategist agents only use system models
  apiKeys: z
    .array(z.string().min(1, 'API key cannot be empty'))
    .min(1, 'At least one API key is required'),
});

/**
 * Zod schema for trimming configuration (strategist uses simpler trimming)
 */
export const StrategistTrimmingConfigSchema = z.object({
  type: z.nativeEnum(TrimmingTypeEnum),
  threshold: z.number().int().positive('Threshold must be a positive integer'),
});

/**
 * Zod schema for strategist agent configuration
 * Simplified version with only essential fields
 */
export const AgentStrategistConfigSchema = z.object({
  // Core identification
  id: z.string().min(1, 'Strategy ID is required'),
  name: z.string().min(1, 'Strategy name is required'),
  description: z.string().min(1, 'Strategy description is required'),

  // Strategy-specific instruction
  instruction: z.string().min(1, 'Strategy instruction is required'),

  // Strategy content steps
  content: z.array(StrategyContentStepSchema).default([]),

  // Single example field (user examples take precedence over default)
  example: z.array(StrategyContentStepSchema).default([]),

  // Model configuration (system models only)
  model: StrategistModelConfigSchema,

  // Trimming configuration
  trimming: StrategistTrimmingConfigSchema,

  // Vector store ID (nullable)
  vectorStoreId: z.string().uuid().nullable(),
});



// Export inferred types for TypeScript
export type StrategyContentStep = z.infer<typeof StrategyContentStepSchema>;
export type StrategistModelConfig = z.infer<typeof StrategistModelConfigSchema>;
export type AgentStrategistConfig = z.infer<typeof AgentStrategistConfigSchema>;
