import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Enum cho thứ tự sắp xếp
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * DTO cơ bản cho query với phân trang và sắp xếp
 */
export class QueryDto {
  /**
   * Trang hiện tại (bắt đầu từ 1)
   * @example 1
   */
  @ApiProperty({
    description: 'Trang hiện tại (bắt đầu từ 1)',
    example: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Trang phải là số nguyên' })
  @Min(1, { message: 'Trang phải lớn hơn hoặc bằng 1' })
  @Type(() => Number)
  page?: number = 1;

  /**
   * Số lượng item trên mỗi trang
   * @example 10
   */
  @ApiProperty({
    description: 'Số lượng item trên mỗi trang',
    example: 10,
    default: 10,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Số lượng item phải là số nguyên' })
  @Min(1, { message: 'Số lượng item phải lớn hơn hoặc bằng 1' })
  @Type(() => Number)
  limit?: number = 10;

  /**
   * Trường sắp xếp
   * @example "createdAt"
   */
  @ApiProperty({
    description: 'Trường sắp xếp',
    example: 'createdAt',
    required: false,
  })
  @IsOptional()
  sortBy?: string;

  /**
   * Thứ tự sắp xếp
   * @example "DESC"
   */
  @ApiProperty({
    description: 'Thứ tự sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection, {
    message: `Thứ tự sắp xếp phải là một trong các giá trị: ${Object.values(SortDirection).join(', ')}`,
  })
  sortDirection?: SortDirection = SortDirection.DESC;
}
