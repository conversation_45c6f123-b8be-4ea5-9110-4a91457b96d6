import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';

/**
 * Service for retrieving Zalo template configurations from database
 * Replaces ConfigService usage for template IDs that are stored in DB
 */
@Injectable()
export class ZaloTemplateConfigService {
  private readonly logger = new Logger(ZaloTemplateConfigService.name);

  // Cache for template IDs to avoid frequent DB queries
  private templateCache = new Map<string, string>();
  private cacheExpiry = new Map<string, number>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor(private readonly dataSource: DataSource) {}

  /**
   * Get order confirmation template ID
   */
  async getOrderConfirmationTemplateId(): Promise<string | null> {
    return this.getTemplateId('ZALO_ORDER_CONFIRMATION_TEMPLATE_ID');
  }

  /**
   * Get delivery update template ID
   */
  async getDeliveryUpdateTemplateId(): Promise<string | null> {
    return this.getTemplateId('ZALO_DELIVERY_UPDATE_TEMPLATE_ID');
  }

  /**
   * Get payment notification template ID
   */
  async getPaymentNotificationTemplateId(): Promise<string | null> {
    return this.getTemplateId('ZALO_PAYMENT_NOTIFICATION_TEMPLATE_ID');
  }

  /**
   * Generic method to get template ID from database with caching
   */
  private async getTemplateId(configKey: string): Promise<string | null> {
    try {
      // Check cache first
      const cached = this.templateCache.get(configKey);
      const cacheTime = this.cacheExpiry.get(configKey);

      if (cached && cacheTime && Date.now() < cacheTime) {
        this.logger.debug(
          `Using cached template ID for ${configKey}: ${cached}`,
        );
        return cached;
      }

      // Query database for template configuration
      const templateId = await this.queryTemplateFromDatabase(configKey);

      if (templateId) {
        // Cache the result
        this.templateCache.set(configKey, templateId);
        this.cacheExpiry.set(configKey, Date.now() + this.CACHE_TTL);

        this.logger.debug(
          `Retrieved template ID for ${configKey}: ${templateId}`,
        );
        return templateId;
      }

      this.logger.warn(`No template ID found for ${configKey}`);
      return null;
    } catch (error) {
      this.logger.error(
        `Error retrieving template ID for ${configKey}: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * Query template ID from database
   * This is a mock implementation - replace with actual DB schema
   */
  private async queryTemplateFromDatabase(
    configKey: string,
  ): Promise<string | null> {
    try {
      // Mock implementation - replace with actual table structure
      // Assuming there's a configuration table like:
      // CREATE TABLE zalo_configurations (
      //   id SERIAL PRIMARY KEY,
      //   config_key VARCHAR(255) UNIQUE NOT NULL,
      //   config_value TEXT NOT NULL,
      //   active BOOLEAN DEFAULT true,
      //   created_at TIMESTAMP DEFAULT NOW(),
      //   updated_at TIMESTAMP DEFAULT NOW()
      // );

      const query = `
        SELECT config_value 
        FROM zalo_configurations 
        WHERE config_key = $1 AND active = true
        LIMIT 1
      `;

      const results = await this.dataSource.query(query, [configKey]);

      if (results.length > 0) {
        return results[0].config_value;
      }

      // Fallback: Mock template IDs for development/testing
      return this.getMockTemplateId(configKey);
    } catch (error) {
      this.logger.error(
        `Database query failed for ${configKey}: ${error.message}`,
      );

      // Fallback to mock data if database query fails
      return this.getMockTemplateId(configKey);
    }
  }

  /**
   * Mock template IDs for development/testing
   * Remove this when actual database table is implemented
   */
  private getMockTemplateId(configKey: string): string | null {
    const mockTemplates = {
      ZALO_ORDER_CONFIRMATION_TEMPLATE_ID:
        'mock_order_confirmation_template_123',
      ZALO_DELIVERY_UPDATE_TEMPLATE_ID: 'mock_delivery_update_template_456',
      ZALO_PAYMENT_NOTIFICATION_TEMPLATE_ID:
        'mock_payment_notification_template_789',
    };

    const templateId = mockTemplates[configKey as keyof typeof mockTemplates];

    if (templateId) {
      this.logger.debug(
        `Using mock template ID for ${configKey}: ${templateId}`,
      );
      return templateId;
    }

    return null;
  }

  /**
   * Clear template cache (useful for testing or when configurations change)
   */
  clearCache(): void {
    this.templateCache.clear();
    this.cacheExpiry.clear();
    this.logger.debug('Template cache cleared');
  }

  /**
   * Get all template configurations
   */
  async getAllTemplateConfigs(): Promise<Record<string, string>> {
    try {
      const query = `
        SELECT config_key, config_value 
        FROM zalo_configurations 
        WHERE active = true AND config_key LIKE 'ZALO_%_TEMPLATE_ID'
      `;

      const results = await this.dataSource.query(query);

      const configs: Record<string, string> = {};
      results.forEach((row: any) => {
        configs[row.config_key] = row.config_value;
      });

      return configs;
    } catch (error) {
      this.logger.error(
        `Error retrieving all template configs: ${error.message}`,
        error.stack,
      );

      // Return mock data as fallback
      return {
        ZALO_ORDER_CONFIRMATION_TEMPLATE_ID:
          'mock_order_confirmation_template_123',
        ZALO_DELIVERY_UPDATE_TEMPLATE_ID: 'mock_delivery_update_template_456',
        ZALO_PAYMENT_NOTIFICATION_TEMPLATE_ID:
          'mock_payment_notification_template_789',
      };
    }
  }

  /**
   * Update template configuration in database
   */
  async updateTemplateConfig(
    configKey: string,
    templateId: string,
  ): Promise<boolean> {
    try {
      const query = `
        INSERT INTO zalo_configurations (config_key, config_value, active, created_at, updated_at)
        VALUES ($1, $2, true, NOW(), NOW())
        ON CONFLICT (config_key) 
        DO UPDATE SET 
          config_value = EXCLUDED.config_value,
          updated_at = NOW()
      `;

      await this.dataSource.query(query, [configKey, templateId]);

      // Clear cache for this key
      this.templateCache.delete(configKey);
      this.cacheExpiry.delete(configKey);

      this.logger.log(`Updated template config ${configKey} = ${templateId}`);
      return true;
    } catch (error) {
      this.logger.error(
        `Error updating template config ${configKey}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }
}
