/**
 * Interface cho thông tin audience trong admin email campaign
 */
export interface CampaignAudience {
  /**
   * Tên của audience
   */
  name: string;

  /**
   * Email của audience
   */
  email: string;
}

/**
 * Interface cho thông tin segment trong admin email campaign
 */
export interface CampaignSegment {
  /**
   * ID của segment
   */
  id: number;

  /**
   * Tên của segment
   */
  name: string;

  /**
   * Mô tả của segment (tùy chọn)
   */
  description?: string;
}

/**
 * Interface cho nội dung email campaign
 */
export interface EmailCampaignContent {
  /**
   * Nội dung HTML email
   */
  html?: string;

  /**
   * Nội dung text thuần
   */
  text?: string;
}

/**
 * Enum cho trạng thái admin email campaign
 */
export enum AdminEmailCampaignStatus {
  DRAFT = 'DRAFT',
  SCHEDULED = 'SCHEDULED',
  SENDING = 'SENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  PAUSED = 'PAUSED'
}

/**
 * Type cho email server configuration
 */
export interface EmailServerConfig {
  host: string;
  port: number;
  secure?: boolean;
  auth: {
    user: string;
    pass: string;
  };
  [key: string]: any;
}
