import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { UserAudienceCustomFieldDefinition } from '../entities/user-audience-custom-field-definition.entity';

/**
 * Repository cho UserAudienceCustomFieldDefinition entity
 */
@Injectable()
export class UserAudienceCustomFieldDefinitionRepository extends Repository<UserAudienceCustomFieldDefinition> {
  private readonly logger = new Logger(UserAudienceCustomFieldDefinitionRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserAudienceCustomFieldDefinition, dataSource.createEntityManager());
  }

  /**
   * Tìm definition theo field key và user ID
   */
  async findByFieldKeyAndUserId(
    fieldKey: string,
    userId: number,
  ): Promise<UserAudienceCustomFieldDefinition | null> {
    try {
      return await this.findOne({
        where: {
          fieldKey,
          userId,
        },
      });
    } catch (error) {
      this.logger.error(`Error finding field definition: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Tạo definition mới
   */
  async createDefinition(data: Partial<UserAudienceCustomFieldDefinition>): Promise<UserAudienceCustomFieldDefinition> {
    try {
      const definition = this.create(data);
      return await this.save(definition);
    } catch (error) {
      this.logger.error(`Error creating field definition: ${error.message}`, error.stack);
      throw error;
    }
  }
}
