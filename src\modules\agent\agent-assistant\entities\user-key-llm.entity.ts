import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { ProviderEnum } from '../enums/model-capabilities.enum';

/**
 * UserKeyLlm entity
 * Stores LLM API Keys created by users
 */
@Entity('user_key_llm')
export class UserKeyLlm {
  /**
   * UUID user key
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Identifier name for the key
   */
  @Column({ name: 'name', type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * LLM provider
   */
  @Column({ name: 'provider', type: 'varchar', default: 'OPENAI', nullable: true })
  provider?: ProviderEnum;

  /**
   * Personalized API key
   */
  @Column({ name: 'api_key', type: 'text', nullable: false })
  apiKey: string;

  /**
   * User who owns this key
   */
  @Column({ name: 'user_id', type: 'int', nullable: true })
  userId?: number;

  /**
   * Creation time
   */
  @Column({ 
    name: 'created_at', 
    type: 'bigint', 
    nullable: true,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint'
  })
  createdAt?: number;

  /**
   * Update time
   */
  @Column({ 
    name: 'updated_at', 
    type: 'bigint', 
    nullable: true,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint'
  })
  updatedAt?: number;

  /**
   * Soft delete time
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt?: number;
}
