import { Injectable, Logger } from '@nestjs/common';
import { ZaloCustomerService } from './zalo-customer.service';
import { ZaloMessageProcessingService } from './zalo-message-processing.service';
import { ZaloThreadManagementService } from './zalo-thread-management.service';
import { ZaloOfficialAccount, ZaloCustomer, UserConvertCustomer } from '../entities';
import { MessageIngestResult } from '../interfaces';
import { ZaloWebhookDto } from '../../../../shared/dto/zalo-webhook-v2.dto';
import { CustomerMemoryRepository } from '../repositories/customer-memory.repository';
import { UserConvertCustomerRepository } from '../repositories/user-convert-customer.repository';
import { ConvertCustomerContext } from '../schemas/agent-assistant.schema';

/**
 * Service for loading context for Zalo message processing
 * Orchestrates customer mapping, thread management, and message processing
 */
@Injectable()
export class ZaloContextLoadingService {
  private readonly logger = new Logger(ZaloContextLoadingService.name);

  constructor(
    private readonly customerService: ZaloCustomerService,
    private readonly threadManagementService: ZaloThreadManagementService,
    private readonly messageProcessingService: ZaloMessageProcessingService,
    private readonly customerMemoryRepository: CustomerMemoryRepository,
    private readonly userConvertCustomerRepository: UserConvertCustomerRepository,
  ) {}

  /**
   * Load comprehensive customer data including profile, platform data, and memories
   * @param zaloCustomer Zalo customer record
   * @param agentId Agent ID for loading agent memories
   * @returns Comprehensive customer context data
   */
  async loadComprehensiveCustomerData(
    zaloCustomer: ZaloCustomer,
    agentId: string,
  ): Promise<ConvertCustomerContext> {
    try {
      this.logger.debug(`Loading comprehensive customer data for customer ${zaloCustomer.id}, agent ${agentId}`);

      // 1. Load UserConvertCustomer profile data
      let customerProfile: UserConvertCustomer | null = null;
      if (zaloCustomer.customerId) {
        customerProfile = await this.userConvertCustomerRepository.findById(zaloCustomer.customerId);
        if (!customerProfile) {
          this.logger.warn(`UserConvertCustomer not found for ID ${zaloCustomer.customerId}`);
        }
      }

      // 2. Load customer memories
      const customerMemories = zaloCustomer.customerId
        ? await this.customerMemoryRepository.findByCustomerId(zaloCustomer.customerId)
        : [];


      // 4. Transform data to match schema format
      const convertCustomerContext: ConvertCustomerContext = {
        id: zaloCustomer.customerId || undefined, // Ensure null becomes undefined for optional field
        customerProfile: customerProfile ? {
          name: customerProfile.name,
          avatar: customerProfile.avatar,
          phone: customerProfile.phone,
          email: customerProfile.email,
          address: customerProfile.address,
          tags: customerProfile.tags || [],
          metadata: customerProfile.metadata || [],
          facebookLink: customerProfile.facebookLink,
          linkedinLink: customerProfile.linkedinLink,
          twitterLink: customerProfile.twitterLink,
          zaloLink: customerProfile.zaloLink,
          websiteLink: customerProfile.websiteLink,
        } : undefined,

        customerPlatformData: {
          zaloUserId: zaloCustomer.zaloUserId,
          userIdByApp: zaloCustomer.userIdByApp,
          userExternalId: zaloCustomer.userExternalId,
          displayName: zaloCustomer.displayName,
          userAlias: zaloCustomer.userAlias,
          isSensitive: zaloCustomer.isSensitive,
          userIsFollower: zaloCustomer.userIsFollower,
          userLastInteractionDate: zaloCustomer.userLastInteractionDate,
          avatar: zaloCustomer.avatar,
          tagsAndNotesInfo: zaloCustomer.tagsAndNotesInfo,
          sharedInfo: zaloCustomer.sharedInfo,
          dynamicParam: zaloCustomer.dynamicParam,
          firstInteractionAt: zaloCustomer.firstInteractionAt,
          lastInteractionAt: zaloCustomer.lastInteractionAt,
          interactionCount: zaloCustomer.interactionCount,
          createdAt: zaloCustomer.createdAt,
          updatedAt: zaloCustomer.updatedAt,
        },

        customerMemories: customerMemories?.map(memory => ({
          id: memory.id,
          title: memory.structuredContent.title,
          reason: memory.structuredContent.reason,
          content: memory.structuredContent.content,
          createdAt: memory.createdAt,
        })) || [],
      };

      this.logger.debug(`Loaded comprehensive customer data: ${customerMemories.length} customer memories`);
      return convertCustomerContext;

    } catch (error) {
      this.logger.error(`Failed to load comprehensive customer data:`, error);
      throw error;
    }
  }

  /**
   * Load context for Zalo message processing
   * @param zaloUserId Zalo user ID (sender)
   * @param messageData Webhook message data
   * @param oa Zalo Official Account object
   * @returns Message ingestion result
   */
  async loadContext(
    zaloUserId: string,
    messageData: ZaloWebhookDto,
    oa: ZaloOfficialAccount,
  ): Promise<MessageIngestResult | null> {
    try {
      this.logger.debug(`Loading context for Zalo user ${zaloUserId}, OA ${oa.oaId}`);

      // 1. OA object is already provided and validated by processor

      // 2. Map/create Zalo customer
      const customer = await this.customerService.mapOrCreateCustomer(
        oa.userId,
        zaloUserId,
        oa.oaId,
        oa.accessToken
      );
      
      if (!customer) {
        this.logger.error(`Failed to map Zalo customer ${zaloUserId}`);
        throw new Error(`Failed to map Zalo customer ${zaloUserId}`);
      }

      // 3. Get or create conversation thread
      const threadId = await this.threadManagementService.getOrCreateThread(
        oa.userId, 
        customer.id, 
        zaloUserId, 
        oa.oaId
      );

      // 4. Save incoming message
      const messageId = await this.messageProcessingService.saveIncomingMessage(
        threadId,
        messageData,
        oa.oaId, // External Zalo OA ID (string)
        customer.id, // Customer UUID (not raw Zalo User ID)
        oa.id // Internal database ID (number)
      );

      // 5. Validate agent ID exists
      if (!oa.agentId) {
        this.logger.error(`No agent configured for OA ${oa.oaId}`);
        throw new Error(`No agent configured for OA ${oa.oaId}`);
      }

      // 6. Create abort controller for cancellation support
      const abortController = new AbortController();

      this.logger.log(`Context loaded successfully for thread ${threadId}`);

      return {
        threadId,
        customer,
        messageId,
        oa,
        abortController,
      };

    } catch (error) {
      this.logger.error(`Failed to load context for Zalo user ${zaloUserId}:`, error);
      return null;
    }
  }
}
