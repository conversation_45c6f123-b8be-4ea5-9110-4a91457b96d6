import {
  Column,
  Entity,
  Index,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { NodeGroupEnum } from '../enums/node-group.enum';
import { ICredentialDefinition, INodeProperty } from '../interfaces/node-manager.interface';

@Entity('node_definitions')
@Index(['typeName', 'version'], { unique: true })
export class NodeDefinition {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'type_name', type: 'varchar', length: 255 })
  typeName: string;

  @Column({ type: 'int' })
  version: number;

  @Column({ name: 'display_name', type: 'varchar', length: 255 })
  displayName: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    name: 'group_name',
    type: 'enum',
    enum: NodeGroupEnum,
    nullable: true,
  })
  groupName: NodeGroupEnum;

  @Column({ type: 'varchar', length: 255, nullable: true })
  icon: string;

  @Column({ type: 'jsonb' })
  properties: INodeProperty[];

  @Column({ type: 'jsonb', nullable: true })
  inputs: string[];

  @Column({ type: 'jsonb', nullable: true })
  outputs: string[];

  @Column({ type: 'jsonb', nullable: true })
  credentials: ICredentialDefinition[];

  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  createdAt: number;

  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  updatedAt: number;
}
