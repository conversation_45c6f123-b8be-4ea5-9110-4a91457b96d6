import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { ZaloService } from './zalo.service';
import { ZaloOaService } from './zalo-oa.service';
import { ZaloZnsService } from './zalo-zns.service';
import { ZaloZnsInfoService } from './zalo-zns-info.service';
import { ZaloWebhookService } from './zalo-webhook.service';
import { ZaloAgentService } from './zalo-agent.service';
import { ZaloSocialService } from './zalo-social.service';
import { ZaloConsultationService } from './zalo-consultation.service';
import { ZaloTransactionService } from './zalo-transaction.service';
import { ZaloPromotionService } from './zalo-promotion.service';
import { ZaloAnonymousService } from './zalo-anonymous.service';
import { ZaloMessageService } from './zalo-message.service';
import { ZaloMessageManagementService } from './zalo-message-management.service';
import { ZaloUserManagementService } from './zalo-user-management.service';
import { ZaloUserInfoFieldsService } from './zalo-user-info-fields.service';
import { ZaloGroupMessageService } from './zalo-group-message.service';
import { ZaloGmfGroupManagementService } from './zalo-gmf-group-management.service';
import { ZaloCallService } from './zalo-call.service';
import { ZaloContentService } from './zalo-content.service';
import { ZaloZnsQualityService } from './zalo-zns-quality.service';
import { ZaloZnsComponentService } from './zalo-zns-component.service';
import { ZaloZnsErrorService } from './zalo-zns-error.service';

import { ZaloTemplateConfigService } from './zalo-template-config.service';
import { ZaloTokenUtilsService } from './zalo-token-utils.service';
import { ZaloOATokenAdapterService } from './zalo-oa-token-adapter.service';
import { KeyPairEncryptionService } from '../encryption/key-pair-encryption.service';
import { Integration } from '../../../shared/entities/integration.entity';
import { IntegrationProvider } from '../../../shared/entities/integration-provider.entity';

@Module({
  imports: [
    HttpModule.register({
      timeout: 30000, // 30 seconds
      maxRedirects: 5,
    }),
    ConfigModule,
    TypeOrmModule.forFeature([Integration, IntegrationProvider]),
    ScheduleModule.forRoot(),
  ],
  providers: [
    ZaloService,
    ZaloOaService,
    ZaloZnsService,
    ZaloZnsInfoService,
    ZaloWebhookService,
    ZaloAgentService,
    ZaloSocialService,
    ZaloConsultationService,
    ZaloTransactionService,
    ZaloPromotionService,
    ZaloAnonymousService,
    ZaloMessageService,
    ZaloMessageManagementService,
    ZaloUserManagementService,
    ZaloUserInfoFieldsService,
    ZaloGroupMessageService,
    ZaloGmfGroupManagementService,
    ZaloCallService,
    ZaloContentService,
    ZaloZnsQualityService,
    ZaloZnsComponentService,
    ZaloZnsErrorService,

    ZaloTemplateConfigService,
    ZaloOATokenAdapterService,
    ZaloTokenUtilsService,
  ],
  exports: [
    ZaloService,
    ZaloOaService,
    ZaloZnsService,
    ZaloZnsInfoService,
    ZaloWebhookService,
    ZaloAgentService,
    ZaloSocialService,
    ZaloConsultationService,
    ZaloTransactionService,
    ZaloPromotionService,
    ZaloAnonymousService,
    ZaloMessageService,
    ZaloMessageManagementService,
    ZaloUserManagementService,
    ZaloUserInfoFieldsService,
    ZaloGroupMessageService,
    ZaloGmfGroupManagementService,
    ZaloCallService,
    ZaloContentService,
    ZaloZnsQualityService,
    ZaloZnsComponentService,
    ZaloZnsErrorService,

    ZaloTemplateConfigService,
    ZaloOATokenAdapterService,
    ZaloTokenUtilsService,
  ],
})
export class ZaloModule {}
