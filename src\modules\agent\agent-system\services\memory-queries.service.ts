import { Injectable, Logger } from '@nestjs/common';
import { ChatDatabaseService } from '../database.service';
import {
  InsertUserMemorySQL,
  InsertAgentMemorySQL,
  UserMemorySearchResult,
  AgentMemorySearchResult,
} from '../../interfaces';

/**
 * Memory Queries Service
 *
 * @description Simplified service for LangChain tools with only required operations:
 * - Bulk insert with validation (single query, no loops)
 * - Simple retrieval by user/agent ID (chronological order, no embeddings)
 *
 * Matches actual database schema:
 * - user_memories: user_id (integer), structured_content (jsonb), metadata (jsonb), created_at (bigint)
 * - agent_memories: agent_id (uuid), structured_content (jsonb), metadata (jsonb), created_at (bigint)
 */
@Injectable()
export class MemoryQueriesService {
  private readonly logger = new Logger(MemoryQueriesService.name);

  constructor(private readonly databaseService: ChatDatabaseService) {}

  /**
   * Bulk insert user memories using a single query
   *
   * @param memories - Array of user memory data
   * @returns Promise<string[]> - Array of inserted memory IDs
   */
  async bulkInsertUserMemories(
    memories: InsertUserMemorySQL[],
  ): Promise<string[]> {
    if (!memories || memories.length === 0) {
      throw new Error('Memories array cannot be empty');
    }

    this.logger.debug(`Bulk inserting ${memories.length} user memories`);

    // Build VALUES clause for bulk insert
    const values: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    for (const memory of memories) {
      const now = Date.now();
      values.push(
        `($${paramIndex}, $${paramIndex + 1}, $${paramIndex + 2}, ${now})`,
      );

      params.push(
        memory.userId,
        JSON.stringify(memory.structuredContent),
        memory.metadata ? JSON.stringify(memory.metadata) : null,
      );

      paramIndex += 3;
    }

    const insertQuery = `
      INSERT INTO user_memories (user_id, structured_content, metadata, created_at)
      VALUES ${values.join(', ')}
      RETURNING id
    `;

    try {
      const results = await this.databaseService.query(insertQuery, params);
      const insertedIds = results.map((row: any) => row.id);

      this.logger.log(
        `Successfully bulk inserted ${insertedIds.length} user memories`,
      );
      return insertedIds;
    } catch (error) {
      this.logger.error(
        `Failed to bulk insert user memories: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to bulk insert user memories: ${error.message}`);
    }
  }

  /**
   * Bulk insert agent memories using a single query
   *
   * @param memories - Array of agent memory data
   * @returns Promise<string[]> - Array of inserted memory IDs
   */
  async bulkInsertAgentMemories(
    memories: InsertAgentMemorySQL[],
  ): Promise<string[]> {
    if (!memories || memories.length === 0) {
      throw new Error('Memories array cannot be empty');
    }

    this.logger.debug(`Bulk inserting ${memories.length} agent memories`);

    // Build VALUES clause for bulk insert
    const values: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    for (const memory of memories) {
      const now = Date.now();
      values.push(
        `($${paramIndex}, $${paramIndex + 1}, $${paramIndex + 2}, ${now})`,
      );

      params.push(
        memory.agentId,
        JSON.stringify(memory.structuredContent),
        memory.metadata ? JSON.stringify(memory.metadata) : null,
      );

      paramIndex += 3;
    }

    const insertQuery = `
      INSERT INTO agent_memories (agent_id, structured_content, metadata, created_at)
      VALUES ${values.join(', ')}
      RETURNING id
    `;

    try {
      const results = await this.databaseService.query(insertQuery, params);
      const insertedIds = results.map((row: any) => row.id);

      this.logger.log(
        `Successfully bulk inserted ${insertedIds.length} agent memories`,
      );
      return insertedIds;
    } catch (error) {
      this.logger.error(
        `Failed to bulk insert agent memories: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to bulk insert agent memories: ${error.message}`);
    }
  }

  // NOTE: Removed getSimilarUserMemories and getSimilarAgentMemories methods
  // These methods were using embedding functionality that no longer exists.
  // Use getAllUserMemories and getAllAgentMemories instead for simple retrieval.

  /**
   * Get all user memories by user ID (simple full load)
   *
   * @param userId - User ID to retrieve memories for
   * @param limit - Maximum number of results to return
   * @returns Promise<UserMemorySearchResult[]>
   */
  async getAllUserMemories(
    userId: number,
    limit: number = 100,
  ): Promise<UserMemorySearchResult[]> {
    this.logger.debug(`Retrieving all user memories for user ${userId}`, {
      limit,
    });

    try {
      const query = `
        SELECT
          id,
          structured_content,
          metadata,
          created_at
        FROM user_memories
        WHERE user_id = $1
        ORDER BY created_at DESC
        LIMIT $2
      `;

      const result = await this.databaseService.query(query, [userId, limit]);

      const memories: UserMemorySearchResult[] = result.map((row: any) => ({
        id: row.id,
        userId: userId, // Add required userId field
        structuredContent: row.structured_content,
        metadata: row.metadata,
        createdAt: row.created_at,
        similarity: 1.0, // Add required similarity field (full match for simple retrieval)
      }));

      this.logger.debug(`Retrieved ${memories.length} user memories`, {
        userId,
        resultCount: memories.length,
      });

      return memories;
    } catch (error) {
      this.logger.error(
        `Failed to retrieve all user memories for user ${userId}`,
        {
          userId,
          error: error.message,
          stack: error.stack,
        },
      );

      throw new Error(`Failed to retrieve all user memories: ${error.message}`);
    }
  }

  /**
   * Get all agent memories by agent ID (simple full load)
   *
   * @param agentId - Agent ID to retrieve memories for
   * @param limit - Maximum number of results to return
   * @returns Promise<AgentMemorySearchResult[]>
   */
  async getAllAgentMemories(
    agentId: string,
    limit: number = 100,
  ): Promise<AgentMemorySearchResult[]> {
    this.logger.debug(`Retrieving all agent memories for agent ${agentId}`, {
      limit,
    });

    try {
      const query = `
        SELECT
          id,
          structured_content,
          metadata,
          created_at
        FROM agent_memories
        WHERE agent_id = $1
        ORDER BY created_at DESC
        LIMIT $2
      `;

      const result = await this.databaseService.query(query, [agentId, limit]);

      const memories: AgentMemorySearchResult[] = result.map((row: any) => ({
        id: row.id,
        agentId: agentId, // Add required agentId field
        structuredContent: row.structured_content,
        metadata: row.metadata,
        createdAt: row.created_at,
      }));

      this.logger.debug(`Retrieved ${memories.length} agent memories`, {
        agentId,
        resultCount: memories.length,
      });

      return memories;
    } catch (error) {
      this.logger.error(
        `Failed to retrieve all agent memories for agent ${agentId}`,
        {
          agentId,
          error: error.message,
          stack: error.stack,
        },
      );

      throw new Error(
        `Failed to retrieve all agent memories: ${error.message}`,
      );
    }
  }

  /**
   * Get a single user memory by ID
   * @param memoryId - ID of the memory to retrieve
   * @returns Promise<UserMemorySearchResult | null>
   */
  async getUserMemoryById(
    memoryId: string,
  ): Promise<UserMemorySearchResult | null> {
    this.logger.debug(`Getting user memory by ID`, { memoryId });

    try {
      const query = `
        SELECT
          id,
          user_id,
          structured_content,
          metadata,
          created_at
        FROM user_memories
        WHERE id = $1
      `;

      const result = await this.databaseService.query(query, [memoryId]);

      if (result.length === 0) {
        return null;
      }

      const row = result[0];
      const memory: UserMemorySearchResult = {
        id: row.id,
        userId: row.user_id,
        structuredContent: row.structured_content,
        metadata: row.metadata,
        createdAt: row.created_at,
        similarity: 1.0, // Full match for direct ID lookup
      };

      this.logger.debug(`Retrieved user memory by ID`, {
        memoryId,
        found: true,
      });

      return memory;
    } catch (error) {
      this.logger.error(`Failed to get user memory by ID ${memoryId}`, {
        memoryId,
        error: error.message,
        stack: error.stack,
      });

      throw new Error(`Failed to get user memory: ${error.message}`);
    }
  }

  /**
   * Get a single agent memory by ID
   * @param memoryId - ID of the memory to retrieve
   * @returns Promise<AgentMemorySearchResult | null>
   */
  async getAgentMemoryById(
    memoryId: string,
  ): Promise<AgentMemorySearchResult | null> {
    this.logger.debug(`Getting agent memory by ID`, { memoryId });

    try {
      const query = `
        SELECT
          id,
          agent_id,
          structured_content,
          metadata,
          created_at
        FROM agent_memories
        WHERE id = $1
      `;

      const result = await this.databaseService.query(query, [memoryId]);

      if (result.length === 0) {
        return null;
      }

      const row = result[0];
      const memory: AgentMemorySearchResult = {
        id: row.id,
        agentId: row.agent_id,
        structuredContent: row.structured_content,
        metadata: row.metadata,
        createdAt: row.created_at,
      };

      this.logger.debug(`Retrieved agent memory by ID`, {
        memoryId,
        found: true,
      });

      return memory;
    } catch (error) {
      this.logger.error(`Failed to get agent memory by ID ${memoryId}`, {
        memoryId,
        error: error.message,
        stack: error.stack,
      });

      throw new Error(`Failed to get agent memory: ${error.message}`);
    }
  }

  /**
   * Update a user memory by ID
   * @param memoryId - ID of the memory to update
   * @param updates - Partial user memory record with updates
   * @returns Promise<boolean> - Success status
   */
  async updateUserMemory(
    memoryId: string,
    updates: Partial<UserMemorySearchResult>,
  ): Promise<boolean> {
    this.logger.debug(`Updating user memory`, { memoryId, updates });

    try {
      const query = `
        UPDATE user_memories
        SET structured_content = $2, metadata = $3
        WHERE id = $1
      `;

      const result = await this.databaseService.query(query, [
        memoryId,
        updates.structuredContent,
        updates.metadata,
      ]);

      // For UPDATE queries, success is determined by the query executing without error
      // If the memory ID doesn't exist, the query will still succeed but update 0 rows
      const success = true; // Query executed successfully

      this.logger.debug(`Updated user memory`, {
        memoryId,
        success,
        resultLength: result.length,
      });

      return success;
    } catch (error) {
      this.logger.error(`Failed to update user memory ${memoryId}`, {
        memoryId,
        error: error.message,
        stack: error.stack,
      });

      throw new Error(`Failed to update user memory: ${error.message}`);
    }
  }

  /**
   * Update an agent memory by ID
   * @param memoryId - ID of the memory to update
   * @param updates - Partial agent memory record with updates
   * @returns Promise<boolean> - Success status
   */
  async updateAgentMemory(
    memoryId: string,
    updates: Partial<AgentMemorySearchResult>,
  ): Promise<boolean> {
    this.logger.debug(`Updating agent memory`, { memoryId, updates });

    try {
      const query = `
        UPDATE agent_memories
        SET structured_content = $2, metadata = $3
        WHERE id = $1
      `;

      const result = await this.databaseService.query(query, [
        memoryId,
        updates.structuredContent,
        updates.metadata,
      ]);

      // For UPDATE queries, success is determined by the query executing without error
      // If the memory ID doesn't exist, the query will still succeed but update 0 rows
      const success = true; // Query executed successfully

      this.logger.debug(`Updated agent memory`, {
        memoryId,
        success,
        resultLength: result.length,
      });

      return success;
    } catch (error) {
      this.logger.error(`Failed to update agent memory ${memoryId}`, {
        memoryId,
        error: error.message,
        stack: error.stack,
      });

      throw new Error(`Failed to update agent memory: ${error.message}`);
    }
  }
}
