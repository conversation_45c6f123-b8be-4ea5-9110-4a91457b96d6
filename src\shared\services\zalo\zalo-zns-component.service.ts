import { Injectable, Logger } from '@nestjs/common';
import {
  ZnsComponentType,
  ZnsComponentFormat,
  ZnsTemplateComponentDto,
  ZnsTextComponentDto,
  ZnsHeaderComponentDto,
  ZnsBodyComponentDto,
  ZnsFooterComponentDto,
  ZnsButtonComponentDto,
  ZnsInteractiveComponentDto,
  ZnsButtonType,
} from './dto/zalo-zns.dto';

/**
 * Service cho quản lý và validation component ZNS
 *
 * Điều kiện sử dụng component:
 * - Component phải tuân thủ cấu trúc chuẩn của Zalo ZNS
 * - Text component không được vượt quá giới hạn ký tự
 * - Header component phải có định dạng phù hợp
 * - Button component phải có URL hoặc payload hợp lệ
 * - Interactive component phải có ít nhất một action
 * - Tham số động phải được định nghĩa đúng format {{1}}, {{2}}, etc.
 *
 * Tài liệu tham khảo:
 * - https://developers.zalo.me/docs/zalo-notification-service/phu-luc/component
 */
@Injectable()
export class ZaloZnsComponentService {
  private readonly logger = new Logger(ZaloZnsComponentService.name);

  // Giới hạn ký tự cho các loại component
  private readonly CHARACTER_LIMITS = {
    TEXT: 1024,
    HEADER_TEXT: 60,
    BODY: 1024,
    FOOTER: 60,
    BUTTON_TEXT: 20,
    URL: 2000,
  };

  // Regex patterns cho validation
  private readonly PATTERNS = {
    PARAMETER: /\{\{(\d+)\}\}/g,
    URL: /^https?:\/\/.+/,
    PHONE: /^\+?[1-9]\d{1,14}$/,
  };

  /**
   * Validate template component theo chuẩn ZNS
   * @param component Component cần validate
   * @returns Kết quả validation
   */
  validateTemplateComponent(component: ZnsTemplateComponentDto): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate component type
    if (!Object.values(ZnsComponentType).includes(component.type)) {
      errors.push(`Loại component không hợp lệ: ${component.type}`);
      return { isValid: false, errors, warnings };
    }

    // Validate specific component rules
    switch (component.type) {
      case ZnsComponentType.TEXT:
        this.validateTextComponent(component.text, errors, warnings);
        break;

      case ZnsComponentType.HEADER:
        this.validateHeaderComponent(component.header, errors, warnings);
        break;

      case ZnsComponentType.BODY:
        this.validateBodyComponent(component.body, errors, warnings);
        break;

      case ZnsComponentType.FOOTER:
        this.validateFooterComponent(component.footer, errors, warnings);
        break;

      case ZnsComponentType.BUTTON:
        this.validateButtonComponent(component.button, errors, warnings);
        break;

      case ZnsComponentType.INTERACTIVE:
        this.validateInteractiveComponent(
          component.interactive,
          errors,
          warnings,
        );
        break;

      default:
        warnings.push(
          `Component type ${component.type} chưa được hỗ trợ validation`,
        );
        break;
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Validate text component
   */
  private validateTextComponent(
    textComponent: ZnsTextComponentDto | undefined,
    errors: string[],
    warnings: string[],
  ): void {
    if (!textComponent) {
      errors.push('Component text không được để trống');
      return;
    }

    if (!textComponent.text || textComponent.text.trim().length === 0) {
      errors.push('Nội dung text không được để trống');
      return;
    }

    if (textComponent.text.length > this.CHARACTER_LIMITS.TEXT) {
      errors.push(
        `Nội dung text vượt quá giới hạn ${this.CHARACTER_LIMITS.TEXT} ký tự`,
      );
    }

    // Validate parameters
    this.validateParameters(
      textComponent.text,
      textComponent.parameters,
      errors,
      warnings,
    );
  }

  /**
   * Validate header component
   */
  private validateHeaderComponent(
    headerComponent: ZnsHeaderComponentDto | undefined,
    errors: string[],
    warnings: string[],
  ): void {
    if (!headerComponent) {
      errors.push('Component header không được để trống');
      return;
    }

    if (!headerComponent.format) {
      errors.push('Header component phải có định dạng');
      return;
    }

    if (!Object.values(ZnsComponentFormat).includes(headerComponent.format)) {
      errors.push(`Định dạng header không hợp lệ: ${headerComponent.format}`);
    }

    switch (headerComponent.format) {
      case ZnsComponentFormat.TEXT:
        if (!headerComponent.text || headerComponent.text.trim().length === 0) {
          errors.push('Header text không được để trống');
        } else if (
          headerComponent.text.length > this.CHARACTER_LIMITS.HEADER_TEXT
        ) {
          errors.push(
            `Header text vượt quá giới hạn ${this.CHARACTER_LIMITS.HEADER_TEXT} ký tự`,
          );
        }
        this.validateParameters(
          headerComponent.text,
          headerComponent.parameters,
          errors,
          warnings,
        );
        break;

      case ZnsComponentFormat.IMAGE:
      case ZnsComponentFormat.VIDEO:
      case ZnsComponentFormat.DOCUMENT:
        if (!headerComponent.media_url) {
          errors.push('Header media phải có URL');
        } else if (!this.PATTERNS.URL.test(headerComponent.media_url)) {
          errors.push('URL media header không hợp lệ');
        }
        break;
    }
  }

  /**
   * Validate body component
   */
  private validateBodyComponent(
    bodyComponent: ZnsBodyComponentDto | undefined,
    errors: string[],
    warnings: string[],
  ): void {
    if (!bodyComponent) {
      errors.push('Component body không được để trống');
      return;
    }

    if (!bodyComponent.text || bodyComponent.text.trim().length === 0) {
      errors.push('Nội dung body không được để trống');
      return;
    }

    if (bodyComponent.text.length > this.CHARACTER_LIMITS.BODY) {
      errors.push(
        `Nội dung body vượt quá giới hạn ${this.CHARACTER_LIMITS.BODY} ký tự`,
      );
    }

    // Validate parameters
    this.validateParameters(
      bodyComponent.text,
      bodyComponent.parameters,
      errors,
      warnings,
    );
  }

  /**
   * Validate footer component
   */
  private validateFooterComponent(
    footerComponent: ZnsFooterComponentDto | undefined,
    errors: string[],
    warnings: string[],
  ): void {
    if (!footerComponent) {
      return; // Footer is optional
    }

    if (!footerComponent.text || footerComponent.text.trim().length === 0) {
      errors.push('Nội dung footer không được để trống');
      return;
    }

    if (footerComponent.text.length > this.CHARACTER_LIMITS.FOOTER) {
      errors.push(
        `Nội dung footer vượt quá giới hạn ${this.CHARACTER_LIMITS.FOOTER} ký tự`,
      );
    }
  }

  /**
   * Validate button component
   */
  private validateButtonComponent(
    buttonComponent: ZnsButtonComponentDto | undefined,
    errors: string[],
    warnings: string[],
  ): void {
    if (!buttonComponent) {
      errors.push('Component button không được để trống');
      return;
    }

    if (!buttonComponent.text || buttonComponent.text.trim().length === 0) {
      errors.push('Tiêu đề button không được để trống');
      return;
    }

    if (buttonComponent.text.length > this.CHARACTER_LIMITS.BUTTON_TEXT) {
      errors.push(
        `Tiêu đề button vượt quá giới hạn ${this.CHARACTER_LIMITS.BUTTON_TEXT} ký tự`,
      );
    }

    if (!buttonComponent.sub_type) {
      errors.push('Button phải có loại button');
      return;
    }

    if (!Object.values(ZnsButtonType).includes(buttonComponent.sub_type)) {
      errors.push(`Loại button không hợp lệ: ${buttonComponent.sub_type}`);
    }

    // Validate specific button types
    switch (buttonComponent.sub_type) {
      case ZnsButtonType.OPEN_URL:
        if (!buttonComponent.url) {
          errors.push('Button URL phải có đường dẫn');
        } else if (!this.PATTERNS.URL.test(buttonComponent.url)) {
          errors.push('URL button không hợp lệ');
        } else if (buttonComponent.url.length > this.CHARACTER_LIMITS.URL) {
          errors.push(
            `URL button vượt quá giới hạn ${this.CHARACTER_LIMITS.URL} ký tự`,
          );
        }
        break;

      case ZnsButtonType.PHONE_CODE:
        if (!buttonComponent.phone_number) {
          errors.push('Button gọi điện phải có số điện thoại');
        } else if (!this.PATTERNS.PHONE.test(buttonComponent.phone_number)) {
          errors.push('Số điện thoại button không hợp lệ');
        }
        break;
    }

    // Validate parameters
    this.validateParameters(
      buttonComponent.text,
      buttonComponent.parameters,
      errors,
      warnings,
    );
  }

  /**
   * Validate interactive component
   */
  private validateInteractiveComponent(
    interactiveComponent: ZnsInteractiveComponentDto | undefined,
    errors: string[],
    warnings: string[],
  ): void {
    if (!interactiveComponent) {
      errors.push('Component interactive không được để trống');
      return;
    }

    if (!interactiveComponent.interactive_type) {
      errors.push('Interactive component phải có loại interactive');
      return;
    }

    const validInteractiveTypes = ['button', 'list', 'product'];
    if (
      !validInteractiveTypes.includes(interactiveComponent.interactive_type)
    ) {
      errors.push(
        `Loại interactive không hợp lệ: ${interactiveComponent.interactive_type}`,
      );
    }

    // Body is required for interactive
    if (!interactiveComponent.body) {
      errors.push('Interactive component phải có body');
    } else {
      this.validateBodyComponent(interactiveComponent.body, errors, warnings);
    }

    // Validate optional components
    if (interactiveComponent.header) {
      this.validateHeaderComponent(
        interactiveComponent.header,
        errors,
        warnings,
      );
    }

    if (interactiveComponent.footer) {
      this.validateFooterComponent(
        interactiveComponent.footer,
        errors,
        warnings,
      );
    }

    // Validate buttons
    if (
      interactiveComponent.buttons &&
      interactiveComponent.buttons.length > 0
    ) {
      if (interactiveComponent.buttons.length > 3) {
        errors.push('Interactive component không được có quá 3 button');
      }

      interactiveComponent.buttons.forEach((button, index) => {
        const buttonErrors: string[] = [];
        const buttonWarnings: string[] = [];
        this.validateButtonComponent(button, buttonErrors, buttonWarnings);

        buttonErrors.forEach((error) =>
          errors.push(`Button ${index + 1}: ${error}`),
        );
        buttonWarnings.forEach((warning) =>
          warnings.push(`Button ${index + 1}: ${warning}`),
        );
      });
    } else if (interactiveComponent.interactive_type === 'button') {
      errors.push('Interactive button phải có ít nhất một button');
    }
  }

  /**
   * Validate parameters in text
   */
  private validateParameters(
    text: string | undefined,
    parameters: string[] | undefined,
    errors: string[],
    warnings: string[],
  ): void {
    if (!text) return;

    const matches = Array.from(text.matchAll(this.PATTERNS.PARAMETER));
    const parameterNumbers = matches.map((match) => parseInt(match[1]));
    const uniqueParameters = [...new Set(parameterNumbers)].sort(
      (a, b) => a - b,
    );

    // Check if parameters are sequential starting from 1
    for (let i = 0; i < uniqueParameters.length; i++) {
      if (uniqueParameters[i] !== i + 1) {
        errors.push(
          `Tham số phải được đánh số tuần tự từ 1. Thiếu tham số {{${i + 1}}}`,
        );
        break;
      }
    }

    // Check if provided parameters match the text
    if (parameters) {
      if (parameters.length !== uniqueParameters.length) {
        warnings.push(
          `Số lượng tham số được cung cấp (${parameters.length}) không khớp với số tham số trong text (${uniqueParameters.length})`,
        );
      }
    } else if (uniqueParameters.length > 0) {
      warnings.push(
        `Text có ${uniqueParameters.length} tham số nhưng không cung cấp danh sách tham số`,
      );
    }
  }

  /**
   * Tạo component template mẫu
   * @param type Loại component
   * @param customText Text tùy chỉnh (tùy chọn)
   * @returns Component template mẫu
   */
  createSampleComponent(
    type: ZnsComponentType,
    customText?: string,
  ): ZnsTemplateComponentDto {
    const baseComponent: ZnsTemplateComponentDto = {
      type,
    };

    switch (type) {
      case ZnsComponentType.TEXT:
        baseComponent.text = {
          type: ZnsComponentType.TEXT,
          text: customText || 'Nội dung text mẫu với tham số {{1}}',
          parameters: customText
            ? this.extractParameters(customText)
            : ['param1'],
        };
        break;

      case ZnsComponentType.HEADER:
        baseComponent.header = {
          type: ZnsComponentType.HEADER,
          format: ZnsComponentFormat.TEXT,
          text: customText || 'Tiêu đề mẫu',
        };
        break;

      case ZnsComponentType.BODY:
        baseComponent.body = {
          type: ZnsComponentType.BODY,
          text: customText || 'Nội dung body mẫu với tham số {{1}} và {{2}}',
          parameters: customText
            ? this.extractParameters(customText)
            : ['param1', 'param2'],
        };
        break;

      case ZnsComponentType.FOOTER:
        baseComponent.footer = {
          type: ZnsComponentType.FOOTER,
          text: customText || 'Footer mẫu',
        };
        break;

      case ZnsComponentType.BUTTON:
        baseComponent.button = {
          type: ZnsComponentType.BUTTON,
          sub_type: ZnsButtonType.OPEN_URL,
          text: customText || 'Xem chi tiết',
          url: 'https://example.com',
        };
        break;

      case ZnsComponentType.INTERACTIVE:
        baseComponent.interactive = {
          type: ZnsComponentType.INTERACTIVE,
          interactive_type: 'button',
          body: {
            type: ZnsComponentType.BODY,
            text: customText || 'Chọn một trong các tùy chọn bên dưới:',
          },
          buttons: [
            {
              type: ZnsComponentType.BUTTON,
              sub_type: ZnsButtonType.OPEN_URL,
              text: 'Tùy chọn 1',
              url: 'https://example.com/option1',
            },
            {
              type: ZnsComponentType.BUTTON,
              sub_type: ZnsButtonType.OPEN_URL,
              text: 'Tùy chọn 2',
              url: 'https://example.com/option2',
            },
          ],
        };
        break;

      default:
        throw new Error(`Không hỗ trợ tạo component mẫu cho loại: ${type}`);
    }

    return baseComponent;
  }

  /**
   * Tạo template ZNS hoàn chỉnh với các component cơ bản
   * @param templateName Tên template
   * @param headerText Text cho header (tùy chọn)
   * @param bodyText Text cho body
   * @param footerText Text cho footer (tùy chọn)
   * @param buttons Danh sách button (tùy chọn)
   * @returns Template ZNS hoàn chỉnh
   */
  createCompleteTemplate(
    templateName: string,
    bodyText: string,
    headerText?: string,
    footerText?: string,
    buttons?: Array<{
      text: string;
      url?: string;
      phone?: string;
      type?: ZnsButtonType;
    }>,
  ): {
    templateName: string;
    components: ZnsTemplateComponentDto[];
  } {
    const components: ZnsTemplateComponentDto[] = [];

    // Add header if provided
    if (headerText) {
      components.push(
        this.createSampleComponent(ZnsComponentType.HEADER, headerText),
      );
    }

    // Add body (required)
    components.push(
      this.createSampleComponent(ZnsComponentType.BODY, bodyText),
    );

    // Add footer if provided
    if (footerText) {
      components.push(
        this.createSampleComponent(ZnsComponentType.FOOTER, footerText),
      );
    }

    // Add buttons if provided
    if (buttons && buttons.length > 0) {
      if (buttons.length === 1) {
        // Single button
        const button = buttons[0];
        const buttonComponent: ZnsTemplateComponentDto = {
          type: ZnsComponentType.BUTTON,
          button: {
            type: ZnsComponentType.BUTTON,
            sub_type:
              button.type ||
              (button.url ? ZnsButtonType.OPEN_URL : ZnsButtonType.PHONE_CODE),
            text: button.text,
            url: button.url,
            phone_number: button.phone,
          },
        };
        components.push(buttonComponent);
      } else {
        // Multiple buttons - use interactive
        const interactiveComponent: ZnsTemplateComponentDto = {
          type: ZnsComponentType.INTERACTIVE,
          interactive: {
            type: ZnsComponentType.INTERACTIVE,
            interactive_type: 'button',
            body: {
              type: ZnsComponentType.BODY,
              text: 'Chọn một trong các tùy chọn:',
            },
            buttons: buttons.slice(0, 3).map((button) => ({
              type: ZnsComponentType.BUTTON,
              sub_type:
                button.type ||
                (button.url
                  ? ZnsButtonType.OPEN_URL
                  : ZnsButtonType.PHONE_CODE),
              text: button.text,
              url: button.url,
              phone_number: button.phone,
            })),
          },
        };
        components.push(interactiveComponent);
      }
    }

    return {
      templateName,
      components,
    };
  }

  /**
   * Trích xuất tham số từ text
   * @param text Text chứa tham số
   * @returns Danh sách tham số
   */
  private extractParameters(text: string): string[] {
    const matches = Array.from(text.matchAll(this.PATTERNS.PARAMETER));
    const parameterNumbers = matches.map((match) => parseInt(match[1]));
    const uniqueParameters = [...new Set(parameterNumbers)].sort(
      (a, b) => a - b,
    );

    return uniqueParameters.map((num) => `param${num}`);
  }

  /**
   * Thay thế tham số trong text bằng giá trị thực
   * @param text Text chứa tham số
   * @param values Giá trị thay thế
   * @returns Text đã thay thế tham số
   */
  replaceParameters(text: string, values: Record<string, string>): string {
    return text.replace(this.PATTERNS.PARAMETER, (match, paramNumber) => {
      const paramKey = `param${paramNumber}`;
      return values[paramKey] || match;
    });
  }

  /**
   * Đếm số ký tự trong component
   * @param component Component cần đếm
   * @returns Số ký tự
   */
  countCharacters(component: ZnsTemplateComponentDto): number {
    let count = 0;

    switch (component.type) {
      case ZnsComponentType.TEXT:
        count += component.text?.text?.length || 0;
        break;

      case ZnsComponentType.HEADER:
        count += component.header?.text?.length || 0;
        break;

      case ZnsComponentType.BODY:
        count += component.body?.text?.length || 0;
        break;

      case ZnsComponentType.FOOTER:
        count += component.footer?.text?.length || 0;
        break;

      case ZnsComponentType.BUTTON:
        count += component.button?.text?.length || 0;
        break;

      case ZnsComponentType.INTERACTIVE:
        if (component.interactive) {
          count += component.interactive.header?.text?.length || 0;
          count += component.interactive.body?.text?.length || 0;
          count += component.interactive.footer?.text?.length || 0;
          if (component.interactive.buttons) {
            component.interactive.buttons.forEach((button) => {
              count += button.text?.length || 0;
            });
          }
        }
        break;
    }

    return count;
  }

  /**
   * Kiểm tra xem template có vượt quá giới hạn ký tự không
   * @param components Danh sách component
   * @returns Kết quả kiểm tra
   */
  checkCharacterLimits(components: ZnsTemplateComponentDto[]): {
    isValid: boolean;
    totalCharacters: number;
    maxAllowed: number;
    componentBreakdown: Array<{
      type: string;
      characters: number;
      limit: number;
      isValid: boolean;
    }>;
  } {
    const maxAllowed = 1024; // Giới hạn tổng cho template
    let totalCharacters = 0;
    const componentBreakdown: Array<{
      type: string;
      characters: number;
      limit: number;
      isValid: boolean;
    }> = [];

    components.forEach((component) => {
      const characters = this.countCharacters(component);
      totalCharacters += characters;

      let limit = 0;
      switch (component.type) {
        case ZnsComponentType.TEXT:
          limit = this.CHARACTER_LIMITS.TEXT;
          break;
        case ZnsComponentType.HEADER:
          limit = this.CHARACTER_LIMITS.HEADER_TEXT;
          break;
        case ZnsComponentType.BODY:
          limit = this.CHARACTER_LIMITS.BODY;
          break;
        case ZnsComponentType.FOOTER:
          limit = this.CHARACTER_LIMITS.FOOTER;
          break;
        case ZnsComponentType.BUTTON:
          limit = this.CHARACTER_LIMITS.BUTTON_TEXT;
          break;
        case ZnsComponentType.INTERACTIVE:
          limit = this.CHARACTER_LIMITS.BODY; // Use body limit for interactive
          break;
      }

      componentBreakdown.push({
        type: component.type,
        characters,
        limit,
        isValid: characters <= limit,
      });
    });

    return {
      isValid:
        totalCharacters <= maxAllowed &&
        componentBreakdown.every((c) => c.isValid),
      totalCharacters,
      maxAllowed,
      componentBreakdown,
    };
  }
}
