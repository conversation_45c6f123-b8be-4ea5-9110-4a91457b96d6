import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import {
  InputModalityEnum,
  OutputModalityEnum,
  SamplingParameterEnum,
  ModelFeatureEnum,
  ProviderEnum
} from '../enums/model-capabilities.enum';
import { ModelPricingInterface, DEFAULT_MODEL_PRICING } from '../interfaces/model-pricing.interface';

/**
 * ModelRegistry entity
 * Stores model configuration information (input/output, sampling, features, etc.)
 */
@Entity('model_registry')
export class ModelRegistry {
  /**
   * UUID of registry, auto-generated
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Model name pattern representative of the model
   */
  @Column({ name: 'model_name_pattern', type: 'varchar', length: 255, nullable: false })
  modelNamePattern: string;

  /**
   * Supported input data types (text, image, audio, etc.)
   */
  @Column({ name: 'input_modalities', type: 'jsonb', default: '[]' })
  inputModalities: InputModalityEnum[];

  /**
   * Supported output data types
   */
  @Column({ name: 'output_modalities', type: 'jsonb', default: '[]' })
  outputModalities: OutputModalityEnum[];

  /**
   * Sampling parameters like temperature, top_p, etc.
   */
  @Column({ name: 'sampling_parameters', type: 'jsonb', default: '[]' })
  samplingParameters: SamplingParameterEnum[];

  /**
   * Set of special features (like tool-use, function-calling)
   */
  @Column({ name: 'features', type: 'jsonb', default: '[]' })
  features: ModelFeatureEnum[];

  /**
   * Creation time (epoch millis)
   */
  @Column({ 
    name: 'created_at', 
    type: 'bigint', 
    nullable: true,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint'
  })
  createdAt?: number;

  /**
   * Creator (linked to employees)
   */
  @Column({ name: 'created_by', type: 'int', nullable: true })
  createdBy?: number;

  /**
   * Update time
   */
  @Column({ 
    name: 'updated_at', 
    type: 'bigint', 
    nullable: true,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint'
  })
  updatedAt?: number;

  /**
   * Updater
   */
  @Column({ name: 'updated_by', type: 'int', nullable: true })
  updatedBy?: number;

  /**
   * Soft delete time
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt?: number;

  /**
   * Person who performed deletion
   */
  @Column({ name: 'deleted_by', type: 'bigint', nullable: true })
  deletedBy?: number;

  /**
   * Model provider (OPENAI, ANTHROPIC, etc.)
   */
  @Column({ name: 'provider', type: 'varchar', default: 'OPENAI', nullable: false })
  provider: ProviderEnum;

  /**
   * Base pricing configuration
   */
  @Column({
    name: 'base_pricing',
    type: 'jsonb',
    default: JSON.stringify(DEFAULT_MODEL_PRICING),
    nullable: false
  })
  basePricing: ModelPricingInterface;

  /**
   * Fine-tune pricing configuration
   */
  @Column({
    name: 'fine_tune_pricing',
    type: 'jsonb',
    default: JSON.stringify(DEFAULT_MODEL_PRICING),
    nullable: false
  })
  fineTunePricing: ModelPricingInterface;

  /**
   * Training pricing
   */
  @Column({ name: 'training_pricing', type: 'int', default: 0, nullable: false })
  trainingPricing: number;

  /**
   * Whether fine-tuning is supported
   */
  @Column({ name: 'fine_tune', type: 'boolean', default: false, nullable: false })
  fineTune: boolean;
}
