import { IsIn, IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Enum cho các kiểu dữ liệu của trường tùy chỉnh
 */
export enum CustomFieldType {
  TEXT = 'text',
  NUMBER = 'number',
  DATE = 'date',
  BOOLEAN = 'boolean',
  JSON = 'json',
}

/**
 * DTO cho việc tạo trường tùy chỉnh
 */
export class CreateCustomFieldDto {
  /**
   * Tên trường
   * @example "Địa chỉ"
   */
  @ApiProperty({
    description: 'Tên trường',
    example: 'Địa chỉ',
  })
  @IsNotEmpty({ message: 'Tên trường không được để trống' })
  @IsString({ message: 'Tên trường phải là chuỗi' })
  fieldName: string;

  /**
   * <PERSON>i<PERSON> trị trường
   * @example "<PERSON>à N<PERSON>, Vi<PERSON>t Nam"
   */
  @ApiProperty({
    description: '<PERSON>i<PERSON> trị trường',
    example: 'Hà Nội, Việt Nam',
  })
  @IsNotEmpty({ message: 'Giá trị trường không được để trống' })
  fieldValue: any;

  /**
   * Kiểu dữ liệu của trường
   * @example "text"
   */
  @ApiProperty({
    description: 'Kiểu dữ liệu của trường',
    enum: CustomFieldType,
    example: CustomFieldType.TEXT,
  })
  @IsNotEmpty({ message: 'Kiểu dữ liệu không được để trống' })
  @IsIn(Object.values(CustomFieldType), {
    message: `Kiểu dữ liệu phải là một trong các giá trị: ${Object.values(CustomFieldType).join(', ')}`,
  })
  fieldType: CustomFieldType;
}
