import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { BaseNodeExecutor } from '../base/base-node.executor';
import { 
  Executor<PERSON>ontext, 
  ValidationResult,
  DetailedNodeExecutionResult 
} from '../base/node-executor.interface';
import { NodeExecutionConfig } from '../../types';
import { NodeGroupEnum } from '../../../../enums/node-group.enum';

/**
 * Integration service types
 */
type IntegrationServiceType = 
  | 'facebook'
  | 'zalo'
  | 'google'
  | 'microsoft'
  | 'slack'
  | 'discord'
  | 'telegram'
  | 'whatsapp'
  | 'email'
  | 'sms'
  | 'webhook'
  | 'database'
  | 'file-storage'
  | 'payment'
  | 'analytics'
  | 'crm'
  | 'custom';

/**
 * Integration operation types
 */
type IntegrationOperationType = 
  | 'send-message'
  | 'get-profile'
  | 'upload-file'
  | 'download-file'
  | 'create-record'
  | 'update-record'
  | 'delete-record'
  | 'query-data'
  | 'send-notification'
  | 'authenticate'
  | 'webhook-call'
  | 'api-call'
  | 'sync-data'
  | 'batch-operation';

/**
 * Authentication configuration
 */
interface AuthConfig {
  /** Authentication type */
  type: 'oauth2' | 'api-key' | 'bearer' | 'basic' | 'custom';
  
  /** OAuth2 configuration */
  oauth2?: {
    clientId: string;
    clientSecret: string;
    accessToken?: string;
    refreshToken?: string;
    tokenUrl?: string;
    scope?: string[];
  };
  
  /** API key configuration */
  apiKey?: {
    key: string;
    header?: string;
    queryParam?: string;
  };
  
  /** Bearer token */
  bearerToken?: string;
  
  /** Basic auth */
  basic?: {
    username: string;
    password: string;
  };
  
  /** Custom headers */
  customHeaders?: Record<string, string>;
}

/**
 * Integration node configuration
 */
interface IntegrationNodeConfig {
  /** Service type */
  service: IntegrationServiceType;
  
  /** Operation type */
  operation: IntegrationOperationType;
  
  /** Authentication */
  auth: AuthConfig;
  
  /** Service-specific configuration */
  config: {
    /** Base URL */
    baseUrl?: string;
    
    /** API version */
    apiVersion?: string;
    
    /** Rate limiting */
    rateLimit?: {
      requestsPerSecond: number;
      burstLimit?: number;
    };
    
    /** Timeout */
    timeout?: number;
    
    /** Retry configuration */
    retry?: {
      enabled: boolean;
      maxAttempts: number;
      backoffStrategy: 'fixed' | 'exponential';
      initialDelay: number;
    };
  };
  
  /** Request configuration */
  request: {
    /** HTTP method */
    method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
    
    /** Endpoint path */
    endpoint: string;
    
    /** Request headers */
    headers?: Record<string, string>;
    
    /** Query parameters */
    params?: Record<string, any>;
    
    /** Request body */
    body?: any;
    
    /** File upload */
    files?: Array<{
      fieldName: string;
      fileName: string;
      content: Buffer | string;
      mimeType?: string;
    }>;
  };
  
  /** Response handling */
  response?: {
    /** Expected status codes */
    expectedStatusCodes?: number[];
    
    /** Response transformation */
    transform?: {
      /** Extract specific fields */
      extract?: string[];
      
      /** Field mapping */
      mapping?: Record<string, string>;
      
      /** Custom transformation function */
      customTransform?: string;
    };
    
    /** Error handling */
    errorHandling?: {
      /** Map error codes to messages */
      errorMapping?: Record<string, string>;
      
      /** Ignore specific errors */
      ignoreErrors?: string[];
    };
  };
  
  /** Service-specific options */
  serviceOptions?: {
    /** Facebook specific */
    facebook?: {
      pageId?: string;
      appId?: string;
      fields?: string[];
    };
    
    /** Zalo specific */
    zalo?: {
      oaId?: string;
      templateId?: string;
    };
    
    /** Google specific */
    google?: {
      projectId?: string;
      serviceAccount?: any;
      scopes?: string[];
    };
    
    /** Email specific */
    email?: {
      provider: 'smtp' | 'sendgrid' | 'mailgun' | 'ses';
      from: string;
      to: string | string[];
      subject?: string;
      template?: string;
    };
    
    /** SMS specific */
    sms?: {
      provider: 'twilio' | 'nexmo' | 'aws-sns';
      from: string;
      to: string;
    };
  };
}

/**
 * Integration execution result
 */
interface IntegrationExecutionResult {
  /** Success status */
  success: boolean;
  
  /** Response data */
  data: any;
  
  /** HTTP status code */
  statusCode?: number;
  
  /** Response headers */
  headers?: Record<string, string>;
  
  /** Service-specific metadata */
  serviceMetadata?: {
    /** Message ID for messaging services */
    messageId?: string;
    
    /** File ID for file operations */
    fileId?: string;
    
    /** Record ID for database operations */
    recordId?: string;
    
    /** Rate limit information */
    rateLimit?: {
      remaining: number;
      resetTime: number;
    };
  };
  
  /** Execution metadata */
  metadata: {
    service: IntegrationServiceType;
    operation: IntegrationOperationType;
    executionTime: number;
    retryCount?: number;
    cached?: boolean;
  };
}

/**
 * Executor cho Integration nodes - handle external service integrations
 */
@Injectable()
export class IntegrationNodeExecutor extends BaseNodeExecutor {
  readonly nodeGroup = NodeGroupEnum.INTEGRATION;
  readonly supportedNodeTypes = [
    'facebook-integration',
    'zalo-integration',
    'google-integration',
    'microsoft-integration',
    'slack-integration',
    'discord-integration',
    'telegram-integration',
    'whatsapp-integration',
    'email-integration',
    'sms-integration',
    'webhook-integration',
    'database-integration',
    'file-storage-integration',
    'payment-integration',
    'analytics-integration',
    'crm-integration',
    'custom-integration',
    'api-integration',
  ];
  readonly executorName = 'IntegrationNodeExecutor';
  readonly version = '1.0.0';
  
  constructor(private readonly httpService: HttpService) {
    super();
  }
  
  /**
   * Execute integration node
   */
  protected async executeNode(
    context: ExecutorContext,
    config: NodeExecutionConfig
  ): Promise<DetailedNodeExecutionResult> {
    const startTime = Date.now();
    
    try {
      // Parse integration configuration
      const integrationConfig = this.parseIntegrationConfig(context);
      
      // Prepare request
      const request = await this.prepareRequest(integrationConfig, context);
      
      // Execute integration
      const result = await this.executeIntegration(integrationConfig, request, config);
      
      // Process response
      const outputData = this.processResponse(result, integrationConfig);
      
      const executionTime = Date.now() - startTime;
      
      return {
        success: true,
        outputData,
        metadata: {
          executionTime,
          customMetrics: {
            service: result.metadata.service,
            operation: result.metadata.operation,
            statusCode: result.statusCode,
            retryCount: result.metadata.retryCount || 0,
            responseSize: this.calculateResponseSize(result.data),
          },
          logs: [
            `Integration: ${result.metadata.service} - ${result.metadata.operation}`,
            `Status: ${result.statusCode || 'N/A'}`,
            `Execution time: ${executionTime}ms`,
            `Response size: ${this.calculateResponseSize(result.data)} bytes`,
          ],
        },
      };
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        success: false,
        error,
        shouldRetry: await this.shouldRetryIntegrationError(error, context),
        metadata: {
          executionTime,
          logs: [
            `Integration failed: ${error.message}`,
            `Execution time: ${executionTime}ms`,
          ],
        },
      };
    }
  }
  
  /**
   * Validate integration node input
   */
  protected async validateNodeSpecificInput(
    context: ExecutorContext,
    result: ValidationResult
  ): Promise<void> {
    const params = context.node.parameters as any;
    
    // Validate service type
    if (!params.service) {
      result.errors.push({
        code: 'MISSING_SERVICE',
        message: 'Integration node requires a service type',
        field: 'service',
        severity: 'error',
      });
    }
    
    // Validate operation type
    if (!params.operation) {
      result.errors.push({
        code: 'MISSING_OPERATION',
        message: 'Integration node requires an operation type',
        field: 'operation',
        severity: 'error',
      });
    }
    
    // Validate authentication
    if (!params.auth) {
      result.errors.push({
        code: 'MISSING_AUTH',
        message: 'Integration node requires authentication configuration',
        field: 'auth',
        severity: 'error',
      });
    } else {
      this.validateAuthConfig(params.auth, result);
    }
    
    // Validate request configuration
    if (!params.request) {
      result.errors.push({
        code: 'MISSING_REQUEST',
        message: 'Integration node requires request configuration',
        field: 'request',
        severity: 'error',
      });
    } else {
      if (!params.request.endpoint) {
        result.errors.push({
          code: 'MISSING_ENDPOINT',
          message: 'Request configuration requires endpoint',
          field: 'request.endpoint',
          severity: 'error',
        });
      }
    }
    
    // Service-specific validation
    this.validateServiceSpecificConfig(params, result);
  }
  
  // Private helper methods
  
  private parseIntegrationConfig(context: ExecutorContext): IntegrationNodeConfig {
    const params = context.node.parameters as any;
    
    return {
      service: params.service,
      operation: params.operation,
      auth: params.auth,
      config: {
        baseUrl: params.config?.baseUrl,
        apiVersion: params.config?.apiVersion,
        rateLimit: params.config?.rateLimit,
        timeout: params.config?.timeout || 30000,
        retry: {
          enabled: params.config?.retry?.enabled !== false,
          maxAttempts: params.config?.retry?.maxAttempts || 3,
          backoffStrategy: params.config?.retry?.backoffStrategy || 'exponential',
          initialDelay: params.config?.retry?.initialDelay || 1000,
        },
      },
      request: params.request,
      response: params.response,
      serviceOptions: params.serviceOptions,
    };
  }
  
  private async prepareRequest(
    config: IntegrationNodeConfig,
    context: ExecutorContext
  ): Promise<any> {
    const baseUrl = config.config.baseUrl || this.getDefaultBaseUrl(config.service);
    const endpoint = this.buildEndpoint(config, context);
    
    const requestConfig: any = {
      method: config.request.method || 'POST',
      url: `${baseUrl}${endpoint}`,
      timeout: config.config.timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'XState-Workflow-Engine/1.0',
        ...config.request.headers,
      },
    };
    
    // Add authentication
    this.addAuthentication(requestConfig, config.auth);
    
    // Add query parameters
    if (config.request.params) {
      requestConfig.params = this.resolveVariables(config.request.params, context);
    }
    
    // Add request body
    if (config.request.body) {
      requestConfig.data = this.resolveVariables(config.request.body, context);
    }
    
    // Handle file uploads
    if (config.request.files && config.request.files.length > 0) {
      const FormData = require('form-data');
      const formData = new FormData();
      
      for (const file of config.request.files) {
        formData.append(file.fieldName, file.content, {
          filename: file.fileName,
          contentType: file.mimeType,
        });
      }
      
      requestConfig.data = formData;
      requestConfig.headers = {
        ...requestConfig.headers,
        ...formData.getHeaders(),
      };
    }
    
    return requestConfig;
  }
  
  private async executeIntegration(
    config: IntegrationNodeConfig,
    request: any,
    nodeConfig: NodeExecutionConfig
  ): Promise<IntegrationExecutionResult> {
    const startTime = Date.now();
    let retryCount = 0;
    
    while (retryCount <= (config.config.retry?.maxAttempts || 3)) {
      try {
        // Apply rate limiting
        await this.applyRateLimit(config);
        
        // Execute request
        const response = await firstValueFrom(
          this.httpService.request(request)
        );
        
        return {
          success: true,
          data: response.data,
          statusCode: response.status,
          headers: response.headers,
          serviceMetadata: this.extractServiceMetadata(response, config),
          metadata: {
            service: config.service,
            operation: config.operation,
            executionTime: Date.now() - startTime,
            retryCount,
          },
        };
        
      } catch (error) {
        retryCount++;
        
        if (retryCount > (config.config.retry?.maxAttempts || 3)) {
          throw error;
        }
        
        if (!this.shouldRetryError(error, config)) {
          throw error;
        }
        
        // Wait before retry
        const delay = this.calculateRetryDelay(retryCount, config.config.retry!);
        await this.sleep(delay);
      }
    }
    
    throw new Error('Max retry attempts exceeded');
  }
  
  private processResponse(
    result: IntegrationExecutionResult,
    config: IntegrationNodeConfig
  ): any {
    let data = result.data;
    
    // Apply response transformation
    if (config.response?.transform) {
      const transform = config.response.transform;
      
      // Extract specific fields
      if (transform.extract && Array.isArray(transform.extract)) {
        const extracted: any = {};
        for (const field of transform.extract) {
          extracted[field] = this.getNestedValue(data, field);
        }
        data = extracted;
      }
      
      // Apply field mapping
      if (transform.mapping) {
        const mapped: any = {};
        for (const [sourceField, targetField] of Object.entries(transform.mapping)) {
          mapped[targetField] = this.getNestedValue(data, sourceField);
        }
        data = mapped;
      }
      
      // Apply custom transformation
      if (transform.customTransform) {
        try {
          const func = new Function('data', transform.customTransform);
          data = func(data);
        } catch (error) {
          this.logger.warn('Custom transformation failed:', error);
        }
      }
    }
    
    return {
      data,
      statusCode: result.statusCode,
      headers: result.headers,
      serviceMetadata: result.serviceMetadata,
      metadata: result.metadata,
    };
  }
  
  private validateAuthConfig(auth: any, result: ValidationResult): void {
    if (!auth.type) {
      result.errors.push({
        code: 'MISSING_AUTH_TYPE',
        message: 'Authentication configuration requires type',
        field: 'auth.type',
        severity: 'error',
      });
      return;
    }
    
    switch (auth.type) {
      case 'oauth2':
        if (!auth.oauth2?.clientId) {
          result.errors.push({
            code: 'MISSING_OAUTH2_CLIENT_ID',
            message: 'OAuth2 authentication requires clientId',
            field: 'auth.oauth2.clientId',
            severity: 'error',
          });
        }
        break;
        
      case 'api-key':
        if (!auth.apiKey?.key) {
          result.errors.push({
            code: 'MISSING_API_KEY',
            message: 'API key authentication requires key',
            field: 'auth.apiKey.key',
            severity: 'error',
          });
        }
        break;
        
      case 'bearer':
        if (!auth.bearerToken) {
          result.errors.push({
            code: 'MISSING_BEARER_TOKEN',
            message: 'Bearer authentication requires token',
            field: 'auth.bearerToken',
            severity: 'error',
          });
        }
        break;
        
      case 'basic':
        if (!auth.basic?.username || !auth.basic?.password) {
          result.errors.push({
            code: 'MISSING_BASIC_CREDENTIALS',
            message: 'Basic authentication requires username and password',
            field: 'auth.basic',
            severity: 'error',
          });
        }
        break;
    }
  }
  
  private validateServiceSpecificConfig(params: any, result: ValidationResult): void {
    switch (params.service) {
      case 'facebook':
        if (params.serviceOptions?.facebook && !params.serviceOptions.facebook.pageId) {
          result.warnings.push({
            code: 'MISSING_FACEBOOK_PAGE_ID',
            message: 'Facebook integration may require pageId',
            field: 'serviceOptions.facebook.pageId',
            suggestion: 'Set pageId for Facebook page operations',
          });
        }
        break;
        
      case 'email':
        if (params.serviceOptions?.email) {
          const email = params.serviceOptions.email;
          if (!email.from || !email.to) {
            result.errors.push({
              code: 'MISSING_EMAIL_RECIPIENTS',
              message: 'Email integration requires from and to addresses',
              field: 'serviceOptions.email',
              severity: 'error',
            });
          }
        }
        break;
        
      case 'sms':
        if (params.serviceOptions?.sms) {
          const sms = params.serviceOptions.sms;
          if (!sms.from || !sms.to) {
            result.errors.push({
              code: 'MISSING_SMS_NUMBERS',
              message: 'SMS integration requires from and to numbers',
              field: 'serviceOptions.sms',
              severity: 'error',
            });
          }
        }
        break;
    }
  }
  
  private getDefaultBaseUrl(service: IntegrationServiceType): string {
    const baseUrls: Record<string, string> = {
      facebook: 'https://graph.facebook.com',
      zalo: 'https://openapi.zalo.me',
      google: 'https://www.googleapis.com',
      microsoft: 'https://graph.microsoft.com',
      slack: 'https://slack.com/api',
      discord: 'https://discord.com/api',
      telegram: 'https://api.telegram.org',
    };
    
    return baseUrls[service] || '';
  }
  
  private buildEndpoint(config: IntegrationNodeConfig, context: ExecutorContext): string {
    let endpoint = config.request.endpoint;
    
    // Replace variables in endpoint
    endpoint = this.replaceVariables(endpoint, context);
    
    // Add API version if specified
    if (config.config.apiVersion) {
      endpoint = `/${config.config.apiVersion}${endpoint}`;
    }
    
    return endpoint;
  }
  
  private addAuthentication(requestConfig: any, auth: AuthConfig): void {
    switch (auth.type) {
      case 'oauth2':
        if (auth.oauth2?.accessToken) {
          requestConfig.headers['Authorization'] = `Bearer ${auth.oauth2.accessToken}`;
        }
        break;
        
      case 'api-key':
        if (auth.apiKey) {
          if (auth.apiKey.header) {
            requestConfig.headers[auth.apiKey.header] = auth.apiKey.key;
          } else if (auth.apiKey.queryParam) {
            requestConfig.params = requestConfig.params || {};
            requestConfig.params[auth.apiKey.queryParam] = auth.apiKey.key;
          } else {
            requestConfig.headers['X-API-Key'] = auth.apiKey.key;
          }
        }
        break;
        
      case 'bearer':
        if (auth.bearerToken) {
          requestConfig.headers['Authorization'] = `Bearer ${auth.bearerToken}`;
        }
        break;
        
      case 'basic':
        if (auth.basic) {
          requestConfig.auth = {
            username: auth.basic.username,
            password: auth.basic.password,
          };
        }
        break;
        
      case 'custom':
        if (auth.customHeaders) {
          Object.assign(requestConfig.headers, auth.customHeaders);
        }
        break;
    }
  }
  
  private async applyRateLimit(config: IntegrationNodeConfig): Promise<void> {
    if (config.config.rateLimit) {
      // TODO: Implement rate limiting logic
      // This could use Redis or in-memory storage to track request rates
    }
  }
  
  private shouldRetryError(error: any, config: IntegrationNodeConfig): boolean {
    if (!config.config.retry?.enabled) {
      return false;
    }
    
    // Retry on specific HTTP status codes
    const retryableStatusCodes = [408, 429, 500, 502, 503, 504];
    
    if (error.response?.status) {
      return retryableStatusCodes.includes(error.response.status);
    }
    
    // Retry on network errors
    const networkErrors = ['ECONNRESET', 'ECONNREFUSED', 'ETIMEDOUT', 'ENOTFOUND'];
    return networkErrors.some(code => error.code === code || error.message.includes(code));
  }
  
  private calculateRetryDelay(attempt: number, retryConfig: any): number {
    const baseDelay = retryConfig.initialDelay || 1000;
    
    if (retryConfig.backoffStrategy === 'exponential') {
      return baseDelay * Math.pow(2, attempt - 1);
    }
    
    return baseDelay;
  }
  
  private async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  private extractServiceMetadata(response: any, config: IntegrationNodeConfig): any {
    const metadata: any = {};
    
    // Extract rate limit information
    if (response.headers['x-ratelimit-remaining']) {
      metadata.rateLimit = {
        remaining: parseInt(response.headers['x-ratelimit-remaining']),
        resetTime: parseInt(response.headers['x-ratelimit-reset']),
      };
    }
    
    // Service-specific metadata extraction
    switch (config.service) {
      case 'facebook':
        if (response.data.id) {
          metadata.messageId = response.data.id;
        }
        break;
        
      case 'zalo':
        if (response.data.msg_id) {
          metadata.messageId = response.data.msg_id;
        }
        break;
    }
    
    return metadata;
  }
  
  private async shouldRetryIntegrationError(error: any, context: ExecutorContext): Promise<boolean> {
    // Similar to HTTP node retry logic
    const retryableErrors = ['ECONNRESET', 'ECONNREFUSED', 'ETIMEDOUT', 'ENOTFOUND'];
    
    if (error.response?.status) {
      const retryableStatusCodes = [408, 429, 500, 502, 503, 504];
      return retryableStatusCodes.includes(error.response.status);
    }
    
    return retryableErrors.some(code => error.code === code || error.message.includes(code));
  }
  
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }
  
  private resolveVariables(obj: any, context: ExecutorContext): any {
    if (typeof obj === 'string') {
      return this.replaceVariables(obj, context);
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.resolveVariables(item, context));
    }
    
    if (obj && typeof obj === 'object') {
      const resolved: any = {};
      for (const [key, value] of Object.entries(obj)) {
        resolved[key] = this.resolveVariables(value, context);
      }
      return resolved;
    }
    
    return obj;
  }
  
  private replaceVariables(text: string, context: ExecutorContext): string {
    return text.replace(/\{\{(\w+(?:\.\w+)*)\}\}/g, (match, path) => {
      const value = this.getNestedValue(context.inputData, path);
      return value != null ? String(value) : match;
    });
  }
  
  private calculateResponseSize(data: any): number {
    if (typeof data === 'string') {
      return Buffer.byteLength(data, 'utf8');
    }
    
    if (typeof data === 'object') {
      return Buffer.byteLength(JSON.stringify(data), 'utf8');
    }
    
    return 0;
  }
}
