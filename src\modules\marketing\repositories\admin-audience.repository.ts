import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { AdminAudience } from '../entities/admin-audience.entity';

/**
 * Repository cho AdminAudience entity trong worker
 * Chỉ cần các method cơ bản để đọc dữ liệu
 */
@Injectable()
export class AdminAudienceRepository {
  constructor(
    @InjectRepository(AdminAudience)
    private readonly repository: Repository<AdminAudience>,
  ) {}

  /**
   * Tìm audience theo ID
   */
  async findById(id: number): Promise<AdminAudience | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm audiences theo danh sách IDs
   */
  async findByIds(ids: number[]): Promise<AdminAudience[]> {
    if (ids.length === 0) return [];
    return this.repository.find({ where: { id: In(ids) } });
  }

  /**
   * Tìm audience theo email
   */
  async findByEmail(email: string): Promise<AdminAudience | null> {
    return this.repository.findOne({ where: { email } });
  }

  /**
   * Tìm audiences theo danh sách emails
   */
  async findByEmails(emails: string[]): Promise<AdminAudience[]> {
    if (emails.length === 0) return [];
    return this.repository.find({ where: { email: In(emails) } });
  }

  /**
   * Đếm tổng số audiences
   */
  async count(): Promise<number> {
    return this.repository.count();
  }
}
