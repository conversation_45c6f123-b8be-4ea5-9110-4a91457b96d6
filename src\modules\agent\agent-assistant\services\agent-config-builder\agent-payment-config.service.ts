import { Injectable } from '@nestjs/common';
import { PaymentGatewayRepository, UserProviderShipmentRepository, AgentUserRepository } from '../../repositories';
import { PaymentMethodEnum } from '../../enums';
import { PaymentGateway, UserProviderShipment } from '../../entities';
import { PaymentConfig } from '../../interfaces/payment-config.interface';

/**
 * Agent Payment Configuration Service
 * 
 * Handles payment gateway, shipment provider, and payment method configuration.
 * Responsible for: "How can this agent handle transactions?"
 */
@Injectable()
export class AgentPaymentConfigService {
  constructor(
    private readonly paymentGatewayRepository: PaymentGatewayRepository,
    private readonly shipmentRepository: UserProviderShipmentRepository,
    private readonly agentUserRepository: AgentUserRepository,
  ) {}

  /**
   * Get payment configuration for an agent
   */
  async getPaymentConfig(agentId: string, userId: number): Promise<PaymentConfig> {
    // Get AgentUser data to extract payment configuration
    const agentUser = await this.agentUserRepository.findByIdAndUser(agentId, userId);
    if (!agentUser) {
      throw new Error('Agent not found or access denied');
    }
    // Fetch payment gateway data if ID exists
    let paymentGateway: PaymentGateway | null = null;
    if (agentUser.paymentGatewayId) {
      paymentGateway = await this.paymentGatewayRepository.findById(String(agentUser.paymentGatewayId));
    }

    // Fetch shipment provider data if ID exists
    let shipmentProvider: UserProviderShipment | null = null;
    if (agentUser.userProviderShipmentId) {
      shipmentProvider = await this.shipmentRepository.findById(agentUser.userProviderShipmentId);
    }

    return {
      paymentGateway,
      shipmentProvider,
      receiverPayShippingFee: agentUser.receiverPayShippingFee ?? true, // Default to true
      paymentMethods: this.parsePaymentMethods(agentUser.paymentMethods),
    };
  }

  /**
   * Parse and validate payment methods from JSONB
   */
  private parsePaymentMethods(paymentMethodsJson: any): PaymentMethodEnum[] {
    // Handle null, undefined, or non-array values
    if (!paymentMethodsJson || !Array.isArray(paymentMethodsJson)) {
      return [];
    }
    
    // Filter and validate payment methods
    return paymentMethodsJson.filter(method =>
      Object.values(PaymentMethodEnum).includes(method as PaymentMethodEnum)
    ) as PaymentMethodEnum[];
  }

  /**
   * Get payment configuration using specific payment properties (no database lookup)
   * Use this when agentUser has already been fetched and validated
   */
  async getPaymentConfigFromValidatedAgent(paymentProps: {
    paymentGatewayId?: number;
    userProviderShipmentId?: string;
    receiverPayShippingFee?: boolean;
    paymentMethods?: any;
  }): Promise<PaymentConfig> {
    // Get payment gateway data if ID exists
    let paymentGateway: any = null;
    if (paymentProps.paymentGatewayId) {
      paymentGateway = await this.paymentGatewayRepository.findById(String(paymentProps.paymentGatewayId));
    }

    // Get shipment provider data if ID exists
    let shipmentProvider: any = null;
    if (paymentProps.userProviderShipmentId) {
      shipmentProvider = await this.shipmentRepository.findById(paymentProps.userProviderShipmentId);
    }

    return {
      paymentGateway,
      shipmentProvider,
      paymentMethods: this.parsePaymentMethods(paymentProps.paymentMethods),
      receiverPayShippingFee: paymentProps.receiverPayShippingFee || false,
    };
  }
}
