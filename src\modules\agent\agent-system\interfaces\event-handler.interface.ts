import type { EventProcessingContext } from '../schemas/agent-system.schema';

/**
 * Interface for LangGraph event handlers (Strategy Pattern)
 */
export interface LangGraphEventHandler {
  /**
   * Check if this handler can process the given event
   * @param event - LangGraph event type
   * @param data - Event data
   * @param tags - Event tags
   * @returns True if handler can process this event
   */
  canHandle(event: string, data: any, tags: string[]): boolean;

  /**
   * Process the event
   * @param context - Event processing context
   * @returns Promise that resolves when processing is complete
   */
  handle(context: EventProcessingContext): Promise<void>;
}

/**
 * Event handler registry interface
 */
export interface EventHandlerRegistry {
  /**
   * Register an event handler
   * @param handler - Handler to register
   */
  register(handler: LangGraphEventHandler): void;

  /**
   * Get handler for specific event
   * @param event - Event type
   * @param data - Event data
   * @param tags - Event tags
   * @returns Handler if found, undefined otherwise
   */
  getHandler(
    event: string,
    data: any,
    tags: string[],
  ): LangGraphEventHandler | undefined;

  /**
   * Get all registered handlers
   * @returns Array of all handlers sorted by priority
   */
  getAllHandlers(): LangGraphEventHandler[];
}
