import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thống kê tổng quan về marketing
 */
export class MarketingOverviewStatisticsDto {
  @ApiProperty({
    description: 'Tổng số audience',
    example: 150,
  })
  totalAudiences: number;

  @ApiProperty({
    description: 'Tổng số segment',
    example: 10,
  })
  totalSegments: number;

  @ApiProperty({
    description: 'Tổng số campaign',
    example: 5,
  })
  totalCampaigns: number;

  @ApiProperty({
    description: 'Tổng số tag',
    example: 20,
  })
  totalTags: number;

  @ApiProperty({
    description: 'Số lượng audience được thêm trong khoảng thời gian',
    example: 25,
  })
  newAudiences: number;

  @ApiProperty({
    description: 'Số lượng campaign đã chạy trong khoảng thời gian',
    example: 3,
  })
  activeCampaigns: number;

  @ApiProperty({
    description: 'Tỷ lệ mở email trung bình (%)',
    example: 35.5,
  })
  averageOpenRate: number;

  @ApiProperty({
    description: 'Tỷ lệ click email trung bình (%)',
    example: 12.3,
  })
  averageClickRate: number;

  @ApiProperty({
    description: 'Thời gian cập nhật thống kê (Unix timestamp)',
    example: 1619171200,
  })
  updatedAt: number;
}
