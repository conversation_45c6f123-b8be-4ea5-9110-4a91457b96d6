/**
 * Interface cho Google AI Model
 * Khớp chính xác với cấu trúc JSON mà API trả về
 */
export interface GoogleAIModel {
  name: string;
  baseModelId: string;
  version: string;
  displayName?: string;
  description?: string;
  inputTokenLimit?: number;
  outputTokenLimit?: number;
  supportedGenerationMethods?: string[];
  temperature?: number;
  maxTemperature?: number;
  topP?: number;
  topK?: number;
  // Các field được thêm bởi service để hỗ trợ pattern matching
  normalizedName?: string;
  modelType?: 'base' | 'fine-tuned' | 'unknown';
}

/**
 * Interface cho Google AI Models Response
 */
export interface GoogleAIModelsResponse {
  models: GoogleAIModel[];
}

/**
 * Interface cho tham số tạo fine-tuning job của Google AI
 */
export interface GoogleAIFineTuningParams {
  /** ID của model cơ sở để fine-tune (ví dụ: gemini-1.5-pro) */
  baseModelId: string;
  /** Tên hiển thị cho model fine-tuned */
  displayName: string;
  /** <PERSON>ô tả về model fine-tuned */
  description?: string;
  /** URL của file dữ liệu huấn luyện (định dạng JSONL) trong Cloud Storage */
  trainingDataUri: string;
  /** URL của file dữ liệu validation (định dạng JSONL) trong Cloud Storage (tùy chọn) */
  validationDataUri?: string;
  /** Siêu tham số cho quá trình fine-tuning */
  hyperParameters?: {
    /** Số epoch để huấn luyện (mặc định: 3) */
    epochCount?: number;
    /** Kích thước batch (mặc định: 4) */
    batchSize?: number;
    /** Tốc độ học (mặc định: 1e-5) */
    learningRate?: number;
  };
}

/**
 * Interface cho kết quả tạo fine-tuning job của Google AI
 */
export interface GoogleAIFineTuningResponse {
  /** Tên của fine-tuning job (định dạng: projects/{project}/locations/{location}/tuningJobs/{tuning_job}) */
  name: string;
  /** Thời gian tạo (định dạng ISO) */
  createTime: string;
  /** Thời gian cập nhật cuối cùng (định dạng ISO) */
  updateTime: string;
  /** Trạng thái của job */
  state: 'JOB_STATE_UNSPECIFIED' | 'JOB_STATE_QUEUED' | 'JOB_STATE_RUNNING' | 'JOB_STATE_SUCCEEDED' | 'JOB_STATE_FAILED' | 'JOB_STATE_CANCELLED';
  /** ID của model cơ sở */
  baseModelId: string;
  /** Tên hiển thị cho model fine-tuned */
  displayName: string;
  /** Mô tả về model fine-tuned */
  description?: string;
  /** Thông tin về dữ liệu huấn luyện */
  trainingData: {
    /** URL của file dữ liệu huấn luyện */
    datasetUri: string;
    /** Số lượng ví dụ huấn luyện */
    sampleCount?: number;
  };
  /** Thông tin về dữ liệu validation (nếu có) */
  validationData?: {
    /** URL của file dữ liệu validation */
    datasetUri: string;
    /** Số lượng ví dụ validation */
    sampleCount?: number;
  };
  /** Siêu tham số đã sử dụng */
  hyperParameters: {
    /** Số epoch để huấn luyện */
    epochCount: number;
    /** Kích thước batch */
    batchSize: number;
    /** Tốc độ học */
    learningRate: number;
  };
  /** Thông tin về model đã fine-tune (chỉ có khi job hoàn thành) */
  tunedModel?: {
    /** Tên của model đã fine-tune */
    name: string;
    /** ID của model đã fine-tune */
    modelId: string;
  };
  /** Thông tin về lỗi (nếu có) */
  error?: {
    /** Mã lỗi */
    code: number;
    /** Thông báo lỗi */
    message: string;
  };
}
