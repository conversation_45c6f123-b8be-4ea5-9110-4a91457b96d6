/**
 * Interface for OpenAI Chat Completion Response
 */
export interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string | null;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

/**
 * Interface for Vector Store Configuration
 */
export interface VectorStoreConfig {
  name: string;
  description?: string;
  storage?: number;
}

/**
 * Interface for Vector Store Update Configuration
 */
export interface UpdateVectorStoreConfig {
  name: string;
  storage: number;
}

/**
 * Interface for Vector Store Response
 */
export interface VectorStoreResponse {
  vectorStoreId: string;
  fileId?: string | string[];
}

/**
 * Interface for Vector Store Detail Response
 */
export interface VectorStoreDetailResponse {
  id: string;
  name: string;
  storage: number;
  createdAt: number;
  updatedAt: number;
}

/**
 * Interface for OpenAI Model
 */
export interface OpenAIModel {
  id: string;
  object: string;
  created: number;
  owned_by: string;
  permission: {
    id: string;
    object: string;
    created: number;
    allow_create_engine: boolean;
    allow_sampling: boolean;
    allow_logprobs: boolean;
    allow_search_indices: boolean;
    allow_view: boolean;
    allow_fine_tuning: boolean;
    organization: string;
    group: null | string;
    is_blocking: boolean;
  }[];
  root: string;
  parent: null | string;
}

/**
 * Interface for OpenAI Models List Response
 */
export interface OpenAIModelsResponse {
  object: string;
  data: OpenAIModel[];
}

/**
 * Interface for Embedding Response
 */
export interface EmbeddingResponse {
  data: {
    embedding: number[];
    index: number;
    object: string;
  }[];
  model: string;
  object: string;
  usage: {
    prompt_tokens: number;
    total_tokens: number;
  };
}

/**
 * Interface cho tham số tạo fine-tuning job
 */
export interface CreateFineTuningJobParams {
  /** ID của file training data đã upload lên OpenAI */
  trainingFileId: string;
  /** ID của model cơ sở để fine-tune (ví dụ: gpt-3.5-turbo) */
  model: string;
  /** Mô tả về fine-tuning job */
  suffix?: string;
  /** Siêu tham số cho quá trình fine-tuning */
  hyperparameters?: {
    /** Số epoch để huấn luyện (mặc định: 3, tối đa: 50) */
    nEpochs?: number;
    /** Kích thước batch (mặc định: auto) */
    batchSize?: number | 'auto';
    /** Tốc độ học (mặc định: auto) */
    learningRateMultiplier?: number | 'auto';
  };
  /** ID của file validation data đã upload lên OpenAI */
  validationFileId?: string;
}

/**
 * Interface cho kết quả tạo fine-tuning job
 */
export interface FineTuningJobResponse {
  /** ID của fine-tuning job */
  id: string;
  /** Loại đối tượng */
  object: string;
  /** Thời gian tạo (timestamp) */
  createdAt: number;
  /** Thời gian cập nhật cuối cùng (timestamp) */
  updatedAt: number;
  /** ID của model cơ sở */
  model: string;
  /** ID của model fine-tuned (chỉ có khi job hoàn thành) */
  fineTunedModel: string | null;
  /** ID của tổ chức */
  organizationId: string;
  /** Trạng thái của job */
  status: 'validating_files' | 'queued' | 'running' | 'succeeded' | 'failed' | 'cancelled';
  /** ID của file training data */
  trainingFile: string;
  /** ID của file validation data (nếu có) */
  validationFile: string | null;
  /** Kết quả của quá trình fine-tuning */
  resultFiles: string[];
  /** Thông tin về lỗi (nếu có) */
  error: {
    code: string;
    message: string;
    param: string | null;
  } | null;
  /** Siêu tham số đã sử dụng */
  hyperparameters: {
    nEpochs: number | 'auto';
    batchSize: number | 'auto';
    learningRateMultiplier: number | 'auto';
  };
  /** Số lượng token đã xử lý */
  trainedTokens: number | null;
}
