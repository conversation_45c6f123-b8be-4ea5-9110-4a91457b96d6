/**
 * Custom Redis Error Classes for Enhanced Error Handling
 *
 * These error classes provide structured error information for different
 * types of Redis failures, enabling better error handling and logging.
 */

export enum RedisErrorCode {
  CONNECTION_FAILED = 'REDIS_CONNECTION_FAILED',
  CONNECTION_TIMEOUT = 'REDIS_CONNECTION_TIMEOUT',
  COMMAND_TIMEOUT = 'REDIS_COMMAND_TIMEOUT',
  AUTHENTICATION_FAILED = 'REDIS_AUTH_FAILED',
  PERMISSION_DENIED = 'REDIS_PERMISSION_DENIED',
  INVALID_COMMAND = 'REDIS_INVALID_COMMAND',
  MEMORY_FULL = 'REDIS_MEMORY_FULL',
  SERVER_ERROR = 'REDIS_SERVER_ERROR',
  NETWORK_ERROR = 'REDIS_NETWORK_ERROR',
  CIRCUIT_BREAKER_OPEN = 'REDIS_CIRCUIT_BREAKER_OPEN',
  RETRY_EXHAUSTED = 'REDIS_RETRY_EXHAUSTED',
  STREAM_ERROR = 'REDIS_STREAM_ERROR',
  PUBSUB_ERROR = 'REDIS_PUBSUB_ERROR',
  UNKNOWN_ERROR = 'REDIS_UNKNOWN_ERROR',
}

export interface RedisErrorContext {
  operation: string;
  stream?: string;
  channel?: string;
  data?: any;
  attempt?: number;
  maxAttempts?: number;
  duration?: number;
  timestamp: number;
}

/**
 * Base Redis Error Class
 */
export class RedisError extends Error {
  public readonly code: RedisErrorCode;
  public readonly context: RedisErrorContext;
  public readonly originalError?: Error;
  public readonly isRetryable: boolean;

  constructor(
    code: RedisErrorCode,
    message: string,
    context: RedisErrorContext,
    originalError?: Error,
    isRetryable: boolean = false,
  ) {
    super(message);
    this.name = 'RedisError';
    this.code = code;
    this.context = context;
    this.originalError = originalError;
    this.isRetryable = isRetryable;

    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, RedisError);
    }
  }

  /**
   * Get structured error information for logging
   */
  toLogObject(): Record<string, any> {
    return {
      errorCode: this.code,
      message: this.message,
      context: this.context,
      isRetryable: this.isRetryable,
      originalError: this.originalError
        ? {
            name: this.originalError.name,
            message: this.originalError.message,
            stack: this.originalError.stack,
          }
        : undefined,
      timestamp: new Date().toISOString(),
    };
  }
}

/**
 * Connection-related Redis errors
 */
export class RedisConnectionError extends RedisError {
  constructor(
    message: string,
    context: RedisErrorContext,
    originalError?: Error,
  ) {
    super(
      RedisErrorCode.CONNECTION_FAILED,
      message,
      context,
      originalError,
      true,
    );
    this.name = 'RedisConnectionError';
  }
}

/**
 * Timeout-related Redis errors
 */
export class RedisTimeoutError extends RedisError {
  constructor(
    message: string,
    context: RedisErrorContext,
    originalError?: Error,
  ) {
    super(
      RedisErrorCode.COMMAND_TIMEOUT,
      message,
      context,
      originalError,
      true,
    );
    this.name = 'RedisTimeoutError';
  }
}

/**
 * Stream operation errors
 */
export class RedisStreamError extends RedisError {
  constructor(
    message: string,
    context: RedisErrorContext,
    originalError?: Error,
  ) {
    super(RedisErrorCode.STREAM_ERROR, message, context, originalError, true);
    this.name = 'RedisStreamError';
  }
}

/**
 * Pub/Sub operation errors
 */
export class RedisPubSubError extends RedisError {
  constructor(
    message: string,
    context: RedisErrorContext,
    originalError?: Error,
  ) {
    super(RedisErrorCode.PUBSUB_ERROR, message, context, originalError, true);
    this.name = 'RedisPubSubError';
  }
}

/**
 * Circuit breaker errors
 */
export class RedisCircuitBreakerError extends RedisError {
  constructor(message: string, context: RedisErrorContext) {
    super(
      RedisErrorCode.CIRCUIT_BREAKER_OPEN,
      message,
      context,
      undefined,
      false,
    );
    this.name = 'RedisCircuitBreakerError';
  }
}

/**
 * Retry exhausted errors
 */
export class RedisRetryExhaustedError extends RedisError {
  constructor(
    message: string,
    context: RedisErrorContext,
    originalError?: Error,
  ) {
    super(
      RedisErrorCode.RETRY_EXHAUSTED,
      message,
      context,
      originalError,
      false,
    );
    this.name = 'RedisRetryExhaustedError';
  }
}

/**
 * Error factory for creating appropriate Redis errors based on original error
 */
export class RedisErrorFactory {
  static createFromError(
    originalError: any,
    operation: string,
    context: Partial<RedisErrorContext> = {},
  ): RedisError {
    const fullContext: RedisErrorContext = {
      operation,
      timestamp: Date.now(),
      ...context,
    };

    const errorMessage = originalError?.message || 'Unknown Redis error';
    const errorCode = originalError?.code;

    // Connection errors
    if (this.isConnectionError(originalError)) {
      return new RedisConnectionError(
        `Redis connection failed during ${operation}: ${errorMessage}`,
        fullContext,
        originalError,
      );
    }

    // Timeout errors
    if (this.isTimeoutError(originalError)) {
      return new RedisTimeoutError(
        `Redis operation timed out during ${operation}: ${errorMessage}`,
        fullContext,
        originalError,
      );
    }

    // Stream errors
    if (operation.includes('XADD') || operation.includes('XREAD')) {
      return new RedisStreamError(
        `Redis stream operation failed during ${operation}: ${errorMessage}`,
        fullContext,
        originalError,
      );
    }

    // Pub/Sub errors
    if (operation.includes('PUBLISH') || operation.includes('SUBSCRIBE')) {
      return new RedisPubSubError(
        `Redis pub/sub operation failed during ${operation}: ${errorMessage}`,
        fullContext,
        originalError,
      );
    }

    // Circuit breaker errors
    if (errorMessage.includes('circuit breaker')) {
      return new RedisCircuitBreakerError(
        `Redis circuit breaker is open for ${operation}`,
        fullContext,
      );
    }

    // Default to generic Redis error
    return new RedisError(
      RedisErrorCode.UNKNOWN_ERROR,
      `Redis operation failed during ${operation}: ${errorMessage}`,
      fullContext,
      originalError,
      this.isRetryableError(originalError),
    );
  }

  private static isConnectionError(error: any): boolean {
    const connectionErrorPatterns = [
      'ECONNRESET',
      'ECONNREFUSED',
      'ENOTFOUND',
      'EAI_AGAIN',
      'Connection is closed',
      'Redis connection lost',
    ];

    const errorMessage = error?.message || '';
    const errorCode = error?.code || '';

    return connectionErrorPatterns.some(
      (pattern) => errorMessage.includes(pattern) || errorCode === pattern,
    );
  }

  private static isTimeoutError(error: any): boolean {
    const timeoutErrorPatterns = [
      'ETIMEDOUT',
      'timeout',
      'Connection timeout',
      'Command timeout',
    ];

    const errorMessage = error?.message || '';
    const errorCode = error?.code || '';

    return timeoutErrorPatterns.some(
      (pattern) =>
        errorMessage.toLowerCase().includes(pattern.toLowerCase()) ||
        errorCode === pattern,
    );
  }

  private static isRetryableError(error: any): boolean {
    const retryablePatterns = [
      'ECONNRESET',
      'ECONNREFUSED',
      'ETIMEDOUT',
      'ENOTFOUND',
      'EAI_AGAIN',
      'EPIPE',
      'READONLY',
      'LOADING',
      'BUSY',
    ];

    const errorMessage = error?.message || '';
    const errorCode = error?.code || '';

    return retryablePatterns.some(
      (pattern) => errorMessage.includes(pattern) || errorCode === pattern,
    );
  }
}
