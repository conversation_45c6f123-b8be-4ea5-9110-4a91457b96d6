/**
 * @file User Memory Record Interface
 *
 * Defines the structure for user memory records as stored in the database.
 * This interface aligns with the actual user_memories table schema.
 */

/**
 * User memory record structure for database operations
 *
 * @interface UserMemoryRecord
 * @description Represents a user memory record as stored in the user_memories table.
 * Matches the actual database schema with structured_content and embeddable_text.
 *
 * @example
 * ```typescript
 * const userMemory: UserMemoryRecord = {
 *   id: "550e8400-e29b-41d4-a716-************",
 *   userId: 123,
 *   structuredContent: {
 *     title: "User Preference",
 *     summary: "User prefers dark mode and compact layout",
 *     source: "conversation"
 *   },
 *   createdAt: 1705312200000,
 *   metadata: {
 *     importance: 8,
 *     tags: ["preferences", "ui"]
 *   }
 * };
 * ```
 */
export interface UserMemoryRecord {
  /**
   * Unique identifier for the memory record (database primary key)
   *
   * @type {string}
   * @description UUID primary key from the user_memories table.
   */
  id: string;

  /**
   * User identifier associated with this memory
   *
   * @type {number}
   * @description Foreign key reference to users table (integer).
   */
  userId: number;

  /**
   * Structured content of the memory
   *
   * @type {Record<string, any>}
   * @description JSONB field containing structured fact data (title, summary, source, etc.).
   */
  structuredContent: Record<string, any>;

  /**
   * Timestamp when the memory was created
   *
   * @type {number}
   * @description Database timestamp (created_at column) as Unix timestamp in milliseconds.
   */
  createdAt: number;

  /**
   * Additional metadata for the memory (optional)
   *
   * @type {Record<string, any> | null}
   * @description JSONB field containing additional context and metadata.
   */
  metadata?: Record<string, any> | null;
}

/**
 * Interface for creating a new user memory record
 *
 * @description Omits auto-generated fields (id, createdAt) for new record creation.
 */
export type CreateUserMemoryRecord = Omit<
  UserMemoryRecord,
  'id' | 'createdAt'
> & {
  /**
   * Optional metadata for new records
   */
  metadata?: Record<string, any>;
};

/**
 * Interface for user memory search results
 *
 * @description Extends UserMemoryRecord with similarity score for search operations.
 */
export interface UserMemorySearchResult extends UserMemoryRecord {
  /**
   * Similarity score from vector search (0-1, where 1 is perfect match)
   *
   * @type {number}
   * @description Cosine similarity score between the query embedding and this memory's embedding.
   */
  similarity: number;
}

/**
 * Type guard to check if an object is a valid UserMemoryRecord
 *
 * @param obj - Object to check
 * @returns True if the object conforms to the UserMemoryRecord interface
 */
export function isUserMemoryRecord(obj: any): obj is UserMemoryRecord {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.userId === 'number' &&
    typeof obj.structuredContent === 'object' &&
    typeof obj.createdAt === 'number' &&
    (obj.metadata === null ||
      obj.metadata === undefined ||
      (typeof obj.metadata === 'object' && obj.metadata !== null))
  );
}

/**
 * Database query result type for user memory operations
 *
 * @description Raw database result that may need transformation to UserMemoryRecord.
 */
export interface UserMemoryQueryResult {
  id: string;
  user_id: number;
  structured_content: Record<string, any>;
  embedding: number[];
  created_at: number | string;
  metadata?: Record<string, any> | null;
}

/**
 * Transform database query result to UserMemoryRecord
 *
 * @param queryResult - Raw database query result
 * @returns Properly typed UserMemoryRecord
 */
export function transformUserMemoryQueryResult(
  queryResult: UserMemoryQueryResult,
): UserMemoryRecord {
  return {
    id: queryResult.id,
    userId: queryResult.user_id,
    structuredContent: queryResult.structured_content,
    createdAt:
      typeof queryResult.created_at === 'string'
        ? parseInt(queryResult.created_at, 10)
        : queryResult.created_at,
    metadata: queryResult.metadata || null,
  };
}
