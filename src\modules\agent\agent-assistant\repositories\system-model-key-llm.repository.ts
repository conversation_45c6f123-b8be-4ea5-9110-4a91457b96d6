import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SystemModelKeyLlm } from '../entities/system-model-key-llm.entity';
import { SystemModelWithKeys } from '../interfaces/system-model-key.interface';



@Injectable()
export class SystemModelKeyLlmRepository {
  constructor(
    @InjectRepository(SystemModelKeyLlm)
    private readonly repository: Repository<SystemModelKeyLlm>,
  ) {}

  async findKeysByModelId(modelId: string): Promise<SystemModelWithKeys[]> {
    return this.repository
      .createQueryBuilder('smkl')
      .innerJoin('system_key_llm', 'skl', 'smkl.llm_key_id = skl.id')
      .select([
        'smkl.model_id as "modelId"',
        'smkl.llm_key_id as "llmKeyId"',
        'skl.name as "keyName"',
        'skl.provider as "provider"',
        'skl.api_key as "apiKey"',
        'skl.created_at as "keyCreatedAt"',
        'skl.deleted_at as "keyDeletedAt"'
      ])
      .where('smkl.model_id = :modelId', { modelId })
      .andWhere('skl.deleted_at IS NULL')
      .getRawMany();
  }
}
