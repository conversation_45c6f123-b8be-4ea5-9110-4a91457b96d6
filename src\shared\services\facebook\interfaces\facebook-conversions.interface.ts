/**
 * Interface cho Facebook Pixel
 */
export interface FacebookPixel {
  id: string;
  name: string;
  creation_time: string;
  last_fired_time?: string;
  code: string;
  is_created_by_business: boolean;
  owner_ad_account?: {
    id: string;
    name: string;
  };
  owner_business?: {
    id: string;
    name: string;
  };
  pixel_domain_control_rule?: string;
  is_unavailable?: boolean;
  data_use_setting?: string;
  enable_automatic_matching?: boolean;
  first_party_cookie_status?: string;
  is_consolidated_container?: boolean;
  consolidated_tracking?: ConsolidatedTracking;
  restricted_use?: RestrictedUse;
  valid_domains?: string[];
  microdata?: Microdata;
  automatic_matching_fields?: string[];
  can_proxy?: boolean;
  config?: PixelConfig;
  domain_control_rule?: string;
  enable_auto_assign_to_accounts?: boolean;
  has_1p_pixel_event?: boolean;
  is_eligible_for_deletion?: boolean;
  is_restricted_use?: boolean;
  last_used_time?: string;
  matched_system_users?: MatchedSystemUser[];
  owner_pixelable_object?: PixelableObject;
  sort_by_type?: string;
}

/**
 * Interface cho Consolidated Tracking
 */
export interface ConsolidatedTracking {
  pixel_id: string;
  tracking_specs: TrackingSpec[];
}

/**
 * Interface cho Tracking Spec
 */
export interface TrackingSpec {
  action_type: string[];
  post_type?: string[];
  conversion_id?: string[];
  creative?: string[];
  dataset_id?: string[];
  event?: string[];
  event_creator?: string[];
  event_type?: string[];
  fb_pixel?: string[];
  fb_pixel_event?: string[];
  leadgen?: string[];
  object?: string[];
  object_domain?: string[];
  offer?: string[];
  offer_creator?: string[];
  offsite_pixel?: string[];
  page?: string[];
  page_parent?: string[];
  post?: string[];
  post_object?: string[];
  post_object_wall?: string[];
  post_wall?: string[];
  question?: string[];
  question_option?: string[];
  response?: string[];
  subtype?: string[];
}

/**
 * Interface cho Restricted Use
 */
export interface RestrictedUse {
  type: string;
  location: string;
}

/**
 * Interface cho Microdata
 */
export interface Microdata {
  content_ids?: string[];
  contents?: Content[];
  currency?: string;
  num_items?: number;
  order_id?: string;
  search_string?: string;
  status?: string;
  value?: number;
  content_category?: string;
  content_name?: string;
  content_type?: string;
  description?: string;
  brand?: string;
  delivery_category?: string;
  item_number?: string;
  availability?: string;
  condition?: string;
  price?: number;
  sale_price?: number;
  shipping?: number;
  shipping_weight?: number;
  expiration_date?: string;
  sale_price_effective_date?: string;
  title?: string;
  gtin?: string;
  mpn?: string;
  product_catalog_id?: string;
  product_group_id?: string;
  product_item_id?: string;
  google_product_category?: string;
  fb_product_category?: string;
  additional_image_urls?: string[];
  age_group?: string;
  color?: string;
  gender?: string;
  material?: string;
  pattern?: string;
  size?: string;
  size_type?: string;
  size_system?: string;
  item_group_id?: string;
  custom_label_0?: string;
  custom_label_1?: string;
  custom_label_2?: string;
  custom_label_3?: string;
  custom_label_4?: string;
  origin_country?: string;
  importer_name?: string;
  importer_address?: string;
  manufacturer_info?: string;
  wa_compliance_category?: string;
  target_country?: string;
  target_language?: string;
  display_ads_id?: string;
  display_ads_similar_ids?: string[];
  display_ads_title?: string;
  display_ads_link?: string;
  display_ads_value?: number;
  ios_url?: string;
  ios_app_store_id?: string;
  ios_app_name?: string;
  android_url?: string;
  android_package?: string;
  android_class?: string;
  android_app_name?: string;
  windows_phone_url?: string;
  windows_phone_app_id?: string;
  windows_phone_app_name?: string;
}

/**
 * Interface cho Content
 */
export interface Content {
  id: string;
  quantity?: number;
  item_price?: number;
  title?: string;
  description?: string;
  brand?: string;
  category?: string;
  delivery_category?: string;
}

/**
 * Interface cho Pixel Config
 */
export interface PixelConfig {
  microdata_format_version?: string;
  detailed_activity_logging?: boolean;
  diagnostic_logging?: boolean;
  automatic_matching?: boolean;
  first_party_cookie?: boolean;
  blocked_domains?: string[];
  sensitive_domains?: string[];
  cookieless_tracking?: CookielessTracking;
  restricted_domains?: string[];
  trust_domain_list?: string[];
  click_tracking?: boolean;
  microdata_extraction?: boolean;
  automatic_setup?: boolean;
  partner_integration?: PartnerIntegration[];
}

/**
 * Interface cho Cookieless Tracking
 */
export interface CookielessTracking {
  enabled: boolean;
  pixel_id?: string;
}

/**
 * Interface cho Partner Integration
 */
export interface PartnerIntegration {
  partner_name: string;
  partner_id?: string;
  enabled: boolean;
  configuration?: Record<string, any>;
}

/**
 * Interface cho Matched System User
 */
export interface MatchedSystemUser {
  id: string;
  name: string;
}

/**
 * Interface cho Pixelable Object
 */
export interface PixelableObject {
  id: string;
  type: string;
  name?: string;
}

/**
 * Interface cho Custom Conversion
 */
export interface CustomConversion {
  id: string;
  name: string;
  description?: string;
  account_id: string;
  pixel_id: string;
  pixel_rule: PixelRule;
  creation_time: string;
  last_fired_time?: string;
  is_archived: boolean;
  default_conversion_value: number;
  custom_event_type: string;
  data_sources?: string[];
  event_source_type?: string;
  first_fired_time?: string;
  is_unavailable?: boolean;
  aggregation_rule?: string;
  business?: {
    id: string;
    name: string;
  };
  advanced_rule_json?: string;
}

/**
 * Interface cho Pixel Rule
 */
export interface PixelRule {
  url?: UrlRule;
  event?: EventRule;
}

/**
 * Interface cho URL Rule
 */
export interface UrlRule {
  operator: 'i_contains' | 'i_not_contains' | 'i_equals' | 'i_not_equals' | 'i_starts_with' | 'i_not_starts_with' | 'i_ends_with' | 'i_not_ends_with' | 'i_regex_match' | 'i_regex_not_match';
  value: string;
}

/**
 * Interface cho Event Rule
 */
export interface EventRule {
  event_type: string;
  url_rule?: UrlRule;
  domain_rule?: DomainRule;
  path_rule?: PathRule;
  content_rule?: ContentRule[];
}

/**
 * Interface cho Domain Rule
 */
export interface DomainRule {
  operator: 'i_contains' | 'i_not_contains' | 'i_equals' | 'i_not_equals';
  value: string;
}

/**
 * Interface cho Path Rule
 */
export interface PathRule {
  operator: 'i_contains' | 'i_not_contains' | 'i_equals' | 'i_not_equals' | 'i_starts_with' | 'i_not_starts_with' | 'i_ends_with' | 'i_not_ends_with' | 'i_regex_match' | 'i_regex_not_match';
  value: string;
}

/**
 * Interface cho Content Rule
 */
export interface ContentRule {
  content_key: string;
  operator: 'i_contains' | 'i_not_contains' | 'i_equals' | 'i_not_equals' | 'i_starts_with' | 'i_not_starts_with' | 'i_ends_with' | 'i_not_ends_with' | 'i_regex_match' | 'i_regex_not_match' | 'i_is_any' | 'i_is_not_any' | 'i_gt' | 'i_gte' | 'i_lt' | 'i_lte';
  value: string;
}

/**
 * Interface cho Offline Event Set
 */
export interface OfflineEventSet {
  id: string;
  name: string;
  business_id: string;
  is_mta_use: boolean;
  is_restricted_use: boolean;
  is_unavailable: boolean;
  last_upload_app: string;
  last_upload_app_changed_time: number;
  match_rate_approx: number;
  usage: OfflineEventSetUsage;
  owner_business: {
    id: string;
    name: string;
  };
  config?: OfflineEventSetConfig;
  data_use_setting?: string;
  description?: string;
  duplicate_entries?: number;
  enable_auto_assign_to_accounts?: boolean;
  event_stats?: OfflineEventStats;
  event_time_max?: number;
  event_time_min?: number;
  first_party_cookie_status?: string;
  has_bapi_domains?: boolean;
  is_consolidated_container?: boolean;
  is_created_by_business?: boolean;
  last_fired_time?: string;
  last_upload_time?: string;
  matched_entries?: number;
  owner_ad_account?: {
    id: string;
    name: string;
  };
  upload_tag?: string;
  valid_entries?: number;
}

/**
 * Interface cho Offline Event Set Usage
 */
export interface OfflineEventSetUsage {
  ad_account_id: string;
  usage_type: string;
}

/**
 * Interface cho Offline Event Set Config
 */
export interface OfflineEventSetConfig {
  enable_auto_assign_to_accounts?: boolean;
  is_restricted_use?: boolean;
  breakdowns_config?: BreakdownsConfig;
}

/**
 * Interface cho Breakdowns Config
 */
export interface BreakdownsConfig {
  breakdowns?: string[];
  default_summary_breakdowns?: string[];
}

/**
 * Interface cho Offline Event Stats
 */
export interface OfflineEventStats {
  count_fired: number;
  count_uploaded: number;
  last_upload_time: string;
  last_fired_time?: string;
  usage?: OfflineEventSetUsage[];
}

/**
 * Interface cho Offline Event
 */
export interface OfflineEvent {
  event_name: string;
  event_time: number;
  user_data: UserData;
  custom_data?: CustomData;
  event_source_url?: string;
  opt_out?: boolean;
  event_id?: string;
  action_source?: 'email' | 'website' | 'phone_call' | 'chat' | 'physical_store' | 'system_generated' | 'other';
  data_processing_options?: string[];
  data_processing_options_country?: number;
  data_processing_options_state?: number;
}

/**
 * Interface cho User Data
 */
export interface UserData {
  email?: string[];
  phone?: string[];
  gender?: string[];
  date_of_birth?: string[];
  last_name?: string[];
  first_name?: string[];
  city?: string[];
  state?: string[];
  zip?: string[];
  country?: string[];
  external_id?: string[];
  client_ip_address?: string;
  client_user_agent?: string;
  fbc?: string;
  fbp?: string;
  subscription_id?: string;
  fb_login_id?: string;
  lead_id?: string;
  f5first?: string;
  f5last?: string;
  fi?: string;
  dobd?: string;
  dobm?: string;
  doby?: string;
  ln?: string;
  fn?: string;
  ct?: string;
  st?: string;
  zp?: string;
  country_code?: string;
  external_ids?: string[];
}

/**
 * Interface cho Custom Data
 */
export interface CustomData {
  value?: number;
  currency?: string;
  content_name?: string;
  content_category?: string;
  content_ids?: string[];
  contents?: Content[];
  content_type?: string;
  order_id?: string;
  predicted_ltv?: number;
  num_items?: number;
  search_string?: string;
  status?: string;
  item_number?: string;
  delivery_category?: string;
  custom_properties?: Record<string, any>;
}

/**
 * Interface cho yêu cầu tạo Facebook Pixel
 */
export interface CreatePixelRequest {
  name: string;
}

/**
 * Interface cho yêu cầu tạo Custom Conversion
 */
export interface CreateCustomConversionRequest {
  name: string;
  description?: string;
  pixel_id: string;
  pixel_rule: PixelRule;
  default_conversion_value?: number;
  custom_event_type: string;
  advanced_rule_json?: string;
}

/**
 * Interface cho yêu cầu tạo Offline Event Set
 */
export interface CreateOfflineEventSetRequest {
  name: string;
  description?: string;
  config?: OfflineEventSetConfig;
}

/**
 * Interface cho yêu cầu upload Offline Events
 */
export interface UploadOfflineEventsRequest {
  data: OfflineEvent[];
  test_event_code?: string;
  partner_agent?: string;
  namespace_id?: string;
  upload_id?: string;
  upload_tag?: string;
  upload_source?: string;
}

/**
 * Interface cho phản hồi upload Offline Events
 */
export interface UploadOfflineEventsResponse {
  events_received: number;
  messages: string[];
  fbtrace_id: string;
  num_processed_entries?: number;
  num_invalid_entries?: number;
  invalid_entries?: InvalidEntry[];
}

/**
 * Interface cho Invalid Entry
 */
export interface InvalidEntry {
  line_number: number;
  error_message: string;
  error_code?: number;
}

/**
 * Interface cho phản hồi danh sách Pixels
 */
export interface GetPixelsResponse {
  data: FacebookPixel[];
  paging?: {
    cursors?: {
      before?: string;
      after?: string;
    };
    next?: string;
    previous?: string;
  };
}

/**
 * Interface cho phản hồi danh sách Custom Conversions
 */
export interface GetCustomConversionsResponse {
  data: CustomConversion[];
  paging?: {
    cursors?: {
      before?: string;
      after?: string;
    };
    next?: string;
    previous?: string;
  };
}

/**
 * Interface cho phản hồi danh sách Offline Event Sets
 */
export interface GetOfflineEventSetsResponse {
  data: OfflineEventSet[];
  paging?: {
    cursors?: {
      before?: string;
      after?: string;
    };
    next?: string;
    previous?: string;
  };
}

/**
 * Interface cho Conversion Data
 */
export interface ConversionData {
  pixel_id: string;
  event_name: string;
  event_time: number;
  event_source_url?: string;
  user_data: UserData;
  custom_data?: CustomData;
  event_id?: string;
  action_source?: string;
  opt_out?: boolean;
  data_processing_options?: string[];
  data_processing_options_country?: number;
  data_processing_options_state?: number;
}

/**
 * Interface cho phản hồi Conversion Data
 */
export interface GetConversionsResponse {
  data: ConversionData[];
  paging?: {
    cursors?: {
      before?: string;
      after?: string;
    };
    next?: string;
    previous?: string;
  };
}
