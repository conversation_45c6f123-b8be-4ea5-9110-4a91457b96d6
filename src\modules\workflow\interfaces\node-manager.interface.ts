/**
 * @file Workflow Module - Node Manager Interfaces
 *
 * File này định nghĩa tất cả các interface liên quan đến node management:
 * - Node types và property types
 * - Node definitions và properties
 * - Credential definitions
 * - Dynamic UI generation (loadOptions mechanism)
 * - API requests/responses cho node management
 *
 */

import { AllCoreNodeParameters } from "./core";

// =================================================================
// SECTION 1: NODE & PROPERTY ENUMS
// =================================================================

/**
 * Enum định nghĩa tất cả các loại giao diện (type) có thể có cho một thuộc tính
 * trong mảng `properties` của một node.
 */
export enum EPropertyType {
    String = 'string',
    Password = 'password',
    CodeEditor = 'codeEditor',
    Json = 'json',
    Number = 'number',
    Boolean = 'boolean',
    Slider = 'slider',
    Options = 'options',
    Collection = 'collection',
    Array = 'array',
}

/**
 * Enum định nghĩa các credential names - bộ định tuyến cho credentials
 * Khớp với integration provider types trong database
 */
export enum ECredentialName {
    // === GOOGLE SERVICES (OAuth2) ===
    /** Google OAuth2 - Sheets, Drive, Gmail */
    GOOGLE_OAUTH = 'googleOAuth',

    // === SOCIAL MEDIA (OAuth2) ===
    /** Facebook OAuth2 - Pages, Ads, Leads */
    FACEBOOK_OAUTH = 'facebookOAuth',

    /** Zalo OA OAuth2 */
    ZALO_OA_OAUTH = 'zaloOaOAuth',
}

/**
 * Enum định nghĩa các loại authentication cho node credentials
 * Khớp với hệ thống integration hiện tại
 */
export enum ENodeAuthType {
    /** API Key authentication - lưu trữ key đơn giản */
    API_KEY = 'apiKey',

    /** OAuth2 authentication - lưu trữ access_token + refresh_token */
    OAUTH2 = 'oAuth2',

    /** Basic authentication - username + password */
    BASIC = 'basic',

    /** Bearer token authentication */
    BEARER = 'bearer',

    /** Custom authentication - tùy chỉnh theo provider */
    CUSTOM = 'custom',
}

/**
 * Enum định nghĩa các resources cho load options API
 * Giúp type-safe và dễ dàng routing trong backend
 */
export enum ELoadOptionsResource {
    // === AI & MODELS ===
    AI_MODELS = 'ai:models',
    AI_PROVIDERS = 'ai:providers',

    // === CREDENTIALS & INTEGRATIONS ===
    CREDENTIALS = 'credentials',
    INTEGRATIONS = 'integrations',

    // === GOOGLE SERVICES ===
    GOOGLE_SHEETS = 'google:sheets',
    GOOGLE_DRIVE = 'google:drive',
    GOOGLE_WORKSHEETS = 'google:worksheets',
    GOOGLE_SHEET_COLUMNS = 'google:sheet-columns',

    // === DATABASE ===
    DATABASE_TABLES = 'database:tables',
    DATABASE_COLUMNS = 'database:columns',

    // === WEBHOOKS ===
    WEBHOOK_ENDPOINTS = 'webhook:endpoints',

    // === WORKFLOW ===
    WORKFLOW_VARIABLES = 'workflow:variables',
    WORKFLOW_NODES = 'workflow:nodes',
}

/**
 * Enum định nghĩa các methods cho load options API
 * Giúp type-safe và dễ dàng switch case trong backend
 */
export enum ELoadOptionsMethod {
    // === GENERAL METHODS ===
    /** Lấy tất cả items */
    GET_ALL = 'getAll',
    /** Lấy items với parameters cụ thể */
    GET_WITH_PARAMS = 'getWithParams',
    /** Lấy items theo type/category */
    GET_BY_TYPE = 'getByType',
    /** Lấy items đã kết nối/active */
    GET_CONNECTED = 'getConnected',

    // === AI SPECIFIC ===
    /** Lấy models có sẵn */
    LIST_AVAILABLE = 'listAvailable',
    /** Lấy models với parameters cụ thể */
    LIST_AVAILABLE_WITH_PARAMS = 'listAvailableWithParams',
    /** Lấy models theo provider */
    LIST_BY_PROVIDER = 'listByProvider',

    // === GOOGLE SPECIFIC ===
    /** Lấy danh sách Google Sheets */
    GET_SHEETS = 'getSheets',
    /** Lấy danh sách Google Drive files */
    GET_DRIVE_FILES = 'getDriveFiles',
    /** Lấy worksheets từ một sheet */
    GET_WORKSHEETS = 'getWorksheets',
    /** Lấy columns từ worksheet */
    GET_SHEET_COLUMNS = 'getSheetColumns',

    // === DATABASE SPECIFIC ===
    /** Lấy danh sách tables */
    GET_TABLES = 'getTables',
    /** Lấy columns từ table */
    GET_TABLE_COLUMNS = 'getTableColumns',

    // === UTILITY ===
    /** Refresh và lấy lại data */
    REFRESH_AND_GET = 'refreshAndGet',
    /** Validate connection */
    VALIDATE_CONNECTION = 'validateConnection',
}

/**
 * Enum định nghĩa các loại node cụ thể trong hệ thống
 * Giúp type-safe và tránh lỗi typo khi tham chiếu node types
 */
export enum ENodeType {
    // HTTP Nodes
    /** HTTP Request node */
    HTTP_REQUEST = 'http-request',

    /** Webhook node */
    WEBHOOK = 'webhook',

    // Logic Nodes
    /** If Condition node */
    IF_CONDITION = 'if-condition',

    /** Switch node */
    SWITCH = 'switch',

    /** Loop node */
    LOOP = 'loop',

    /** Edit Fields node */
    EDIT_FIELDS = 'edit-fields',

    /** Merge node */
    MERGE = 'merge',

    /** Filter node */
    FILTER = 'filter',

    /** Wait node */
    WAIT = 'wait',
}

/**
 * Enum định nghĩa các nhóm/danh mục của node để phân loại trong palette
 * Giúp dễ dàng truy vấn và tổ chức các node theo chức năng
 */
export enum ENodeGroup {
    /** Nhóm AI - Các node liên quan đến trí tuệ nhân tạo */
    AI = 'AI',

    /** Nhóm HTTP - Các node thực hiện HTTP requests */
    HTTP = 'HTTP',

    /** Nhóm Database - Các node tương tác với cơ sở dữ liệu */
    Database = 'Database',

    /** Nhóm File - Các node xử lý file và storage */
    File = 'File',

    /** Nhóm Email - Các node gửi/nhận email */
    Email = 'Email',

    /** Nhóm Notification - Các node gửi thông báo (Slack, Discord, SMS...) */
    Notification = 'Notification',

    /** Nhóm Transform - Các node biến đổi dữ liệu */
    Transform = 'Transform',

    /** Nhóm Logic - Các node xử lý logic (if/else, switch, loop...) */
    Logic = 'Logic',

    /** Nhóm Integration - Các node tích hợp với third-party services */
    Integration = 'Integration',

    /** Nhóm Utility - Các node tiện ích khác */
    Utility = 'Utility',
}

// =================================================================
// SECTION 2: CREDENTIAL INTERFACES
// =================================================================

/**
 * Interface cho định nghĩa một loại credential mà node yêu cầu.
 * Mô tả cách thức xác thực cần thiết để node có thể hoạt động.
 *
 * ✅ Cập nhật để khớp với hệ thống integration hiện tại:
 * - Sử dụng enum thay vì string literals
 * - Hỗ trợ nhiều loại auth hơn
 * - Tích hợp với integration providers
 */
export interface ICredentialDefinition {
    /**
     * Tên định danh của credential - BỘ ĐỊNH TUYẾN quan trọng
     * Sử dụng enum để type-safe và tránh typo
     * Khớp với integration provider type trong database
     */
    name: ECredentialName;

    /** Tên hiển thị cho người dùng */
    displayName: string;

    /** Mô tả về credential này và cách sử dụng */
    description?: string;

    /** Có bắt buộc phải có credential này không */
    required?: boolean;

    /**
     * Loại xác thực - sử dụng enum để type-safe
     * Khớp với các loại integration providers trong hệ thống
     */
    authType: ENodeAuthType;

    /**
     * Provider type trong hệ thống integration
     * Ví dụ: 'openai', 'google', 'facebook', 'zalo'
     * Dùng để filter integrations theo provider
     */
    providerType?: string;

    /**
     * URL để thực hiện OAuth (chỉ dùng cho OAuth2)
     * Có thể là relative path: '/api/auth/google/connect'
     */
    authUrl?: string;

    /**
     * Danh sách quyền cần thiết (chỉ dùng cho OAuth2)
     * Ví dụ: ['https://www.googleapis.com/auth/spreadsheets.readonly']
     */
    scopes?: string[];

    /**
     * Metadata bổ sung cho credential
     * Ví dụ: { "apiVersion": "v1", "baseUrl": "https://api.openai.com" }
     */
    metadata?: Record<string, any>;

    /**
     * Có hỗ trợ test connection không
     * Nếu true, sẽ có button "Test Connection" trong UI
     */
    testable?: boolean;

    /**
     * URL endpoint để test connection
     * Ví dụ: '/api/integrations/test-connection'
     */
    testUrl?: string;
}

// =================================================================
// SECTION 3: PROPERTY INTERFACES
// =================================================================

/**
 * Interface cơ sở cho tất cả các loại thuộc tính trong node
 * Chứa các trường chung mà mọi thuộc tính đều có
 */
interface IBaseProperty {
    /** Tên thuộc tính - dùng làm key trong parameters object */
    name: string;

    /** Tên hiển thị cho người dùng trong giao diện */
    displayName: string;

    /** Mô tả chi tiết về thuộc tính này - hiển thị dưới dạng tooltip */
    description?: string;

    /** Có bắt buộc nhập không - nếu true, form sẽ validate */
    required?: boolean;
}

// --- Các loại thuộc tính cụ thể ---

/**
 * Interface cho các thuộc tính dạng chuỗi văn bản
 * Bao gồm: text thường, password, code editor, và JSON
 */
interface IStringProperty extends IBaseProperty {
    type: EPropertyType.String | EPropertyType.Password | EPropertyType.CodeEditor | EPropertyType.Json;
}

/**
 * Interface cho thuộc tính số
 * Hiển thị dưới dạng input number với validation
 */
interface INumberProperty extends IBaseProperty {
    type: EPropertyType.Number;

    /** Giá trị tối thiểu cho phép */
    minValue?: number;

    /** Giá trị tối đa cho phép */
    maxValue?: number;

    /** Bước nhảy giữa các giá trị */
    step?: number;
}

/**
 * Interface cho thuộc tính boolean
 * Hiển thị dưới dạng checkbox hoặc toggle switch
 */
interface IBooleanProperty extends IBaseProperty {
    type: EPropertyType.Boolean;
}

/**
 * Interface cho thuộc tính slider - thanh trượt để chọn giá trị số
 * Hỗ trợ cả giá trị cố định và tải động từ API
 */
interface ISliderProperty extends IBaseProperty {
    type: EPropertyType.Slider;

    /** Giá trị tối thiểu của slider (nếu không dùng loadRange) */
    minValue?: number;

    /** Giá trị tối đa của slider (nếu không dùng loadRange) */
    maxValue?: number;

    /** Bước nhảy giữa các giá trị (ví dụ: 0.1, 1, 10) */
    step?: number;

    /**
     * Cấu hình tải khoảng giá trị động từ API
     * Hữu ích khi min/max phụ thuộc vào cấu hình khác
     * Ví dụ: slider temperature phụ thuộc vào model AI được chọn
     */
    loadRange?: {
        /** Tên tài nguyên để lấy khoảng giá trị */
        resource: string;

        /** Phương thức lấy range */
        method: string;

        /** Các trường phụ thuộc để xác định range */
        dependsOn?: string[];
    };
}

/**
 * Interface hợp nhất cho thuộc tính lựa chọn (options).
 * Có thể xử lý cả lựa chọn tĩnh (static) và động (dynamic).
 */
interface IOptionsProperty extends IBaseProperty {
    type: EPropertyType.Options;

    /** Danh sách lựa chọn tĩnh - được định nghĩa trước */
    options?: {
        name: string;           // Tên hiển thị cho người dùng
        value: any;            // Giá trị thực tế được lưu
        parameters?: Record<string, any>; // Thông số bổ sung cho option này
    }[];

    /**
     * Cấu hình tải dữ liệu động từ API trung tâm /api/v1/load-options
     * Cơ chế này cho phép dropdown tải dữ liệu real-time và phụ thuộc vào các trường khác
     */
    loadOptions?: {
        /** Tài nguyên cần tải - sử dụng enum để type-safe */
        resource: ELoadOptionsResource;

        /** Phương thức/hành động trên tài nguyên - sử dụng enum để type-safe */
        method: ELoadOptionsMethod;

        /**
         * Danh sách tên các thuộc tính khác mà trường này phụ thuộc vào
         * FE sẽ gửi giá trị của các trường này trong request dependencies
         * Ví dụ: ["provider", "integration_id"] - model phụ thuộc vào provider và integration
         */
        dependsOn?: string[];

        /** Có hỗ trợ phân trang không - nếu true, response sẽ có nextPageCursor */
        pagination?: boolean;
    };

    /** Cho phép chọn nhiều giá trị (multi-select dropdown) */
    isMultiSelect?: boolean;
}

/**
 * Interface cho thuộc tính collection - nhóm các thuộc tính con
 * Tạo ra một object chứa nhiều trường con bên trong
 */
interface ICollectionProperty extends IBaseProperty {
    type: EPropertyType.Collection;

    /** Danh sách các thuộc tính con trong collection này */
    properties: INodeProperty[];
}

/**
 * Interface cho thuộc tính array - mảng các object
 * Cho phép người dùng thêm/xóa nhiều item, mỗi item có cấu trúc giống nhau
 */
interface IArrayProperty extends IBaseProperty {
    type: EPropertyType.Array;

    /** Cấu trúc của mỗi item trong mảng */
    properties: INodeProperty[];
}

// --- Kiểu Union cuối cùng ---

/**
 * Union type tổng hợp tất cả các loại thuộc tính có thể có trong một node
 * Đây là kiểu dữ liệu chính được sử dụng trong mảng properties của INodeDefinitionEntity
 *
 * TypeScript sẽ tự động phân biệt loại thuộc tính dựa trên trường 'type'
 * và cung cấp type safety + intellisense phù hợp cho từng loại
 */
export type INodeProperty =
    | IStringProperty      // Các loại input text, password, code editor, JSON
    | INumberProperty      // Input số với validation
    | IBooleanProperty     // Checkbox/toggle
    | ISliderProperty      // Thanh trượt chọn giá trị số
    | IOptionsProperty     // Dropdown/select với options tĩnh hoặc động
    | ICollectionProperty  // Nhóm các thuộc tính con (object)
    | IArrayProperty;      // Mảng các object có cấu trúc giống nhau

// =================================================================
// SECTION 5: LOAD OPTIONS API INTERFACES
// =================================================================

/**
 * Request payload cho API tải options động: POST /api/v1/load-options
 * API này hoạt động như một bộ định tuyến trung tâm, chuyển tiếp request
 * đến các service cụ thể dựa trên resource và method
 */
export interface ILoadOptionsRequest {
    /** Tài nguyên cần tải - sử dụng enum để type-safe */
    resource: ELoadOptionsResource;

    /** Phương thức/hành động trên tài nguyên - sử dụng enum để type-safe */
    method: ELoadOptionsMethod;

    /**
     * Dữ liệu phụ thuộc từ các trường khác trong form
     * Ví dụ: { "provider": "openai", "integration_id": "cred-123" }
     * BE sẽ sử dụng để xác định context và thực hiện logic phù hợp
     *
     * ⭐ QUAN TRỌNG: integration_id được sử dụng cho OAuth2 authentication
     */
    dependencies?: Record<string, any>;

    /** Cursor cho phân trang - lấy trang tiếp theo */
    nextPageCursor?: string | number;
}

// =================================================================
// SECTION 6: NODE PARAMETERS & UNION TYPES
// =================================================================

/**
 * Union type cho parameters của node - Dữ liệu cấu hình cụ thể của một node
 *
 * Đây là union type chứa tất cả các loại parameters cụ thể của từng node type.
 * Thay vì generic object, giờ đây type-safe với từng loại node.
 *
 * Ví dụ: IHttpRequestParameters | IIfConditionParameters | ...
 */
export type INodeParameters = AllCoreNodeParameters | Record<string, any>;