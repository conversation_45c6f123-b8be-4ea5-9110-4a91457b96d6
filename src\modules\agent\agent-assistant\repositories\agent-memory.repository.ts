import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AgentMemory } from '../entities/agent-memory.entity';

/**
 * Repository for AgentMemory entity
 * Handles CRUD operations for agent memories
 */
@Injectable()
export class AgentMemoryRepository {
  constructor(
    @InjectRepository(AgentMemory)
    private readonly repository: Repository<AgentMemory>,
  ) {}

  /**
   * Find all memories for a specific agent
   * @param agentId Agent ID to find memories for
   * @returns Array of agent memories ordered by creation date (newest first)
   */
  async findByAgentId(agentId: string): Promise<AgentMemory[]> {
    return this.repository.find({
      where: { agentId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Create a new agent memory
   * @param memoryData Memory data to create
   * @returns Created agent memory
   */
  async create(memoryData: Partial<AgentMemory>): Promise<AgentMemory> {
    const memory = this.repository.create(memoryData);
    return this.repository.save(memory);
  }

  /**
   * Find a memory by ID
   * @param id Memory ID
   * @returns Memory record or null if not found
   */
  async findById(id: string): Promise<AgentMemory | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Update an existing agent memory
   * @param id Memory ID to update
   * @param updateData Data to update
   * @returns Updated memory record or null if not found
   */
  async update(
    id: string,
    updateData: Partial<AgentMemory>,
  ): Promise<AgentMemory | null> {
    const result = await this.repository.update(id, updateData);

    if (result.affected === 0) {
      return null;
    }

    return this.findById(id);
  }

  /**
   * Delete an agent memory
   * @param id Memory ID to delete
   * @returns Success status
   */
  async delete(id: string): Promise<boolean> {
    const result = await this.repository.delete(id);
    return (
      result.affected !== undefined &&
      result.affected !== null &&
      result.affected > 0
    );
  }

  /**
   * Find memories by agent ID with pagination
   * @param agentId Agent ID
   * @param limit Maximum number of records to return
   * @param offset Number of records to skip
   * @returns Array of agent memories with pagination
   */
  async findByAgentIdPaginated(
    agentId: string,
    limit: number = 10,
    offset: number = 0,
  ): Promise<AgentMemory[]> {
    return this.repository.find({
      where: { agentId },
      order: { createdAt: 'DESC' },
      take: limit,
      skip: offset,
    });
  }

  /**
   * Count total memories for an agent
   * @param agentId Agent ID
   * @returns Total count of memories for the agent
   */
  async countByAgentId(agentId: string): Promise<number> {
    return this.repository.count({
      where: { agentId },
    });
  }

  /**
   * Find recent memories across all agents
   * @param limit Maximum number of records to return
   * @returns Array of recent agent memories
   */
  async findRecent(limit: number = 50): Promise<AgentMemory[]> {
    return this.repository.find({
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  /**
   * Delete all memories for a specific agent
   * @param agentId Agent ID
   * @returns Number of deleted records
   */
  async deleteByAgentId(agentId: string): Promise<number> {
    const result = await this.repository.delete({ agentId });
    return result.affected || 0;
  }

  /**
   * Bulk create multiple agent memories
   * @param memoriesData Array of memory data to create
   * @returns Array of created agent memories
   */
  async bulkCreate(
    memoriesData: Partial<AgentMemory>[],
  ): Promise<AgentMemory[]> {
    const memories = this.repository.create(memoriesData);
    return this.repository.save(memories);
  }
}
