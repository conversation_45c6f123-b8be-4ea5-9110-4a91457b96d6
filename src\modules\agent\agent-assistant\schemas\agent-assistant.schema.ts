import { z } from 'zod';
import {
  InputModalityEnum,
  OutputModalityEnum,
  SamplingParameterEnum,
  ModelFeatureEnum,
  ProviderEnum,
  PaymentMethodEnum,
  ProviderShipmentTypeEnum,
  GenderEnum,
  PlatformContextEnum,
  TrimmingTypeEnum,
  ModelTypeEnum,
} from '../enums';

/**
 * Zod schema for customer memory data structure
 */
export const CustomerMemorySchema = z.object({
  id: z.string().uuid(),
  title: z.string(),
  reason: z.string(),
  content: z.string(),
  createdAt: z.number(),
});

/**
 * Zod schema for agent memory data structure
 */
export const AgentMemorySchema = z.object({
  id: z.string().uuid(),
  title: z.string(),
  reason: z.string(),
  content: z.string(),
  createdAt: z.number(),
});

/**
 * Zod schema for customer profile data from user_convert_customers
 */
export const CustomerProfileSchema = z.object({
  name: z.string().optional(),
  avatar: z.string().optional(),
  phone: z.string().optional(),
  email: z.record(z.string(), z.string()).optional(),
  address: z.string().optional(),
  tags: z.array(z.string()).default([]),
  metadata: z.array(z.any()).default([]),
  facebookLink: z.string().optional(),
  linkedinLink: z.string().optional(),
  twitterLink: z.string().optional(),
  zaloLink: z.string().optional(),
  websiteLink: z.string().optional(),
});

/**
 * Zod schema for platform-specific customer data from zalo_customers
 */
export const CustomerPlatformDataSchema = z.object({
  zaloUserId: z.string(),
  userIdByApp: z.string().optional(),
  userExternalId: z.string().optional(),
  displayName: z.string().optional(),
  userAlias: z.string().optional(),
  isSensitive: z.boolean().default(false),
  userIsFollower: z.boolean().default(false),
  userLastInteractionDate: z.string().optional(),
  avatar: z.string().optional(),
  tagsAndNotesInfo: z.record(z.string(), z.any()).optional(),
  sharedInfo: z.record(z.string(), z.any()).optional(),
  dynamicParam: z.string().optional(),
  firstInteractionAt: z.number(),
  lastInteractionAt: z.number(),
  interactionCount: z.number().int().default(1),
  createdAt: z.number(),
  updatedAt: z.number(),
});

/**
 * Zod schema for Zalo Official Account data
 * Matches the actual ZaloOfficialAccount entity structure
 */
export const ZaloOfficialAccountSchema = z.object({
  id: z.number().int().positive(),
  userId: z.number().int().positive(),
  oaId: z.string().min(1),
  name: z.string(),
  description: z.string().optional(),
  avatarUrl: z.string().optional(),
  accessToken: z.string().min(1),
  refreshToken: z.string().optional(),
  expiresAt: z.number(),
  status: z.string().default('active'),
  createdAt: z.number(),
  updatedAt: z.number(),
  agentId: z.string().uuid().optional(),
});

/**
 * Clean Zalo Official Account schema without redundant fields
 * Used in agent configuration to avoid redundant userId and agentId
 */
export const CleanZaloOfficialAccountSchema = z.object({
  id: z.number().int().positive(),
  oaId: z.string().min(1),
  name: z.string(),
  description: z.string().optional(),
  avatarUrl: z.string().optional(),
  accessToken: z.string().min(1),
  refreshToken: z.string().optional(),
  expiresAt: z.number(),
  status: z.string().default('active'),
  createdAt: z.number(),
  updatedAt: z.number(),
});

/**
 * Zod schema for comprehensive customer context data
 * Groups all customer-related information into a single object
 */
export const ConvertCustomerContextSchema = z.object({
  id: z.string().uuid().optional(),
  customerProfile: CustomerProfileSchema.optional(),
  customerPlatformData: CustomerPlatformDataSchema.optional(),
  customerMemories: z.array(CustomerMemorySchema).optional(),
});

/**
 * Zod schema for gender enum
 */
export const GenderEnumSchema = z.nativeEnum(GenderEnum);

/**
 * Zod schema for payment method enum
 */
export const PaymentMethodEnumSchema = z.nativeEnum(PaymentMethodEnum);

/**
 * Zod schema for provider shipment type enum
 */
export const ProviderShipmentTypeEnumSchema = z.nativeEnum(ProviderShipmentTypeEnum);

/**
 * Zod schema for profile agent data
 */
export const ProfileAgentSchema = z.object({
  gender: GenderEnumSchema.optional(),
  dateOfBirth: z.union([z.string(), z.date()]).optional(),
  position: z.string().optional(),
  education: z.string().optional(),
  skills: z.array(z.string()).optional(),
  personality: z.array(z.string()).optional(),
  languages: z.array(z.string()).optional(),
  nations: z.string().optional(),
});

/**
 * Zod schema for trimming types
 */
export const TrimmingTypeSchema = z.nativeEnum(TrimmingTypeEnum);

/**
 * Zod schema for model parameters
 */
export const ModelParametersSchema = z
  .object({
    temperature: z.number().min(0).max(2).optional(),
    topP: z.number().min(0).max(1).optional(),
    topK: z.number().int().positive().optional(),
    maxTokens: z.number().int().positive().optional(),
    maxOutputTokens: z.number().int().positive().optional(),
  })
  .optional();

/**
 * Zod schema for model pricing
 */
export const ModelPricingSchema = z.object({
  inputRate: z.number().nonnegative('Input rate must be non-negative'),
  outputRate: z.number().nonnegative('Output rate must be non-negative'),
});

/**
 * Zod schema for assistant model configuration
 */
export const AssistantModelConfigSchema = z.object({
  name: z.string().min(1, 'Model name is required'),
  provider: z.nativeEnum(ProviderEnum),
  inputModalities: z.array(z.nativeEnum(InputModalityEnum)),
  outputModalities: z.array(z.nativeEnum(OutputModalityEnum)),
  samplingParameters: z.array(z.nativeEnum(SamplingParameterEnum)),
  features: z.array(z.nativeEnum(ModelFeatureEnum)),
  parameters: ModelParametersSchema,
  pricing: ModelPricingSchema,
  type: z.nativeEnum(ModelTypeEnum),
  apiKeys: z
    .array(z.string().min(1, 'API key cannot be empty'))
    .min(1, 'At least one API key is required'),
});

/**
 * Zod schema for trimming configuration
 */
export const TrimmingConfigSchema = z.object({
  type: TrimmingTypeSchema,
  threshold: z.number().int().positive('Threshold must be a positive integer'),
});

/**
 * Zod schema for agent assistant configuration
 */
export const AgentAssistantConfigSchema = z.object({
  id: z.string().min(1, 'Agent ID is required'),
  name: z.string().min(1, 'Agent name is required'),
  description: z.string().optional().default('Default Description'),
  instruction: z.string().optional().default('You are a helpful AI assistant.'),
  mcpConfig: z.any(),
  vectorStoreId: z.string().nullable().optional(),
  isSupervisor: z.boolean(),
  profile: ProfileAgentSchema.nullable().optional(),
  trimmingConfig: TrimmingConfigSchema,
  model: AssistantModelConfigSchema,

  // Agent assistant specific fields for customer-facing agents
  paymentGatewayId: z.number().int().positive().nullable().optional(),
  userProviderShipmentId: z.string().uuid().nullable().optional(),
  receiverPayShippingFee: z.boolean().default(true),
  paymentMethods: z.array(PaymentMethodEnumSchema).default([]),

  // Agent memories for enhanced context
  agentMemories: z.array(AgentMemorySchema).default([]).optional(),
});

/**
 * Zod schema for strategy content step
 */
export const StrategyContentStepSchema = z.object({
  stepOrder: z.number().int().positive('Step order must be a positive integer'),
  content: z.string().min(1, 'Content cannot be empty'),
});


/**
 * Zod schema for strategist agent configuration
 * Simplified version with only essential fields
 */
export const AgentStrategistConfigSchema = z.object({
  // Core identification
  id: z.string().min(1, 'Strategy ID is required'),
  name: z.string().min(1, 'Strategy name is required'),
  description: z.string().min(1, 'Strategy description is required'),

  // Strategy-specific instruction
  instruction: z.string().min(1, 'Strategy instruction is required'),

  // Strategy content steps
  content: z.array(StrategyContentStepSchema).default([]),

  // Single example field (user examples take precedence over default)
  example: z.array(StrategyContentStepSchema).default([]),

  // Model configuration (system models only)
  model: AssistantModelConfigSchema,

  // Trimming configuration
  trimming: TrimmingConfigSchema,

  // Vector store ID (nullable)
  vectorStoreId: z.string().uuid().nullable(),
});

/**
 * Zod schema for agent assistant configuration map
 */
export const AgentAssistantConfigMapSchema = z.record(
  z.string().min(1, 'Agent ID key cannot be empty'),
  AgentAssistantConfigSchema,
);

/**
 * Zod schema for agent assistant custom configurable type
 */
export const AgentAssistantCustomConfigurableTypeSchema = z.object({
  thread_id: z.string().optional(),
  checkpoint_id: z.string().optional(),
  userId: z.number().int().positive().optional(),
  mainAgent: AgentAssistantConfigSchema,
  strategistAgent: AgentStrategistConfigSchema.nullable().optional(),
  attachmentImageMap: z.record(z.string(), z.string()).optional(),
  contextualTools: z.array(z.any()).optional(),

  // Context data fields for rich SystemMessage generation
  replyToContext: z
    .object({
      messageId: z.string().min(1, 'Message ID is required'),
      originalMessageData: z.any(),
    })
    .optional(),
  attachmentContext: z.array(z.any()).optional(),

  // Agent assistant specific context (clean version without redundant fields)
  zaloOfficialAccount: CleanZaloOfficialAccountSchema.optional(),
  platformContext: z.nativeEnum(PlatformContextEnum).optional(),

  // Enhanced customer context data grouped together
  convertCustomerContext: ConvertCustomerContextSchema.optional(),
});

// Export inferred types for TypeScript
// Note: GenderEnum is imported from ../enums, no need to infer

// Memory and customer context types
export type CustomerMemory = z.infer<typeof CustomerMemorySchema>;
export type AgentMemory = z.infer<typeof AgentMemorySchema>;
export type CustomerProfile = z.infer<typeof CustomerProfileSchema>;
export type CustomerPlatformData = z.infer<typeof CustomerPlatformDataSchema>;
export type ZaloOfficialAccount = z.infer<typeof ZaloOfficialAccountSchema>;
export type ConvertCustomerContext = z.infer<typeof ConvertCustomerContextSchema>;

// Existing types
export type ProfileAgent = z.infer<typeof ProfileAgentSchema>;
export type AssistantModelConfig = z.infer<typeof AssistantModelConfigSchema>;
export type AgentAssistantConfig = z.infer<typeof AgentAssistantConfigSchema>;
export type AgentAssistantConfigMap = z.infer<typeof AgentAssistantConfigMapSchema>;
export type AgentAssistantCustomConfigurableType = z.infer<typeof AgentAssistantCustomConfigurableTypeSchema>;
