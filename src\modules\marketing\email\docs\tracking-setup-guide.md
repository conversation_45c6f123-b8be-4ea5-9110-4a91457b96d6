# Email Tracking Setup Guide

## 🚀 Quick Start Setup

### 📋 Prerequisites Checklist

#### ✅ Infrastructure Requirements
- [ ] Redis server running (for caching tracking events)
- [ ] PostgreSQL/MySQL database (for persistent storage)
- [ ] Node.js application with NestJS
- [ ] Email provider configured (SMTP/SendGrid/Mailgun)

#### ✅ Environment Variables
```bash
# Required for basic tracking
REDIS_HOST=localhost
REDIS_PORT=6379
DATABASE_URL=postgresql://user:pass@localhost:5432/db

# Required for advanced tracking
BASE_URL=https://your-domain.com
EMAIL_PROVIDER=smtp # or sendgrid, mailgun, ses

# Optional for webhooks
SENDGRID_WEBHOOK_SECRET=your_secret
MAILGUN_WEBHOOK_SECRET=your_secret
```

## 🔧 Step-by-Step Implementation

### 📊 Phase 1: Basic Tracking (30 minutes)

#### Step 1: Enable Basic Tracking
```typescript
// src/modules/marketing/email/email-marketing.processor.ts

async processEmailJob(job: Job<EmailMarketingJobDto>) {
  const { campaignId, audienceId, email, content } = job.data;
  
  // Generate tracking ID
  const trackingId = this.emailTrackingService.generateTrackingId(
    campaignId, 
    audienceId
  );

  try {
    // Send email
    await this.sendEmail(email, content);
    
    // ✅ Track successful send
    await this.emailTrackingService.trackEmailSent(
      campaignId,
      audienceId, 
      email,
      trackingId
    );
    
  } catch (error) {
    // ✅ Track failed send
    await this.emailTrackingService.trackEmailFailed(
      campaignId,
      audienceId,
      email, 
      trackingId,
      error
    );
    throw error;
  }
}
```

#### Step 2: Verify Basic Tracking
```bash
# Check Redis for tracking events
redis-cli
> KEYS "email_tracking:*"
> GET "email_tracking:1640995200000:0.123456"

# Check database for processed events
psql -d your_db -c "SELECT * FROM user_campaign_history WHERE campaign_id = 1 ORDER BY created_at DESC LIMIT 10;"
```

### 📈 Phase 2: Engagement Tracking (1 hour)

#### Step 1: Enable Pixel Tracking
```typescript
// src/modules/marketing/email/email-marketing.processor.ts

async processEmailContent(content: string, trackingId: string): Promise<string> {
  // ✅ Inject tracking pixel
  const contentWithPixel = this.emailTemplateService.injectTrackingPixel(
    content,
    trackingId,
    process.env.BASE_URL
  );
  
  return contentWithPixel;
}
```

#### Step 2: Enable Click Tracking
```typescript
async processEmailContent(content: string, trackingId: string): Promise<string> {
  // Process variables first
  let processedContent = this.emailTemplateService.injectVariables(content, variables);
  
  // ✅ Inject link tracking
  processedContent = this.emailTemplateService.injectLinkTracking(
    processedContent,
    trackingId,
    process.env.BASE_URL
  );
  
  // ✅ Inject pixel tracking
  processedContent = this.emailTemplateService.injectTrackingPixel(
    processedContent,
    trackingId,
    process.env.BASE_URL
  );
  
  return processedContent;
}
```

#### Step 3: Test Engagement Tracking
```bash
# Test pixel tracking
curl http://localhost:3000/api/email-tracking/pixel/1_100_1640995200000_abc123

# Test click tracking
curl "http://localhost:3000/api/email-tracking/click/1_100_1640995200000_abc123?url=https%3A//example.com"

# Check tracking events
redis-cli
> KEYS "email_tracking:*"
```

### 🚀 Phase 3: Advanced Features (2-3 hours)

#### Step 1: Setup Webhook Endpoints
```typescript
// Add to your main app module or create separate webhook module

@Module({
  imports: [EmailMarketingModule],
  controllers: [EmailWebhookController],
})
export class WebhookModule {}
```

#### Step 2: Configure Email Provider Webhooks

##### SendGrid Setup
```bash
# Configure SendGrid webhook URL
curl -X POST "https://api.sendgrid.com/v3/user/webhooks/event/settings" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "url": "https://your-domain.com/api/email-webhook/sendgrid",
    "group_resubscribe": true,
    "delivered": true,
    "group_unsubscribe": true,
    "spam_report": true,
    "bounce": true,
    "deferred": true,
    "unsubscribe": true,
    "processed": true,
    "open": true,
    "click": true,
    "dropped": true
  }'
```

##### Mailgun Setup
```bash
# Configure Mailgun webhook
curl -s --user 'api:YOUR_API_KEY' \
  https://api.mailgun.net/v3/YOUR_DOMAIN/webhooks \
  -F url='https://your-domain.com/api/email-webhook/mailgun' \
  -F id='delivered'
```

#### Step 3: Enable Advanced Click Tracking
```typescript
// Use advanced click tracking service
async processAdvancedClick(trackingId: string, url: string, metadata: any) {
  const result = await this.emailClickTrackingService.processClick(
    trackingId,
    url,
    metadata
  );
  
  if (result.success) {
    console.log(`Click tracked: ${trackingId} -> ${url}`);
    console.log(`First click: ${result.isFirstClick}`);
  }
  
  return result;
}
```

## ⚙️ Configuration Options

### 🔧 Basic Configuration
```typescript
// src/config/email-tracking.config.ts

export const emailTrackingConfig = {
  // Basic settings
  enableTracking: true,
  enablePixelTracking: true,
  enableClickTracking: true,
  
  // Redis settings
  redis: {
    ttl: 3600, // 1 hour
    keyPrefix: 'email_tracking:',
  },
  
  // Batch processing
  batchProcessor: {
    interval: 30000, // 30 seconds
    batchSize: 100,
  },
};
```

### 🚀 Advanced Configuration
```typescript
export const advancedTrackingConfig = {
  // Click tracking settings
  clickTracking: {
    enableRateLimit: true,
    maxClicksPerMinute: 10,
    enableDuplicateDetection: true,
    enableDeviceDetection: true,
  },
  
  // Security settings
  security: {
    enableUrlValidation: true,
    blockPrivateIPs: true,
    blockLocalhost: true,
    allowedProtocols: ['http:', 'https:'],
  },
  
  // Analytics settings
  analytics: {
    enableRealTimeAnalytics: true,
    enableHeatmaps: true,
    retentionDays: 90,
  },
  
  // Webhook settings
  webhooks: {
    enableWebhooks: true,
    retryAttempts: 3,
    retryDelay: 5000,
  },
};
```

## 📊 Monitoring & Debugging

### 🔍 Health Check Endpoints
```typescript
// Add health check endpoints
@Controller('api/email-tracking/health')
export class EmailTrackingHealthController {
  
  @Get('status')
  async getStatus() {
    return {
      redis: await this.checkRedisConnection(),
      database: await this.checkDatabaseConnection(),
      tracking: await this.checkTrackingService(),
    };
  }
  
  @Get('metrics')
  async getMetrics() {
    return {
      totalTrackedEmails: await this.getTotalTrackedEmails(),
      openRate: await this.getAverageOpenRate(),
      clickRate: await this.getAverageClickRate(),
      errorRate: await this.getErrorRate(),
    };
  }
}
```

### 📈 Monitoring Dashboard
```typescript
// Basic monitoring queries
const monitoringQueries = {
  // Daily email volume
  dailyVolume: `
    SELECT DATE(created_at) as date, 
           COUNT(*) as total,
           COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent,
           COUNT(CASE WHEN status = 'opened' THEN 1 END) as opened,
           COUNT(CASE WHEN status = 'clicked' THEN 1 END) as clicked
    FROM user_campaign_history 
    WHERE created_at >= NOW() - INTERVAL '7 days'
    GROUP BY DATE(created_at)
    ORDER BY date DESC;
  `,
  
  // Campaign performance
  campaignPerformance: `
    SELECT campaign_id,
           COUNT(*) as total_sent,
           COUNT(CASE WHEN status = 'opened' THEN 1 END) as opens,
           COUNT(CASE WHEN status = 'clicked' THEN 1 END) as clicks,
           ROUND(COUNT(CASE WHEN status = 'opened' THEN 1 END) * 100.0 / COUNT(*), 2) as open_rate,
           ROUND(COUNT(CASE WHEN status = 'clicked' THEN 1 END) * 100.0 / COUNT(*), 2) as click_rate
    FROM user_campaign_history 
    WHERE created_at >= NOW() - INTERVAL '30 days'
    GROUP BY campaign_id
    ORDER BY total_sent DESC;
  `,
};
```

## 🚨 Troubleshooting Guide

### ❌ Common Issues & Solutions

#### Issue 1: Tracking Events Not Saved
```bash
# Check Redis connection
redis-cli ping

# Check Redis keys
redis-cli
> KEYS "email_tracking:*"
> TTL "email_tracking:1640995200000:0.123456"

# Check batch processor logs
tail -f logs/email-tracking-service.log | grep "batch"
```

#### Issue 2: Pixel Tracking Not Working
```bash
# Test pixel endpoint directly
curl -I http://localhost:3000/api/email-tracking/pixel/test_tracking_id

# Check if pixel is injected in email content
echo "Email content should contain: <img src=\".../api/email-tracking/pixel/...\""

# Check email client compatibility
# Gmail, Outlook, Apple Mail handle images differently
```

#### Issue 3: Click Tracking Redirects Failing
```bash
# Test click endpoint
curl -L "http://localhost:3000/api/email-tracking/click/test_id?url=https%3A//example.com"

# Check URL encoding
node -e "console.log(encodeURIComponent('https://example.com'))"

# Check rate limiting
redis-cli
> GET "email_click_rate_limit:127.0.0.1"
```

#### Issue 4: Webhooks Not Receiving Events
```bash
# Test webhook endpoint
curl -X POST http://localhost:3000/api/email-webhook/sendgrid \
  -H "Content-Type: application/json" \
  -d '[{"event":"delivered","email":"<EMAIL>"}]'

# Check webhook configuration at email provider
# Verify webhook URL is accessible from internet
# Check webhook logs for errors
```

## 📋 Production Deployment Checklist

### 🔧 Infrastructure
- [ ] Redis cluster setup for high availability
- [ ] Database indexes for tracking queries
- [ ] Load balancer configuration
- [ ] SSL certificates for tracking endpoints

### 📊 Monitoring
- [ ] Application performance monitoring (APM)
- [ ] Error tracking and alerting
- [ ] Resource usage monitoring
- [ ] Business metrics dashboards

### 🔒 Security
- [ ] Rate limiting configuration
- [ ] Webhook signature verification
- [ ] CORS configuration for tracking endpoints
- [ ] Security headers implementation

### 📈 Performance
- [ ] Database query optimization
- [ ] Redis memory optimization
- [ ] CDN setup for tracking pixels
- [ ] Caching strategy implementation

### 🧪 Testing
- [ ] Load testing for tracking endpoints
- [ ] Email client compatibility testing
- [ ] Webhook delivery testing
- [ ] Failover scenario testing

Theo dõi guide này để implement tracking một cách có hệ thống và hiệu quả! 🚀
