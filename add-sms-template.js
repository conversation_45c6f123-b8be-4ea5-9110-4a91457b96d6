const { Client } = require('pg');
require('dotenv').config();

async function addSmsTemplate() {
  const client = new Client({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
    ssl: process.env.DB_SSL === 'true'
  });

  try {
    await client.connect();
    console.log('🔗 Đã kết nối database');

    // Kiểm tra xem template đã tồn tại chưa
    const checkQuery = `
      SELECT id FROM admin_template_sms 
      WHERE category = 'SMS_OTP_VERIFY_2FA'
    `;
    
    const existingTemplate = await client.query(checkQuery);
    
    if (existingTemplate.rows.length > 0) {
      console.log('⚠️  Template SMS_OTP_VERIFY_2FA đã tồn tại');
      return;
    }

    // Thêm template mới
    const insertQuery = `
      INSERT INTO admin_template_sms (name, category, content, placeholders, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6)
    `;
    
    const currentTime = Date.now();
    const values = [
      'Template SMS OTP 2FA',
      'SMS_OTP_VERIFY_2FA',
      'Ma xac thuc 2FA cua ban la: {{TWO_FA_CODE}}. Ma co hieu luc trong 5 phut.',
      JSON.stringify(['TWO_FA_CODE']),
      currentTime,
      currentTime
    ];

    await client.query(insertQuery, values);
    console.log('✅ Đã thêm template SMS_OTP_VERIFY_2FA thành công');

  } catch (error) {
    console.error('❌ Lỗi:', error.message);
  } finally {
    await client.end();
    console.log('🔌 Đã đóng kết nối database');
  }
}

addSmsTemplate();
