import { Module } from '@nestjs/common';
import { ZaloWebhookProcessor } from './zalo-webhook.processor';
import { UsageDeductionProcessor } from './usage-deduction.processor';

/**
 * Module cho các webhook processors
 */
@Module({
  providers: [
    ZaloWebhookProcessor,
    UsageDeductionProcessor,
  ],
  exports: [
    ZaloWebhookProcessor,
    UsageDeductionProcessor,
  ],
})
export class WebhookProcessorsModule {}
