import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ZaloModule } from '../../../shared/services/zalo/zalo.module';
import { InfraModule } from '../../../infra/infra.module';
import {
  ZaloOaService,
  ZaloCustomerService,
  ZaloContextLoadingService,
  ZaloMessageProcessingService,
  ZaloThreadManagementService,
  ZaloMessageContentParserService,
  AgentConfigBuilderService,
} from './services';
import { ZaloOAAgentAdapterService } from './services/zalo-oa-agent-adapter.service';
import {
  AgentCoreConfigService,
  AgentModelConfigService,
  AgentPaymentConfigService,
  AgentPlatformContextService,
  AgentMcpConfigService,
  AgentMediaContextService,

  AgentStrategistConfigBuilderService,
} from './services/agent-config-builder';
import {
  Agent,
  AgentUser,
  AgentStrategy,
  AgentStrategyUser,
  AgentUserMcp,
  UserMcp,
  UserModel,
  UserKeyLlm,
  ModelRegistry,
  SystemModels,
  UserModelFineTune,
  SystemKeyLlm,
  SystemModelKeyLlm,
  UserModelKeyLlm,
  PaymentGateway,
  UserProviderShipment,
  ZaloOfficialAccount,
  ZaloCustomer,
  ZaloConversationThread,
  ZaloAiMessage,
  UserAgentRun,
  ZaloMedia,
  ZaloThreadMediaContext,
  UserConvertCustomer,
  CustomerMemory,
  AgentMemory,
} from './entities';
import { Integration } from '../../../shared/entities/integration.entity';
import { IntegrationProvider } from '../../../shared/entities/integration-provider.entity';
import {
  AgentRepository,
  AgentUserRepository,
  AgentStrategyRepository,
  AgentStrategyUserRepository,
  AgentUserMcpRepository,
  UserMcpRepository,
  UserModelRepository,
  UserKeyLlmRepository,
  ModelRegistryRepository,
  SystemModelsRepository,
  UserModelFineTuneRepository,
  SystemKeyLlmRepository,
  SystemModelKeyLlmRepository,
  UserModelKeyLlmRepository,
  PaymentGatewayRepository,
  UserProviderShipmentRepository,
  ZaloOfficialAccountRepository,
  ZaloCustomerRepository,
  ZaloConversationThreadRepository,
  ZaloAiMessageRepository,
  UserAgentRunRepository,
  ZaloMediaRepository,
  ZaloThreadMediaContextRepository,
  UserConvertCustomerRepository,
  CustomerMemoryRepository,
  AgentMemoryRepository,
} from './repositories';
import { HttpModule } from '@nestjs/axios';
import { ZaloAiResponseProcessor } from './zalo/zalo-ai-response.processor';
import { AgentAssistantRichSystemMessageBuilderService } from './services/agent-assistant-rich-system-message-builder.service';
import { AssistantGraphFactory } from './core/assistant-graph.factory';
import { ZaloToolsService } from './zalo/zalo-tools/zalo-tools.service';

/**
 * Agent Assistant Module
 *
 *
 * This module handles AI agent interactions for customer-facing platforms
 * like Zalo, Messenger, Telegram, etc.
 *
 * Features:
 * - ZaloAiResponseProcessor: Real AI response processing for Zalo messages
 * - Context Loading Services: OA management, customer mapping, thread handling
 * - Integration with existing AgentSystemService for AI processing
 */
@Module({
  imports: [
    // TypeORM entities
    TypeOrmModule.forFeature([
      Agent,
      AgentUser,
      AgentStrategy,
      AgentStrategyUser,
      AgentUserMcp,
      UserMcp,
      UserModel,
      UserKeyLlm,
      ModelRegistry,
      SystemModels,
      UserModelFineTune,
      SystemKeyLlm,
      SystemModelKeyLlm,
      UserModelKeyLlm,
      PaymentGateway,
      UserProviderShipment,
      ZaloOfficialAccount,
      ZaloCustomer,
      ZaloConversationThread,
      ZaloAiMessage,
      UserAgentRun,
      ZaloMedia,
      ZaloThreadMediaContext,
      UserConvertCustomer,
      CustomerMemory,
      AgentMemory,
      Integration,
      IntegrationProvider,
    ]),

    HttpModule,
    ZaloModule,        // Existing Zalo services (ZaloUserManagementService, etc.)
    InfraModule,       // S3Service for avatar processing
  ],
  providers: [
    // Repositories
    AgentRepository,
    AgentUserRepository,
    AgentStrategyRepository,
    AgentStrategyUserRepository,
    AgentUserMcpRepository,
    UserMcpRepository,
    UserModelRepository,
    UserKeyLlmRepository,
    ModelRegistryRepository,
    SystemModelsRepository,
    UserModelFineTuneRepository,
    SystemKeyLlmRepository,
    SystemModelKeyLlmRepository,
    UserModelKeyLlmRepository,
    PaymentGatewayRepository,
    UserProviderShipmentRepository,
    ZaloOfficialAccountRepository,
    ZaloCustomerRepository,
    ZaloConversationThreadRepository,
    ZaloAiMessageRepository,
    UserAgentRunRepository,
    ZaloMediaRepository,
    ZaloThreadMediaContextRepository,
    UserConvertCustomerRepository,
    CustomerMemoryRepository,
    AgentMemoryRepository,
    AssistantGraphFactory,

    // Core services
    ZaloOAAgentAdapterService,
    ZaloOaService,
    ZaloCustomerService,
    ZaloContextLoadingService,
    ZaloMessageProcessingService,
    ZaloThreadManagementService,
    ZaloMessageContentParserService,

    // Agent config builder services
    AgentMediaContextService,
    AgentCoreConfigService,
    AgentModelConfigService,
    AgentPaymentConfigService,
    AgentPlatformContextService,
    AgentMcpConfigService,


    // Strategist config builder services
    AgentStrategistConfigBuilderService,

    AgentConfigBuilderService,
    AgentAssistantRichSystemMessageBuilderService,
    ZaloToolsService,

    // Queue processors
    ZaloAiResponseProcessor, // 🚀 REAL AI PROCESSOR
  ],
  exports: [
    ZaloOaService,
    AssistantGraphFactory,
    ZaloCustomerService,
    ZaloContextLoadingService,
    ZaloMessageProcessingService,
    ZaloThreadManagementService,
    ZaloAiResponseProcessor,
    CustomerMemoryRepository,
    ZaloToolsService,
  ],
})
export class AgentAssistantModule {}
