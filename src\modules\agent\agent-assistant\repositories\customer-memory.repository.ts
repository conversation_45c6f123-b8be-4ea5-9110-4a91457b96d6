import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CustomerMemory } from '../entities/customer-memory.entity';

/**
 * Repository for CustomerMemory entity
 * Handles CRUD operations for customer memories
 */
@Injectable()
export class CustomerMemoryRepository {
  constructor(
    @InjectRepository(CustomerMemory)
    private readonly repository: Repository<CustomerMemory>,
  ) {}

  /**
   * Find all memories for a specific customer
   * @param customerId Customer ID to find memories for
   * @returns Array of customer memories ordered by creation date (newest first)
   */
  async findByCustomerId(customerId: string): Promise<CustomerMemory[]> {
    return this.repository.find({
      where: { customerId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Create a new customer memory
   * @param memoryData Memory data to create
   * @returns Created customer memory
   */
  async create(memoryData: Partial<CustomerMemory>): Promise<CustomerMemory> {
    const memory = this.repository.create(memoryData);
    return this.repository.save(memory);
  }

  /**
   * Find a memory by ID
   * @param id Memory ID
   * @returns Memory record or null if not found
   */
  async findById(id: string): Promise<CustomerMemory | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Update an existing customer memory
   * @param id Memory ID to update
   * @param updateData Data to update
   * @returns Updated memory record or null if not found
   */
  async update(
    id: string,
    updateData: Partial<CustomerMemory>,
  ): Promise<CustomerMemory | null> {
    const result = await this.repository.update(id, updateData);

    if (result.affected === 0) {
      return null;
    }

    return this.findById(id);
  }

  /**
   * Delete a customer memory
   * @param id Memory ID to delete
   * @returns Success status
   */
  async delete(id: string): Promise<boolean> {
    const result = await this.repository.delete(id);
    return (
      result.affected !== undefined &&
      result.affected !== null &&
      result.affected > 0
    );
  }

  /**
   * Find memories by customer ID with pagination
   * @param customerId Customer ID
   * @param limit Maximum number of records to return
   * @param offset Number of records to skip
   * @returns Array of customer memories with pagination
   */
  async findByCustomerIdPaginated(
    customerId: string,
    limit: number = 10,
    offset: number = 0,
  ): Promise<CustomerMemory[]> {
    return this.repository.find({
      where: { customerId },
      order: { createdAt: 'DESC' },
      take: limit,
      skip: offset,
    });
  }

  /**
   * Count total memories for a customer
   * @param customerId Customer ID
   * @returns Total count of memories for the customer
   */
  async countByCustomerId(customerId: string): Promise<number> {
    return this.repository.count({
      where: { customerId },
    });
  }

  /**
   * Find recent memories across all customers
   * @param limit Maximum number of records to return
   * @returns Array of recent customer memories
   */
  async findRecent(limit: number = 50): Promise<CustomerMemory[]> {
    return this.repository.find({
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  /**
   * Delete all memories for a specific customer
   * @param customerId Customer ID
   * @returns Number of deleted records
   */
  async deleteByCustomerId(customerId: string): Promise<number> {
    const result = await this.repository.delete({ customerId });
    return result.affected || 0;
  }

  /**
   * Bulk create multiple customer memories
   * @param memoriesData Array of memory data to create
   * @returns Array of created customer memories
   */
  async bulkCreate(
    memoriesData: Partial<CustomerMemory>[],
  ): Promise<CustomerMemory[]> {
    const memories = this.repository.create(memoriesData);
    return this.repository.save(memories);
  }
}
