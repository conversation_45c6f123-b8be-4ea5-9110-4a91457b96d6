import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { SystemKeyLlm } from '../entities/system-key-llm.entity';

@Injectable()
export class SystemKeyLlmRepository {
  constructor(
    @InjectRepository(SystemKeyLlm)
    private readonly repository: Repository<SystemKeyLlm>,
  ) {}

  async findById(id: string): Promise<SystemKeyLlm | null> {
    return this.repository.findOne({ 
      where: { 
        id,
        deletedAt: IsNull()
      }
    });
  }
}
