import { TrimmingTypeEnum } from "../enums";
import { ProfileAgent } from "./agent-assistant.interface";

/**
 * Core agent data interface
 */
export interface CoreAgentData {
  id: string;
  name: string;
  description?: string;
  instruction?: string;
  vectorStoreId?: string | null;
  isSupervisor: boolean;
  profile?: ProfileAgent | null;
  trimmingConfig: {
    type: TrimmingTypeEnum;
    threshold: number;
  };
  modelConfig?: {
    temperature?: number;
    top_p?: number;
    top_k?: number;
    max_tokens?: number;
  };

  // Model-related properties from agentUser
  userModelId?: string;
  modelFineTuneId?: string;
  systemModelId?: string;
  keyLlmId?: string;

  // Payment-related properties from agentUser
  paymentGatewayId?: number;
  userProviderShipmentId?: string;
  receiverPayShippingFee?: boolean;
  paymentMethods?: any;

  // Strategy-related properties from agentUser
  strategyId?: string;
}