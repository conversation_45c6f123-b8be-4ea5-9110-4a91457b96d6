import { Injectable, Logger } from '@nestjs/common';
import {
  ZnsErrorCode,
  ZnsErrorInfoDto,
  ZnsErrorResponseDto,
  ZnsBankBinCode,
  ZnsBinCodeInfoDto,
} from './dto/zalo-zns.dto';

/**
 * Service cho xử lý lỗi và utilities ZNS
 *
 * Chức năng chính:
 * - Xử lý và phân tích mã lỗi ZNS
 * - Cung cấp thông tin chi tiết về lỗi
 * - Đưa ra gợi ý khắc phục
 * - Quản lý thông tin BIN code ngân hàng
 * - Utilities cho validation và format dữ liệu
 *
 * Tài liệu tham khảo:
 * - https://developers.zalo.me/docs/zalo-notification-service/phu-luc/bang-ma-loi
 * - https://developers.zalo.me/docs/zalo-notification-service/phu-luc/danh-sach-bin-code
 */
@Injectable()
export class ZaloZnsErrorService {
  private readonly logger = new Logger(ZaloZnsErrorService.name);

  // Mapping mã lỗi với thông tin chi tiết
  private readonly ERROR_DETAILS: Record<
    ZnsErrorCode,
    {
      message: string;
      description: string;
      suggestion: string;
      category: 'general' | 'template' | 'message' | 'oa' | 'file';
    }
  > = {
    [ZnsErrorCode.SUCCESS]: {
      message: 'Thành công',
      description: 'Yêu cầu được xử lý thành công',
      suggestion: 'Không cần thực hiện thêm hành động nào',
      category: 'general',
    },
    [ZnsErrorCode.INVALID_PARAMETER]: {
      message: 'Tham số không hợp lệ',
      description:
        'Một hoặc nhiều tham số trong yêu cầu không đúng định dạng hoặc thiếu',
      suggestion: 'Kiểm tra lại tất cả tham số theo tài liệu API',
      category: 'general',
    },
    [ZnsErrorCode.INVALID_ACCESS_TOKEN]: {
      message: 'Access token không hợp lệ',
      description:
        'Access token đã hết hạn, không đúng hoặc không có quyền truy cập',
      suggestion:
        'Làm mới access token hoặc kiểm tra quyền của Official Account',
      category: 'general',
    },
    [ZnsErrorCode.PERMISSION_DENIED]: {
      message: 'Không có quyền truy cập',
      description: 'Official Account không có quyền thực hiện hành động này',
      suggestion:
        'Liên hệ Zalo để được cấp quyền hoặc kiểm tra cấu hình Official Account',
      category: 'general',
    },
    [ZnsErrorCode.RATE_LIMIT_EXCEEDED]: {
      message: 'Vượt quá giới hạn tần suất',
      description:
        'Số lượng yêu cầu vượt quá giới hạn cho phép trong khoảng thời gian',
      suggestion:
        'Giảm tần suất gửi yêu cầu hoặc chờ một thời gian trước khi thử lại',
      category: 'general',
    },
    [ZnsErrorCode.INTERNAL_SERVER_ERROR]: {
      message: 'Lỗi hệ thống',
      description: 'Có lỗi xảy ra từ phía server của Zalo',
      suggestion: 'Thử lại sau ít phút hoặc liên hệ support nếu lỗi tiếp tục',
      category: 'general',
    },
    [ZnsErrorCode.TEMPLATE_NOT_FOUND]: {
      message: 'Không tìm thấy template',
      description: 'Template ID không tồn tại hoặc đã bị xóa',
      suggestion: 'Kiểm tra lại template ID hoặc tạo template mới',
      category: 'template',
    },
    [ZnsErrorCode.TEMPLATE_NOT_APPROVED]: {
      message: 'Template chưa được duyệt',
      description: 'Template đang chờ duyệt hoặc đã bị từ chối',
      suggestion:
        'Chờ template được duyệt hoặc chỉnh sửa theo yêu cầu của Zalo',
      category: 'template',
    },
    [ZnsErrorCode.TEMPLATE_EXPIRED]: {
      message: 'Template đã hết hạn',
      description: 'Template đã vượt quá thời gian hiệu lực',
      suggestion: 'Tạo template mới hoặc gia hạn template hiện tại',
      category: 'template',
    },
    [ZnsErrorCode.TEMPLATE_DISABLED]: {
      message: 'Template đã bị vô hiệu hóa',
      description: 'Template đã bị tạm ngưng sử dụng do vi phạm chính sách',
      suggestion: 'Liên hệ Zalo để biết lý do và cách khắc phục',
      category: 'template',
    },
    [ZnsErrorCode.TEMPLATE_INVALID_FORMAT]: {
      message: 'Định dạng template không hợp lệ',
      description: 'Cấu trúc hoặc nội dung template không đúng quy định',
      suggestion: 'Kiểm tra lại format template theo hướng dẫn',
      category: 'template',
    },
    [ZnsErrorCode.TEMPLATE_PARAMETER_MISMATCH]: {
      message: 'Tham số template không khớp',
      description:
        'Dữ liệu truyền vào không khớp với tham số định nghĩa trong template',
      suggestion: 'Kiểm tra lại tham số và dữ liệu truyền vào',
      category: 'template',
    },
    [ZnsErrorCode.INVALID_PHONE_NUMBER]: {
      message: 'Số điện thoại không hợp lệ',
      description: 'Số điện thoại không đúng định dạng hoặc không tồn tại',
      suggestion: 'Kiểm tra lại định dạng số điện thoại (bao gồm mã quốc gia)',
      category: 'message',
    },
    [ZnsErrorCode.PHONE_NUMBER_BLOCKED]: {
      message: 'Số điện thoại bị chặn',
      description: 'Số điện thoại đã từ chối nhận tin nhắn ZNS',
      suggestion: 'Loại bỏ số này khỏi danh sách gửi tin',
      category: 'message',
    },
    [ZnsErrorCode.MESSAGE_QUOTA_EXCEEDED]: {
      message: 'Vượt quá hạn mức tin nhắn',
      description:
        'Đã gửi vượt quá số lượng tin nhắn cho phép trong ngày/tháng',
      suggestion: 'Chờ reset hạn mức hoặc nâng cấp gói dịch vụ',
      category: 'message',
    },
    [ZnsErrorCode.MESSAGE_TOO_LONG]: {
      message: 'Tin nhắn quá dài',
      description: 'Nội dung tin nhắn vượt quá giới hạn ký tự cho phép',
      suggestion: 'Rút gọn nội dung tin nhắn hoặc chia thành nhiều tin',
      category: 'message',
    },
    [ZnsErrorCode.MESSAGE_INVALID_CONTENT]: {
      message: 'Nội dung tin nhắn không hợp lệ',
      description: 'Nội dung chứa từ ngữ không phù hợp hoặc vi phạm chính sách',
      suggestion: 'Kiểm tra và chỉnh sửa nội dung theo quy định',
      category: 'message',
    },
    [ZnsErrorCode.MESSAGE_DUPLICATE]: {
      message: 'Tin nhắn trùng lặp',
      description: 'Tin nhắn giống hệt đã được gửi trong thời gian gần đây',
      suggestion: 'Thay đổi nội dung hoặc chờ một thời gian trước khi gửi lại',
      category: 'message',
    },
    [ZnsErrorCode.OA_NOT_FOUND]: {
      message: 'Không tìm thấy Official Account',
      description: 'Official Account ID không tồn tại',
      suggestion: 'Kiểm tra lại thông tin Official Account',
      category: 'oa',
    },
    [ZnsErrorCode.OA_NOT_APPROVED]: {
      message: 'Official Account chưa được duyệt',
      description: 'Official Account chưa hoàn thành quy trình xét duyệt',
      suggestion: 'Hoàn thành quy trình xét duyệt Official Account',
      category: 'oa',
    },
    [ZnsErrorCode.OA_SUSPENDED]: {
      message: 'Official Account bị tạm ngưng',
      description: 'Official Account đã bị tạm ngưng do vi phạm chính sách',
      suggestion: 'Liên hệ Zalo để biết lý do và cách khôi phục',
      category: 'oa',
    },
    [ZnsErrorCode.OA_INSUFFICIENT_BALANCE]: {
      message: 'Số dư không đủ',
      description: 'Tài khoản không có đủ số dư để thực hiện giao dịch',
      suggestion: 'Nạp thêm tiền vào tài khoản',
      category: 'oa',
    },
    [ZnsErrorCode.OA_FEATURE_NOT_ENABLED]: {
      message: 'Tính năng chưa được kích hoạt',
      description: 'Official Account chưa được kích hoạt tính năng ZNS',
      suggestion: 'Liên hệ Zalo để kích hoạt tính năng ZNS',
      category: 'oa',
    },
    [ZnsErrorCode.FILE_TOO_LARGE]: {
      message: 'File quá lớn',
      description: 'Kích thước file vượt quá giới hạn cho phép',
      suggestion: 'Giảm kích thước file hoặc nén file trước khi upload',
      category: 'file',
    },
    [ZnsErrorCode.FILE_INVALID_FORMAT]: {
      message: 'Định dạng file không hợp lệ',
      description: 'File không đúng định dạng được hỗ trợ',
      suggestion: 'Chuyển đổi file sang định dạng được hỗ trợ',
      category: 'file',
    },
    [ZnsErrorCode.FILE_UPLOAD_FAILED]: {
      message: 'Upload file thất bại',
      description: 'Có lỗi xảy ra trong quá trình upload file',
      suggestion: 'Thử upload lại hoặc kiểm tra kết nối mạng',
      category: 'file',
    },
    [ZnsErrorCode.FILE_NOT_FOUND]: {
      message: 'Không tìm thấy file',
      description: 'File đã bị xóa hoặc không tồn tại',
      suggestion: 'Upload lại file hoặc kiểm tra đường dẫn file',
      category: 'file',
    },
  };

  // Thông tin BIN code ngân hàng Việt Nam
  private readonly BANK_INFO: Record<
    ZnsBankBinCode,
    {
      name: string;
      shortName: string;
      logo: string;
      supportsZns: boolean;
    }
  > = {
    [ZnsBankBinCode.VIETCOMBANK]: {
      name: 'Ngân hàng TMCP Ngoại thương Việt Nam',
      shortName: 'Vietcombank',
      logo: 'https://cdn.example.com/banks/vietcombank.png',
      supportsZns: true,
    },
    [ZnsBankBinCode.VIETINBANK]: {
      name: 'Ngân hàng TMCP Công thương Việt Nam',
      shortName: 'VietinBank',
      logo: 'https://cdn.example.com/banks/vietinbank.png',
      supportsZns: true,
    },
    [ZnsBankBinCode.BIDV]: {
      name: 'Ngân hàng TMCP Đầu tư và Phát triển Việt Nam',
      shortName: 'BIDV',
      logo: 'https://cdn.example.com/banks/bidv.png',
      supportsZns: true,
    },
    [ZnsBankBinCode.AGRIBANK]: {
      name: 'Ngân hàng Nông nghiệp và Phát triển Nông thôn Việt Nam',
      shortName: 'Agribank',
      logo: 'https://cdn.example.com/banks/agribank.png',
      supportsZns: true,
    },
    [ZnsBankBinCode.TECHCOMBANK]: {
      name: 'Ngân hàng TMCP Kỹ thương Việt Nam',
      shortName: 'Techcombank',
      logo: 'https://cdn.example.com/banks/techcombank.png',
      supportsZns: true,
    },
    [ZnsBankBinCode.MB_BANK]: {
      name: 'Ngân hàng TMCP Quân đội',
      shortName: 'MB Bank',
      logo: 'https://cdn.example.com/banks/mbbank.png',
      supportsZns: true,
    },
    [ZnsBankBinCode.ACB]: {
      name: 'Ngân hàng TMCP Á Châu',
      shortName: 'ACB',
      logo: 'https://cdn.example.com/banks/acb.png',
      supportsZns: true,
    },
    [ZnsBankBinCode.VPB]: {
      name: 'Ngân hàng TMCP Việt Nam Thịnh vượng',
      shortName: 'VPBank',
      logo: 'https://cdn.example.com/banks/vpbank.png',
      supportsZns: true,
    },
    [ZnsBankBinCode.TPB]: {
      name: 'Ngân hàng TMCP Tiên Phong',
      shortName: 'TPBank',
      logo: 'https://cdn.example.com/banks/tpbank.png',
      supportsZns: true,
    },
    [ZnsBankBinCode.SACOMBANK]: {
      name: 'Ngân hàng TMCP Sài Gòn Thương tín',
      shortName: 'Sacombank',
      logo: 'https://cdn.example.com/banks/sacombank.png',
      supportsZns: true,
    },
    // Add more banks as needed...
    [ZnsBankBinCode.EXIMBANK]: {
      name: 'Ngân hàng TMCP Xuất Nhập khẩu Việt Nam',
      shortName: 'Eximbank',
      logo: 'https://cdn.example.com/banks/eximbank.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.HDBANK]: {
      name: 'Ngân hàng TMCP Phát triển Thành phố Hồ Chí Minh',
      shortName: 'HDBank',
      logo: 'https://cdn.example.com/banks/hdbank.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.SHB]: {
      name: 'Ngân hàng TMCP Sài Gòn - Hà Nội',
      shortName: 'SHB',
      logo: 'https://cdn.example.com/banks/shb.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.OCB]: {
      name: 'Ngân hàng TMCP Phương Đông',
      shortName: 'OCB',
      logo: 'https://cdn.example.com/banks/ocb.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.MSB]: {
      name: 'Ngân hàng TMCP Hàng Hải',
      shortName: 'MSB',
      logo: 'https://cdn.example.com/banks/msb.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.SEABANK]: {
      name: 'Ngân hàng TMCP Đông Nam Á',
      shortName: 'SeABank',
      logo: 'https://cdn.example.com/banks/seabank.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.VIB]: {
      name: 'Ngân hàng TMCP Quốc tế Việt Nam',
      shortName: 'VIB',
      logo: 'https://cdn.example.com/banks/vib.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.LPB]: {
      name: 'Ngân hàng TMCP Bưu điện Liên Việt',
      shortName: 'LienVietPostBank',
      logo: 'https://cdn.example.com/banks/lpb.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.KIENLONGBANK]: {
      name: 'Ngân hàng TMCP Kiên Long',
      shortName: 'Kienlongbank',
      logo: 'https://cdn.example.com/banks/kienlongbank.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.DONGABANK]: {
      name: 'Ngân hàng TMCP Đông Á',
      shortName: 'DongA Bank',
      logo: 'https://cdn.example.com/banks/dongabank.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.NAMABANK]: {
      name: 'Ngân hàng TMCP Nam Á',
      shortName: 'Nam A Bank',
      logo: 'https://cdn.example.com/banks/namabank.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.PGBANK]: {
      name: 'Ngân hàng TMCP Xăng dầu Petrolimex',
      shortName: 'PG Bank',
      logo: 'https://cdn.example.com/banks/pgbank.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.VIETBANK]: {
      name: 'Ngân hàng TMCP Việt Nam Thương tín',
      shortName: 'VietBank',
      logo: 'https://cdn.example.com/banks/vietbank.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.NCB]: {
      name: 'Ngân hàng TMCP Quốc dân',
      shortName: 'NCB',
      logo: 'https://cdn.example.com/banks/ncb.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.BACABANK]: {
      name: 'Ngân hàng TMCP Bắc Á',
      shortName: 'Bac A Bank',
      logo: 'https://cdn.example.com/banks/bacabank.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.OCEANBANK]: {
      name: 'Ngân hàng TMCP Đại Dương',
      shortName: 'OceanBank',
      logo: 'https://cdn.example.com/banks/oceanbank.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.GPBANK]: {
      name: 'Ngân hàng TMCP Dầu khí Toàn cầu',
      shortName: 'GP Bank',
      logo: 'https://cdn.example.com/banks/gpbank.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.ABBANK]: {
      name: 'Ngân hàng TMCP An Bình',
      shortName: 'ABBANK',
      logo: 'https://cdn.example.com/banks/abbank.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.VCCB]: {
      name: 'Ngân hàng TMCP Bản Việt',
      shortName: 'VietCapital Bank',
      logo: 'https://cdn.example.com/banks/vccb.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.BAOVIETBANK]: {
      name: 'Ngân hàng TMCP Bảo Việt',
      shortName: 'BaoViet Bank',
      logo: 'https://cdn.example.com/banks/baovietbank.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.CBBANK]: {
      name: 'Ngân hàng Thương mại TNHH MTV Xây dựng Việt Nam',
      shortName: 'CB Bank',
      logo: 'https://cdn.example.com/banks/cbbank.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.WOORIBANK]: {
      name: 'Ngân hàng TNHH MTV Woori Việt Nam',
      shortName: 'Woori Bank',
      logo: 'https://cdn.example.com/banks/wooribank.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.SHINHANBANK]: {
      name: 'Ngân hàng TNHH MTV Shinhan Việt Nam',
      shortName: 'Shinhan Bank',
      logo: 'https://cdn.example.com/banks/shinhanbank.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.STANDARDCHARTERED]: {
      name: 'Ngân hàng TNHH MTV Standard Chartered (Việt Nam)',
      shortName: 'Standard Chartered',
      logo: 'https://cdn.example.com/banks/standardchartered.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.PUBLICBANK]: {
      name: 'Ngân hàng TNHH MTV Public Việt Nam',
      shortName: 'Public Bank',
      logo: 'https://cdn.example.com/banks/publicbank.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.NONGHYUP]: {
      name: 'Ngân hàng Nonghyup Hà Nội',
      shortName: 'Nonghyup Bank',
      logo: 'https://cdn.example.com/banks/nonghyup.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.INDOVINABANK]: {
      name: 'Ngân hàng TNHH Indovina',
      shortName: 'Indovina Bank',
      logo: 'https://cdn.example.com/banks/indovinabank.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.CIMB]: {
      name: 'Ngân hàng TNHH MTV CIMB Việt Nam',
      shortName: 'CIMB Bank',
      logo: 'https://cdn.example.com/banks/cimb.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.UOB]: {
      name: 'Ngân hàng United Overseas Bank Việt Nam',
      shortName: 'UOB',
      logo: 'https://cdn.example.com/banks/uob.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.HSBC]: {
      name: 'Ngân hàng TNHH MTV HSBC (Việt Nam)',
      shortName: 'HSBC',
      logo: 'https://cdn.example.com/banks/hsbc.png',
      supportsZns: false,
    },
    [ZnsBankBinCode.HONGLEONG]: {
      name: 'Ngân hàng TNHH MTV Hong Leong Việt Nam',
      shortName: 'Hong Leong Bank',
      logo: 'https://cdn.example.com/banks/hongleong.png',
      supportsZns: false,
    },
  };

  /**
   * Lấy thông tin chi tiết về mã lỗi
   * @param errorCode Mã lỗi ZNS
   * @returns Thông tin chi tiết về lỗi
   */
  getErrorInfo(errorCode: ZnsErrorCode): ZnsErrorInfoDto {
    const details = this.ERROR_DETAILS[errorCode];

    if (!details) {
      return {
        error_code: errorCode,
        error_message: 'Mã lỗi không xác định',
        error_description: `Mã lỗi ${errorCode} không có trong danh sách lỗi được hỗ trợ`,
        suggestion: 'Liên hệ support để được hỗ trợ',
      };
    }

    return {
      error_code: errorCode,
      error_message: details.message,
      error_description: details.description,
      suggestion: details.suggestion,
      additional_info: {
        category: details.category,
      },
    };
  }

  /**
   * Phân tích response lỗi từ Zalo API
   * @param response Response từ Zalo API
   * @returns Thông tin lỗi đã được phân tích
   */
  analyzeErrorResponse(response: any): ZnsErrorResponseDto {
    const errorCode = response.error || ZnsErrorCode.INTERNAL_SERVER_ERROR;
    const errorInfo = this.getErrorInfo(errorCode);

    return {
      error: errorCode,
      message: response.message || errorInfo.error_message,
      data: response.data,
      timestamp: Date.now(),
    };
  }

  /**
   * Kiểm tra xem lỗi có thể retry được không
   * @param errorCode Mã lỗi
   * @returns True nếu có thể retry
   */
  isRetryableError(errorCode: ZnsErrorCode): boolean {
    const retryableErrors = [
      ZnsErrorCode.RATE_LIMIT_EXCEEDED,
      ZnsErrorCode.INTERNAL_SERVER_ERROR,
      ZnsErrorCode.FILE_UPLOAD_FAILED,
    ];

    return retryableErrors.includes(errorCode);
  }

  /**
   * Lấy thời gian chờ trước khi retry
   * @param errorCode Mã lỗi
   * @param retryCount Số lần đã retry
   * @returns Thời gian chờ (milliseconds)
   */
  getRetryDelay(errorCode: ZnsErrorCode, retryCount: number): number {
    switch (errorCode) {
      case ZnsErrorCode.RATE_LIMIT_EXCEEDED:
        // Exponential backoff: 1s, 2s, 4s, 8s, 16s
        return Math.min(1000 * Math.pow(2, retryCount), 16000);

      case ZnsErrorCode.INTERNAL_SERVER_ERROR:
        // Fixed delay: 5s
        return 5000;

      case ZnsErrorCode.FILE_UPLOAD_FAILED:
        // Linear increase: 2s, 4s, 6s, 8s, 10s
        return Math.min(2000 * (retryCount + 1), 10000);

      default:
        return 1000;
    }
  }

  /**
   * Lấy thông tin ngân hàng từ BIN code
   * @param binCode Mã BIN
   * @returns Thông tin ngân hàng
   */
  getBankInfoFromBinCode(binCode: string): ZnsBinCodeInfoDto | null {
    const bankBinCode = binCode as ZnsBankBinCode;
    const bankInfo = this.BANK_INFO[bankBinCode];

    if (!bankInfo) {
      return null;
    }

    return {
      bin_code: bankBinCode,
      bank_name: bankInfo.name,
      bank_short_name: bankInfo.shortName,
      bank_logo: bankInfo.logo,
      card_type: 'DEBIT', // Default, should be determined by BIN range
      card_brand: 'NAPAS', // Default, should be determined by BIN range
      supports_online_payment: true,
      supports_zns: bankInfo.supportsZns,
    };
  }

  /**
   * Lấy danh sách ngân hàng hỗ trợ ZNS
   * @returns Danh sách ngân hàng hỗ trợ ZNS
   */
  getSupportedBanks(): ZnsBinCodeInfoDto[] {
    return Object.entries(this.BANK_INFO)
      .filter(([_, info]) => info.supportsZns)
      .map(([binCode, info]) => ({
        bin_code: binCode as ZnsBankBinCode,
        bank_name: info.name,
        bank_short_name: info.shortName,
        bank_logo: info.logo,
        card_type: 'DEBIT' as const,
        card_brand: 'NAPAS' as const,
        supports_online_payment: true,
        supports_zns: true,
      }));
  }

  /**
   * Validate số điện thoại Việt Nam
   * @param phoneNumber Số điện thoại
   * @returns Kết quả validation
   */
  validateVietnamesePhoneNumber(phoneNumber: string): {
    isValid: boolean;
    formattedNumber?: string;
    carrier?: string;
    errors: string[];
  } {
    const errors: string[] = [];

    // Remove all non-digit characters
    const cleanNumber = phoneNumber.replace(/\D/g, '');

    // Check if empty
    if (!cleanNumber) {
      errors.push('Số điện thoại không được để trống');
      return { isValid: false, errors };
    }

    // Vietnam phone number patterns
    const patterns = {
      mobile: /^(84|0)(3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9])\d{7}$/,
      landline: /^(84|0)(2[0-9])\d{8}$/,
    };

    let formattedNumber: string;
    let carrier: string;

    // Check mobile number
    if (patterns.mobile.test(cleanNumber)) {
      // Format to international format
      if (cleanNumber.startsWith('84')) {
        formattedNumber = `+${cleanNumber}`;
      } else {
        formattedNumber = `+84${cleanNumber.substring(1)}`;
      }

      // Determine carrier
      const prefix = cleanNumber.substring(
        cleanNumber.length - 9,
        cleanNumber.length - 7,
      );
      carrier = this.getCarrierFromPrefix(prefix);
    }
    // Check landline number
    else if (patterns.landline.test(cleanNumber)) {
      if (cleanNumber.startsWith('84')) {
        formattedNumber = `+${cleanNumber}`;
      } else {
        formattedNumber = `+84${cleanNumber.substring(1)}`;
      }
      carrier = 'Landline';
    }
    // Invalid format
    else {
      errors.push('Số điện thoại không đúng định dạng Việt Nam');
      return { isValid: false, errors };
    }

    return {
      isValid: true,
      formattedNumber,
      carrier,
      errors: [],
    };
  }

  /**
   * Xác định nhà mạng từ đầu số
   * @param prefix Đầu số (2 chữ số)
   * @returns Tên nhà mạng
   */
  private getCarrierFromPrefix(prefix: string): string {
    const carrierMap: Record<string, string> = {
      // Viettel
      '32': 'Viettel',
      '33': 'Viettel',
      '34': 'Viettel',
      '35': 'Viettel',
      '36': 'Viettel',
      '37': 'Viettel',
      '38': 'Viettel',
      '39': 'Viettel',
      '86': 'Viettel',
      '96': 'Viettel',
      '97': 'Viettel',
      '98': 'Viettel',

      // Vinaphone
      '81': 'Vinaphone',
      '82': 'Vinaphone',
      '83': 'Vinaphone',
      '84': 'Vinaphone',
      '85': 'Vinaphone',
      '88': 'Vinaphone',
      '91': 'Vinaphone',
      '94': 'Vinaphone',

      // Mobifone
      '70': 'Mobifone',
      '76': 'Mobifone',
      '77': 'Mobifone',
      '78': 'Mobifone',
      '79': 'Mobifone',
      '89': 'Mobifone',
      '90': 'Mobifone',
      '93': 'Mobifone',

      // Vietnamobile
      '56': 'Vietnamobile',
      '58': 'Vietnamobile',
      '92': 'Vietnamobile',

      // Gmobile
      '59': 'Gmobile',
      '99': 'Gmobile',
    };

    return carrierMap[prefix] || 'Unknown';
  }

  /**
   * Tạo tracking ID duy nhất
   * @param prefix Prefix cho tracking ID (tùy chọn)
   * @returns Tracking ID
   */
  generateTrackingId(prefix?: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return prefix
      ? `${prefix}_${timestamp}_${random}`
      : `zns_${timestamp}_${random}`;
  }

  /**
   * Validate template data
   * @param templateData Dữ liệu template
   * @param requiredParams Danh sách tham số bắt buộc
   * @returns Kết quả validation
   */
  validateTemplateData(
    templateData: Record<string, string>,
    requiredParams: string[],
  ): {
    isValid: boolean;
    errors: string[];
    missingParams: string[];
    extraParams: string[];
  } {
    const errors: string[] = [];
    const missingParams: string[] = [];
    const extraParams: string[] = [];

    // Check missing parameters
    requiredParams.forEach((param) => {
      if (!(param in templateData) || !templateData[param]) {
        missingParams.push(param);
        errors.push(`Thiếu tham số bắt buộc: ${param}`);
      }
    });

    // Check extra parameters
    Object.keys(templateData).forEach((param) => {
      if (!requiredParams.includes(param)) {
        extraParams.push(param);
      }
    });

    // Validate parameter values
    Object.entries(templateData).forEach(([key, value]) => {
      if (typeof value !== 'string') {
        errors.push(`Tham số ${key} phải là chuỗi`);
      } else if (value.length > 1000) {
        errors.push(`Tham số ${key} quá dài (tối đa 1000 ký tự)`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      missingParams,
      extraParams,
    };
  }
}
