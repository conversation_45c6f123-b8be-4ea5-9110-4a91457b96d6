import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueName } from '../../queue';
import { SmsSystemJobData } from './dto/sms-system-job.dto';
import { SmsJobName } from './constants';
import { SmsTemplateService } from './services/sms-template.service';
import { SmsEncryptionService } from './services/sms-encryption.service';
import {
  FptSmsBrandnameService,
  OtpResponse,
} from '../../shared/services/sms/fpt-sms-brandname.service';
import { FptSmsProvider } from '../../shared/services/sms/fpt-sms-provider.service';

/**
 * Processor xử lý queue gửi SMS hệ thống
 */
@Injectable()
@Processor(QueueName.SMS)
export class SmsSystemProcessor extends WorkerHost {
  private readonly logger = new Logger(SmsSystemProcessor.name);

  constructor(
    private readonly smsTemplateService: SmsTemplateService,
    private readonly smsEncryptionService: SmsEncryptionService,
    private readonly fprSmsBrandnameService: FptSmsBrandnameService,
    private readonly fptSmsProvider: FptSmsProvider,
  ) {
    super();
  }

  /**
   * Xử lý job gửi SMS hệ thống
   * @param job Job chứa dữ liệu SMS
   */
  async process(job: Job<SmsSystemJobData, any, string>): Promise<void> {
    this.logger.log(
      `Bắt đầu xử lý job SMS: ${job.id} - Type: ${job.data.type}`,
    );

    try {
      switch (job.name) {
        case SmsJobName.SMS_SYSTEM:
          await this.processSmsSystemJob(job);
          break;
        default:
          this.logger.warn(`Job name không được hỗ trợ: ${job.name}`);
          throw new Error(`Job name không được hỗ trợ: ${job.name}`);
      }

      this.logger.log(`Đã xử lý thành công job SMS: ${job.id}`);
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý job SMS: ${job.id} - ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Kiểm tra số điện thoại có phải là số Việt Nam hay không
   * @param phone Số điện thoại cần kiểm tra
   * @returns true nếu là số Việt Nam, false nếu là số quốc tế
   */
  private isVietnamesePhoneNumber(phone: string): boolean {
    // Loại bỏ tất cả ký tự không phải số
    const cleanNumber = phone.replace(/\D/g, '');

    // Patterns cho số điện thoại Việt Nam
    const vietnamesePatterns = {
      // Số di động: 84 + (3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9]) + 7 số
      mobile: /^(84)(3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9])\d{7}$/,
      // Số di động bắt đầu bằng 0: 0 + (3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9]) + 7 số
      mobileWithZero: /^(0)(3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9])\d{7}$/,
      // Số cố định: 84 + 2[0-9] + 8 số
      landline: /^(84)(2[0-9])\d{8}$/,
      // Số cố định bắt đầu bằng 0: 0 + 2[0-9] + 8 số
      landlineWithZero: /^(0)(2[0-9])\d{8}$/,
    };

    return (
      vietnamesePatterns.mobile.test(cleanNumber) ||
      vietnamesePatterns.mobileWithZero.test(cleanNumber) ||
      vietnamesePatterns.landline.test(cleanNumber) ||
      vietnamesePatterns.landlineWithZero.test(cleanNumber)
    );
  }

  /**
   * Xử lý job gửi SMS hệ thống
   * @param job Job chứa dữ liệu SMS hệ thống
   */
  private async processSmsSystemJob(job: Job<SmsSystemJobData>): Promise<void> {
    const { phone, type, data, userId } = job.data;

    this.logger.log(
      `Xử lý SMS hệ thống - Phone: ${phone}, Type: ${type}${
        userId ? `, UserId: ${userId}` : ''
      }`,
    );

    try {
      // Bước 1: Lấy template SMS từ database dựa vào type
      const processedContent = await this.smsTemplateService.processTemplate(
        type,
        data,
      );

      // Bước 2: Mã hóa nội dung SMS OTP
      const encryptedContent = this.smsEncryptionService.encryptOtpContent(
        processedContent,
        userId,
      );

      this.logger.log(`Đã mã hóa nội dung SMS cho phone: ${phone}`);

      // Bước 3: Kiểm tra số điện thoại và gửi SMS qua API phù hợp
      const isVietnameseNumber = this.isVietnamesePhoneNumber(phone);

      if (isVietnameseNumber) {
        // Gửi SMS trong nước qua API brandname OTP
        this.logger.log(`Gửi SMS trong nước cho số: ${phone}`);
        const result = await this.fprSmsBrandnameService.sendOtp({
          BrandName: process.env.FPT_SMS_BRANDNAME || 'REDAI',
          Phone: phone,
          Message: encryptedContent,
        });

        if (result.MessageId) {
          this.logger.log(
            `Đã gửi SMS OTP trong nước thành công - Phone: ${phone}, MessageId: ${result.MessageId}`,
          );
        } else {
          this.logger.error(
            `Gửi SMS OTP trong nước thất bại - Phone: ${phone}, Error: ${result.error_description || ''}`,
          );
          throw new Error(
            `Gửi SMS OTP trong nước thất bại: ${result.error_description}`,
          );
        }
      } else {
        // Gửi SMS quốc tế qua API international
        this.logger.log(`Gửi SMS quốc tế cho số: ${phone}`);
        const result = await this.fprSmsBrandnameService.sendInternationalSms(
          phone,
          encryptedContent,
          {
            brandName: process.env.FPT_SMS_BRANDNAME || 'REDAI',
            requestId: `sms_system_${job.id}_${Date.now()}`,
          },
        );

        if (result.success && result.messageId) {
          this.logger.log(
            `Đã gửi SMS OTP quốc tế thành công - Phone: ${phone}, MessageId: ${result.messageId}`,
          );
        } else {
          this.logger.error(
            `Gửi SMS OTP quốc tế thất bại - Phone: ${phone}, Error: ${result.errorMessage || ''}`,
          );
          throw new Error(
            `Gửi SMS OTP quốc tế thất bại: ${result.errorMessage}`,
          );
        }
      }
    } catch (error) {
      // Phân loại lỗi để xử lý phù hợp
      let errorMessage = error.message;
      if (error.code === 'ETIMEDOUT') {
        errorMessage = `Timeout khi kết nối đến FPT SMS API: ${error.message}`;
      } else if (error.code === 'ECONNREFUSED') {
        errorMessage = `Không thể kết nối đến FPT SMS API: ${error.message}`;
      } else if (error.code === 'ENOTFOUND') {
        errorMessage = `Không tìm thấy FPT SMS API server: ${error.message}`;
      }

      this.logger.error(
        `Lỗi khi xử lý SMS hệ thống - Phone: ${phone}, Type: ${type}, Error: ${errorMessage}`,
        error.stack,
      );

      // Có thể thêm logic retry hoặc fallback ở đây
      throw new Error(errorMessage);
    }
  }
}
