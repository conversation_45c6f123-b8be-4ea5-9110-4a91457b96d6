import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng model_integration trong cơ sở dữ liệu
 * Bảng ánh xạ nhiều-nhiều giữa models và integration
 */
@Entity('model_integration')
export class ModelIntegration {
  /**
   * ID của model
   */
  @PrimaryColumn({ 
    name: 'model_id', 
    type: 'uuid',
    comment: 'ID của model'
  })
  modelId: string;

  /**
   * ID của integration neu id co user_id != null model 1-n integration(user, employee)
   */
  @PrimaryColumn({ 
    name: 'integration_id', 
    type: 'uuid',
    comment: 'ID của integration'
  })
  integrationId: string;
}
