import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import {
  ZaloCampaignMessageContentDto,
  ZaloCampaignStatus,
  ZaloCampaignType,
  ZaloCampaignZnsContentDto,
} from '../dto/zalo';

/**
 * Entity cho chiến dịch Zalo
 */
@Entity('zalo_campaigns')
export class ZaloCampaign {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'oa_id' })
  oaId: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ type: 'enum', enum: ZaloCampaignType })
  type: ZaloCampaignType;

  @Column({ name: 'segment_id' })
  segmentId: number;

  @Column({
    type: 'enum',
    enum: ZaloCampaignStatus,
    default: ZaloCampaignStatus.DRAFT,
  })
  status: ZaloCampaignStatus;

  @Column({ name: 'scheduled_at', type: 'bigint', nullable: true })
  scheduledAt?: number;

  @Column({ name: 'started_at', type: 'bigint', nullable: true })
  startedAt?: number;

  @Column({ name: 'completed_at', type: 'bigint', nullable: true })
  completedAt?: number;

  @Column({ name: 'message_content', type: 'json', nullable: true })
  messageContent?: ZaloCampaignMessageContentDto;

  @Column({ name: 'zns_content', type: 'json', nullable: true })
  znsContent?: ZaloCampaignZnsContentDto;

  @Column({ name: 'total_recipients', default: 0 })
  totalRecipients: number;

  @Column({ name: 'success_count', default: 0 })
  successCount: number;

  @Column({ name: 'failure_count', default: 0 })
  failureCount: number;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
