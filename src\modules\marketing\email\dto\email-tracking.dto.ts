/**
 * DTO cho dữ liệu tracking email
 */
export interface EmailTrackingDto {
  /**
   * ID tracking duy nhất
   */
  trackingId: string;

  /**
   * ID của campaign
   */
  campaignId: number;

  /**
   * ID của audience
   */
  audienceId: number;

  /**
   * Email người nhận
   */
  email: string;

  /**
   * Loại sự kiện tracking
   */
  eventType: 'sent' | 'delivered' | 'opened' | 'clicked' | 'bounced' | 'failed';

  /**
   * Thời gian xảy ra sự kiện
   */
  timestamp: number;

  /**
   * Thông tin bổ sung (IP, User-Agent, etc.)
   */
  metadata?: {
    ip?: string;
    userAgent?: string;
    referer?: string;
    [key: string]: any;
  };
}

/**
 * DTO cho batch tracking data
 */
export interface EmailTrackingBatchDto {
  /**
   * Danh sách tracking events
   */
  events: EmailTrackingDto[];

  /**
   * Thời gian batch
   */
  batchTimestamp: number;
}
