import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { QueueName } from '../../queue';
import { UsageDeductionProcessor } from '../../processors/webhook/usage-deduction.processor';
import { ZaloWebhookProcessor } from '../../processors/webhook/zalo-webhook.processor';
import { UserAddonUsage } from '../../shared/entities/user-addon-usage.entity';
import { Addon } from '../../shared/entities/addon.entity';

/**
 * Module xử lý webhook events trong worker
 * Chủ yếu để handle usage deduction jobs
 */
@Module({
  imports: [
    // TypeORM entities
    TypeOrmModule.forFeature([
      UserAddonUsage,
      Addon,
    ]),

    // BullMQ queue
    BullModule.registerQueue({
      name: QueueName.WEBHOOK,
    }),
  ],
  providers: [
    UsageDeductionProcessor,
    ZaloWebhookProcessor,
  ],
  exports: [
    UsageDeductionProcessor,
    ZaloWebhookProcessor,
  ],
})
export class WebhookModule {}
