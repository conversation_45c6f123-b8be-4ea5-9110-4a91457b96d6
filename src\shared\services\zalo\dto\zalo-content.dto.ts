import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsNumber,
  IsArray,
  IsUrl,
  Min,
  <PERSON>,
  IsBoolean,
} from 'class-validator';

/**
 * Enum định nghĩa trạng thái nội dung
 */
export enum ZaloContentStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  DELETED = 'deleted',
}

/**
 * Enum định nghĩa trạng thái tiến trình
 */
export enum ZaloContentProcessStatus {
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

/**
 * Enum định nghĩa trạng thái video
 */
export enum ZaloVideoStatus {
  READY = 'ready',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

/**
 * DTO cho yêu cầu tạo nội dung bài viết
 */
export class ZaloContentRequestDto {
  @ApiProperty({
    description: 'Tiêu đề bài viết',
    example: 'Khuyến mãi đặc biệt tháng 12',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Mô tả ngắn gọn',
    example: 'Chương trình khuyến mãi lớn nhất trong năm',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Nội dung bài viết (HTML)',
    example: '<p>Nội dung bài viết với <strong>định dạng HTML</strong></p>',
  })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({
    description: 'URL ảnh đại diện',
    example: 'https://example.com/cover-photo.jpg',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  coverPhotoUrl?: string;

  @ApiProperty({
    description: 'URL video (nếu có)',
    example: 'https://example.com/video.mp4',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  videoUrl?: string;

  @ApiProperty({
    description: 'Trạng thái xuất bản',
    enum: ZaloContentStatus,
    example: ZaloContentStatus.DRAFT,
    required: false,
  })
  @IsOptional()
  @IsEnum(ZaloContentStatus)
  status?: ZaloContentStatus;

  @ApiProperty({
    description: 'Thời gian xuất bản (Unix timestamp)',
    example: 1625097600000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  publishTime?: number;

  @ApiProperty({
    description: 'Tags của bài viết',
    example: ['khuyến mãi', 'sale', 'giảm giá'],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

/**
 * DTO cho phản hồi tạo nội dung
 */
export class ZaloContentResponseDto {
  @ApiProperty({
    description: 'ID của nội dung',
    example: 'content_123456789',
  })
  contentId: string;

  @ApiProperty({
    description: 'Trạng thái tạo nội dung',
    enum: ['success', 'processing', 'failed'],
    example: 'success',
  })
  status: 'success' | 'processing' | 'failed';

  @ApiProperty({
    description: 'ID tiến trình (nếu đang xử lý)',
    example: 'process_123456789',
    required: false,
  })
  processId?: string;

  @ApiProperty({
    description: 'Thông báo',
    example: 'Nội dung đã được tạo thành công',
    required: false,
  })
  message?: string;

  @ApiProperty({
    description: 'URL xem trước (nếu có)',
    example: 'https://zalo.me/oa/preview/content_123456789',
    required: false,
  })
  previewUrl?: string;
}

/**
 * DTO cho chi tiết nội dung
 */
export class ZaloContentDetailDto {
  @ApiProperty({
    description: 'ID của nội dung',
    example: 'content_123456789',
  })
  contentId: string;

  @ApiProperty({
    description: 'Tiêu đề bài viết',
    example: 'Khuyến mãi đặc biệt tháng 12',
  })
  title: string;

  @ApiProperty({
    description: 'Mô tả ngắn gọn',
    example: 'Chương trình khuyến mãi lớn nhất trong năm',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Nội dung bài viết',
    example: '<p>Nội dung bài viết với <strong>định dạng HTML</strong></p>',
  })
  content: string;

  @ApiProperty({
    description: 'URL ảnh đại diện',
    example: 'https://example.com/cover-photo.jpg',
    required: false,
  })
  coverPhotoUrl?: string;

  @ApiProperty({
    description: 'URL video (nếu có)',
    example: 'https://example.com/video.mp4',
    required: false,
  })
  videoUrl?: string;

  @ApiProperty({
    description: 'Trạng thái',
    enum: ZaloContentStatus,
    example: ZaloContentStatus.PUBLISHED,
  })
  status: ZaloContentStatus;

  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1625097600000,
  })
  createdTime: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1625097600000,
  })
  updatedTime: number;

  @ApiProperty({
    description: 'Thời gian xuất bản (Unix timestamp)',
    example: 1625097600000,
    required: false,
  })
  publishTime?: number;

  @ApiProperty({
    description: 'Số lượt xem',
    example: 1500,
    required: false,
  })
  viewCount?: number;

  @ApiProperty({
    description: 'Số lượt thích',
    example: 120,
    required: false,
  })
  likeCount?: number;

  @ApiProperty({
    description: 'Số lượt chia sẻ',
    example: 45,
    required: false,
  })
  shareCount?: number;

  @ApiProperty({
    description: 'Tags của bài viết',
    example: ['khuyến mãi', 'sale', 'giảm giá'],
    type: [String],
    required: false,
  })
  tags?: string[];

  @ApiProperty({
    description: 'URL xem trước',
    example: 'https://zalo.me/oa/preview/content_123456789',
    required: false,
  })
  previewUrl?: string;
}

/**
 * DTO cho danh sách nội dung
 */
export class ZaloContentListDto {
  @ApiProperty({
    description: 'Danh sách nội dung',
    type: [ZaloContentDetailDto],
  })
  contents: ZaloContentDetailDto[];

  @ApiProperty({
    description: 'Tổng số nội dung',
    example: 150,
  })
  total: number;

  @ApiProperty({
    description: 'Có còn dữ liệu tiếp theo không',
    example: true,
  })
  hasMore: boolean;

  @ApiProperty({
    description: 'Offset tiếp theo',
    example: 20,
    required: false,
  })
  nextOffset?: number;
}

/**
 * DTO cho query danh sách nội dung
 */
export class ZaloContentListQueryDto {
  @ApiProperty({
    description: 'Trạng thái nội dung',
    enum: ZaloContentStatus,
    example: ZaloContentStatus.PUBLISHED,
    required: false,
  })
  @IsOptional()
  @IsEnum(ZaloContentStatus)
  status?: ZaloContentStatus;

  @ApiProperty({
    description: 'Số lượng kết quả tối đa',
    example: 20,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiProperty({
    description: 'Offset cho phân trang',
    example: 0,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  offset?: number;

  @ApiProperty({
    description: 'Thời gian bắt đầu (Unix timestamp)',
    example: 1625097600000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  fromTime?: number;

  @ApiProperty({
    description: 'Thời gian kết thúc (Unix timestamp)',
    example: 1625184000000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  toTime?: number;

  @ApiProperty({
    description: 'Danh sách tags để lọc',
    example: ['khuyến mãi', 'sale'],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

/**
 * DTO cho yêu cầu upload video
 */
export class ZaloVideoUploadRequestDto {
  @ApiProperty({
    description: 'Tên file video',
    example: 'promotional-video.mp4',
  })
  @IsString()
  @IsNotEmpty()
  filename: string;

  @ApiProperty({
    description: 'Kích thước file (bytes)',
    example: 52428800,
    maximum: 104857600, // 100MB
  })
  @IsNumber()
  @Min(1)
  @Max(104857600) // 100MB
  fileSize: number;

  @ApiProperty({
    description: 'Loại MIME của video',
    example: 'video/mp4',
    enum: ['video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/avi'],
  })
  @IsString()
  @IsNotEmpty()
  mimeType: string;

  @ApiProperty({
    description: 'Mô tả video',
    example: 'Video quảng cáo sản phẩm mới',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}

/**
 * DTO cho phản hồi upload video
 */
export class ZaloVideoUploadResponseDto {
  @ApiProperty({
    description: 'ID của video',
    example: 'video_123456789',
  })
  videoId: string;

  @ApiProperty({
    description: 'URL upload',
    example: 'https://upload.zalo.me/v1/video/upload',
  })
  uploadUrl: string;

  @ApiProperty({
    description: 'Token upload',
    example: 'upload_token_abc123',
  })
  uploadToken: string;

  @ApiProperty({
    description: 'Thời gian hết hạn upload (Unix timestamp)',
    example: 1625101200000,
  })
  expiresTime: number;

  @ApiProperty({
    description: 'Trạng thái',
    enum: ZaloVideoStatus,
    example: ZaloVideoStatus.READY,
  })
  status: ZaloVideoStatus;
}

/**
 * DTO cho tiến trình xử lý
 */
export class ZaloContentProcessDto {
  @ApiProperty({
    description: 'ID tiến trình',
    example: 'process_123456789',
  })
  processId: string;

  @ApiProperty({
    description: 'Trạng thái tiến trình',
    enum: ZaloContentProcessStatus,
    example: ZaloContentProcessStatus.PROCESSING,
  })
  status: ZaloContentProcessStatus;

  @ApiProperty({
    description: 'Phần trăm hoàn thành (0-100)',
    example: 75,
    minimum: 0,
    maximum: 100,
  })
  progress: number;

  @ApiProperty({
    description: 'Thông báo',
    example: 'Đang xử lý nội dung...',
    required: false,
  })
  message?: string;

  @ApiProperty({
    description: 'ID nội dung (nếu hoàn thành)',
    example: 'content_123456789',
    required: false,
  })
  contentId?: string;

  @ApiProperty({
    description: 'Thông tin lỗi (nếu có)',
    required: false,
  })
  error?: {
    code: number;
    message: string;
  };

  @ApiProperty({
    description: 'Thời gian bắt đầu (Unix timestamp)',
    example: 1625097600000,
  })
  startTime: number;

  @ApiProperty({
    description: 'Thời gian hoàn thành (Unix timestamp)',
    example: 1625098200000,
    required: false,
  })
  endTime?: number;
}

/**
 * DTO cho yêu cầu cập nhật nội dung
 */
export class ZaloContentUpdateRequestDto {
  @ApiProperty({
    description: 'Tiêu đề bài viết',
    example: 'Khuyến mãi đặc biệt tháng 12 - Cập nhật',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    description: 'Mô tả ngắn gọn',
    example: 'Chương trình khuyến mãi được cập nhật',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Nội dung bài viết (HTML)',
    example: '<p>Nội dung được cập nhật</p>',
    required: false,
  })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({
    description: 'URL ảnh đại diện',
    example: 'https://example.com/new-cover-photo.jpg',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  coverPhotoUrl?: string;

  @ApiProperty({
    description: 'URL video (nếu có)',
    example: 'https://example.com/new-video.mp4',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  videoUrl?: string;

  @ApiProperty({
    description: 'Trạng thái xuất bản',
    enum: ZaloContentStatus,
    example: ZaloContentStatus.PUBLISHED,
    required: false,
  })
  @IsOptional()
  @IsEnum(ZaloContentStatus)
  status?: ZaloContentStatus;

  @ApiProperty({
    description: 'Thời gian xuất bản (Unix timestamp)',
    example: 1625097600000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  publishTime?: number;

  @ApiProperty({
    description: 'Tags của bài viết',
    example: ['khuyến mãi', 'sale', 'cập nhật'],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

/**
 * DTO cho yêu cầu tìm kiếm nội dung
 */
export class ZaloContentSearchQueryDto {
  @ApiProperty({
    description: 'Từ khóa tìm kiếm',
    example: 'khuyến mãi',
  })
  @IsString()
  @IsNotEmpty()
  query: string;

  @ApiProperty({
    description: 'Trạng thái nội dung',
    enum: ZaloContentStatus,
    example: ZaloContentStatus.PUBLISHED,
    required: false,
  })
  @IsOptional()
  @IsEnum(ZaloContentStatus)
  status?: ZaloContentStatus;

  @ApiProperty({
    description: 'Số lượng kết quả tối đa',
    example: 20,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiProperty({
    description: 'Offset cho phân trang',
    example: 0,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  offset?: number;

  @ApiProperty({
    description: 'Danh sách tags để lọc',
    example: ['khuyến mãi', 'sale'],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

/**
 * DTO cho thống kê nội dung theo ngày
 */
export class ZaloContentDailyStatDto {
  @ApiProperty({
    description: 'Ngày (YYYY-MM-DD)',
    example: '2023-07-15',
  })
  date: string;

  @ApiProperty({
    description: 'Số lượt xem',
    example: 150,
  })
  views: number;

  @ApiProperty({
    description: 'Số lượt thích',
    example: 25,
  })
  likes: number;

  @ApiProperty({
    description: 'Số lượt chia sẻ',
    example: 8,
  })
  shares: number;

  @ApiProperty({
    description: 'Số lượt bình luận',
    example: 12,
  })
  comments: number;
}

/**
 * DTO cho thống kê nội dung
 */
export class ZaloContentStatisticsDto {
  @ApiProperty({
    description: 'ID của nội dung',
    example: 'content_123456789',
  })
  contentId: string;

  @ApiProperty({
    description: 'Tổng số lượt xem',
    example: 1500,
  })
  viewCount: number;

  @ApiProperty({
    description: 'Tổng số lượt thích',
    example: 120,
  })
  likeCount: number;

  @ApiProperty({
    description: 'Tổng số lượt chia sẻ',
    example: 45,
  })
  shareCount: number;

  @ApiProperty({
    description: 'Tổng số lượt bình luận',
    example: 78,
  })
  commentCount: number;

  @ApiProperty({
    description: 'Tỷ lệ tương tác (%)',
    example: 8.5,
  })
  engagementRate: number;

  @ApiProperty({
    description: 'Thống kê theo ngày',
    type: [ZaloContentDailyStatDto],
  })
  dailyStats: ZaloContentDailyStatDto[];
}

/**
 * DTO cho tag
 */
export class ZaloContentTagDto {
  @ApiProperty({
    description: 'Tên tag',
    example: 'khuyến mãi',
  })
  name: string;

  @ApiProperty({
    description: 'Số lượng nội dung có tag này',
    example: 25,
  })
  count: number;
}

/**
 * DTO cho danh sách tags
 */
export class ZaloContentTagsResponseDto {
  @ApiProperty({
    description: 'Danh sách tags',
    type: [ZaloContentTagDto],
  })
  tags: ZaloContentTagDto[];
}

/**
 * DTO cho yêu cầu xuất bản nội dung
 */
export class ZaloContentPublishRequestDto {
  @ApiProperty({
    description: 'Thời gian xuất bản (Unix timestamp)',
    example: 1625097600000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  publishTime?: number;
}

/**
 * DTO cho phản hồi chung
 */
export class ZaloContentCommonResponseDto {
  @ApiProperty({
    description: 'Trạng thái thành công',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Thông báo',
    example: 'Thao tác thành công',
    required: false,
  })
  message?: string;
}

/**
 * DTO cho upload file video
 */
export class ZaloVideoFileUploadDto {
  @ApiProperty({
    description: 'File video',
    type: 'string',
    format: 'binary',
  })
  file: any;

  @ApiProperty({
    description: 'Token upload',
    example: 'upload_token_abc123',
  })
  @IsString()
  @IsNotEmpty()
  uploadToken: string;
}

/**
 * DTO cho phản hồi upload file video
 */
export class ZaloVideoFileUploadResponseDto {
  @ApiProperty({
    description: 'Trạng thái upload thành công',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'URL video sau khi upload',
    example: 'https://video.zalo.me/v1/video_123456789.mp4',
    required: false,
  })
  videoUrl?: string;

  @ApiProperty({
    description: 'Thông báo',
    example: 'Upload video thành công',
    required: false,
  })
  message?: string;
}

// ==================== VIDEO CONTENT DTOs ====================

/**
 * DTO cho yêu cầu tạo nội dung video
 */
export class ZaloVideoContentRequestDto {
  @ApiProperty({
    description: 'Tiêu đề video',
    example: 'Video giới thiệu sản phẩm mới',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Mô tả video',
    example: 'Video giới thiệu chi tiết về sản phẩm mới nhất của chúng tôi',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'URL video đã upload',
    example: 'https://video.zalo.me/v1/video_123456789.mp4',
  })
  @IsString()
  @IsNotEmpty()
  @IsUrl()
  videoUrl: string;

  @ApiProperty({
    description: 'URL ảnh thumbnail',
    example: 'https://example.com/thumbnail.jpg',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  thumbnailUrl?: string;

  @ApiProperty({
    description: 'Trạng thái xuất bản',
    enum: ZaloContentStatus,
    example: ZaloContentStatus.DRAFT,
    required: false,
  })
  @IsOptional()
  @IsEnum(ZaloContentStatus)
  status?: ZaloContentStatus;

  @ApiProperty({
    description: 'Thời gian xuất bản (Unix timestamp)',
    example: 1625097600000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  publishTime?: number;

  @ApiProperty({
    description: 'Tags của video',
    example: ['sản phẩm', 'giới thiệu', 'video'],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

/**
 * DTO cho phản hồi tạo nội dung video
 */
export class ZaloVideoContentResponseDto {
  @ApiProperty({
    description: 'ID của nội dung video',
    example: 'video_content_123456789',
  })
  videoContentId: string;

  @ApiProperty({
    description: 'Trạng thái tạo nội dung',
    enum: ['success', 'processing', 'failed'],
    example: 'success',
  })
  status: 'success' | 'processing' | 'failed';

  @ApiProperty({
    description: 'ID tiến trình (nếu đang xử lý)',
    example: 'process_123456789',
    required: false,
  })
  processId?: string;

  @ApiProperty({
    description: 'Thông báo',
    example: 'Nội dung video đã được tạo thành công',
    required: false,
  })
  message?: string;

  @ApiProperty({
    description: 'URL xem trước (nếu có)',
    example: 'https://zalo.me/oa/preview/video_content_123456789',
    required: false,
  })
  previewUrl?: string;
}

/**
 * DTO cho chi tiết nội dung video
 */
export class ZaloVideoContentDetailDto {
  @ApiProperty({
    description: 'ID của nội dung video',
    example: 'video_content_123456789',
  })
  videoContentId: string;

  @ApiProperty({
    description: 'Tiêu đề video',
    example: 'Video giới thiệu sản phẩm mới',
  })
  title: string;

  @ApiProperty({
    description: 'Mô tả video',
    example: 'Video giới thiệu chi tiết về sản phẩm mới nhất',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'URL video',
    example: 'https://video.zalo.me/v1/video_123456789.mp4',
  })
  videoUrl: string;

  @ApiProperty({
    description: 'URL ảnh thumbnail',
    example: 'https://example.com/thumbnail.jpg',
    required: false,
  })
  thumbnailUrl?: string;

  @ApiProperty({
    description: 'Thời lượng video (giây)',
    example: 120,
    required: false,
  })
  duration?: number;

  @ApiProperty({
    description: 'Kích thước file (bytes)',
    example: 52428800,
    required: false,
  })
  fileSize?: number;

  @ApiProperty({
    description: 'Độ phân giải video',
    required: false,
  })
  resolution?: {
    width: number;
    height: number;
  };

  @ApiProperty({
    description: 'Trạng thái',
    enum: ZaloContentStatus,
    example: ZaloContentStatus.PUBLISHED,
  })
  status: ZaloContentStatus;

  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1625097600000,
  })
  createdTime: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1625097600000,
  })
  updatedTime: number;

  @ApiProperty({
    description: 'Thời gian xuất bản (Unix timestamp)',
    example: 1625097600000,
    required: false,
  })
  publishTime?: number;

  @ApiProperty({
    description: 'Số lượt xem',
    example: 2500,
    required: false,
  })
  viewCount?: number;

  @ApiProperty({
    description: 'Số lượt thích',
    example: 180,
    required: false,
  })
  likeCount?: number;

  @ApiProperty({
    description: 'Số lượt chia sẻ',
    example: 65,
    required: false,
  })
  shareCount?: number;

  @ApiProperty({
    description: 'Số lượt bình luận',
    example: 95,
    required: false,
  })
  commentCount?: number;

  @ApiProperty({
    description: 'Tags của video',
    example: ['sản phẩm', 'giới thiệu', 'video'],
    type: [String],
    required: false,
  })
  tags?: string[];

  @ApiProperty({
    description: 'URL xem trước',
    example: 'https://zalo.me/oa/preview/video_content_123456789',
    required: false,
  })
  previewUrl?: string;
}

/**
 * DTO cho danh sách nội dung video
 */
export class ZaloVideoContentListDto {
  @ApiProperty({
    description: 'Danh sách nội dung video',
    type: [ZaloVideoContentDetailDto],
  })
  videoContents: ZaloVideoContentDetailDto[];

  @ApiProperty({
    description: 'Tổng số nội dung video',
    example: 85,
  })
  total: number;

  @ApiProperty({
    description: 'Có còn dữ liệu tiếp theo không',
    example: true,
  })
  hasMore: boolean;

  @ApiProperty({
    description: 'Offset tiếp theo',
    example: 20,
    required: false,
  })
  nextOffset?: number;
}

/**
 * DTO cho query danh sách nội dung video
 */
export class ZaloVideoContentListQueryDto {
  @ApiProperty({
    description: 'Trạng thái nội dung video',
    enum: ZaloContentStatus,
    example: ZaloContentStatus.PUBLISHED,
    required: false,
  })
  @IsOptional()
  @IsEnum(ZaloContentStatus)
  status?: ZaloContentStatus;

  @ApiProperty({
    description: 'Số lượng kết quả tối đa',
    example: 20,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiProperty({
    description: 'Offset cho phân trang',
    example: 0,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  offset?: number;

  @ApiProperty({
    description: 'Thời gian bắt đầu (Unix timestamp)',
    example: 1625097600000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  fromTime?: number;

  @ApiProperty({
    description: 'Thời gian kết thúc (Unix timestamp)',
    example: 1625184000000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  toTime?: number;

  @ApiProperty({
    description: 'Danh sách tags để lọc',
    example: ['sản phẩm', 'video'],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

/**
 * DTO cho yêu cầu cập nhật nội dung video
 */
export class ZaloVideoContentUpdateRequestDto {
  @ApiProperty({
    description: 'Tiêu đề video',
    example: 'Video giới thiệu sản phẩm mới - Cập nhật',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    description: 'Mô tả video',
    example: 'Mô tả video đã được cập nhật',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'URL video đã upload',
    example: 'https://video.zalo.me/v1/video_new_123456789.mp4',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  videoUrl?: string;

  @ApiProperty({
    description: 'URL ảnh thumbnail',
    example: 'https://example.com/new-thumbnail.jpg',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  thumbnailUrl?: string;

  @ApiProperty({
    description: 'Trạng thái xuất bản',
    enum: ZaloContentStatus,
    example: ZaloContentStatus.PUBLISHED,
    required: false,
  })
  @IsOptional()
  @IsEnum(ZaloContentStatus)
  status?: ZaloContentStatus;

  @ApiProperty({
    description: 'Thời gian xuất bản (Unix timestamp)',
    example: 1625097600000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  publishTime?: number;

  @ApiProperty({
    description: 'Tags của video',
    example: ['sản phẩm', 'giới thiệu', 'cập nhật'],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

/**
 * DTO cho yêu cầu tìm kiếm nội dung video
 */
export class ZaloVideoContentSearchQueryDto {
  @ApiProperty({
    description: 'Từ khóa tìm kiếm',
    example: 'sản phẩm',
  })
  @IsString()
  @IsNotEmpty()
  query: string;

  @ApiProperty({
    description: 'Trạng thái nội dung video',
    enum: ZaloContentStatus,
    example: ZaloContentStatus.PUBLISHED,
    required: false,
  })
  @IsOptional()
  @IsEnum(ZaloContentStatus)
  status?: ZaloContentStatus;

  @ApiProperty({
    description: 'Số lượng kết quả tối đa',
    example: 20,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiProperty({
    description: 'Offset cho phân trang',
    example: 0,
    minimum: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  offset?: number;

  @ApiProperty({
    description: 'Danh sách tags để lọc',
    example: ['sản phẩm', 'video'],
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    description: 'Thời lượng tối thiểu (giây)',
    example: 30,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  durationMin?: number;

  @ApiProperty({
    description: 'Thời lượng tối đa (giây)',
    example: 300,
    minimum: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  durationMax?: number;
}

/**
 * DTO cho thống kê nội dung video theo ngày
 */
export class ZaloVideoContentDailyStatDto {
  @ApiProperty({
    description: 'Ngày (YYYY-MM-DD)',
    example: '2023-07-15',
  })
  date: string;

  @ApiProperty({
    description: 'Số lượt xem',
    example: 250,
  })
  views: number;

  @ApiProperty({
    description: 'Số lượt thích',
    example: 35,
  })
  likes: number;

  @ApiProperty({
    description: 'Số lượt chia sẻ',
    example: 12,
  })
  shares: number;

  @ApiProperty({
    description: 'Số lượt bình luận',
    example: 18,
  })
  comments: number;

  @ApiProperty({
    description: 'Tổng thời gian xem (giây)',
    example: 15000,
  })
  watchTime: number;
}

/**
 * DTO cho thống kê nội dung video
 */
export class ZaloVideoContentStatisticsDto {
  @ApiProperty({
    description: 'ID của nội dung video',
    example: 'video_content_123456789',
  })
  videoContentId: string;

  @ApiProperty({
    description: 'Tổng số lượt xem',
    example: 2500,
  })
  viewCount: number;

  @ApiProperty({
    description: 'Tổng số lượt thích',
    example: 180,
  })
  likeCount: number;

  @ApiProperty({
    description: 'Tổng số lượt chia sẻ',
    example: 65,
  })
  shareCount: number;

  @ApiProperty({
    description: 'Tổng số lượt bình luận',
    example: 95,
  })
  commentCount: number;

  @ApiProperty({
    description: 'Tổng thời gian xem (giây)',
    example: 180000,
  })
  watchTimeTotal: number;

  @ApiProperty({
    description: 'Thời gian xem trung bình (giây)',
    example: 72,
  })
  watchTimeAverage: number;

  @ApiProperty({
    description: 'Tỷ lệ tương tác (%)',
    example: 12.8,
  })
  engagementRate: number;

  @ApiProperty({
    description: 'Tỷ lệ xem hoàn thành (%)',
    example: 68.5,
  })
  completionRate: number;

  @ApiProperty({
    description: 'Thống kê theo ngày',
    type: [ZaloVideoContentDailyStatDto],
  })
  dailyStats: ZaloVideoContentDailyStatDto[];
}

/**
 * DTO cho yêu cầu xuất bản nội dung video
 */
export class ZaloVideoContentPublishRequestDto {
  @ApiProperty({
    description: 'Thời gian xuất bản (Unix timestamp)',
    example: 1625097600000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  publishTime?: number;
}
