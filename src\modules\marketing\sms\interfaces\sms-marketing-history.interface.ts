import { SenderTypeEnum } from "../../enums";

/**
 * Interface cho dữ liệu lịch sử SMS marketing
 */
export interface SmsMarketingHistoryData {
  /**
   * ID của campaign SMS marketing
   */
  campaignId: number;

  /**
   * ID của tin nhắn đã gửi (từ FPT SMS API)
   */
  messageId?: string;

  /**
   * Số điện thoại đã gửi tin
   */
  phone: string;

  /**
   * Tên brandname đã gửi tin
   */
  brandName?: string;

  /**
   * Nội dung tin nhắn đã gửi
   */
  message: string;

  /**
   * ID của đối tác đã gửi tin (từ FPT SMS API)
   */
  partnerId?: string;

  /**
   * Nhà mạng của số điện thoại trên (từ FPT SMS API)
   */
  telco?: string;

  /**
   * Loại chiến dịch SMS (OTP hoặc CAMPAIGN_ADS)
   */
  campaignType: string;

  /**
   * Trạng thái gửi SMS (SUCCESS, FAILED)
   */
  status: 'SUCCESS' | 'FAILED';

  /**
   * Thông báo lỗi nếu gửi thất bại
   */
  errorMessage?: string;

  /**
   * Mã lỗi từ FPT SMS API
   */
  errorCode?: number;

  /**
   * Kiểm tra số điện thoại có phải Việt Nam hay quốc tế
   */
  isVietnameseNumber: boolean;

  /**
   * Thời gian gửi SMS (Unix timestamp)
   */
  sentAt: number;

  /**
   * Loại người gửi SMS (USER hoặc EMPLOYEE)
   */
  senderType?: SenderTypeEnum;

  /**
   * ID của người gửi SMS (user_id hoặc employee_id)
   */
  senderId?: number;
}

/**
 * Interface cho batch insert lịch sử SMS marketing
 */
export interface SmsMarketingHistoryBatch {
  /**
   * Danh sách lịch sử SMS marketing cần lưu
   */
  histories: SmsMarketingHistoryData[];

  /**
   * Kích thước batch (mặc định 100)
   */
  batchSize?: number;
}

/**
 * Interface cho kết quả gửi SMS
 */
export interface SmsSendResult {
  /**
   * Trạng thái gửi SMS
   */
  success: boolean;

  /**
   * ID của tin nhắn đã gửi (từ FPT SMS API)
   */
  messageId?: string;

  /**
   * Tên brandname đã gửi tin
   */
  brandName?: string;

  /**
   * ID của đối tác đã gửi tin (từ FPT SMS API)
   */
  partnerId?: string;

  /**
   * Nhà mạng của số điện thoại trên (từ FPT SMS API)
   */
  telco?: string;

  /**
   * Thông báo lỗi nếu gửi thất bại
   */
  errorMessage?: string;

  /**
   * Mã lỗi từ FPT SMS API
   */
  errorCode?: number;

  /**
   * Kiểm tra số điện thoại có phải Việt Nam hay quốc tế
   */
  isVietnameseNumber: boolean;
}
