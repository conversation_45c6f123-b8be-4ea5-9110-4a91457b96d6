import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloService } from './zalo.service';
import {
  ZaloCreateGroupRequest,
  ZaloCreateGroupResult,
  ZaloUpdateGroupRequest,
  ZaloUpdateGroupAvatarRequest,
  ZaloInviteMemberRequest,
  ZaloPendingMember,
  ZaloMemberActionRequest,
  ZaloAdminActionRequest,
  ZaloRemoveMemberRequest,
  ZaloGroupQuota,
  ZaloRecentChat,
  ZaloGroupConversationMessage,
  ZaloGroupInfo,
  ZaloGroupMember,
} from './zalo.interface';

/**
 * Service xử lý các API quản lý nhóm chat của Zalo Official Account (Group Management Framework - GMF)
 *
 * ĐIỀU KIỆN SỬ DỤNG ZALO GMF GROUP MANAGEMENT:
 *
 * 1. ĐIỀU KIỆN CHUNG:
 *    - OA phải được cấp quyền sử dụng tính năng GMF (Group Message Framework)
 *    - Access token phải có scope "manage_group" và "group_message"
 *    - OA phải có trạng thái hoạt động và được xác thực
 *    - Tuân thủ các giới hạn về số lượng nhóm và thành viên
 *
 * 2. TẠO NHÓM MỚI:
 *    - Tên nhóm: bắt buộc, tối đa 100 ký tự, không chứa ký tự đặc biệt
 *    - Mô tả: tùy chọn, tối đa 500 ký tự
 *    - Avatar: tùy chọn, định dạng JPG/PNG, tối đa 5MB
 *    - Thành viên ban đầu: tối đa 200 người, phải là user đã tương tác với OA
 *    - OA tự động trở thành admin của nhóm
 *
 * 3. QUẢN LÝ THÀNH VIÊN:
 *    - Chỉ admin mới có quyền mời/xóa thành viên
 *    - Mời thành viên: tối đa 50 người mỗi lần, user phải đã tương tác với OA
 *    - Xóa thành viên: không thể xóa admin khác, phải có ít nhất 1 admin
 *    - Thành viên có thể tự rời nhóm
 *
 * 4. QUẢN LÝ ADMIN:
 *    - Chỉ admin hiện tại mới có quyền thêm/xóa admin khác
 *    - Phải có ít nhất 1 admin trong nhóm
 *    - OA luôn có quyền admin và không thể bị xóa
 *
 * 5. GIỚI HẠN VÀ RÀNG BUỘC:
 *    - Số nhóm tối đa: theo gói dịch vụ (thường 10-100 nhóm)
 *    - Số thành viên tối đa mỗi nhóm: 200 người
 *    - Tần suất tạo nhóm: tối đa 10 nhóm/ngày
 *    - Tần suất mời thành viên: tối đa 500 lời mời/ngày
 */
@Injectable()
export class ZaloGmfGroupManagementService {
  private readonly logger = new Logger(ZaloGmfGroupManagementService.name);
  private readonly baseApiUrl = 'https://openapi.zalo.me/v2.0/oa/group';

  constructor(
    private readonly zaloService: ZaloService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Tạo nhóm chat mới
   *
   * ĐIỀU KIỆN:
   * - OA phải có quyền tạo nhóm
   * - Tên nhóm không được trống và tối đa 100 ký tự
   * - Danh sách thành viên ban đầu tối đa 200 người
   * - Tất cả thành viên phải đã tương tác với OA trước đó
   * - Chưa vượt quá giới hạn số nhóm cho phép
   *
   * @param accessToken Access token của Official Account
   * @param groupData Thông tin nhóm cần tạo
   * @returns Thông tin nhóm vừa tạo
   */
  async createGroup(
    accessToken: string,
    groupData: ZaloCreateGroupRequest,
  ): Promise<ZaloCreateGroupResult> {
    try {
      this.logger.debug(`Creating new group: ${groupData.group_name}`);

      // Validate input
      if (!groupData.group_name || groupData.group_name.trim().length === 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Tên nhóm không được để trống',
        );
      }

      if (groupData.group_name.length > 100) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Tên nhóm không được vượt quá 100 ký tự',
        );
      }

      if (groupData.member_uids.length > 200) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Số lượng thành viên ban đầu không được vượt quá 200 người',
        );
      }

      const data = {
        group_name: groupData.group_name.trim(),
        ...(groupData.description && {
          description: groupData.description.trim(),
        }),
        ...(groupData.avatar_url && { avatar_url: groupData.avatar_url }),
        member_uids: groupData.member_uids,
      };

      return await this.zaloService.post<ZaloCreateGroupResult>(
        `${this.baseApiUrl}/create`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(`Error creating group: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tạo nhóm chat',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết nhóm
   *
   * ĐIỀU KIỆN:
   * - OA phải là thành viên của nhóm
   * - Nhóm phải đang hoạt động (không bị xóa)
   *
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm
   * @returns Thông tin chi tiết nhóm
   */
  async getGroupInfo(
    accessToken: string,
    groupId: string,
  ): Promise<ZaloGroupInfo> {
    try {
      this.logger.debug(`Getting group info for group ${groupId}`);

      return await this.zaloService.get<ZaloGroupInfo>(
        `${this.baseApiUrl}/${groupId}/info`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(
        `Error getting group info: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin nhóm',
      );
    }
  }

  /**
   * Cập nhật thông tin nhóm
   *
   * ĐIỀU KIỆN:
   * - OA phải là admin của nhóm
   * - Tên nhóm mới (nếu có) tối đa 100 ký tự
   * - Mô tả mới (nếu có) tối đa 500 ký tự
   *
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm
   * @param updateData Thông tin cần cập nhật
   * @returns Kết quả cập nhật
   */
  async updateGroupInfo(
    accessToken: string,
    groupId: string,
    updateData: ZaloUpdateGroupRequest,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.debug(`Updating group info for group ${groupId}`);

      // Validate input
      if (updateData.group_name && updateData.group_name.length > 100) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Tên nhóm không được vượt quá 100 ký tự',
        );
      }

      if (updateData.description && updateData.description.length > 500) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Mô tả nhóm không được vượt quá 500 ký tự',
        );
      }

      const data = {
        ...(updateData.group_name && {
          group_name: updateData.group_name.trim(),
        }),
        ...(updateData.description && {
          description: updateData.description.trim(),
        }),
      };

      return await this.zaloService.put<{ success: boolean }>(
        `${this.baseApiUrl}/${groupId}/info`,
        accessToken,
        data,
      );
    } catch (error) {
      this.logger.error(
        `Error updating group info: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi cập nhật thông tin nhóm',
      );
    }
  }

  /**
   * Cập nhật avatar nhóm
   *
   * ĐIỀU KIỆN:
   * - OA phải là admin của nhóm
   * - Avatar phải được upload trước qua API upload
   * - Định dạng: JPG, PNG
   * - Kích thước tối đa: 5MB
   *
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm
   * @param avatarData Thông tin avatar mới
   * @returns Kết quả cập nhật
   */
  async updateGroupAvatar(
    accessToken: string,
    groupId: string,
    avatarData: ZaloUpdateGroupAvatarRequest,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.debug(`Updating group avatar for group ${groupId}`);

      return await this.zaloService.put<{ success: boolean }>(
        `${this.baseApiUrl}/${groupId}/avatar`,
        accessToken,
        avatarData,
      );
    } catch (error) {
      this.logger.error(
        `Error updating group avatar: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi cập nhật avatar nhóm',
      );
    }
  }

  /**
   * Mời thành viên vào nhóm
   *
   * ĐIỀU KIỆN:
   * - OA phải là admin của nhóm
   * - Tối đa 50 người mỗi lần mời
   * - Tất cả user được mời phải đã tương tác với OA
   * - Nhóm chưa đạt giới hạn tối đa thành viên (200 người)
   * - Không vượt quá giới hạn lời mời hàng ngày (500 lời mời/ngày)
   *
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm
   * @param inviteData Danh sách user ID cần mời
   * @returns Kết quả mời thành viên
   */
  async inviteMembers(
    accessToken: string,
    groupId: string,
    inviteData: ZaloInviteMemberRequest,
  ): Promise<{ success: boolean; invited_count: number }> {
    try {
      this.logger.debug(
        `Inviting ${inviteData.member_uids.length} members to group ${groupId}`,
      );

      // Validate input
      if (inviteData.member_uids.length === 0) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Danh sách thành viên không được để trống',
        );
      }

      if (inviteData.member_uids.length > 50) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Không thể mời quá 50 người trong một lần',
        );
      }

      return await this.zaloService.post<{
        success: boolean;
        invited_count: number;
      }>(`${this.baseApiUrl}/${groupId}/invite`, accessToken, inviteData);
    } catch (error) {
      this.logger.error(
        `Error inviting members: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi mời thành viên vào nhóm',
      );
    }
  }

  /**
   * Lấy danh sách thành viên chờ duyệt
   *
   * ĐIỀU KIỆN:
   * - OA phải là admin của nhóm
   * - Nhóm phải có cài đặt yêu cầu duyệt thành viên mới
   *
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm
   * @param offset Offset để phân trang (mặc định: 0)
   * @param count Số lượng tối đa trả về (mặc định: 20, tối đa: 50)
   * @returns Danh sách thành viên chờ duyệt
   */
  async getPendingMembers(
    accessToken: string,
    groupId: string,
    offset: number = 0,
    count: number = 20,
  ): Promise<{ members: ZaloPendingMember[]; total: number }> {
    try {
      this.logger.debug(`Getting pending members for group ${groupId}`);

      const params = new URLSearchParams();
      params.append('offset', offset.toString());
      params.append('count', Math.min(Math.max(count, 1), 50).toString());

      return await this.zaloService.get<{
        members: ZaloPendingMember[];
        total: number;
      }>(
        `${this.baseApiUrl}/${groupId}/pending?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(
        `Error getting pending members: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách thành viên chờ duyệt',
      );
    }
  }

  /**
   * Duyệt thành viên vào nhóm
   *
   * ĐIỀU KIỆN:
   * - OA phải là admin của nhóm
   * - Các user ID phải có trong danh sách chờ duyệt
   * - Nhóm chưa đạt giới hạn tối đa thành viên
   *
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm
   * @param memberData Danh sách user ID cần duyệt
   * @returns Kết quả duyệt thành viên
   */
  async acceptMembers(
    accessToken: string,
    groupId: string,
    memberData: ZaloMemberActionRequest,
  ): Promise<{ success: boolean; accepted_count: number }> {
    try {
      this.logger.debug(
        `Accepting ${memberData.member_uids.length} members to group ${groupId}`,
      );

      return await this.zaloService.post<{
        success: boolean;
        accepted_count: number;
      }>(`${this.baseApiUrl}/${groupId}/accept`, accessToken, memberData);
    } catch (error) {
      this.logger.error(
        `Error accepting members: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi duyệt thành viên vào nhóm',
      );
    }
  }

  /**
   * Từ chối thành viên vào nhóm
   *
   * ĐIỀU KIỆN:
   * - OA phải là admin của nhóm
   * - Các user ID phải có trong danh sách chờ duyệt
   *
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm
   * @param memberData Danh sách user ID cần từ chối
   * @returns Kết quả từ chối thành viên
   */
  async rejectMembers(
    accessToken: string,
    groupId: string,
    memberData: ZaloMemberActionRequest,
  ): Promise<{ success: boolean; rejected_count: number }> {
    try {
      this.logger.debug(
        `Rejecting ${memberData.member_uids.length} members from group ${groupId}`,
      );

      return await this.zaloService.post<{
        success: boolean;
        rejected_count: number;
      }>(`${this.baseApiUrl}/${groupId}/reject`, accessToken, memberData);
    } catch (error) {
      this.logger.error(
        `Error rejecting members: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi từ chối thành viên vào nhóm',
      );
    }
  }

  /**
   * Lấy danh sách thành viên nhóm
   *
   * ĐIỀU KIỆN:
   * - OA phải là thành viên của nhóm
   * - Có quyền xem danh sách thành viên
   *
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm
   * @param offset Offset để phân trang (mặc định: 0)
   * @param count Số lượng tối đa trả về (mặc định: 50, tối đa: 100)
   * @returns Danh sách thành viên nhóm
   */
  async getGroupMembers(
    accessToken: string,
    groupId: string,
    offset: number = 0,
    count: number = 50,
  ): Promise<{ members: ZaloGroupMember[]; total: number }> {
    try {
      this.logger.debug(`Getting group members for group ${groupId}`);

      const params = new URLSearchParams();
      params.append('offset', offset.toString());
      params.append('count', Math.min(Math.max(count, 1), 100).toString());

      return await this.zaloService.get<{
        members: ZaloGroupMember[];
        total: number;
      }>(
        `${this.baseApiUrl}/${groupId}/members?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(
        `Error getting group members: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách thành viên nhóm',
      );
    }
  }

  /**
   * Thêm quyền admin cho thành viên
   *
   * ĐIỀU KIỆN:
   * - OA phải là admin của nhóm
   * - Các user ID phải là thành viên hiện tại của nhóm
   * - Không thể thêm admin cho người đã là admin
   *
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm
   * @param adminData Danh sách user ID cần thêm quyền admin
   * @returns Kết quả thêm admin
   */
  async addAdmins(
    accessToken: string,
    groupId: string,
    adminData: ZaloAdminActionRequest,
  ): Promise<{ success: boolean; added_count: number }> {
    try {
      this.logger.debug(
        `Adding ${adminData.admin_uids.length} admins to group ${groupId}`,
      );

      return await this.zaloService.post<{
        success: boolean;
        added_count: number;
      }>(`${this.baseApiUrl}/${groupId}/admins/add`, accessToken, adminData);
    } catch (error) {
      this.logger.error(`Error adding admins: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi thêm quyền admin',
      );
    }
  }

  /**
   * Xóa quyền admin của thành viên
   *
   * ĐIỀU KIỆN:
   * - OA phải là admin của nhóm
   * - Các user ID phải là admin hiện tại của nhóm
   * - Không thể xóa quyền admin của chính mình
   * - Phải còn ít nhất 1 admin sau khi xóa
   * - Không thể xóa quyền admin của OA
   *
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm
   * @param adminData Danh sách user ID cần xóa quyền admin
   * @returns Kết quả xóa admin
   */
  async removeAdmins(
    accessToken: string,
    groupId: string,
    adminData: ZaloAdminActionRequest,
  ): Promise<{ success: boolean; removed_count: number }> {
    try {
      this.logger.debug(
        `Removing ${adminData.admin_uids.length} admins from group ${groupId}`,
      );

      return await this.zaloService.post<{
        success: boolean;
        removed_count: number;
      }>(`${this.baseApiUrl}/${groupId}/admins/remove`, accessToken, adminData);
    } catch (error) {
      this.logger.error(`Error removing admins: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xóa quyền admin',
      );
    }
  }

  /**
   * Xóa thành viên khỏi nhóm
   *
   * ĐIỀU KIỆN:
   * - OA phải là admin của nhóm
   * - Các user ID phải là thành viên hiện tại của nhóm
   * - Không thể xóa admin khác (phải xóa quyền admin trước)
   * - Không thể xóa chính mình
   * - Không thể xóa OA khỏi nhóm
   *
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm
   * @param memberData Danh sách user ID cần xóa
   * @returns Kết quả xóa thành viên
   */
  async removeMembers(
    accessToken: string,
    groupId: string,
    memberData: ZaloRemoveMemberRequest,
  ): Promise<{ success: boolean; removed_count: number }> {
    try {
      this.logger.debug(
        `Removing ${memberData.member_uids.length} members from group ${groupId}`,
      );

      return await this.zaloService.post<{
        success: boolean;
        removed_count: number;
      }>(
        `${this.baseApiUrl}/${groupId}/members/remove`,
        accessToken,
        memberData,
      );
    } catch (error) {
      this.logger.error(
        `Error removing members: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xóa thành viên khỏi nhóm',
      );
    }
  }

  /**
   * Xóa nhóm chat
   *
   * ĐIỀU KIỆN:
   * - OA phải là admin của nhóm
   * - Chỉ có thể xóa nhóm do OA tạo ra
   * - Nhóm phải đang hoạt động (chưa bị xóa)
   * - Tất cả dữ liệu nhóm sẽ bị xóa vĩnh viễn
   *
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm cần xóa
   * @returns Kết quả xóa nhóm
   */
  async deleteGroup(
    accessToken: string,
    groupId: string,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.debug(`Deleting group ${groupId}`);

      return await this.zaloService.delete<{ success: boolean }>(
        `${this.baseApiUrl}/${groupId}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(`Error deleting group: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xóa nhóm chat',
      );
    }
  }

  /**
   * Lấy danh sách nhóm của OA
   *
   * ĐIỀU KIỆN:
   * - OA phải có quyền truy cập danh sách nhóm
   * - Chỉ trả về các nhóm mà OA đang tham gia
   *
   * @param accessToken Access token của Official Account
   * @param offset Offset để phân trang (mặc định: 0)
   * @param count Số lượng tối đa trả về (mặc định: 20, tối đa: 50)
   * @returns Danh sách nhóm của OA
   */
  async getGroupsOfOA(
    accessToken: string,
    offset: number = 0,
    count: number = 20,
  ): Promise<{ groups: ZaloGroupInfo[]; total: number }> {
    try {
      this.logger.debug(`Getting groups of OA`);

      const params = new URLSearchParams();
      params.append('offset', offset.toString());
      params.append('count', Math.min(Math.max(count, 1), 50).toString());

      return await this.zaloService.get<{
        groups: ZaloGroupInfo[];
        total: number;
      }>(`${this.baseApiUrl}/list?${params.toString()}`, accessToken);
    } catch (error) {
      this.logger.error(
        `Error getting groups of OA: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách nhóm của OA',
      );
    }
  }

  /**
   * Lấy thông tin hạn mức nhóm
   *
   * ĐIỀU KIỆN:
   * - OA phải có quyền truy cập thông tin hạn mức
   * - Thông tin hạn mức được cập nhật theo thời gian thực
   *
   * @param accessToken Access token của Official Account
   * @returns Thông tin hạn mức nhóm
   */
  async getGroupQuota(accessToken: string): Promise<ZaloGroupQuota> {
    try {
      this.logger.debug(`Getting group quota`);

      return await this.zaloService.get<ZaloGroupQuota>(
        `${this.baseApiUrl}/quota`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(
        `Error getting group quota: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin hạn mức nhóm',
      );
    }
  }

  /**
   * Lấy danh sách cuộc trò chuyện gần đây
   *
   * ĐIỀU KIỆN:
   * - OA phải có quyền truy cập lịch sử trò chuyện
   * - Chỉ trả về các nhóm có hoạt động gần đây
   *
   * @param accessToken Access token của Official Account
   * @param offset Offset để phân trang (mặc định: 0)
   * @param count Số lượng tối đa trả về (mặc định: 20, tối đa: 50)
   * @returns Danh sách cuộc trò chuyện gần đây
   */
  async getRecentChats(
    accessToken: string,
    offset: number = 0,
    count: number = 20,
  ): Promise<{ chats: ZaloRecentChat[]; total: number }> {
    try {
      this.logger.debug(`Getting recent chats`);

      const params = new URLSearchParams();
      params.append('offset', offset.toString());
      params.append('count', Math.min(Math.max(count, 1), 50).toString());

      return await this.zaloService.get<{
        chats: ZaloRecentChat[];
        total: number;
      }>(`${this.baseApiUrl}/recent?${params.toString()}`, accessToken);
    } catch (error) {
      this.logger.error(
        `Error getting recent chats: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách cuộc trò chuyện gần đây',
      );
    }
  }

  /**
   * Lấy lịch sử tin nhắn trong nhóm
   *
   * ĐIỀU KIỆN:
   * - OA phải là thành viên của nhóm
   * - Có quyền xem lịch sử tin nhắn
   * - Chỉ có thể xem tin nhắn trong khoảng thời gian cho phép (thường 30 ngày)
   *
   * @param accessToken Access token của Official Account
   * @param groupId ID của nhóm
   * @param offset Offset để phân trang (mặc định: 0)
   * @param count Số lượng tin nhắn tối đa trả về (mặc định: 20, tối đa: 100)
   * @param fromTime Thời gian bắt đầu (Unix timestamp, tùy chọn)
   * @param toTime Thời gian kết thúc (Unix timestamp, tùy chọn)
   * @returns Lịch sử tin nhắn trong nhóm
   */
  async getGroupConversation(
    accessToken: string,
    groupId: string,
    offset: number = 0,
    count: number = 20,
    fromTime?: number,
    toTime?: number,
  ): Promise<{ messages: ZaloGroupConversationMessage[]; total: number }> {
    try {
      this.logger.debug(`Getting group conversation for group ${groupId}`);

      const params = new URLSearchParams();
      params.append('offset', offset.toString());
      params.append('count', Math.min(Math.max(count, 1), 100).toString());

      if (fromTime) {
        params.append('from_time', fromTime.toString());
      }

      if (toTime) {
        params.append('to_time', toTime.toString());
      }

      return await this.zaloService.get<{
        messages: ZaloGroupConversationMessage[];
        total: number;
      }>(
        `${this.baseApiUrl}/${groupId}/conversation?${params.toString()}`,
        accessToken,
      );
    } catch (error) {
      this.logger.error(
        `Error getting group conversation: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy lịch sử tin nhắn nhóm',
      );
    }
  }
}
