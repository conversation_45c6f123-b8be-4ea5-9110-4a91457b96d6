import { Injectable } from '@nestjs/common';
import { BaseLangGraphEventHandler } from './base-event-handler';
import { EventProcessingContext } from '../schemas';
import { ToolCallInterruptBlock } from '../interfaces';

/**
 * Handler for interrupt events from LangGraph
 * Handles on_chain_stream events with __interrupt__ data
 */
@Injectable()
export class InterruptEventsHandler extends BaseLangGraphEventHandler {
  /**
   * Check if this handler can process the given event
   * @param event - LangGraph event type
   * @param data - Event data
   * @param tags - Event tags
   * @returns True if this is a chain stream event with interrupt data
   */
  canHandle(event: string, data: any, tags: string[]): boolean {
    return event === 'on_chain_stream' && data.chunk?.[2]?.['__interrupt__'];
  }

  /**
   * Process interrupt events
   * Handles user decisions and tool call interrupts
   * @param context - Event processing context
   */
  async handle(context: EventProcessingContext): Promise<void> {
    const [{ value }] = context.data.chunk[2]['__interrupt__'];
    const interruptValue = value;

    // Enhanced interrupt logging (from original)
    const displayValue =
      JSON.stringify(interruptValue).length > 100
        ? `${JSON.stringify(interruptValue).substring(0, 100)}...`
        : JSON.stringify(interruptValue);

    this.logEvent('⚠️', `Tool call interrupt: ${displayValue}`, context, {
      interruptType: interruptValue.role || 'unknown',
      hasPrompt: !!interruptValue.prompt,
    });

    const contentBlocks: ToolCallInterruptBlock[] = [
      {
        type: 'tool_call_interrupt',
      },
    ];

    await context.userMessagesQueries.createMessage({
      thread_id: context.threadId,
      role: 'assistant',
      content: {
        contentBlocks,
      },
      created_by: context.runData.created_by,
    });

    await context.emitEventCallback({
      type: 'tool_call_interrupt',
      data: interruptValue,
    });
  }
}
