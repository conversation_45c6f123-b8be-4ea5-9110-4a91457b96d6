/**
 * Enum định nghĩa trạng thái của fine-tune job
 */
export enum FineTuneJobStatus {
  /**
   * Job đang chờ xử lý
   */
  PENDING = 'PENDING',

  /**
   * <PERSON><PERSON> validate files
   */
  VALIDATING_FILES = 'VALIDATING_FILES',

  /**
   * <PERSON><PERSON> trong hàng đợi
   */
  QUEUED = 'QUEUED',

  /**
   * Đang chạy fine-tuning
   */
  RUNNING = 'RUNNING',

  /**
   * Hoàn thành thành công
   */
  SUCCEEDED = 'SUCCEEDED',

  /**
   * Thất bại
   */
  FAILED = 'FAILED',

  /**
   * Đã hủy
   */
  CANCELLED = 'CANCELLED',
}

/**
 * Mapping trạng thái từ OpenAI sang internal status
 */
export const OPENAI_STATUS_MAPPING: Record<string, FineTuneJobStatus> = {
  'validating_files': FineTuneJobStatus.VALIDATING_FILES,
  'queued': FineTuneJobStatus.QUEUED,
  'running': FineTuneJobStatus.RUNNING,
  'succeeded': FineTuneJobStatus.SUCCEEDED,
  'failed': FineTuneJobStatus.FAILED,
  'cancelled': FineTuneJobStatus.CANCELLED,
};

/**
 * Mapping trạng thái từ Google sang internal status
 */
export const GOOGLE_STATUS_MAPPING: Record<string, FineTuneJobStatus> = {
  'PENDING': FineTuneJobStatus.PENDING,
  'RUNNING': FineTuneJobStatus.RUNNING,
  'SUCCEEDED': FineTuneJobStatus.SUCCEEDED,
  'FAILED': FineTuneJobStatus.FAILED,
  'CANCELLED': FineTuneJobStatus.CANCELLED,
};

/**
 * Trạng thái kết thúc (terminal states)
 */
export const TERMINAL_STATUSES = [
  FineTuneJobStatus.SUCCEEDED,
  FineTuneJobStatus.FAILED,
  FineTuneJobStatus.CANCELLED,
];

/**
 * Kiểm tra xem trạng thái có phải là terminal không
 */
export function isTerminalStatus(status: FineTuneJobStatus): boolean {
  return TERMINAL_STATUSES.includes(status);
}

/**
 * Kiểm tra xem trạng thái có thành công không
 */
export function isSuccessStatus(status: FineTuneJobStatus): boolean {
  return status === FineTuneJobStatus.SUCCEEDED;
}

/**
 * Kiểm tra xem trạng thái có thất bại không
 */
export function isFailureStatus(status: FineTuneJobStatus): boolean {
  return status === FineTuneJobStatus.FAILED || status === FineTuneJobStatus.CANCELLED;
}
