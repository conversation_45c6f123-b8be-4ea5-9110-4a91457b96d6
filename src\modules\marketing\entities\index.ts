export * from './user-tag.entity';
export * from './user-segment.entity';
export * from './user-audience.entity';
export * from './user-audience-custom-field.entity';
export * from './user-audience-custom-field-definition.entity';
export * from './user-campaign.entity';
export * from './user-campaign-history.entity';
export * from './user-template-email.entity';

// Admin entities
export * from './admin-email-campaign.entity';
export * from './admin-audience.entity';
export * from './admin-segment.entity';

// Export types
export * from '../types/campaign.types';
export {
  AdminEmailCampaignStatus,
  EmailCampaignContent,
  EmailServerConfig
} from '../types/admin-email-campaign.types';

// SMS entities
export * from './sms-campaign-user.entity';
export * from './sms-campaign-admin.entity';
export * from './user-template-sms.entity';
export * from './sms-server-configuration.entity';
export * from './sms-marketing-history.entity';

// Zalo entities
// export * from './zalo-official-account.entity'; // Migrated to Integration entity
export * from './zalo-zns-template.entity';
export * from './zalo-message.entity';
export * from './zalo-zns-message.entity';
export * from './zalo-follower.entity';
export * from './zalo-webhook-log.entity';

// Zalo Marketing entities
export * from './zalo-segment.entity';
export * from './zalo-campaign.entity';
export * from './zalo-campaign-log.entity';
export * from './zalo-automation.entity';
export * from './zalo-automation-log.entity';

// Google Ads entities
export * from './google-ads-account.entity';
export * from './google-ads-campaign.entity';
export * from './google-ads-ad-group.entity';
export * from './google-ads-keyword.entity';
export * from './google-ads-performance.entity';
