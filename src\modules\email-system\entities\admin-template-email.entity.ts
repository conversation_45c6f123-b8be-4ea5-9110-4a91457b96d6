import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng admin_template_email trong cơ sở dữ liệu
 * <PERSON>h sách template email bên admin
 */
@Entity('admin_template_email')
export class AdminTemplateEmail {
  /**
   * ID của template
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Tên mẫu
   */
  @Column({ name: 'name', length: 100, nullable: true, comment: 'Tên mẫu' })
  name: string;

  /**
   * Ti<PERSON>u đề email
   */
  @Column({ name: 'subject', length: 255, nullable: true, comment: 'Tiêu đề' })
  subject: string;

  /**
   * <PERSON>h mục email
   */
  @Column({
    name: 'category',
    length: 100,
    nullable: true,
    unique: true,
    comment: 'Danh mục email',
  })
  category: string;

  /**
   * Nội dung email
   */
  @Column({
    name: 'content',
    type: 'text',
    nullable: true,
    comment: 'Nội dung email',
  })
  content: string;

  /**
   * <PERSON>h sách các placeholder
   */
  @Column({
    name: 'placeholders',
    type: 'json',
    nullable: true,
    comment: '<PERSON>h sách các placeholder',
  })
  placeholders: any;

  /**
   * ID của người tạo
   */
  @Column({ name: 'created_by', type: 'bigint', nullable: true })
  createdBy: number;

  /**
   * ID của người cập nhật
   */
  @Column({ name: 'updated_by', type: 'bigint', nullable: true })
  updatedBy: number;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian tạo',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: true,
    comment: 'Thời gian cập nhật',
  })
  updatedAt: number;
}
