# Zalo Token Utils Service

Service utils quản lý access token cho Zalo Official Account trong Worker module với tính năng retry và tự động refresh token.

## Tính năng chính

- ✅ Lấy access token hợp lệ từ database
- ✅ Tự động refresh token khi hết hạn
- ✅ Retry mechanism với exponential backoff
- ✅ Hỗ trợ cả oaId và userId + zaloOfficialAccountId
- ✅ API call wrapper với tự động retry
- ✅ Cleanup expired tokens
- ✅ Deactivate OA khi token không thể refresh

## Cách sử dụng

### 1. Import service

```typescript
import { ZaloTokenUtilsService } from '@shared/services/zalo';

@Injectable()
export class YourService {
  constructor(
    private readonly zaloTokenUtils: ZaloTokenUtilsService,
  ) {}
}
```

### 2. Lấy access token với retry

```typescript
// Sử dụng oaId
const accessToken = await this.zaloTokenUtils.getValidAccessTokenWithRetry('oaId123', 3);

// Sử dụng userId và zaloOfficialAccountId
const accessToken = await this.zaloTokenUtils.getValidAccessTokenWithRetry('userId', 'oaDbId', 3);
```

### 3. Thực hiện API call với retry

```typescript
// Với oaId
const result = await this.zaloTokenUtils.executeWithTokenRetry(
  async (accessToken) => {
    // Your API call here
    return await this.httpService.post('https://openapi.zalo.me/v2.0/oa/message', data, {
      headers: { access_token: accessToken }
    }).toPromise();
  },
  'oaId123',
  3 // maxRetries
);

// Với userId và zaloOfficialAccountId
const result = await this.zaloTokenUtils.executeWithTokenRetryByUserAndId(
  async (accessToken) => {
    // Your API call here
    return await this.httpService.post('https://openapi.zalo.me/v2.0/oa/message', data, {
      headers: { access_token: accessToken }
    }).toPromise();
  },
  'userId',
  'zaloOfficialAccountId',
  3 // maxRetries
);
```

### 4. Kiểm tra trạng thái OA

```typescript
const isActive = await this.zaloTokenUtils.isOfficialAccountActive('oaId123');
const oaInfo = await this.zaloTokenUtils.getOfficialAccount('oaId123');
```

### 5. Cleanup expired tokens (có thể chạy định kỳ)

```typescript
await this.zaloTokenUtils.cleanupExpiredTokens();
```

## API Methods

### getValidAccessTokenWithRetry()

Lấy access token hợp lệ với retry mechanism.

**Overloads:**
- `getValidAccessTokenWithRetry(oaId: string, maxRetries?: number): Promise<string>`
- `getValidAccessTokenWithRetry(userId: string, zaloOfficialAccountId: string, maxRetries?: number): Promise<string>`

**Parameters:**
- `oaId`: ID của Official Account trên Zalo
- `userId`: ID của user trong database
- `zaloOfficialAccountId`: ID của Zalo Official Account trong database
- `maxRetries`: Số lần retry tối đa (mặc định: 3)

### executeWithTokenRetry()

Thực hiện API call với retry mechanism và tự động refresh token.

**Parameters:**
- `apiCall`: Function thực hiện API call
- `oaId`: ID của Official Account
- `maxRetries`: Số lần retry tối đa (mặc định: 3)

### executeWithTokenRetryByUserAndId()

Thực hiện API call với retry mechanism cho user và zaloOfficialAccountId.

**Parameters:**
- `apiCall`: Function thực hiện API call
- `userId`: ID của user
- `zaloOfficialAccountId`: ID của Zalo Official Account trong database
- `maxRetries`: Số lần retry tối đa (mặc định: 3)

## Error Handling

Service sẽ throw `AppException` với các error codes:
- `RESOURCE_NOT_FOUND`: Không tìm thấy Official Account
- `VALIDATION_ERROR`: OA không active hoặc thiếu refresh token
- `CONFIGURATION_ERROR`: Thiếu ZALO_APP_ID hoặc ZALO_APP_SECRET
- `EXTERNAL_SERVICE_ERROR`: Lỗi từ Zalo API
- `DATABASE_ERROR`: Lỗi cập nhật database

## Retry Strategy

- **Exponential backoff**: Delay tăng dần (1s, 2s, 4s, max 5s)
- **Smart retry**: Không retry cho lỗi không thể khôi phục (RESOURCE_NOT_FOUND, CONFIGURATION_ERROR)
- **Token refresh**: Tự động refresh token khi gặp lỗi 401/403

## Configuration

Cần cấu hình các biến môi trường:
- `ZALO_APP_ID`: ID của Zalo App
- `ZALO_APP_SECRET`: Secret của Zalo App

## Dependencies

- `@nestjs/common`
- `@nestjs/config`
- `@nestjs/axios`
- `@nestjs/typeorm`
- `typeorm`
- `rxjs`
- `@common/exceptions`
