/**
 * Enum định nghĩa các loại input modality
 */
export enum InputModalityEnum {
  TEXT = 'text',
  IMAGE = 'image',
  AUDIO = 'audio',
  VIDEO = 'video',
}

/**
 * Enum định nghĩa các loại output modality
 */
export enum OutputModalityEnum {
  TEXT = 'text',
  IMAGE = 'image',
  AUDIO = 'audio',
  VIDEO = 'video',
}

/**
 * Enum định nghĩa các tham số sampling
 */
export enum SamplingParameterEnum {
  TEMPERATURE = 'temperature',
  TOP_P = 'top_p',
  TOP_K = 'top_k',
  MAX_TOKENS = 'max_tokens',
  MAX_OUTPUT_TOKENS = 'max_output_tokens',
}

/**
 * Enum định nghĩa các feature đặc biệt
 */
export enum FeatureEnum {
  TOOL_CALL = 'tool_call',
  PARALLEL_TOOL_CALL = 'parallel_tool_call',
  FUNCTION_CALLING = 'function_calling',
  JSON_MODE = 'json_mode',
  VISION = 'vision',
}
