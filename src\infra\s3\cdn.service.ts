import { TimeIntervalEnum } from '@common/dto/time-interval.enum';
import { Injectable, Logger } from '@nestjs/common';
import { env } from 'src/config';
import * as crypto from 'crypto';
@Injectable()
export class CdnService {
  private readonly logger = new Logger(CdnService.name);
  private readonly cdnUrl: string;
  private readonly secretKey: string;

  constructor() {
    this.cdnUrl = env.cdn.CDN_URL;
    this.secretKey = env.cdn.CDN_SECRET_KEY;
  }

  /**
   * Tạo URL có chữ ký để xem tài nguyên từ CDN
   * @param key Đường dẫn tài nguyên trên CDN
   * @param time Thời gian hết hạn
   * @returns URL có chữ ký
   */
  generateUrlView(key: string, time: TimeIntervalEnum): string | null {
    if (!key || key.length === 0) {
      return null;
    }

    try {
      const unixTime = Math.floor(Date.now() / 1000);
      const expires = unixTime + time;
      const text = `${expires}|${key}`;

      // Tạo chữ ký HMAC-SHA1
      const hmac = crypto.createHmac('sha1', this.secretKey);
      hmac.update(text);
      const signature = hmac
        .digest('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '');

      // Trả về URL với chữ ký và thời gian hết hạn
      return `${this.cdnUrl}/${key}?expires=${expires}&signature=${signature}`;
    } catch (error) {
      this.logger.error(`Error generating signed URL: ${error.message}`);
      throw error;
    }
  }
}
