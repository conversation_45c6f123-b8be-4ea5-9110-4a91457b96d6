import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { OpenAIFineTuneJobResponse, PollingResult } from '../interfaces';

/**
 * Service để tương tác với OpenAI Fine-tuning API
 */
@Injectable()
export class OpenAIFineTuneService {
  private readonly logger = new Logger(OpenAIFineTuneService.name);
  private readonly baseURL = 'https://api.openai.com/v1';

  /**
   * Tạo HTTP client với API key
   */
  private createHttpClient(apiKey: string): AxiosInstance {
    return axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000,
    });
  }

  /**
   * Lấy trạng thái của fine-tune job từ OpenAI
   * @param jobId - ID của fine-tune job
   * @param apiKey - API key đã decrypt
   * @returns Thông tin trạng thái job
   */
  async getFineTuneJobStatus(jobId: string, apiKey: string): Promise<PollingResult> {
    try {
      this.logger.debug(`Getting OpenAI fine-tune job status for job: ${jobId}`);

      const httpClient = this.createHttpClient(apiKey);
      
      const response = await httpClient.get<OpenAIFineTuneJobResponse>(
        `/fine_tuning/jobs/${jobId}`
      );

      const jobData = response.data;
      
      this.logger.debug(`OpenAI job status: ${jobData.status}`, {
        jobId,
        status: jobData.status,
        fineTunedModel: jobData.fine_tuned_model,
      });

      return this.mapOpenAIStatusToResult(jobData);
    } catch (error) {
      this.logger.error(`Failed to get OpenAI fine-tune job status for job: ${jobId}`, {
        error: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });

      // Nếu job không tồn tại (404), coi như failed
      if (error.response?.status === 404) {
        return {
          success: false,
          status: 'failed',
          error: 'Job not found',
          shouldContinuePolling: false,
        };
      }

      // Nếu là lỗi authentication (401), coi như failed
      if (error.response?.status === 401) {
        return {
          success: false,
          status: 'failed',
          error: 'Authentication failed',
          shouldContinuePolling: false,
        };
      }

      // Các lỗi khác, tiếp tục polling
      return {
        success: false,
        status: 'error',
        error: error.message,
        shouldContinuePolling: true,
      };
    }
  }

  /**
   * Map OpenAI status thành PollingResult
   */
  private mapOpenAIStatusToResult(jobData: OpenAIFineTuneJobResponse): PollingResult {
    const { status, fine_tuned_model, error } = jobData;

    switch (status) {
      case 'succeeded':
        return {
          success: true,
          status: 'succeeded',
          modelId: fine_tuned_model || undefined,
          shouldContinuePolling: false,
        };

      case 'failed':
      case 'cancelled':
        return {
          success: false,
          status,
          error: error?.message || `Job ${status}`,
          shouldContinuePolling: false,
        };

      case 'validating_files':
      case 'queued':
      case 'running':
        return {
          success: false,
          status,
          shouldContinuePolling: true,
        };

      default:
        this.logger.warn(`Unknown OpenAI status: ${status}`);
        return {
          success: false,
          status: status || 'unknown',
          shouldContinuePolling: true,
        };
    }
  }

  /**
   * Kiểm tra API key có hợp lệ không
   */
  async validateApiKey(apiKey: string): Promise<boolean> {
    try {
      const httpClient = this.createHttpClient(apiKey);
      
      // Gọi API đơn giản để kiểm tra key
      await httpClient.get('/models', {
        params: { limit: 1 },
      });

      return true;
    } catch (error) {
      this.logger.warn('OpenAI API key validation failed', {
        status: error.response?.status,
        error: error.message,
      });
      return false;
    }
  }

  /**
   * Lấy danh sách fine-tune jobs
   */
  async listFineTuneJobs(apiKey: string, limit: number = 20): Promise<OpenAIFineTuneJobResponse[]> {
    try {
      const httpClient = this.createHttpClient(apiKey);
      
      const response = await httpClient.get('/fine_tuning/jobs', {
        params: { limit },
      });

      return response.data.data || [];
    } catch (error) {
      this.logger.error('Failed to list OpenAI fine-tune jobs', error);
      throw error;
    }
  }

  /**
   * Hủy fine-tune job
   */
  async cancelFineTuneJob(jobId: string, apiKey: string): Promise<boolean> {
    try {
      const httpClient = this.createHttpClient(apiKey);
      
      await httpClient.post(`/fine_tuning/jobs/${jobId}/cancel`);
      
      this.logger.log(`Successfully cancelled OpenAI fine-tune job: ${jobId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to cancel OpenAI fine-tune job: ${jobId}`, error);
      return false;
    }
  }

  /**
   * Lấy events của fine-tune job
   */
  async getFineTuneJobEvents(jobId: string, apiKey: string): Promise<any[]> {
    try {
      const httpClient = this.createHttpClient(apiKey);
      
      const response = await httpClient.get(`/fine_tuning/jobs/${jobId}/events`);
      
      return response.data.data || [];
    } catch (error) {
      this.logger.error(`Failed to get OpenAI fine-tune job events: ${jobId}`, error);
      return [];
    }
  }
}
