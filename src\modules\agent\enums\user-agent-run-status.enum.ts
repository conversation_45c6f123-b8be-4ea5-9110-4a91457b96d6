export enum UserAgentRunStatus {
  /**
   * Initial state when run is created
   */
  CREATED = 'created',

  /**
   * When worker starts processing the run
   */
  RUNNING = 'running',

  /**
   * Successful completion of the run
   */
  COMPLETED = 'completed',

  /**
   * Error or failure during run processing
   */
  FAILED = 'failed',

  /**
   * Run was cancelled by user or system
   */
  CANCELLED = 'cancelled',
}

/**
 * Valid status transitions for user agent runs
 */
export const VALID_STATUS_TRANSITIONS: Record<
  UserAgentRunStatus,
  UserAgentRunStatus[]
> = {
  [UserAgentRunStatus.CREATED]: [
    UserAgentRunStatus.RUNNING,
    UserAgentRunStatus.FAILED,
    UserAgentRunStatus.CANCELLED, // ✅ Can cancel before processing
  ],
  [UserAgentRunStatus.RUNNING]: [
    UserAgentRunStatus.COMPLETED,
    UserAgentRunStatus.FAILED,
    UserAgentRunStatus.CANCELLED, // ✅ Can cancel during processing
  ],
  [UserAgentRunStatus.COMPLETED]: [], // Terminal state
  [UserAgentRunStatus.FAILED]: [], // Terminal state
  [UserAgentRunStatus.CANCELLED]: [], // ✅ Terminal state
};

/**
 * Validates if a status transition is allowed
 * @param from Current status
 * @param to Target status
 * @returns true if transition is valid, false otherwise
 */
export function isValidStatusTransition(
  from: UserAgentRunStatus,
  to: UserAgentRunStatus,
): boolean {
  return VALID_STATUS_TRANSITIONS[from]?.includes(to) ?? false;
}

/**
 * Gets all possible next statuses from current status
 * @param current Current status
 * @returns Array of valid next statuses
 */
export function getValidNextStatuses(
  current: UserAgentRunStatus,
): UserAgentRunStatus[] {
  return VALID_STATUS_TRANSITIONS[current] || [];
}
