import { Injectable, Logger } from '@nestjs/common';
import {
  ICondition,
  IConditionResult,
  EComparisonOperator,
  EDataType,
  IConditionGroup,
  ELogicalOperator
} from '../../../../interfaces';

/**
 * Shared service for condition evaluation
 * Used by IF_CONDITION, SWITCH, FILTER nodes
 */
@Injectable()
export class ConditionEvaluatorService {
  private readonly logger = new Logger(ConditionEvaluatorService.name);

  /**
   * Evaluate multiple condition groups with logical operators
   */
  async evaluateConditionGroups(
    groups: IConditionGroup[],
    groupsOperator: ELogicalOperator,
    data: Record<string, any>
  ): Promise<{
    finalResult: boolean;
    details: any;
    evaluationTime: number;
  }> {
    const startTime = Date.now();
    
    try {
      this.logger.debug(`Evaluating ${groups.length} condition groups with ${groupsOperator} operator`);
      
      const groupResults = await Promise.all(
        groups.map((group, index) => this.evaluateConditionGroup(group, data, index))
      );
      
      const finalResult = groupsOperator === ELogicalOperator.AND
        ? groupResults.every(r => r.result)
        : groupResults.some(r => r.result);
      
      const evaluationTime = Date.now() - startTime;
      
      this.logger.debug(`Condition evaluation completed: ${finalResult} (${evaluationTime}ms)`);
      
      return {
        finalResult,
        details: {
          condition_groups: groupResults,
          final_result: finalResult,
          groups_operator: groupsOperator,
          total_groups: groups.length,
        },
        evaluationTime
      };
      
    } catch (error) {
      this.logger.error('Failed to evaluate condition groups:', error);
      throw error;
    }
  }

  /**
   * Evaluate single condition group
   */
  private async evaluateConditionGroup(
    group: IConditionGroup,
    data: Record<string, any>,
    groupIndex: number
  ): Promise<{
    logical_operator: ELogicalOperator;
    result: boolean;
    conditions: Array<{
      field: string;
      operator: EComparisonOperator;
      expected_value: any;
      actual_value: any;
      result: boolean;
    }>;
  }> {
    try {
      this.logger.debug(`Evaluating condition group ${groupIndex} with ${group.conditions.length} conditions`);
      
      const conditionResults = await Promise.all(
        group.conditions.map(condition => this.evaluateCondition(condition, data))
      );
      
      const groupResult = group.logical_operator === ELogicalOperator.AND
        ? conditionResults.every(r => r.result)
        : conditionResults.some(r => r.result);
      
      return {
        logical_operator: group.logical_operator,
        result: groupResult,
        conditions: conditionResults.map((result, index) => ({
          field: group.conditions[index].field,
          operator: group.conditions[index].operator,
          expected_value: result.expected_value,
          actual_value: result.actual_value,
          result: result.result,
        }))
      };
      
    } catch (error) {
      this.logger.error(`Failed to evaluate condition group ${groupIndex}:`, error);
      throw error;
    }
  }

  /**
   * Evaluate single condition
   */
  async evaluateCondition(
    condition: ICondition,
    data: Record<string, any>
  ): Promise<IConditionResult> {
    const startTime = Date.now();
    
    try {
      // Get actual value from data
      const actualValue = this.getFieldValue(data, condition.field);
      
      // Perform comparison
      const result = this.compareValues(
        actualValue,
        condition.value,
        condition.operator,
        condition.data_type,
        condition
      );
      
      return {
        result,
        actual_value: actualValue,
        expected_value: condition.value,
        evaluation_time: Date.now() - startTime
      };
      
    } catch (error) {
      this.logger.error(`Failed to evaluate condition for field ${condition.field}:`, error);
      
      return {
        result: false,
        actual_value: null,
        expected_value: condition.value,
        evaluation_time: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * Get field value from data using dot notation
   */
  private getFieldValue(data: Record<string, any>, fieldPath: string): any {
    try {
      return fieldPath.split('.').reduce((obj, key) => {
        if (obj === null || obj === undefined) {
          return undefined;
        }
        return obj[key];
      }, data);
    } catch (error) {
      this.logger.warn(`Failed to get field value for path: ${fieldPath}`);
      return undefined;
    }
  }

  /**
   * Compare values based on operator and data type
   */
  private compareValues(
    actual: any,
    expected: any,
    operator: EComparisonOperator,
    dataType: EDataType,
    condition: ICondition
  ): boolean {
    // Handle null/undefined cases first
    if (operator === EComparisonOperator.IS_NULL) {
      return actual === null || actual === undefined;
    }
    
    if (operator === EComparisonOperator.IS_NOT_NULL) {
      return actual !== null && actual !== undefined;
    }
    
    if (operator === EComparisonOperator.IS_EMPTY) {
      return this.isEmpty(actual);
    }
    
    if (operator === EComparisonOperator.IS_NOT_EMPTY) {
      return !this.isEmpty(actual);
    }

    // Universal operators
    switch (operator) {
      case EComparisonOperator.EQUALS:
        return actual === expected;
        
      case EComparisonOperator.NOT_EQUALS:
        return actual !== expected;
    }

    // Type-specific operators
    switch (dataType) {
      case EDataType.STRING:
        return this.compareStringValues(actual, expected, operator, condition);
        
      case EDataType.NUMBER:
        return this.compareNumberValues(actual, expected, operator);
        
      case EDataType.BOOLEAN:
        return this.compareBooleanValues(actual, expected, operator);
        
      case EDataType.DATE:
      case EDataType.DATETIME:
      case EDataType.TIME:
        return this.compareDateValues(actual, expected, operator, condition);
        
      case EDataType.ARRAY:
        return this.compareArrayValues(actual, expected, operator, condition);
        
      case EDataType.OBJECT:
        return this.compareObjectValues(actual, expected, operator, condition);
        
      default:
        throw new Error(`Unsupported data type: ${dataType}`);
    }
  }

  private isEmpty(value: any): boolean {
    if (value === null || value === undefined) return true;
    if (typeof value === 'string') return value.trim() === '';
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === 'object') return Object.keys(value).length === 0;
    return false;
  }

  private compareStringValues(
    actual: any,
    expected: any,
    operator: EComparisonOperator,
    condition: ICondition
  ): boolean {
    const actualStr = String(actual || '');
    const expectedStr = String(expected || '');
    const caseSensitive = condition.case_sensitive !== false;
    
    const actualValue = caseSensitive ? actualStr : actualStr.toLowerCase();
    const expectedValue = caseSensitive ? expectedStr : expectedStr.toLowerCase();

    switch (operator) {
      case EComparisonOperator.CONTAINS:
        return actualValue.includes(expectedValue);
        
      case EComparisonOperator.NOT_CONTAINS:
        return !actualValue.includes(expectedValue);
        
      case EComparisonOperator.STARTS_WITH:
        return actualValue.startsWith(expectedValue);
        
      case EComparisonOperator.ENDS_WITH:
        return actualValue.endsWith(expectedValue);
        
      case EComparisonOperator.REGEX_MATCH:
        const flags = condition.additional_params?.regex_flags || (caseSensitive ? 'g' : 'gi');
        const regex = new RegExp(expectedStr, flags);
        return regex.test(actualStr);
        
      case EComparisonOperator.LENGTH_EQUALS:
        return actualStr.length === Number(expected);
        
      case EComparisonOperator.LENGTH_GREATER_THAN:
        return actualStr.length > Number(expected);
        
      case EComparisonOperator.LENGTH_LESS_THAN:
        return actualStr.length < Number(expected);
        
      default:
        throw new Error(`Unsupported string operator: ${operator}`);
    }
  }

  private compareNumberValues(
    actual: any,
    expected: any,
    operator: EComparisonOperator
  ): boolean {
    const actualNum = Number(actual);
    const expectedNum = Number(expected);
    
    if (isNaN(actualNum) || isNaN(expectedNum)) {
      return false;
    }

    switch (operator) {
      case EComparisonOperator.GREATER_THAN:
        return actualNum > expectedNum;
        
      case EComparisonOperator.GREATER_THAN_OR_EQUAL:
        return actualNum >= expectedNum;
        
      case EComparisonOperator.LESS_THAN:
        return actualNum < expectedNum;
        
      case EComparisonOperator.LESS_THAN_OR_EQUAL:
        return actualNum <= expectedNum;
        
      case EComparisonOperator.BETWEEN:
        // Expected should be array [min, max]
        if (Array.isArray(expected) && expected.length === 2) {
          return actualNum >= Number(expected[0]) && actualNum <= Number(expected[1]);
        }
        return false;
        
      case EComparisonOperator.NOT_BETWEEN:
        if (Array.isArray(expected) && expected.length === 2) {
          return actualNum < Number(expected[0]) || actualNum > Number(expected[1]);
        }
        return true;
        
      default:
        throw new Error(`Unsupported number operator: ${operator}`);
    }
  }

  private compareBooleanValues(
    actual: any,
    _expected: any,
    operator: EComparisonOperator
  ): boolean {
    switch (operator) {
      case EComparisonOperator.IS_TRUE:
        return Boolean(actual) === true;
        
      case EComparisonOperator.IS_FALSE:
        return Boolean(actual) === false;
        
      default:
        throw new Error(`Unsupported boolean operator: ${operator}`);
    }
  }

  private compareDateValues(
    actual: any,
    expected: any,
    operator: EComparisonOperator,
    condition: ICondition
  ): boolean {
    const actualDate = new Date(actual);
    const expectedDate = new Date(expected);
    
    if (isNaN(actualDate.getTime())) {
      return false;
    }

    switch (operator) {
      case EComparisonOperator.DATE_IS_BEFORE:
        return actualDate < expectedDate;
        
      case EComparisonOperator.DATE_IS_AFTER:
        return actualDate > expectedDate;
        
      case EComparisonOperator.DATE_IS_TODAY:
        const today = new Date();
        return this.isSameDay(actualDate, today);
        
      case EComparisonOperator.DATE_IS_YESTERDAY:
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        return this.isSameDay(actualDate, yesterday);
        
      case EComparisonOperator.DATE_IS_TOMORROW:
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        return this.isSameDay(actualDate, tomorrow);
        
      case EComparisonOperator.DATE_DAYS_AGO:
        const daysAgo = condition.additional_params?.days_count || 0;
        const targetDate = new Date();
        targetDate.setDate(targetDate.getDate() - daysAgo);
        return this.isSameDay(actualDate, targetDate);
        
      default:
        throw new Error(`Unsupported date operator: ${operator}`);
    }
  }

  private compareArrayValues(
    actual: any,
    expected: any,
    operator: EComparisonOperator,
    _condition: ICondition
  ): boolean {
    if (!Array.isArray(actual)) {
      return false;
    }

    switch (operator) {
      case EComparisonOperator.ARRAY_CONTAINS:
        return actual.includes(expected);
        
      case EComparisonOperator.ARRAY_NOT_CONTAINS:
        return !actual.includes(expected);
        
      case EComparisonOperator.ARRAY_LENGTH_EQUALS:
        return actual.length === Number(expected);
        
      case EComparisonOperator.ARRAY_LENGTH_GREATER_THAN:
        return actual.length > Number(expected);
        
      case EComparisonOperator.ARRAY_LENGTH_LESS_THAN:
        return actual.length < Number(expected);
        
      case EComparisonOperator.IN_ARRAY:
        return Array.isArray(expected) && expected.includes(actual);
        
      default:
        throw new Error(`Unsupported array operator: ${operator}`);
    }
  }

  private compareObjectValues(
    actual: any,
    expected: any,
    operator: EComparisonOperator,
    condition: ICondition
  ): boolean {
    if (typeof actual !== 'object' || actual === null) {
      return false;
    }

    switch (operator) {
      case EComparisonOperator.HAS_PROPERTY:
        const propertyName = condition.additional_params?.property_name || expected;
        return propertyName in actual;
        
      case EComparisonOperator.NOT_HAS_PROPERTY:
        const propName = condition.additional_params?.property_name || expected;
        return !(propName in actual);
        
      case EComparisonOperator.PROPERTY_EQUALS:
        const prop = condition.additional_params?.property_name;
        return prop ? actual[prop] === expected : false;
        
      case EComparisonOperator.PROPERTY_NOT_EQUALS:
        const propName2 = condition.additional_params?.property_name;
        return propName2 ? actual[propName2] !== expected : false;
        
      default:
        throw new Error(`Unsupported object operator: ${operator}`);
    }
  }

  private isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }
}
