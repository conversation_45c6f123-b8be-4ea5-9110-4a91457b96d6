import { NodeExecutionResult, LangGraphExecutionResult } from './workflow-context.interface';

/**
 * Base event interface cho tất cả workflow events
 */
export interface BaseWorkflowEvent {
  /** Timestamp khi event được tạo */
  timestamp: number;
  
  /** ID của execution để tracking */
  executionId: string;
  
  /** Metadata bổ sung */
  metadata?: Record<string, any>;
}

/**
 * Event để load workflow definition từ database
 */
export interface LoadWorkflowEvent extends BaseWorkflowEvent {
  type: 'LOAD_WORKFLOW';
  workflowId: string;
  userId: number;
  triggerData: any;
  triggerType: 'manual' | 'webhook' | 'schedule';
  options?: {
    enableSSE?: boolean;
    timeout?: number;
    retryOnFailure?: boolean;
    skipValidation?: boolean;
    maxConcurrency?: number;
  };
}

/**
 * Event để bắt đầu thực thi workflow
 */
export interface StartExecutionEvent extends BaseWorkflowEvent {
  type: 'START_EXECUTION';
  triggerData: any;
}

/**
 * Event khi một node hoàn thành thành công
 */
export interface NodeCompletedEvent extends BaseWorkflowEvent {
  type: 'NODE_COMPLETED';
  nodeId: string;
  outputData: any;
  executionTime: number;
  metadata?: {
    memoryUsage?: number;
    httpStatusCode?: number;
    tokenUsage?: {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
    };
    [key: string]: any;
  };
}

/**
 * Event khi một node thất bại
 */
export interface NodeFailedEvent extends BaseWorkflowEvent {
  type: 'NODE_FAILED';
  nodeId: string;
  error: Error;
  retryCount: number;
  canRetry: boolean;
}

/**
 * Event khi một node bắt đầu thực thi
 */
export interface NodeStartedEvent extends BaseWorkflowEvent {
  type: 'NODE_STARTED';
  nodeId: string;
  inputData: any;
  nodeType: string;
}

/**
 * Event khi một agent node hoàn thành (từ LangGraph)
 */
export interface AgentCompletedEvent extends BaseWorkflowEvent {
  type: 'AGENT_COMPLETED';
  nodeId: string;
  agentId: string;
  result: LangGraphExecutionResult;
}

/**
 * Event khi một agent node thất bại
 */
export interface AgentFailedEvent extends BaseWorkflowEvent {
  type: 'AGENT_FAILED';
  nodeId: string;
  agentId: string;
  error: Error;
  retryCount: number;
}

/**
 * Event để retry một node đã thất bại
 */
export interface RetryNodeEvent extends BaseWorkflowEvent {
  type: 'RETRY_NODE';
  nodeId: string;
  retryCount: number;
  maxRetries: number;
}

/**
 * Event để retry toàn bộ workflow
 */
export interface RetryWorkflowEvent extends BaseWorkflowEvent {
  type: 'RETRY_WORKFLOW';
  fromNodeId?: string; // Retry từ node cụ thể
  resetState?: boolean; // Reset toàn bộ state hay chỉ failed nodes
}

/**
 * Event để pause workflow execution
 */
export interface PauseExecutionEvent extends BaseWorkflowEvent {
  type: 'PAUSE_EXECUTION';
  reason?: string;
  pausedBy?: 'user' | 'system' | 'error';
}

/**
 * Event để resume workflow execution
 */
export interface ResumeExecutionEvent extends BaseWorkflowEvent {
  type: 'RESUME_EXECUTION';
  resumedBy?: 'user' | 'system';
}

/**
 * Event để cancel workflow execution
 */
export interface CancelExecutionEvent extends BaseWorkflowEvent {
  type: 'CANCEL_EXECUTION';
  reason?: string;
  cancelledBy?: 'user' | 'system' | 'timeout';
}

/**
 * Event khi workflow hoàn thành thành công
 */
export interface WorkflowCompletedEvent extends BaseWorkflowEvent {
  type: 'WORKFLOW_COMPLETED';
  totalExecutionTime: number;
  completedNodes: number;
  totalNodes: number;
  finalOutputData: any;
}

/**
 * Event khi workflow thất bại
 */
export interface WorkflowFailedEvent extends BaseWorkflowEvent {
  type: 'WORKFLOW_FAILED';
  error: Error;
  failedNodeId?: string;
  completedNodes: number;
  totalNodes: number;
}

/**
 * Event khi dependencies của nodes được cập nhật
 */
export interface DependenciesUpdatedEvent extends BaseWorkflowEvent {
  type: 'DEPENDENCIES_UPDATED';
  readyNodes: string[];
  waitingNodes: string[];
}

/**
 * Event để cập nhật execution context
 */
export interface UpdateContextEvent extends BaseWorkflowEvent {
  type: 'UPDATE_CONTEXT';
  updates: {
    executionData?: Record<string, any>;
    metadata?: Record<string, any>;
    options?: Record<string, any>;
  };
}

/**
 * Event khi có timeout
 */
export interface TimeoutEvent extends BaseWorkflowEvent {
  type: 'TIMEOUT';
  timeoutType: 'workflow' | 'node';
  nodeId?: string;
  timeoutDuration: number;
}

/**
 * Event để gửi SSE notification
 */
export interface SSENotificationEvent extends BaseWorkflowEvent {
  type: 'SSE_NOTIFICATION';
  eventType: 'node_started' | 'node_completed' | 'node_failed' | 'workflow_completed' | 'workflow_failed';
  data: any;
  userId: number;
}

/**
 * Event khi workflow được loaded thành công
 */
export interface WorkflowLoadedEvent extends BaseWorkflowEvent {
  type: 'WORKFLOW_LOADED';
  workflowId: string;
  totalNodes: number;
  rootNodes: string[];
  dependencyGraph: any;
}

/**
 * Event khi có validation error
 */
export interface ValidationErrorEvent extends BaseWorkflowEvent {
  type: 'VALIDATION_ERROR';
  validationType: 'workflow' | 'node' | 'dependencies';
  nodeId?: string;
  errors: string[];
}

/**
 * Union type cho tất cả workflow events
 */
export type WorkflowEvent = 
  | LoadWorkflowEvent
  | StartExecutionEvent
  | NodeCompletedEvent
  | NodeFailedEvent
  | NodeStartedEvent
  | AgentCompletedEvent
  | AgentFailedEvent
  | RetryNodeEvent
  | RetryWorkflowEvent
  | PauseExecutionEvent
  | ResumeExecutionEvent
  | CancelExecutionEvent
  | WorkflowCompletedEvent
  | WorkflowFailedEvent
  | DependenciesUpdatedEvent
  | UpdateContextEvent
  | TimeoutEvent
  | SSENotificationEvent
  | WorkflowLoadedEvent
  | ValidationErrorEvent;

/**
 * Event type strings cho XState machine
 */
export const WorkflowEventTypes = {
  LOAD_WORKFLOW: 'LOAD_WORKFLOW',
  START_EXECUTION: 'START_EXECUTION',
  NODE_COMPLETED: 'NODE_COMPLETED',
  NODE_FAILED: 'NODE_FAILED',
  NODE_STARTED: 'NODE_STARTED',
  AGENT_COMPLETED: 'AGENT_COMPLETED',
  AGENT_FAILED: 'AGENT_FAILED',
  RETRY_NODE: 'RETRY_NODE',
  RETRY_WORKFLOW: 'RETRY_WORKFLOW',
  PAUSE_EXECUTION: 'PAUSE_EXECUTION',
  RESUME_EXECUTION: 'RESUME_EXECUTION',
  CANCEL_EXECUTION: 'CANCEL_EXECUTION',
  WORKFLOW_COMPLETED: 'WORKFLOW_COMPLETED',
  WORKFLOW_FAILED: 'WORKFLOW_FAILED',
  DEPENDENCIES_UPDATED: 'DEPENDENCIES_UPDATED',
  UPDATE_CONTEXT: 'UPDATE_CONTEXT',
  TIMEOUT: 'TIMEOUT',
  SSE_NOTIFICATION: 'SSE_NOTIFICATION',
  WORKFLOW_LOADED: 'WORKFLOW_LOADED',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
} as const;

/**
 * Helper type để extract event type từ event object
 */
export type EventType = WorkflowEvent['type'];

/**
 * Helper function để tạo base event properties
 */
export function createBaseEvent(executionId: string, metadata?: Record<string, any>): BaseWorkflowEvent {
  return {
    timestamp: Date.now(),
    executionId,
    metadata,
  };
}
