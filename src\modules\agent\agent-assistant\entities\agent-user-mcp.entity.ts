import { <PERSON><PERSON><PERSON>, Column, PrimaryColumn } from 'typeorm';

/**
 * AgentUserMcp entity
 * Links user agents to their MCP configurations
 */
@Entity('agent_user_mcp')
export class AgentUserMcp {
  /**
   * Agent ID reference to agents_user
   */
  @PrimaryColumn({ name: 'agent_id', type: 'uuid' })
  agentId: string;

  /**
   * MCP ID reference to user_mcp
   */
  @PrimaryColumn({ name: 'mcp_id', type: 'uuid' })
  mcpId: string;
}
