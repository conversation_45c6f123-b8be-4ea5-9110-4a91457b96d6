/**
 * Message direction enum - incoming vs outgoing messages
 */
export enum MessageDirectionEnum {
  INCOMING = 'incoming',
  OUTGOING = 'outgoing',
}

/**
 * Message type enum - different types of messages
 */
export enum MessageTypeEnum {
  TEXT = 'text',
  IMAGE = 'image',
  STICKER = 'sticker',
  FILE = 'file',
  LOCATION = 'location',
  AUDIO = 'audio',
  VIDEO = 'video',
  LINK = 'link',
  CONTACT = 'contact',
  TEMPLATE = 'template',
}
