import { Injectable, Logger } from '@nestjs/common';
import { createActor, ActorRefFrom } from 'xstate';
import { workflowMachine } from './workflow.machine';
import { nodeExecutionMachine } from './node-execution.machine';
import { MachineServicesProvider } from './machine-services';
import { allGuards } from './guards';
import { allActions } from './actions';
import { WorkflowContext, NodeExecutionContext } from '../types';

/**
 * Machine integration service - connects XState machines with NestJS services
 */
@Injectable()
export class MachineIntegrationService {
  private readonly logger = new Logger(MachineIntegrationService.name);
  private readonly activeWorkflows = new Map<string, ActorRefFrom<typeof workflowMachine>>();
  private readonly activeNodes = new Map<string, ActorRefFrom<typeof nodeExecutionMachine>>();

  constructor(
    private readonly machineServices: MachineServicesProvider,
  ) {}

  /**
   * Create and configure workflow machine with all services
   */
  createWorkflowMachine(context: WorkflowContext) {
    try {
      this.logger.debug(`Creating workflow machine for execution: ${context.executionId}`);

      // Configure machine with services, guards, and actions
      const configuredMachine = workflowMachine.provide({
        actors: this.machineServices.getMachineServices(),
        guards: allGuards,
        actions: allActions,
        delays: {
          CHECKPOINT_INTERVAL: ({ context }) => {
            return context.options?.checkpointInterval || 300000; // 5 minutes
          },
          TIMEOUT_DELAY: ({ context }) => {
            return context.options?.timeout || 3600000; // 1 hour
          },
          RETRY_DELAY: ({ context }) => {
            const retryCount = context.metadata?.retryCount || 0;
            return Math.min(1000 * Math.pow(2, retryCount), 30000); // Exponential backoff
          },
        },
      });

      // Create actor with initial context
      const workflowActor = createActor(configuredMachine, {
        input: context,
      });

      // Store actor reference
      this.activeWorkflows.set(context.executionId, workflowActor);

      // Setup event listeners
      this.setupWorkflowEventListeners(workflowActor, context);

      return workflowActor;

    } catch (error) {
      this.logger.error(`Failed to create workflow machine for ${context.executionId}:`, error);
      throw error;
    }
  }

  /**
   * Create and configure node execution machine
   */
  createNodeExecutionMachine(nodeContext: NodeExecutionContext, config?: any) {
    try {
      this.logger.debug(`Creating node execution machine for node: ${nodeContext.node.id}`);

      // Configure machine with services, guards, and actions
      const configuredMachine = nodeExecutionMachine.provide({
        actors: this.machineServices.getMachineServices(),
        guards: allGuards,
        actions: allActions,
        delays: {
          RETRY_DELAY: ({ context }) => {
            // Exponential backoff: 1s, 2s, 4s, 8s...
            return Math.min(1000 * Math.pow(2, context.retryCount), 30000);
          },
          EXECUTION_TIMEOUT: ({ context }) => {
            return context.config?.timeout || 300000; // 5 minutes default
          },
        },
      });

      // Create actor
      const nodeActor = createActor(configuredMachine, {
        input: {
          nodeContext,
          config: config || {},
        },
      });

      // Store actor reference
      const nodeKey = `${nodeContext.executionId}-${nodeContext.node.id}`;
      this.activeNodes.set(nodeKey, nodeActor);

      // Setup event listeners
      this.setupNodeEventListeners(nodeActor, nodeContext);

      return nodeActor;

    } catch (error) {
      this.logger.error(`Failed to create node execution machine for ${nodeContext.node.id}:`, error);
      throw error;
    }
  }

  /**
   * Start workflow execution
   */
  async startWorkflow(context: WorkflowContext) {
    try {
      this.logger.log(`Starting workflow execution: ${context.executionId}`);

      const workflowActor = this.createWorkflowMachine(context);
      
      // Start the machine
      workflowActor.start();

      // Send initial load event
      workflowActor.send({
        type: 'LOAD_WORKFLOW',
        workflowId: context.workflowId,
        executionId: context.executionId,
        triggerData: context.triggerData,
      });

      return workflowActor;

    } catch (error) {
      this.logger.error(`Failed to start workflow ${context.executionId}:`, error);
      throw error;
    }
  }

  /**
   * Pause workflow execution
   */
  async pauseWorkflow(executionId: string, reason?: string) {
    try {
      const workflowActor = this.activeWorkflows.get(executionId);
      if (!workflowActor) {
        throw new Error(`Workflow not found: ${executionId}`);
      }

      this.logger.log(`Pausing workflow: ${executionId}`);
      workflowActor.send({
        type: 'PAUSE_EXECUTION',
        reason,
      });

    } catch (error) {
      this.logger.error(`Failed to pause workflow ${executionId}:`, error);
      throw error;
    }
  }

  /**
   * Resume workflow execution
   */
  async resumeWorkflow(executionId: string) {
    try {
      const workflowActor = this.activeWorkflows.get(executionId);
      if (!workflowActor) {
        throw new Error(`Workflow not found: ${executionId}`);
      }

      this.logger.log(`Resuming workflow: ${executionId}`);
      workflowActor.send({
        type: 'RESUME_EXECUTION',
      });

    } catch (error) {
      this.logger.error(`Failed to resume workflow ${executionId}:`, error);
      throw error;
    }
  }

  /**
   * Cancel workflow execution
   */
  async cancelWorkflow(executionId: string, reason?: string) {
    try {
      const workflowActor = this.activeWorkflows.get(executionId);
      if (!workflowActor) {
        throw new Error(`Workflow not found: ${executionId}`);
      }

      this.logger.log(`Cancelling workflow: ${executionId}`);
      workflowActor.send({
        type: 'CANCEL_EXECUTION',
        reason,
      });

    } catch (error) {
      this.logger.error(`Failed to cancel workflow ${executionId}:`, error);
      throw error;
    }
  }

  /**
   * Get workflow status
   */
  getWorkflowStatus(executionId: string) {
    const workflowActor = this.activeWorkflows.get(executionId);
    if (!workflowActor) {
      return null;
    }

    return {
      executionId,
      state: workflowActor.getSnapshot().value,
      context: workflowActor.getSnapshot().context,
      status: this.mapStateToStatus(workflowActor.getSnapshot().value),
    };
  }

  /**
   * Get all active workflows
   */
  getActiveWorkflows() {
    const workflows: any[] = [];
    
    for (const [executionId, actor] of this.activeWorkflows.entries()) {
      workflows.push({
        executionId,
        state: actor.getSnapshot().value,
        status: this.mapStateToStatus(actor.getSnapshot().value),
        startTime: actor.getSnapshot().context.metadata?.startTime,
      });
    }

    return workflows;
  }

  /**
   * Cleanup completed workflow
   */
  cleanupWorkflow(executionId: string) {
    try {
      const workflowActor = this.activeWorkflows.get(executionId);
      if (workflowActor) {
        workflowActor.stop();
        this.activeWorkflows.delete(executionId);
        
        // Cleanup associated node actors
        for (const [nodeKey, nodeActor] of this.activeNodes.entries()) {
          if (nodeKey.startsWith(executionId)) {
            nodeActor.stop();
            this.activeNodes.delete(nodeKey);
          }
        }
        
        this.logger.debug(`Cleaned up workflow: ${executionId}`);
      }
    } catch (error) {
      this.logger.error(`Failed to cleanup workflow ${executionId}:`, error);
    }
  }

  /**
   * Setup workflow event listeners
   */
  private setupWorkflowEventListeners(
    workflowActor: ActorRefFrom<typeof workflowMachine>,
    context: WorkflowContext
  ) {
    // Listen for state changes
    workflowActor.subscribe((snapshot) => {
      this.logger.debug(`Workflow ${context.executionId} state: ${snapshot.value}`);
      
      // Handle final states
      if (snapshot.status === 'done') {
        this.handleWorkflowCompletion(context.executionId, snapshot);
      }
    });

    // Listen for errors
    workflowActor.subscribe({
      error: (error) => {
        this.logger.error(`Workflow ${context.executionId} error:`, error);
        this.handleWorkflowError(context.executionId, error);
      },
    });
  }

  /**
   * Setup node event listeners
   */
  private setupNodeEventListeners(
    nodeActor: ActorRefFrom<typeof nodeExecutionMachine>,
    nodeContext: NodeExecutionContext
  ) {
    // Listen for state changes
    nodeActor.subscribe((snapshot) => {
      this.logger.debug(`Node ${nodeContext.node.id} state: ${snapshot.value}`);
      
      // Handle final states
      if (snapshot.status === 'done') {
        this.handleNodeCompletion(nodeContext, snapshot);
      }
    });

    // Listen for errors
    nodeActor.subscribe({
      error: (error) => {
        this.logger.error(`Node ${nodeContext.node.id} error:`, error);
        this.handleNodeError(nodeContext, error);
      },
    });
  }

  /**
   * Handle workflow completion
   */
  private handleWorkflowCompletion(executionId: string, snapshot: any) {
    this.logger.log(`Workflow completed: ${executionId}`);
    
    // Emit completion event
    // Implementation would emit to event bus or notification system
    
    // Schedule cleanup
    setTimeout(() => {
      this.cleanupWorkflow(executionId);
    }, 5000); // Cleanup after 5 seconds
  }

  /**
   * Handle workflow error
   */
  private handleWorkflowError(executionId: string, error: any) {
    this.logger.error(`Workflow error: ${executionId}`, error);
    
    // Emit error event
    // Implementation would emit to event bus or notification system
  }

  /**
   * Handle node completion
   */
  private handleNodeCompletion(nodeContext: NodeExecutionContext, snapshot: any) {
    this.logger.debug(`Node completed: ${nodeContext.node.id}`);
    
    // Cleanup node actor
    const nodeKey = `${nodeContext.executionId}-${nodeContext.node.id}`;
    this.activeNodes.delete(nodeKey);
  }

  /**
   * Handle node error
   */
  private handleNodeError(nodeContext: NodeExecutionContext, error: any) {
    this.logger.error(`Node error: ${nodeContext.node.id}`, error);
    
    // Cleanup node actor
    const nodeKey = `${nodeContext.executionId}-${nodeContext.node.id}`;
    this.activeNodes.delete(nodeKey);
  }

  /**
   * Map machine state to human-readable status
   */
  private mapStateToStatus(state: any): string {
    if (typeof state === 'string') {
      return state;
    }
    
    if (typeof state === 'object') {
      // Handle parallel states
      if (state.executing) {
        return 'executing';
      }
      
      // Return first state key
      return Object.keys(state)[0] || 'unknown';
    }
    
    return 'unknown';
  }

  /**
   * Get machine statistics
   */
  getMachineStatistics() {
    return {
      activeWorkflows: this.activeWorkflows.size,
      activeNodes: this.activeNodes.size,
      workflowStates: Array.from(this.activeWorkflows.values()).map(actor => 
        this.mapStateToStatus(actor.getSnapshot().value)
      ),
    };
  }

  /**
   * Force cleanup all machines (for shutdown)
   */
  async shutdown() {
    this.logger.log('Shutting down machine integration service');
    
    // Stop all workflow actors
    for (const [executionId, actor] of this.activeWorkflows.entries()) {
      try {
        actor.stop();
      } catch (error) {
        this.logger.error(`Error stopping workflow ${executionId}:`, error);
      }
    }
    
    // Stop all node actors
    for (const [nodeKey, actor] of this.activeNodes.entries()) {
      try {
        actor.stop();
      } catch (error) {
        this.logger.error(`Error stopping node ${nodeKey}:`, error);
      }
    }
    
    // Clear maps
    this.activeWorkflows.clear();
    this.activeNodes.clear();
    
    this.logger.log('Machine integration service shutdown complete');
  }
}
