import {
  <PERSON>,
  <PERSON>,
  Body,
  Headers,
  <PERSON><PERSON>,
  <PERSON>q,
  Res,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { EmailTrackingService } from './services';

/**
 * Controller xử lý webhooks từ email providers
 * Hỗ trợ delivery status, bounces, complaints từ các providers như SendGrid, Mailgun, SES
 */
@Controller('api/email-webhook')
export class EmailWebhookController {
  private readonly logger = new Logger(EmailWebhookController.name);

  constructor(private readonly emailTrackingService: EmailTrackingService) {}

  /**
   * Webhook endpoint cho SendGrid
   * @param body Webhook payload từ SendGrid
   * @param req Request object
   * @param res Response object
   */
  @Post('sendgrid')
  async handleSendGridWebhook(
    @Body() body: any[],
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    try {
      this.logger.debug(`Received SendGrid webhook: ${JSON.stringify(body)}`);

      if (!Array.isArray(body)) {
        res.status(400).send('Invalid webhook payload');
        return;
      }

      for (const event of body) {
        await this.processSendGridEvent(event);
      }

      res.status(200).send('OK');
    } catch (error) {
      this.logger.error(
        `Error processing SendGrid webhook: ${error.message}`,
        error.stack,
      );
      res.status(500).send('Internal Server Error');
    }
  }

  /**
   * Webhook endpoint cho Mailgun
   * @param body Webhook payload từ Mailgun
   * @param signature Mailgun signature để verify
   * @param req Request object
   * @param res Response object
   */
  @Post('mailgun')
  async handleMailgunWebhook(
    @Body() body: any,
    @Headers('X-Mailgun-Signature') signature: string,
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    try {
      this.logger.debug(`Received Mailgun webhook: ${JSON.stringify(body)}`);

      // TODO: Verify Mailgun signature
      // if (!this.verifyMailgunSignature(body, signature)) {
      //   res.status(401).send('Unauthorized');
      //   return;
      // }

      await this.processMailgunEvent(body);
      res.status(200).send('OK');
    } catch (error) {
      this.logger.error(
        `Error processing Mailgun webhook: ${error.message}`,
        error.stack,
      );
      res.status(500).send('Internal Server Error');
    }
  }

  /**
   * Webhook endpoint cho Amazon SES (via SNS)
   * @param body SNS notification từ SES
   * @param req Request object
   * @param res Response object
   */
  @Post('ses')
  async handleSESWebhook(
    @Body() body: any,
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    try {
      this.logger.debug(`Received SES webhook: ${JSON.stringify(body)}`);

      // Handle SNS subscription confirmation
      if (body.Type === 'SubscriptionConfirmation') {
        this.logger.log(`SNS subscription confirmation: ${body.SubscribeURL}`);
        res.status(200).send('OK');
        return;
      }

      // Process SES notification
      if (body.Type === 'Notification' && body.Message) {
        const message = JSON.parse(body.Message);
        await this.processSESEvent(message);
      }

      res.status(200).send('OK');
    } catch (error) {
      this.logger.error(
        `Error processing SES webhook: ${error.message}`,
        error.stack,
      );
      res.status(500).send('Internal Server Error');
    }
  }

  /**
   * Generic SMTP webhook endpoint cho các providers khác
   * @param body Webhook payload
   * @param req Request object
   * @param res Response object
   */
  @Post('smtp')
  async handleSMTPWebhook(
    @Body() body: any,
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    try {
      this.logger.debug(`Received SMTP webhook: ${JSON.stringify(body)}`);

      await this.processGenericSMTPEvent(body);
      res.status(200).send('OK');
    } catch (error) {
      this.logger.error(
        `Error processing SMTP webhook: ${error.message}`,
        error.stack,
      );
      res.status(500).send('Internal Server Error');
    }
  }

  /**
   * Xử lý event từ SendGrid
   * @param event SendGrid event
   */
  private async processSendGridEvent(event: any): Promise<void> {
    try {
      const trackingId = this.extractTrackingIdFromEvent(event);
      if (!trackingId) {
        this.logger.warn(
          `No tracking ID found in SendGrid event: ${JSON.stringify(event)}`,
        );
        return;
      }

      const metadata = {
        provider: 'sendgrid',
        messageId: event.sg_message_id,
        timestamp: event.timestamp,
        reason: event.reason,
        status: event.status,
        ...event,
      };

      switch (event.event) {
        case 'delivered':
          await this.emailTrackingService.trackEmailDelivered(
            trackingId,
            metadata,
          );
          break;
        case 'bounce':
          await this.emailTrackingService.trackEmailBounced(
            trackingId,
            metadata,
          );
          break;
        case 'dropped':
        case 'deferred':
          await this.emailTrackingService.trackEmailBounced(
            trackingId,
            metadata,
          );
          break;
        case 'open':
          await this.emailTrackingService.trackEmailOpened(
            trackingId,
            metadata,
          );
          break;
        case 'click':
          await this.emailTrackingService.trackEmailClicked(
            trackingId,
            event.url,
            metadata,
          );
          break;
        default:
          this.logger.debug(`Unhandled SendGrid event: ${event.event}`);
      }
    } catch (error) {
      this.logger.error(
        `Error processing SendGrid event: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Xử lý event từ Mailgun
   * @param event Mailgun event
   */
  private async processMailgunEvent(event: any): Promise<void> {
    try {
      const trackingId = this.extractTrackingIdFromEvent(event);
      if (!trackingId) {
        this.logger.warn(
          `No tracking ID found in Mailgun event: ${JSON.stringify(event)}`,
        );
        return;
      }

      const metadata = {
        provider: 'mailgun',
        messageId: event['message-id'],
        timestamp: event.timestamp,
        reason: event.reason,
        ...event,
      };

      switch (event.event) {
        case 'delivered':
          await this.emailTrackingService.trackEmailDelivered(
            trackingId,
            metadata,
          );
          break;
        case 'failed':
        case 'bounced':
          await this.emailTrackingService.trackEmailBounced(
            trackingId,
            metadata,
          );
          break;
        case 'opened':
          await this.emailTrackingService.trackEmailOpened(
            trackingId,
            metadata,
          );
          break;
        case 'clicked':
          await this.emailTrackingService.trackEmailClicked(
            trackingId,
            event.url,
            metadata,
          );
          break;
        default:
          this.logger.debug(`Unhandled Mailgun event: ${event.event}`);
      }
    } catch (error) {
      this.logger.error(
        `Error processing Mailgun event: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Xử lý event từ Amazon SES
   * @param message SES message
   */
  private async processSESEvent(message: any): Promise<void> {
    try {
      const trackingId = this.extractTrackingIdFromEvent(message);
      if (!trackingId) {
        this.logger.warn(
          `No tracking ID found in SES event: ${JSON.stringify(message)}`,
        );
        return;
      }

      const metadata = {
        provider: 'ses',
        messageId: message.mail?.messageId,
        timestamp: message.mail?.timestamp,
        ...message,
      };

      if (message.eventType === 'delivery') {
        await this.emailTrackingService.trackEmailDelivered(
          trackingId,
          metadata,
        );
      } else if (message.eventType === 'bounce') {
        await this.emailTrackingService.trackEmailBounced(trackingId, metadata);
      } else if (message.eventType === 'complaint') {
        await this.emailTrackingService.trackEmailBounced(trackingId, metadata);
      }
    } catch (error) {
      this.logger.error(
        `Error processing SES event: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Xử lý generic SMTP event
   * @param event Generic SMTP event
   */
  private async processGenericSMTPEvent(event: any): Promise<void> {
    try {
      const trackingId = this.extractTrackingIdFromEvent(event);
      if (!trackingId) {
        this.logger.warn(
          `No tracking ID found in SMTP event: ${JSON.stringify(event)}`,
        );
        return;
      }

      const metadata = {
        provider: 'smtp',
        ...event,
      };

      switch (event.type || event.event) {
        case 'delivered':
        case 'delivery':
          await this.emailTrackingService.trackEmailDelivered(
            trackingId,
            metadata,
          );
          break;
        case 'bounced':
        case 'bounce':
        case 'failed':
          await this.emailTrackingService.trackEmailBounced(
            trackingId,
            metadata,
          );
          break;
        default:
          this.logger.debug(
            `Unhandled SMTP event: ${event.type || event.event}`,
          );
      }
    } catch (error) {
      this.logger.error(
        `Error processing SMTP event: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Trích xuất tracking ID từ event
   * @param event Event data
   * @returns Tracking ID hoặc null
   */
  private extractTrackingIdFromEvent(event: any): string | null {
    // Thử các cách khác nhau để lấy tracking ID
    if (event.trackingId) {
      return event.trackingId;
    }

    // Từ custom headers
    if (event.customHeaders && event.customHeaders['X-Tracking-ID']) {
      return event.customHeaders['X-Tracking-ID'];
    }

    // Từ message ID (nếu có format đặc biệt)
    if (event.messageId && event.messageId.includes('tracking-')) {
      const match = event.messageId.match(/tracking-([^@]+)/);
      return match ? match[1] : null;
    }

    // Từ email subject hoặc content (nếu có embed tracking ID)
    if (event.subject && event.subject.includes('[tracking:')) {
      const match = event.subject.match(/\[tracking:([^\]]+)\]/);
      return match ? match[1] : null;
    }

    return null;
  }
}
