import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { AppModule } from '../../../app.module';
import { ZaloTokenUtilsService } from './zalo-token-utils.service';
import { ZaloOATokenAdapterService } from './zalo-oa-token-adapter.service';

/**
 * Script test refresh token Zalo thực tế
 * Chạy: npm run start:dev -- --script=test-refresh-token
 */

async function testRefreshToken() {
  const logger = new Logger('ZaloTokenTest');
  
  try {
    logger.log('🚀 Starting Zalo Token Refresh Test...');
    
    // Khởi tạo NestJS app
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // Lấy services
    const zaloTokenUtils = app.get(ZaloTokenUtilsService);
    const zaloOATokenAdapter = app.get(ZaloOATokenAdapterService);

    logger.log('✅ Services initialized successfully');

    // Test 1: Liệt kê các OA trong database
    logger.log('📋 Test 1: Listing Official Accounts...');
    const accounts = await zaloOATokenAdapter.findAll();

    logger.log(`Found ${accounts.length} Official Accounts:`);
    accounts.slice(0, 5).forEach((account, index) => {
      const expiresDate = new Date(account.expiresAt);
      const isExpired = account.expiresAt <= Date.now();

      logger.log(`${index + 1}. OA: ${account.oaId} (${account.name})`);
      logger.log(`   - User ID: ${account.userId}, DB ID: ${account.id}`);
      logger.log(`   - Expires: ${expiresDate.toISOString()} ${isExpired ? '(EXPIRED)' : '(VALID)'}`);
    });

    if (accounts.length === 0) {
      logger.warn('⚠️ No Official Accounts found. Please add some OAs first.');
      await app.close();
      return;
    }
    
    // Chọn OA đầu tiên để test
    const testOa = accounts[0];
    logger.log(`🎯 Selected OA for testing: ${testOa.oaId} (${testOa.name})`);
    
    // Test 2: Kiểm tra trạng thái OA
    logger.log('📋 Test 2: Checking OA status...');
    const isActive = await zaloTokenUtils.isOfficialAccountActive(testOa.oaId);
    logger.log(`OA ${testOa.oaId} is active: ${isActive}`);
    
    if (!isActive) {
      logger.error('❌ Selected OA is not active');
      await app.close();
      return;
    }
    
    // Test 3: Lấy thông tin chi tiết OA
    logger.log('📋 Test 3: Getting OA details...');
    const oaDetails = await zaloTokenUtils.getOfficialAccount(testOa.oaId);
    if (oaDetails) {
      logger.log(`OA Details:`);
      logger.log(`  - Name: ${oaDetails.name}`);
      logger.log(`  - Status: ${oaDetails.status}`);
      logger.log(`  - Expires: ${new Date(oaDetails.expiresAt).toISOString()}`);
      logger.log(`  - Has Refresh Token: ${!!oaDetails.refreshToken}`);
      logger.log(`  - Token Preview: ${oaDetails.accessToken.substring(0, 30)}...`);
    }
    
    // Test 4: Test lấy access token (sẽ refresh nếu cần)
    logger.log('📋 Test 4: Getting access token with retry...');
    try {
      const startTime = Date.now();
      const accessToken = await zaloTokenUtils.getValidAccessTokenWithRetry(testOa.oaId, 3);
      const endTime = Date.now();
      
      logger.log(`✅ Successfully got access token in ${endTime - startTime}ms`);
      logger.log(`Token length: ${accessToken.length} characters`);
      logger.log(`Token preview: ${accessToken.substring(0, 30)}...`);
    } catch (error: any) {
      logger.error(`❌ Failed to get access token: ${error.message}`);
    }
    
    // Test 5: Test với userId và zaloOfficialAccountId
    logger.log('📋 Test 5: Getting access token by user and OA ID...');
    try {
      const startTime = Date.now();
      const accessToken = await zaloTokenUtils.getValidAccessTokenWithRetry(
        testOa.userId.toString(), 
        testOa.id.toString(), 
        3
      );
      const endTime = Date.now();
      
      logger.log(`✅ Successfully got access token in ${endTime - startTime}ms`);
      logger.log(`Token preview: ${accessToken.substring(0, 30)}...`);
    } catch (error: any) {
      logger.error(`❌ Failed to get access token: ${error.message}`);
    }
    
    // Test 6: Test API call với retry mechanism
    logger.log('📋 Test 6: Testing API call with retry mechanism...');
    try {
      const result = await zaloTokenUtils.executeWithTokenRetry(
        async (accessToken) => {
          // Test với API lấy thông tin OA
          const response = await fetch('https://openapi.zalo.me/v2.0/oa/getoa', {
            method: 'GET',
            headers: {
              'access_token': accessToken,
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          return await response.json();
        },
        testOa.oaId,
        3
      );

      logger.log(`✅ API call successful`);
      logger.log(`OA Info from API:`, JSON.stringify(result, null, 2));
    } catch (error: any) {
      logger.error(`❌ API call failed: ${error.message}`);
    }
    
    // Test 7: Test force refresh (cẩn thận!)
    const shouldTestForceRefresh = process.argv.includes('--force-refresh');
    if (shouldTestForceRefresh) {
      logger.log('📋 Test 7: Testing force refresh token...');
      logger.warn('⚠️ This will force expire the current token and refresh it');
      
      try {
        // Lấy token hiện tại
        const currentOa = await zaloTokenUtils.getOfficialAccount(testOa.oaId);
        if (currentOa) {
          logger.log(`Current token expires: ${new Date(currentOa.expiresAt).toISOString()}`);

          // Force expire token thông qua adapter service
          await zaloOATokenAdapter.save({
            id: currentOa.id,
            expiresAt: Date.now() - 1000, // 1 giây trước
            status: 'active',
          });
          
          logger.log('🔄 Forced token expiration, now testing refresh...');
          
          // Lấy token mới (sẽ trigger refresh)
          const startTime = Date.now();
          const newAccessToken = await zaloTokenUtils.getValidAccessTokenWithRetry(testOa.oaId, 3);
          const endTime = Date.now();
          
          logger.log(`✅ Token refreshed successfully in ${endTime - startTime}ms`);
          logger.log(`New token preview: ${newAccessToken.substring(0, 30)}...`);
          
          // Kiểm tra token mới
          const refreshedOa = await zaloTokenUtils.getOfficialAccount(testOa.oaId);
          if (refreshedOa) {
            logger.log(`New token expires: ${new Date(refreshedOa.expiresAt).toISOString()}`);
            logger.log(`Token changed: ${currentOa.accessToken !== newAccessToken}`);
          }
        }
      } catch (error: any) {
        logger.error(`❌ Force refresh test failed: ${error.message}`);
      }
    } else {
      logger.log('📋 Test 7: Skipped force refresh test (use --force-refresh to enable)');
    }
    
    // Test 8: Cleanup expired tokens
    logger.log('📋 Test 8: Testing cleanup expired tokens...');
    try {
      const startTime = Date.now();
      await zaloTokenUtils.cleanupExpiredTokens();
      const endTime = Date.now();
      
      logger.log(`✅ Cleanup completed in ${endTime - startTime}ms`);
    } catch (error: any) {
      logger.error(`❌ Cleanup failed: ${error.message}`);
    }
    
    logger.log('🏁 All tests completed!');
    logger.log('💡 To test force refresh, run with --force-refresh flag');
    
    await app.close();
    
  } catch (error: any) {
    logger.error(`❌ Test script failed: ${error.message}`, error.stack);
    process.exit(1);
  }
}

// Chạy test nếu file này được execute trực tiếp
if (require.main === module) {
  testRefreshToken();
}

export { testRefreshToken };
