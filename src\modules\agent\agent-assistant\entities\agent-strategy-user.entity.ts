import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { IStrategyContentStep } from '../interfaces/strategy-content-step.interface';

/**
 * AgentStrategyUser entity
 * Stores information about strategy agents owned by users, including custom examples and ownership time
 */
@Entity('agents_strategy_user')
export class AgentStrategyUser {
  /**
   * UUID unique identifier for agents_strategy_user record
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Foreign key reference to agents_strategy table
   */
  @Column({ name: 'agents_strategy_id', type: 'uuid', nullable: true })
  agentsStrategyId?: string;

  /**
   * List of custom examples for strategy agent per user, JSONB format
   */
  @Column({ name: 'example', type: 'jsonb', default: '[]' })
  example: IStrategyContentStep[];

  /**
   * User ID who owns this strategy, references users table
   */
  @Column({ name: 'user_id', type: 'int', nullable: true })
  userId?: number;

  /**
   * Time when user owned the strategy, calculated in epoch time (milliseconds)
   */
  @Column({ 
    name: 'owned_at', 
    type: 'bigint', 
    nullable: false,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint'
  })
  ownedAt: number;
}
